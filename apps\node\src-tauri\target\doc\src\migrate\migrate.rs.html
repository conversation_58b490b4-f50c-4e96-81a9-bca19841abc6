<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\bin\migrate.rs`."><title>migrate.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="migrate" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">migrate/</div>migrate.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>sqlx::SqlitePool;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>std::env;
<a href=#3 id=3 data-nosnippet>3</a>
<a href=#4 id=4 data-nosnippet>4</a><span class="attr">#[tokio::main]
<a href=#5 id=5 data-nosnippet>5</a></span><span class="kw">async fn </span>main() -&gt; <span class="prelude-ty">Result</span>&lt;(), Box&lt;<span class="kw">dyn </span>std::error::Error&gt;&gt; {
<a href=#6 id=6 data-nosnippet>6</a>    <span class="comment">// 从环境变量或默认值获取数据库URL
<a href=#7 id=7 data-nosnippet>7</a>    </span><span class="kw">let </span>database_url = env::var(<span class="string">"SQLITE_DATABASE_URL"</span>)
<a href=#8 id=8 data-nosnippet>8</a>        .unwrap_or_else(|<span class="kw">_</span>| <span class="string">"sqlite:./data/crawler_node.db"</span>.to_string());
<a href=#9 id=9 data-nosnippet>9</a>
<a href=#10 id=10 data-nosnippet>10</a>    <span class="macro">println!</span>(<span class="string">"连接数据库: {}"</span>, database_url);
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a>    <span class="comment">// 确保数据目录存在
<a href=#13 id=13 data-nosnippet>13</a>    </span><span class="kw">if let </span><span class="prelude-val">Some</span>(parent) = std::path::Path::new(database_url.trim_start_matches(<span class="string">"sqlite:"</span>)).parent() {
<a href=#14 id=14 data-nosnippet>14</a>        tokio::fs::create_dir_all(parent).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#15 id=15 data-nosnippet>15</a>    }
<a href=#16 id=16 data-nosnippet>16</a>
<a href=#17 id=17 data-nosnippet>17</a>    <span class="comment">// 连接数据库
<a href=#18 id=18 data-nosnippet>18</a>    </span><span class="kw">let </span>pool = SqlitePool::connect(<span class="kw-2">&amp;</span>database_url).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#19 id=19 data-nosnippet>19</a>
<a href=#20 id=20 data-nosnippet>20</a>    <span class="macro">println!</span>(<span class="string">"运行数据库迁移..."</span>);
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a>    <span class="comment">// 运行迁移
<a href=#23 id=23 data-nosnippet>23</a>    </span><span class="macro">sqlx::migrate!</span>(<span class="string">"./migrations"</span>).run(<span class="kw-2">&amp;</span>pool).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#24 id=24 data-nosnippet>24</a>
<a href=#25 id=25 data-nosnippet>25</a>    <span class="macro">println!</span>(<span class="string">"数据库迁移完成！"</span>);
<a href=#26 id=26 data-nosnippet>26</a>
<a href=#27 id=27 data-nosnippet>27</a>    pool.close().<span class="kw">await</span>;
<a href=#28 id=28 data-nosnippet>28</a>    <span class="prelude-val">Ok</span>(())
<a href=#29 id=29 data-nosnippet>29</a>}</code></pre></div></section></main></body></html>