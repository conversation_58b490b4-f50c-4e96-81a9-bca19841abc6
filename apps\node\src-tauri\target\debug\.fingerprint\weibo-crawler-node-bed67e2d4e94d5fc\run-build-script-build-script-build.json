{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9430400796423198903, "build_script_build", false, 15153237506077225485]], "local": [{"RerunIfChanged": {"output": "debug\\build\\weibo-crawler-node-bed67e2d4e94d5fc\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}