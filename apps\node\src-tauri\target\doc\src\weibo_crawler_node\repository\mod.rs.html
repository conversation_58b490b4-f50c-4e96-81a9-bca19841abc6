<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\repository\mod.rs`."><title>mod.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node\repository/</div>mod.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::Result;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>async_trait::async_trait;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>sqlx::SqlitePool;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::sync::Arc;
<a href=#5 id=5 data-nosnippet>5</a>
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">pub mod </span>task_repository;
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">pub mod </span>proxy_repository;
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">pub mod </span>account_repository;
<a href=#9 id=9 data-nosnippet>9</a>
<a href=#10 id=10 data-nosnippet>10</a><span class="kw">pub use </span>task_repository::TaskRepository;
<a href=#11 id=11 data-nosnippet>11</a><span class="kw">pub use </span>proxy_repository::ProxyRepository;
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">pub use </span>account_repository::AccountRepository;
<a href=#13 id=13 data-nosnippet>13</a>
<a href=#14 id=14 data-nosnippet>14</a><span class="doccomment">/// 基础仓库特征
<a href=#15 id=15 data-nosnippet>15</a></span><span class="attr">#[async_trait]
<a href=#16 id=16 data-nosnippet>16</a></span><span class="kw">pub trait </span>Repository&lt;T, ID&gt; {
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">async fn </span>find_by_id(<span class="kw-2">&amp;</span><span class="self">self</span>, id: ID) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;T&gt;&gt;;
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">async fn </span>find_all(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;T&gt;&gt;;
<a href=#19 id=19 data-nosnippet>19</a>    <span class="kw">async fn </span>create(<span class="kw-2">&amp;</span><span class="self">self</span>, entity: T) -&gt; <span class="prelude-ty">Result</span>&lt;T&gt;;
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">async fn </span>update(<span class="kw-2">&amp;</span><span class="self">self</span>, id: ID, entity: T) -&gt; <span class="prelude-ty">Result</span>&lt;T&gt;;
<a href=#21 id=21 data-nosnippet>21</a>    <span class="kw">async fn </span>delete(<span class="kw-2">&amp;</span><span class="self">self</span>, id: ID) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">async fn </span>count(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;i64&gt;;
<a href=#23 id=23 data-nosnippet>23</a>}
<a href=#24 id=24 data-nosnippet>24</a>
<a href=#25 id=25 data-nosnippet>25</a><span class="doccomment">/// 分页查询特征
<a href=#26 id=26 data-nosnippet>26</a></span><span class="attr">#[async_trait]
<a href=#27 id=27 data-nosnippet>27</a></span><span class="kw">pub trait </span>PaginatedRepository&lt;T&gt; {
<a href=#28 id=28 data-nosnippet>28</a>    <span class="kw">async fn </span>find_paginated(<span class="kw-2">&amp;</span><span class="self">self</span>, page: u32, limit: u32) -&gt; <span class="prelude-ty">Result</span>&lt;PaginatedResult&lt;T&gt;&gt;;
<a href=#29 id=29 data-nosnippet>29</a>}
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a><span class="doccomment">/// 分页结果
<a href=#32 id=32 data-nosnippet>32</a></span><span class="attr">#[derive(Debug, Clone)]
<a href=#33 id=33 data-nosnippet>33</a></span><span class="kw">pub struct </span>PaginatedResult&lt;T&gt; {
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">pub </span>items: Vec&lt;T&gt;,
<a href=#35 id=35 data-nosnippet>35</a>    <span class="kw">pub </span>total: i64,
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>page: u32,
<a href=#37 id=37 data-nosnippet>37</a>    <span class="kw">pub </span>limit: u32,
<a href=#38 id=38 data-nosnippet>38</a>    <span class="kw">pub </span>total_pages: u32,
<a href=#39 id=39 data-nosnippet>39</a>}
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a><span class="kw">impl</span>&lt;T&gt; PaginatedResult&lt;T&gt; {
<a href=#42 id=42 data-nosnippet>42</a>    <span class="kw">pub fn </span>new(items: Vec&lt;T&gt;, total: i64, page: u32, limit: u32) -&gt; <span class="self">Self </span>{
<a href=#43 id=43 data-nosnippet>43</a>        <span class="kw">let </span>total_pages = <span class="kw">if </span>limit &gt; <span class="number">0 </span>{
<a href=#44 id=44 data-nosnippet>44</a>            ((total <span class="kw">as </span>f64) / (limit <span class="kw">as </span>f64)).ceil() <span class="kw">as </span>u32
<a href=#45 id=45 data-nosnippet>45</a>        } <span class="kw">else </span>{
<a href=#46 id=46 data-nosnippet>46</a>            <span class="number">0
<a href=#47 id=47 data-nosnippet>47</a>        </span>};
<a href=#48 id=48 data-nosnippet>48</a>
<a href=#49 id=49 data-nosnippet>49</a>        <span class="self">Self </span>{
<a href=#50 id=50 data-nosnippet>50</a>            items,
<a href=#51 id=51 data-nosnippet>51</a>            total,
<a href=#52 id=52 data-nosnippet>52</a>            page,
<a href=#53 id=53 data-nosnippet>53</a>            limit,
<a href=#54 id=54 data-nosnippet>54</a>            total_pages,
<a href=#55 id=55 data-nosnippet>55</a>        }
<a href=#56 id=56 data-nosnippet>56</a>    }
<a href=#57 id=57 data-nosnippet>57</a>}
<a href=#58 id=58 data-nosnippet>58</a>
<a href=#59 id=59 data-nosnippet>59</a><span class="doccomment">/// 仓库管理器 - 统一管理所有仓库
<a href=#60 id=60 data-nosnippet>60</a></span><span class="kw">pub struct </span>RepositoryManager {
<a href=#61 id=61 data-nosnippet>61</a>    <span class="kw">pub </span>task: TaskRepository,
<a href=#62 id=62 data-nosnippet>62</a>    <span class="kw">pub </span>proxy: ProxyRepository,
<a href=#63 id=63 data-nosnippet>63</a>    <span class="kw">pub </span>account: AccountRepository,
<a href=#64 id=64 data-nosnippet>64</a>}
<a href=#65 id=65 data-nosnippet>65</a>
<a href=#66 id=66 data-nosnippet>66</a><span class="kw">impl </span>RepositoryManager {
<a href=#67 id=67 data-nosnippet>67</a>    <span class="kw">pub fn </span>new(pool: Arc&lt;SqlitePool&gt;) -&gt; <span class="self">Self </span>{
<a href=#68 id=68 data-nosnippet>68</a>        <span class="self">Self </span>{
<a href=#69 id=69 data-nosnippet>69</a>            task: TaskRepository::new(pool.clone()),
<a href=#70 id=70 data-nosnippet>70</a>            proxy: ProxyRepository::new(pool.clone()),
<a href=#71 id=71 data-nosnippet>71</a>            account: AccountRepository::new(pool),
<a href=#72 id=72 data-nosnippet>72</a>        }
<a href=#73 id=73 data-nosnippet>73</a>    }
<a href=#74 id=74 data-nosnippet>74</a>}
<a href=#75 id=75 data-nosnippet>75</a>
<a href=#76 id=76 data-nosnippet>76</a><span class="doccomment">/// 查询构建器特征
<a href=#77 id=77 data-nosnippet>77</a></span><span class="kw">pub trait </span>QueryBuilder {
<a href=#78 id=78 data-nosnippet>78</a>    <span class="kw">type </span>Query;
<a href=#79 id=79 data-nosnippet>79</a>    
<a href=#80 id=80 data-nosnippet>80</a>    <span class="kw">fn </span>new() -&gt; <span class="self">Self</span>;
<a href=#81 id=81 data-nosnippet>81</a>    <span class="kw">fn </span>filter(<span class="self">self</span>, field: <span class="kw-2">&amp;</span>str, value: <span class="kw-2">&amp;</span>str) -&gt; <span class="self">Self</span>;
<a href=#82 id=82 data-nosnippet>82</a>    <span class="kw">fn </span>order_by(<span class="self">self</span>, field: <span class="kw-2">&amp;</span>str, direction: OrderDirection) -&gt; <span class="self">Self</span>;
<a href=#83 id=83 data-nosnippet>83</a>    <span class="kw">fn </span>limit(<span class="self">self</span>, limit: u32) -&gt; <span class="self">Self</span>;
<a href=#84 id=84 data-nosnippet>84</a>    <span class="kw">fn </span>offset(<span class="self">self</span>, offset: u32) -&gt; <span class="self">Self</span>;
<a href=#85 id=85 data-nosnippet>85</a>    <span class="kw">fn </span>build(<span class="self">self</span>) -&gt; <span class="self">Self</span>::Query;
<a href=#86 id=86 data-nosnippet>86</a>}
<a href=#87 id=87 data-nosnippet>87</a>
<a href=#88 id=88 data-nosnippet>88</a><span class="doccomment">/// 排序方向
<a href=#89 id=89 data-nosnippet>89</a></span><span class="attr">#[derive(Debug, Clone, Copy)]
<a href=#90 id=90 data-nosnippet>90</a></span><span class="kw">pub enum </span>OrderDirection {
<a href=#91 id=91 data-nosnippet>91</a>    Asc,
<a href=#92 id=92 data-nosnippet>92</a>    Desc,
<a href=#93 id=93 data-nosnippet>93</a>}
<a href=#94 id=94 data-nosnippet>94</a>
<a href=#95 id=95 data-nosnippet>95</a><span class="kw">impl </span>std::fmt::Display <span class="kw">for </span>OrderDirection {
<a href=#96 id=96 data-nosnippet>96</a>    <span class="kw">fn </span>fmt(<span class="kw-2">&amp;</span><span class="self">self</span>, f: <span class="kw-2">&amp;mut </span>std::fmt::Formatter&lt;<span class="lifetime">'_</span>&gt;) -&gt; std::fmt::Result {
<a href=#97 id=97 data-nosnippet>97</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#98 id=98 data-nosnippet>98</a>            OrderDirection::Asc =&gt; <span class="macro">write!</span>(f, <span class="string">"ASC"</span>),
<a href=#99 id=99 data-nosnippet>99</a>            OrderDirection::Desc =&gt; <span class="macro">write!</span>(f, <span class="string">"DESC"</span>),
<a href=#100 id=100 data-nosnippet>100</a>        }
<a href=#101 id=101 data-nosnippet>101</a>    }
<a href=#102 id=102 data-nosnippet>102</a>}
<a href=#103 id=103 data-nosnippet>103</a>
<a href=#104 id=104 data-nosnippet>104</a><span class="doccomment">/// 过滤条件
<a href=#105 id=105 data-nosnippet>105</a></span><span class="attr">#[derive(Debug, Clone)]
<a href=#106 id=106 data-nosnippet>106</a></span><span class="kw">pub struct </span>Filter {
<a href=#107 id=107 data-nosnippet>107</a>    <span class="kw">pub </span>field: String,
<a href=#108 id=108 data-nosnippet>108</a>    <span class="kw">pub </span>operator: FilterOperator,
<a href=#109 id=109 data-nosnippet>109</a>    <span class="kw">pub </span>value: FilterValue,
<a href=#110 id=110 data-nosnippet>110</a>}
<a href=#111 id=111 data-nosnippet>111</a>
<a href=#112 id=112 data-nosnippet>112</a><span class="attr">#[derive(Debug, Clone)]
<a href=#113 id=113 data-nosnippet>113</a></span><span class="kw">pub enum </span>FilterOperator {
<a href=#114 id=114 data-nosnippet>114</a>    Equal,
<a href=#115 id=115 data-nosnippet>115</a>    NotEqual,
<a href=#116 id=116 data-nosnippet>116</a>    GreaterThan,
<a href=#117 id=117 data-nosnippet>117</a>    GreaterThanOrEqual,
<a href=#118 id=118 data-nosnippet>118</a>    LessThan,
<a href=#119 id=119 data-nosnippet>119</a>    LessThanOrEqual,
<a href=#120 id=120 data-nosnippet>120</a>    Like,
<a href=#121 id=121 data-nosnippet>121</a>    In,
<a href=#122 id=122 data-nosnippet>122</a>    NotIn,
<a href=#123 id=123 data-nosnippet>123</a>    IsNull,
<a href=#124 id=124 data-nosnippet>124</a>    IsNotNull,
<a href=#125 id=125 data-nosnippet>125</a>}
<a href=#126 id=126 data-nosnippet>126</a>
<a href=#127 id=127 data-nosnippet>127</a><span class="attr">#[derive(Debug, Clone)]
<a href=#128 id=128 data-nosnippet>128</a></span><span class="kw">pub enum </span>FilterValue {
<a href=#129 id=129 data-nosnippet>129</a>    String(String),
<a href=#130 id=130 data-nosnippet>130</a>    Integer(i64),
<a href=#131 id=131 data-nosnippet>131</a>    Float(f64),
<a href=#132 id=132 data-nosnippet>132</a>    Boolean(bool),
<a href=#133 id=133 data-nosnippet>133</a>    Array(Vec&lt;String&gt;),
<a href=#134 id=134 data-nosnippet>134</a>    Null,
<a href=#135 id=135 data-nosnippet>135</a>}
<a href=#136 id=136 data-nosnippet>136</a>
<a href=#137 id=137 data-nosnippet>137</a><span class="kw">impl </span>std::fmt::Display <span class="kw">for </span>FilterOperator {
<a href=#138 id=138 data-nosnippet>138</a>    <span class="kw">fn </span>fmt(<span class="kw-2">&amp;</span><span class="self">self</span>, f: <span class="kw-2">&amp;mut </span>std::fmt::Formatter&lt;<span class="lifetime">'_</span>&gt;) -&gt; std::fmt::Result {
<a href=#139 id=139 data-nosnippet>139</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#140 id=140 data-nosnippet>140</a>            FilterOperator::Equal =&gt; <span class="macro">write!</span>(f, <span class="string">"="</span>),
<a href=#141 id=141 data-nosnippet>141</a>            FilterOperator::NotEqual =&gt; <span class="macro">write!</span>(f, <span class="string">"!="</span>),
<a href=#142 id=142 data-nosnippet>142</a>            FilterOperator::GreaterThan =&gt; <span class="macro">write!</span>(f, <span class="string">"&gt;"</span>),
<a href=#143 id=143 data-nosnippet>143</a>            FilterOperator::GreaterThanOrEqual =&gt; <span class="macro">write!</span>(f, <span class="string">"&gt;="</span>),
<a href=#144 id=144 data-nosnippet>144</a>            FilterOperator::LessThan =&gt; <span class="macro">write!</span>(f, <span class="string">"&lt;"</span>),
<a href=#145 id=145 data-nosnippet>145</a>            FilterOperator::LessThanOrEqual =&gt; <span class="macro">write!</span>(f, <span class="string">"&lt;="</span>),
<a href=#146 id=146 data-nosnippet>146</a>            FilterOperator::Like =&gt; <span class="macro">write!</span>(f, <span class="string">"LIKE"</span>),
<a href=#147 id=147 data-nosnippet>147</a>            FilterOperator::In =&gt; <span class="macro">write!</span>(f, <span class="string">"IN"</span>),
<a href=#148 id=148 data-nosnippet>148</a>            FilterOperator::NotIn =&gt; <span class="macro">write!</span>(f, <span class="string">"NOT IN"</span>),
<a href=#149 id=149 data-nosnippet>149</a>            FilterOperator::IsNull =&gt; <span class="macro">write!</span>(f, <span class="string">"IS NULL"</span>),
<a href=#150 id=150 data-nosnippet>150</a>            FilterOperator::IsNotNull =&gt; <span class="macro">write!</span>(f, <span class="string">"IS NOT NULL"</span>),
<a href=#151 id=151 data-nosnippet>151</a>        }
<a href=#152 id=152 data-nosnippet>152</a>    }
<a href=#153 id=153 data-nosnippet>153</a>}
<a href=#154 id=154 data-nosnippet>154</a>
<a href=#155 id=155 data-nosnippet>155</a><span class="doccomment">/// 通用查询参数
<a href=#156 id=156 data-nosnippet>156</a></span><span class="attr">#[derive(Debug, Clone)]
<a href=#157 id=157 data-nosnippet>157</a></span><span class="kw">pub struct </span>QueryParams {
<a href=#158 id=158 data-nosnippet>158</a>    <span class="kw">pub </span>filters: Vec&lt;Filter&gt;,
<a href=#159 id=159 data-nosnippet>159</a>    <span class="kw">pub </span>order_by: <span class="prelude-ty">Option</span>&lt;(String, OrderDirection)&gt;,
<a href=#160 id=160 data-nosnippet>160</a>    <span class="kw">pub </span>limit: <span class="prelude-ty">Option</span>&lt;u32&gt;,
<a href=#161 id=161 data-nosnippet>161</a>    <span class="kw">pub </span>offset: <span class="prelude-ty">Option</span>&lt;u32&gt;,
<a href=#162 id=162 data-nosnippet>162</a>}
<a href=#163 id=163 data-nosnippet>163</a>
<a href=#164 id=164 data-nosnippet>164</a><span class="kw">impl </span>Default <span class="kw">for </span>QueryParams {
<a href=#165 id=165 data-nosnippet>165</a>    <span class="kw">fn </span>default() -&gt; <span class="self">Self </span>{
<a href=#166 id=166 data-nosnippet>166</a>        <span class="self">Self </span>{
<a href=#167 id=167 data-nosnippet>167</a>            filters: Vec::new(),
<a href=#168 id=168 data-nosnippet>168</a>            order_by: <span class="prelude-val">None</span>,
<a href=#169 id=169 data-nosnippet>169</a>            limit: <span class="prelude-val">None</span>,
<a href=#170 id=170 data-nosnippet>170</a>            offset: <span class="prelude-val">None</span>,
<a href=#171 id=171 data-nosnippet>171</a>        }
<a href=#172 id=172 data-nosnippet>172</a>    }
<a href=#173 id=173 data-nosnippet>173</a>}
<a href=#174 id=174 data-nosnippet>174</a>
<a href=#175 id=175 data-nosnippet>175</a><span class="kw">impl </span>QueryParams {
<a href=#176 id=176 data-nosnippet>176</a>    <span class="kw">pub fn </span>new() -&gt; <span class="self">Self </span>{
<a href=#177 id=177 data-nosnippet>177</a>        <span class="self">Self</span>::default()
<a href=#178 id=178 data-nosnippet>178</a>    }
<a href=#179 id=179 data-nosnippet>179</a>
<a href=#180 id=180 data-nosnippet>180</a>    <span class="kw">pub fn </span>filter(<span class="kw-2">mut </span><span class="self">self</span>, field: <span class="kw-2">&amp;</span>str, operator: FilterOperator, value: FilterValue) -&gt; <span class="self">Self </span>{
<a href=#181 id=181 data-nosnippet>181</a>        <span class="self">self</span>.filters.push(Filter {
<a href=#182 id=182 data-nosnippet>182</a>            field: field.to_string(),
<a href=#183 id=183 data-nosnippet>183</a>            operator,
<a href=#184 id=184 data-nosnippet>184</a>            value,
<a href=#185 id=185 data-nosnippet>185</a>        });
<a href=#186 id=186 data-nosnippet>186</a>        <span class="self">self
<a href=#187 id=187 data-nosnippet>187</a>    </span>}
<a href=#188 id=188 data-nosnippet>188</a>
<a href=#189 id=189 data-nosnippet>189</a>    <span class="kw">pub fn </span>order_by(<span class="kw-2">mut </span><span class="self">self</span>, field: <span class="kw-2">&amp;</span>str, direction: OrderDirection) -&gt; <span class="self">Self </span>{
<a href=#190 id=190 data-nosnippet>190</a>        <span class="self">self</span>.order_by = <span class="prelude-val">Some</span>((field.to_string(), direction));
<a href=#191 id=191 data-nosnippet>191</a>        <span class="self">self
<a href=#192 id=192 data-nosnippet>192</a>    </span>}
<a href=#193 id=193 data-nosnippet>193</a>
<a href=#194 id=194 data-nosnippet>194</a>    <span class="kw">pub fn </span>limit(<span class="kw-2">mut </span><span class="self">self</span>, limit: u32) -&gt; <span class="self">Self </span>{
<a href=#195 id=195 data-nosnippet>195</a>        <span class="self">self</span>.limit = <span class="prelude-val">Some</span>(limit);
<a href=#196 id=196 data-nosnippet>196</a>        <span class="self">self
<a href=#197 id=197 data-nosnippet>197</a>    </span>}
<a href=#198 id=198 data-nosnippet>198</a>
<a href=#199 id=199 data-nosnippet>199</a>    <span class="kw">pub fn </span>offset(<span class="kw-2">mut </span><span class="self">self</span>, offset: u32) -&gt; <span class="self">Self </span>{
<a href=#200 id=200 data-nosnippet>200</a>        <span class="self">self</span>.offset = <span class="prelude-val">Some</span>(offset);
<a href=#201 id=201 data-nosnippet>201</a>        <span class="self">self
<a href=#202 id=202 data-nosnippet>202</a>    </span>}
<a href=#203 id=203 data-nosnippet>203</a>
<a href=#204 id=204 data-nosnippet>204</a>    <span class="kw">pub fn </span>page(<span class="kw-2">mut </span><span class="self">self</span>, page: u32, limit: u32) -&gt; <span class="self">Self </span>{
<a href=#205 id=205 data-nosnippet>205</a>        <span class="self">self</span>.limit = <span class="prelude-val">Some</span>(limit);
<a href=#206 id=206 data-nosnippet>206</a>        <span class="self">self</span>.offset = <span class="prelude-val">Some</span>((page.saturating_sub(<span class="number">1</span>)) * limit);
<a href=#207 id=207 data-nosnippet>207</a>        <span class="self">self
<a href=#208 id=208 data-nosnippet>208</a>    </span>}
<a href=#209 id=209 data-nosnippet>209</a>}
<a href=#210 id=210 data-nosnippet>210</a>
<a href=#211 id=211 data-nosnippet>211</a><span class="doccomment">/// 事务管理特征
<a href=#212 id=212 data-nosnippet>212</a></span><span class="attr">#[async_trait]
<a href=#213 id=213 data-nosnippet>213</a></span><span class="kw">pub trait </span>TransactionManager {
<a href=#214 id=214 data-nosnippet>214</a>    <span class="kw">type </span>Transaction;
<a href=#215 id=215 data-nosnippet>215</a>    
<a href=#216 id=216 data-nosnippet>216</a>    <span class="kw">async fn </span>begin_transaction(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>::Transaction&gt;;
<a href=#217 id=217 data-nosnippet>217</a>    <span class="kw">async fn </span>commit_transaction(<span class="kw-2">&amp;</span><span class="self">self</span>, tx: <span class="self">Self</span>::Transaction) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#218 id=218 data-nosnippet>218</a>    <span class="kw">async fn </span>rollback_transaction(<span class="kw-2">&amp;</span><span class="self">self</span>, tx: <span class="self">Self</span>::Transaction) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#219 id=219 data-nosnippet>219</a>}
<a href=#220 id=220 data-nosnippet>220</a>
<a href=#221 id=221 data-nosnippet>221</a><span class="doccomment">/// 缓存特征
<a href=#222 id=222 data-nosnippet>222</a></span><span class="attr">#[async_trait]
<a href=#223 id=223 data-nosnippet>223</a></span><span class="kw">pub trait </span>Cacheable&lt;T&gt; {
<a href=#224 id=224 data-nosnippet>224</a>    <span class="kw">async fn </span>get_from_cache(<span class="kw-2">&amp;</span><span class="self">self</span>, key: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;T&gt;&gt;;
<a href=#225 id=225 data-nosnippet>225</a>    <span class="kw">async fn </span>set_cache(<span class="kw-2">&amp;</span><span class="self">self</span>, key: <span class="kw-2">&amp;</span>str, value: <span class="kw-2">&amp;</span>T, ttl: <span class="prelude-ty">Option</span>&lt;u64&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#226 id=226 data-nosnippet>226</a>    <span class="kw">async fn </span>invalidate_cache(<span class="kw-2">&amp;</span><span class="self">self</span>, key: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#227 id=227 data-nosnippet>227</a>    <span class="kw">async fn </span>invalidate_cache_pattern(<span class="kw-2">&amp;</span><span class="self">self</span>, pattern: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#228 id=228 data-nosnippet>228</a>}
<a href=#229 id=229 data-nosnippet>229</a>
<a href=#230 id=230 data-nosnippet>230</a><span class="doccomment">/// 审计日志特征
<a href=#231 id=231 data-nosnippet>231</a></span><span class="attr">#[async_trait]
<a href=#232 id=232 data-nosnippet>232</a></span><span class="kw">pub trait </span>Auditable {
<a href=#233 id=233 data-nosnippet>233</a>    <span class="kw">async fn </span>log_create(<span class="kw-2">&amp;</span><span class="self">self</span>, entity_type: <span class="kw-2">&amp;</span>str, entity_id: <span class="kw-2">&amp;</span>str, data: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#234 id=234 data-nosnippet>234</a>    <span class="kw">async fn </span>log_update(<span class="kw-2">&amp;</span><span class="self">self</span>, entity_type: <span class="kw-2">&amp;</span>str, entity_id: <span class="kw-2">&amp;</span>str, old_data: <span class="kw-2">&amp;</span>str, new_data: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#235 id=235 data-nosnippet>235</a>    <span class="kw">async fn </span>log_delete(<span class="kw-2">&amp;</span><span class="self">self</span>, entity_type: <span class="kw-2">&amp;</span>str, entity_id: <span class="kw-2">&amp;</span>str, data: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#236 id=236 data-nosnippet>236</a>}
<a href=#237 id=237 data-nosnippet>237</a>
<a href=#238 id=238 data-nosnippet>238</a><span class="doccomment">/// 软删除特征
<a href=#239 id=239 data-nosnippet>239</a></span><span class="attr">#[async_trait]
<a href=#240 id=240 data-nosnippet>240</a></span><span class="kw">pub trait </span>SoftDeletable&lt;T, ID&gt; {
<a href=#241 id=241 data-nosnippet>241</a>    <span class="kw">async fn </span>soft_delete(<span class="kw-2">&amp;</span><span class="self">self</span>, id: ID) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#242 id=242 data-nosnippet>242</a>    <span class="kw">async fn </span>restore(<span class="kw-2">&amp;</span><span class="self">self</span>, id: ID) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#243 id=243 data-nosnippet>243</a>    <span class="kw">async fn </span>find_deleted(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;T&gt;&gt;;
<a href=#244 id=244 data-nosnippet>244</a>    <span class="kw">async fn </span>permanently_delete(<span class="kw-2">&amp;</span><span class="self">self</span>, id: ID) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#245 id=245 data-nosnippet>245</a>}</code></pre></div></section></main></body></html>