import { useState, useEffect, useCallback, useRef } from 'react'

/**
 * 防抖 Hook - 延迟更新值直到指定时间内没有新的更新
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * 防抖回调 Hook - 防抖执行回调函数
 */
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback)
  const timeoutRef = useRef<NodeJS.Timeout>()

  // 更新回调引用
  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return useCallback(
    ((...args: any[]) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        callbackRef.current(...args)
      }, delay)
    }) as T,
    [delay]
  )
}

/**
 * 节流 Hook - 限制函数执行频率
 */
export function useThrottle<T>(value: T, limit: number): T {
  const [throttledValue, setThrottledValue] = useState<T>(value)
  const lastRan = useRef<number>(Date.now())

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value)
        lastRan.current = Date.now()
      }
    }, limit - (Date.now() - lastRan.current))

    return () => {
      clearTimeout(handler)
    }
  }, [value, limit])

  return throttledValue
}

/**
 * 节流回调 Hook - 节流执行回调函数
 */
export function useThrottledCallback<T extends (...args: any[]) => any>(
  callback: T,
  limit: number
): T {
  const callbackRef = useRef(callback)
  const lastRan = useRef<number>(0)

  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  return useCallback(
    ((...args: any[]) => {
      if (Date.now() - lastRan.current >= limit) {
        callbackRef.current(...args)
        lastRan.current = Date.now()
      }
    }) as T,
    [limit]
  )
}

/**
 * 搜索防抖 Hook - 专门用于搜索输入的防抖处理
 */
export function useSearchDebounce(
  searchTerm: string,
  delay: number = 300
): {
  debouncedSearchTerm: string
  isSearching: boolean
} {
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm)
  const [isSearching, setIsSearching] = useState(false)

  useEffect(() => {
    setIsSearching(true)
    
    const handler = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm)
      setIsSearching(false)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [searchTerm, delay])

  return {
    debouncedSearchTerm,
    isSearching
  }
}

/**
 * 输入防抖 Hook - 结合输入状态和防抖逻辑
 */
export function useInputDebounce(
  initialValue: string = '',
  delay: number = 300
): {
  value: string
  debouncedValue: string
  setValue: (value: string) => void
  isDebouncing: boolean
} {
  const [value, setValue] = useState(initialValue)
  const [isDebouncing, setIsDebouncing] = useState(false)
  const debouncedValue = useDebounce(value, delay)

  useEffect(() => {
    if (value !== debouncedValue) {
      setIsDebouncing(true)
    } else {
      setIsDebouncing(false)
    }
  }, [value, debouncedValue])

  return {
    value,
    debouncedValue,
    setValue,
    isDebouncing
  }
}
