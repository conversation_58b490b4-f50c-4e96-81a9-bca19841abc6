{"rustc": 1842507548689473721, "features": "[\"compression\", \"default\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"path-all\", \"regex\", \"shell-open\", \"shell-open-api\", \"sys-locale\", \"tauri-runtime-wry\", \"window-close\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 5144538945173598434, "path": 2505088397572599310, "deps": [[40386456601120721, "percent_encoding", false, 14003801311530373528], [1260461579271933187, "serialize_to_javascript", false, 1838381830133384643], [1441306149310335789, "tempfile", false, 16862597425369836560], [3150220818285335163, "url", false, 1746054398145793718], [3722963349756955755, "once_cell", false, 3092077161891181902], [3988549704697787137, "open", false, 355046466722865958], [4381063397040571828, "webview2_com", false, 2334546348545801766], [4405182208873388884, "http", false, 5891631273443115995], [4450062412064442726, "dirs_next", false, 1054019940291537570], [4899080583175475170, "semver", false, 10287098372774563739], [5024769281214949041, "os_info", false, 6106329757691208920], [5180608563399064494, "tauri_macros", false, 5783580968359669956], [5610773616282026064, "build_script_build", false, 1921655379469001006], [5986029879202738730, "log", false, 13216658706447473378], [7653476968652377684, "windows", false, 4435937259384173537], [8008191657135824715, "thiserror", false, 5374037758448816721], [8292277814562636972, "tauri_utils", false, 11396416046195520354], [8319709847752024821, "uuid", false, 9442600809944700742], [8569119365930580996, "serde_json", false, 3777298738341353814], [9451456094439810778, "regex", false, 14688798598914106188], [9623796893764309825, "ignore", false, 6923961828225850390], [9689903380558560274, "serde", false, 4389908846715814937], [9920160576179037441, "getrandom", false, 15414650663404202376], [10629569228670356391, "futures_util", false, 15644822958053016495], [11601763207901161556, "tar", false, 15158337506635623740], [11693073011723388840, "raw_window_handle", false, 2576869162297233201], [11989259058781683633, "dunce", false, 1955325503927001036], [12944427623413450645, "tokio", false, 13941386680418085679], [12986574360607194341, "serde_repr", false, 880615272535003271], [13208667028893622512, "rand", false, 5662173108048055673], [13625485746686963219, "anyhow", false, 11319210697648472172], [14162324460024849578, "tauri_runtime", false, 1129283759608522050], [14564311161534545801, "encoding_rs", false, 17236660259466684603], [14618885535728128396, "sys_locale", false, 11917265496688315558], [16228250612241359704, "tauri_runtime_wry", false, 1691424366440806525], [17155886227862585100, "glob", false, 970660011238121307], [17278893514130263345, "state", false, 5941134223198748290], [17772299992546037086, "flate2", false, 2124762649658070698]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-e6f28168aef0562c\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}