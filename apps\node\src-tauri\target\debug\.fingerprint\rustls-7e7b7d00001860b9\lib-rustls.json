{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 5788819337146887687, "path": 8742123966112767284, "deps": [[2883436298747778685, "pki_types", false, 12344869982792536159], [3722963349756955755, "once_cell", false, 6428016074931338643], [5491919304041016563, "ring", false, 16164389815359145875], [6528079939221783635, "zeroize", false, 8516335403938672892], [8151164558401866693, "<PERSON><PERSON><PERSON>", false, 8566922821231415663], [17003143334332120809, "subtle", false, 3973293851168920472], [17956658536657219733, "build_script_build", false, 18006552977241695110]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-7e7b7d00001860b9\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}