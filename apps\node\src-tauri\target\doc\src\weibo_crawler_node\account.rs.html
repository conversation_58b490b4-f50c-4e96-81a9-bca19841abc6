<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\account.rs`."><title>account.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>account.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{AppError, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>reqwest::{Client, cookie::Jar};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::collections::HashMap;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>std::sync::Arc;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>std::time::Duration;
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span>tokio::sync::RwLock;
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">use </span>tracing::{info, error};
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">use </span>uuid::Uuid;
<a href=#10 id=10 data-nosnippet>10</a>
<a href=#11 id=11 data-nosnippet>11</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#12 id=12 data-nosnippet>12</a></span><span class="kw">pub struct </span>Account {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>id: String,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>username: String,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>password: String, <span class="comment">// 应该加密存储
<a href=#16 id=16 data-nosnippet>16</a>    </span><span class="kw">pub </span>phone: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>email: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">pub </span>status: AccountStatus,
<a href=#19 id=19 data-nosnippet>19</a>    <span class="kw">pub </span>login_count: u64,
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">pub </span>last_login: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#21 id=21 data-nosnippet>21</a>    <span class="kw">pub </span>last_activity: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">pub </span>risk_score: f64, <span class="comment">// 0.0-1.0
<a href=#23 id=23 data-nosnippet>23</a>    </span><span class="kw">pub </span>cookies: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#24 id=24 data-nosnippet>24</a>    <span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#25 id=25 data-nosnippet>25</a>}
<a href=#26 id=26 data-nosnippet>26</a>
<a href=#27 id=27 data-nosnippet>27</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#28 id=28 data-nosnippet>28</a></span><span class="kw">pub enum </span>AccountStatus {
<a href=#29 id=29 data-nosnippet>29</a>    Normal,
<a href=#30 id=30 data-nosnippet>30</a>    Banned,
<a href=#31 id=31 data-nosnippet>31</a>    Abnormal,
<a href=#32 id=32 data-nosnippet>32</a>    Maintenance,
<a href=#33 id=33 data-nosnippet>33</a>}
<a href=#34 id=34 data-nosnippet>34</a>
<a href=#35 id=35 data-nosnippet>35</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#36 id=36 data-nosnippet>36</a></span><span class="kw">pub struct </span>LoginSession {
<a href=#37 id=37 data-nosnippet>37</a>    <span class="kw">pub </span>account_id: String,
<a href=#38 id=38 data-nosnippet>38</a>    <span class="kw">pub </span>session_id: String,
<a href=#39 id=39 data-nosnippet>39</a>    <span class="kw">pub </span>cookies: String,
<a href=#40 id=40 data-nosnippet>40</a>    <span class="kw">pub </span>user_agent: String,
<a href=#41 id=41 data-nosnippet>41</a>    <span class="kw">pub </span>login_time: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#42 id=42 data-nosnippet>42</a>    <span class="kw">pub </span>last_used: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#43 id=43 data-nosnippet>43</a>    <span class="kw">pub </span>is_valid: bool,
<a href=#44 id=44 data-nosnippet>44</a>}
<a href=#45 id=45 data-nosnippet>45</a>
<a href=#46 id=46 data-nosnippet>46</a><span class="kw">pub struct </span>AccountManager {
<a href=#47 id=47 data-nosnippet>47</a>    accounts: Arc&lt;RwLock&lt;HashMap&lt;String, Account&gt;&gt;&gt;,
<a href=#48 id=48 data-nosnippet>48</a>    sessions: Arc&lt;RwLock&lt;HashMap&lt;String, LoginSession&gt;&gt;&gt;,
<a href=#49 id=49 data-nosnippet>49</a>    <span class="attr">#[allow(dead_code)]
<a href=#50 id=50 data-nosnippet>50</a>    </span>login_client: Client,
<a href=#51 id=51 data-nosnippet>51</a>    max_pool_size: usize,
<a href=#52 id=52 data-nosnippet>52</a>    rotation_interval: Duration,
<a href=#53 id=53 data-nosnippet>53</a>}
<a href=#54 id=54 data-nosnippet>54</a>
<a href=#55 id=55 data-nosnippet>55</a><span class="kw">impl </span>AccountManager {
<a href=#56 id=56 data-nosnippet>56</a>    <span class="kw">pub async fn </span>new(max_pool_size: usize) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#57 id=57 data-nosnippet>57</a>        <span class="kw">let </span>cookie_jar = Arc::new(Jar::default());
<a href=#58 id=58 data-nosnippet>58</a>        
<a href=#59 id=59 data-nosnippet>59</a>        <span class="kw">let </span>login_client = Client::builder()
<a href=#60 id=60 data-nosnippet>60</a>            .cookie_provider(cookie_jar)
<a href=#61 id=61 data-nosnippet>61</a>            .timeout(Duration::from_secs(<span class="number">30</span>))
<a href=#62 id=62 data-nosnippet>62</a>            .user_agent(<span class="string">"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"</span>)
<a href=#63 id=63 data-nosnippet>63</a>            .build()
<a href=#64 id=64 data-nosnippet>64</a>            .map_err(|e| AppError::Account(<span class="macro">format!</span>(<span class="string">"创建登录客户端失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#65 id=65 data-nosnippet>65</a>
<a href=#66 id=66 data-nosnippet>66</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#67 id=67 data-nosnippet>67</a>            accounts: Arc::new(RwLock::new(HashMap::new())),
<a href=#68 id=68 data-nosnippet>68</a>            sessions: Arc::new(RwLock::new(HashMap::new())),
<a href=#69 id=69 data-nosnippet>69</a>            login_client,
<a href=#70 id=70 data-nosnippet>70</a>            max_pool_size,
<a href=#71 id=71 data-nosnippet>71</a>            rotation_interval: Duration::from_secs(<span class="number">3600</span>), <span class="comment">// 1小时
<a href=#72 id=72 data-nosnippet>72</a>        </span>})
<a href=#73 id=73 data-nosnippet>73</a>    }
<a href=#74 id=74 data-nosnippet>74</a>
<a href=#75 id=75 data-nosnippet>75</a>    <span class="kw">pub async fn </span>add_account(<span class="kw-2">&amp;</span><span class="self">self</span>, account_config: <span class="kw">crate</span>::commands::AccountConfig) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#76 id=76 data-nosnippet>76</a>        <span class="kw">let </span>account_id = Uuid::new_v4().to_string();
<a href=#77 id=77 data-nosnippet>77</a>
<a href=#78 id=78 data-nosnippet>78</a>        <span class="comment">// 检查账号池大小
<a href=#79 id=79 data-nosnippet>79</a>        </span>{
<a href=#80 id=80 data-nosnippet>80</a>            <span class="kw">let </span>accounts = <span class="self">self</span>.accounts.read().<span class="kw">await</span>;
<a href=#81 id=81 data-nosnippet>81</a>            <span class="kw">if </span>accounts.len() &gt;= <span class="self">self</span>.max_pool_size {
<a href=#82 id=82 data-nosnippet>82</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Account(<span class="string">"账号池已满"</span>.to_string()));
<a href=#83 id=83 data-nosnippet>83</a>            }
<a href=#84 id=84 data-nosnippet>84</a>        }
<a href=#85 id=85 data-nosnippet>85</a>
<a href=#86 id=86 data-nosnippet>86</a>        <span class="comment">// 检查用户名是否已存在
<a href=#87 id=87 data-nosnippet>87</a>        </span>{
<a href=#88 id=88 data-nosnippet>88</a>            <span class="kw">let </span>accounts = <span class="self">self</span>.accounts.read().<span class="kw">await</span>;
<a href=#89 id=89 data-nosnippet>89</a>            <span class="kw">for </span>account <span class="kw">in </span>accounts.values() {
<a href=#90 id=90 data-nosnippet>90</a>                <span class="kw">if </span>account.username == account_config.username {
<a href=#91 id=91 data-nosnippet>91</a>                    <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Account(<span class="string">"用户名已存在"</span>.to_string()));
<a href=#92 id=92 data-nosnippet>92</a>                }
<a href=#93 id=93 data-nosnippet>93</a>            }
<a href=#94 id=94 data-nosnippet>94</a>        }
<a href=#95 id=95 data-nosnippet>95</a>
<a href=#96 id=96 data-nosnippet>96</a>        <span class="kw">let </span>account = Account {
<a href=#97 id=97 data-nosnippet>97</a>            id: account_id.clone(),
<a href=#98 id=98 data-nosnippet>98</a>            username: account_config.username,
<a href=#99 id=99 data-nosnippet>99</a>            password: account_config.password, <span class="comment">// TODO: 应该加密存储
<a href=#100 id=100 data-nosnippet>100</a>            </span>phone: account_config.phone,
<a href=#101 id=101 data-nosnippet>101</a>            email: account_config.email,
<a href=#102 id=102 data-nosnippet>102</a>            status: AccountStatus::Normal,
<a href=#103 id=103 data-nosnippet>103</a>            login_count: <span class="number">0</span>,
<a href=#104 id=104 data-nosnippet>104</a>            last_login: <span class="prelude-val">None</span>,
<a href=#105 id=105 data-nosnippet>105</a>            last_activity: <span class="prelude-val">None</span>,
<a href=#106 id=106 data-nosnippet>106</a>            risk_score: <span class="number">0.0</span>,
<a href=#107 id=107 data-nosnippet>107</a>            cookies: <span class="prelude-val">None</span>,
<a href=#108 id=108 data-nosnippet>108</a>            created_at: chrono::Utc::now(),
<a href=#109 id=109 data-nosnippet>109</a>        };
<a href=#110 id=110 data-nosnippet>110</a>
<a href=#111 id=111 data-nosnippet>111</a>        <span class="kw">let </span><span class="kw-2">mut </span>accounts = <span class="self">self</span>.accounts.write().<span class="kw">await</span>;
<a href=#112 id=112 data-nosnippet>112</a>        accounts.insert(account_id.clone(), account);
<a href=#113 id=113 data-nosnippet>113</a>
<a href=#114 id=114 data-nosnippet>114</a>        <span class="macro">info!</span>(<span class="string">"账号 {} 添加成功"</span>, account_id);
<a href=#115 id=115 data-nosnippet>115</a>        <span class="prelude-val">Ok</span>(account_id)
<a href=#116 id=116 data-nosnippet>116</a>    }
<a href=#117 id=117 data-nosnippet>117</a>
<a href=#118 id=118 data-nosnippet>118</a>    <span class="kw">pub async fn </span>remove_account(<span class="kw-2">&amp;</span><span class="self">self</span>, account_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#119 id=119 data-nosnippet>119</a>        <span class="kw">let </span><span class="kw-2">mut </span>accounts = <span class="self">self</span>.accounts.write().<span class="kw">await</span>;
<a href=#120 id=120 data-nosnippet>120</a>        
<a href=#121 id=121 data-nosnippet>121</a>        <span class="kw">if </span>accounts.remove(account_id).is_some() {
<a href=#122 id=122 data-nosnippet>122</a>            <span class="comment">// 同时移除相关的登录会话
<a href=#123 id=123 data-nosnippet>123</a>            </span><span class="kw">let </span><span class="kw-2">mut </span>sessions = <span class="self">self</span>.sessions.write().<span class="kw">await</span>;
<a href=#124 id=124 data-nosnippet>124</a>            sessions.retain(|<span class="kw">_</span>, session| session.account_id != account_id);
<a href=#125 id=125 data-nosnippet>125</a>            
<a href=#126 id=126 data-nosnippet>126</a>            <span class="macro">info!</span>(<span class="string">"账号 {} 移除成功"</span>, account_id);
<a href=#127 id=127 data-nosnippet>127</a>            <span class="prelude-val">Ok</span>(())
<a href=#128 id=128 data-nosnippet>128</a>        } <span class="kw">else </span>{
<a href=#129 id=129 data-nosnippet>129</a>            <span class="prelude-val">Err</span>(AppError::Account(<span class="macro">format!</span>(<span class="string">"账号 {} 不存在"</span>, account_id)))
<a href=#130 id=130 data-nosnippet>130</a>        }
<a href=#131 id=131 data-nosnippet>131</a>    }
<a href=#132 id=132 data-nosnippet>132</a>
<a href=#133 id=133 data-nosnippet>133</a>    <span class="kw">pub async fn </span>login_account(<span class="kw-2">&amp;</span><span class="self">self</span>, account_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="kw">crate</span>::commands::LoginResult&gt; {
<a href=#134 id=134 data-nosnippet>134</a>        <span class="kw">let </span>account = {
<a href=#135 id=135 data-nosnippet>135</a>            <span class="kw">let </span>accounts = <span class="self">self</span>.accounts.read().<span class="kw">await</span>;
<a href=#136 id=136 data-nosnippet>136</a>            accounts.get(account_id).cloned()
<a href=#137 id=137 data-nosnippet>137</a>                .ok_or_else(|| AppError::Account(<span class="macro">format!</span>(<span class="string">"账号 {} 不存在"</span>, account_id)))<span class="question-mark">?
<a href=#138 id=138 data-nosnippet>138</a>        </span>};
<a href=#139 id=139 data-nosnippet>139</a>
<a href=#140 id=140 data-nosnippet>140</a>        <span class="kw">if </span>!<span class="macro">matches!</span>(account.status, AccountStatus::Normal) {
<a href=#141 id=141 data-nosnippet>141</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="kw">crate</span>::commands::LoginResult {
<a href=#142 id=142 data-nosnippet>142</a>                account_id: account_id.to_string(),
<a href=#143 id=143 data-nosnippet>143</a>                success: <span class="bool-val">false</span>,
<a href=#144 id=144 data-nosnippet>144</a>                error_message: <span class="prelude-val">Some</span>(<span class="string">"账号状态异常"</span>.to_string()),
<a href=#145 id=145 data-nosnippet>145</a>                cookies: <span class="prelude-val">None</span>,
<a href=#146 id=146 data-nosnippet>146</a>            });
<a href=#147 id=147 data-nosnippet>147</a>        }
<a href=#148 id=148 data-nosnippet>148</a>
<a href=#149 id=149 data-nosnippet>149</a>        <span class="comment">// 执行登录逻辑
<a href=#150 id=150 data-nosnippet>150</a>        </span><span class="kw">match </span><span class="self">self</span>.perform_login(<span class="kw-2">&amp;</span>account).<span class="kw">await </span>{
<a href=#151 id=151 data-nosnippet>151</a>            <span class="prelude-val">Ok</span>(session) =&gt; {
<a href=#152 id=152 data-nosnippet>152</a>                <span class="comment">// 更新账号信息
<a href=#153 id=153 data-nosnippet>153</a>                </span>{
<a href=#154 id=154 data-nosnippet>154</a>                    <span class="kw">let </span><span class="kw-2">mut </span>accounts = <span class="self">self</span>.accounts.write().<span class="kw">await</span>;
<a href=#155 id=155 data-nosnippet>155</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(acc) = accounts.get_mut(account_id) {
<a href=#156 id=156 data-nosnippet>156</a>                        acc.login_count += <span class="number">1</span>;
<a href=#157 id=157 data-nosnippet>157</a>                        acc.last_login = <span class="prelude-val">Some</span>(chrono::Utc::now());
<a href=#158 id=158 data-nosnippet>158</a>                        acc.last_activity = <span class="prelude-val">Some</span>(chrono::Utc::now());
<a href=#159 id=159 data-nosnippet>159</a>                        acc.cookies = <span class="prelude-val">Some</span>(session.cookies.clone());
<a href=#160 id=160 data-nosnippet>160</a>                    }
<a href=#161 id=161 data-nosnippet>161</a>                }
<a href=#162 id=162 data-nosnippet>162</a>
<a href=#163 id=163 data-nosnippet>163</a>                <span class="comment">// 保存登录会话
<a href=#164 id=164 data-nosnippet>164</a>                </span>{
<a href=#165 id=165 data-nosnippet>165</a>                    <span class="kw">let </span><span class="kw-2">mut </span>sessions = <span class="self">self</span>.sessions.write().<span class="kw">await</span>;
<a href=#166 id=166 data-nosnippet>166</a>                    sessions.insert(session.session_id.clone(), session.clone());
<a href=#167 id=167 data-nosnippet>167</a>                }
<a href=#168 id=168 data-nosnippet>168</a>
<a href=#169 id=169 data-nosnippet>169</a>                <span class="macro">info!</span>(<span class="string">"账号 {} 登录成功"</span>, account_id);
<a href=#170 id=170 data-nosnippet>170</a>                <span class="prelude-val">Ok</span>(<span class="kw">crate</span>::commands::LoginResult {
<a href=#171 id=171 data-nosnippet>171</a>                    account_id: account_id.to_string(),
<a href=#172 id=172 data-nosnippet>172</a>                    success: <span class="bool-val">true</span>,
<a href=#173 id=173 data-nosnippet>173</a>                    error_message: <span class="prelude-val">None</span>,
<a href=#174 id=174 data-nosnippet>174</a>                    cookies: <span class="prelude-val">Some</span>(session.cookies),
<a href=#175 id=175 data-nosnippet>175</a>                })
<a href=#176 id=176 data-nosnippet>176</a>            }
<a href=#177 id=177 data-nosnippet>177</a>            <span class="prelude-val">Err</span>(e) =&gt; {
<a href=#178 id=178 data-nosnippet>178</a>                <span class="macro">error!</span>(<span class="string">"账号 {} 登录失败: {}"</span>, account_id, e);
<a href=#179 id=179 data-nosnippet>179</a>                
<a href=#180 id=180 data-nosnippet>180</a>                <span class="comment">// 增加风险评分
<a href=#181 id=181 data-nosnippet>181</a>                </span>{
<a href=#182 id=182 data-nosnippet>182</a>                    <span class="kw">let </span><span class="kw-2">mut </span>accounts = <span class="self">self</span>.accounts.write().<span class="kw">await</span>;
<a href=#183 id=183 data-nosnippet>183</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(acc) = accounts.get_mut(account_id) {
<a href=#184 id=184 data-nosnippet>184</a>                        acc.risk_score = (acc.risk_score + <span class="number">0.1</span>).min(<span class="number">1.0</span>);
<a href=#185 id=185 data-nosnippet>185</a>                        
<a href=#186 id=186 data-nosnippet>186</a>                        <span class="comment">// 如果风险评分过高，标记为异常
<a href=#187 id=187 data-nosnippet>187</a>                        </span><span class="kw">if </span>acc.risk_score &gt; <span class="number">0.8 </span>{
<a href=#188 id=188 data-nosnippet>188</a>                            acc.status = AccountStatus::Abnormal;
<a href=#189 id=189 data-nosnippet>189</a>                        }
<a href=#190 id=190 data-nosnippet>190</a>                    }
<a href=#191 id=191 data-nosnippet>191</a>                }
<a href=#192 id=192 data-nosnippet>192</a>
<a href=#193 id=193 data-nosnippet>193</a>                <span class="prelude-val">Ok</span>(<span class="kw">crate</span>::commands::LoginResult {
<a href=#194 id=194 data-nosnippet>194</a>                    account_id: account_id.to_string(),
<a href=#195 id=195 data-nosnippet>195</a>                    success: <span class="bool-val">false</span>,
<a href=#196 id=196 data-nosnippet>196</a>                    error_message: <span class="prelude-val">Some</span>(e.to_string()),
<a href=#197 id=197 data-nosnippet>197</a>                    cookies: <span class="prelude-val">None</span>,
<a href=#198 id=198 data-nosnippet>198</a>                })
<a href=#199 id=199 data-nosnippet>199</a>            }
<a href=#200 id=200 data-nosnippet>200</a>        }
<a href=#201 id=201 data-nosnippet>201</a>    }
<a href=#202 id=202 data-nosnippet>202</a>
<a href=#203 id=203 data-nosnippet>203</a>    <span class="kw">async fn </span>perform_login(<span class="kw-2">&amp;</span><span class="self">self</span>, account: <span class="kw-2">&amp;</span>Account) -&gt; <span class="prelude-ty">Result</span>&lt;LoginSession&gt; {
<a href=#204 id=204 data-nosnippet>204</a>        <span class="comment">// 这里应该实现实际的微博登录逻辑
<a href=#205 id=205 data-nosnippet>205</a>        // 目前返回模拟的登录会话
<a href=#206 id=206 data-nosnippet>206</a>        
<a href=#207 id=207 data-nosnippet>207</a>        // 模拟登录延时
<a href=#208 id=208 data-nosnippet>208</a>        </span>tokio::time::sleep(Duration::from_millis(<span class="number">1000</span>)).<span class="kw">await</span>;
<a href=#209 id=209 data-nosnippet>209</a>
<a href=#210 id=210 data-nosnippet>210</a>        <span class="kw">let </span>session_id = Uuid::new_v4().to_string();
<a href=#211 id=211 data-nosnippet>211</a>        <span class="kw">let </span>cookies = <span class="macro">format!</span>(<span class="string">"session_id={}; user_id={}"</span>, session_id, account.id);
<a href=#212 id=212 data-nosnippet>212</a>
<a href=#213 id=213 data-nosnippet>213</a>        <span class="prelude-val">Ok</span>(LoginSession {
<a href=#214 id=214 data-nosnippet>214</a>            account_id: account.id.clone(),
<a href=#215 id=215 data-nosnippet>215</a>            session_id,
<a href=#216 id=216 data-nosnippet>216</a>            cookies,
<a href=#217 id=217 data-nosnippet>217</a>            user_agent: <span class="string">"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"</span>.to_string(),
<a href=#218 id=218 data-nosnippet>218</a>            login_time: chrono::Utc::now(),
<a href=#219 id=219 data-nosnippet>219</a>            last_used: chrono::Utc::now(),
<a href=#220 id=220 data-nosnippet>220</a>            is_valid: <span class="bool-val">true</span>,
<a href=#221 id=221 data-nosnippet>221</a>        })
<a href=#222 id=222 data-nosnippet>222</a>    }
<a href=#223 id=223 data-nosnippet>223</a>
<a href=#224 id=224 data-nosnippet>224</a>    <span class="kw">pub async fn </span>refresh_cookies(<span class="kw-2">&amp;</span><span class="self">self</span>, account_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#225 id=225 data-nosnippet>225</a>        <span class="kw">let </span>_account = {
<a href=#226 id=226 data-nosnippet>226</a>            <span class="kw">let </span>accounts = <span class="self">self</span>.accounts.read().<span class="kw">await</span>;
<a href=#227 id=227 data-nosnippet>227</a>            accounts.get(account_id).cloned()
<a href=#228 id=228 data-nosnippet>228</a>                .ok_or_else(|| AppError::Account(<span class="macro">format!</span>(<span class="string">"账号 {} 不存在"</span>, account_id)))<span class="question-mark">?
<a href=#229 id=229 data-nosnippet>229</a>        </span>};
<a href=#230 id=230 data-nosnippet>230</a>
<a href=#231 id=231 data-nosnippet>231</a>        <span class="comment">// 重新登录以刷新Cookie
<a href=#232 id=232 data-nosnippet>232</a>        </span><span class="kw">let </span>login_result = <span class="self">self</span>.login_account(account_id).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#233 id=233 data-nosnippet>233</a>        
<a href=#234 id=234 data-nosnippet>234</a>        <span class="kw">if </span>login_result.success {
<a href=#235 id=235 data-nosnippet>235</a>            <span class="macro">info!</span>(<span class="string">"账号 {} Cookie刷新成功"</span>, account_id);
<a href=#236 id=236 data-nosnippet>236</a>            <span class="prelude-val">Ok</span>(())
<a href=#237 id=237 data-nosnippet>237</a>        } <span class="kw">else </span>{
<a href=#238 id=238 data-nosnippet>238</a>            <span class="prelude-val">Err</span>(AppError::Account(<span class="macro">format!</span>(<span class="string">"Cookie刷新失败: {:?}"</span>, login_result.error_message)))
<a href=#239 id=239 data-nosnippet>239</a>        }
<a href=#240 id=240 data-nosnippet>240</a>    }
<a href=#241 id=241 data-nosnippet>241</a>
<a href=#242 id=242 data-nosnippet>242</a>    <span class="kw">pub async fn </span>get_available_account(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;Account&gt;&gt; {
<a href=#243 id=243 data-nosnippet>243</a>        <span class="kw">let </span>accounts = <span class="self">self</span>.accounts.read().<span class="kw">await</span>;
<a href=#244 id=244 data-nosnippet>244</a>        
<a href=#245 id=245 data-nosnippet>245</a>        <span class="comment">// 找到可用的账号，优先选择风险评分低的
<a href=#246 id=246 data-nosnippet>246</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>available_accounts: Vec&lt;<span class="kw-2">&amp;</span>Account&gt; = accounts
<a href=#247 id=247 data-nosnippet>247</a>            .values()
<a href=#248 id=248 data-nosnippet>248</a>            .filter(|a| <span class="macro">matches!</span>(a.status, AccountStatus::Normal) &amp;&amp; a.risk_score &lt; <span class="number">0.5</span>)
<a href=#249 id=249 data-nosnippet>249</a>            .collect();
<a href=#250 id=250 data-nosnippet>250</a>
<a href=#251 id=251 data-nosnippet>251</a>        <span class="kw">if </span>available_accounts.is_empty() {
<a href=#252 id=252 data-nosnippet>252</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>);
<a href=#253 id=253 data-nosnippet>253</a>        }
<a href=#254 id=254 data-nosnippet>254</a>
<a href=#255 id=255 data-nosnippet>255</a>        <span class="comment">// 按风险评分排序
<a href=#256 id=256 data-nosnippet>256</a>        </span>available_accounts.sort_by(|a, b| a.risk_score.partial_cmp(<span class="kw-2">&amp;</span>b.risk_score).unwrap());
<a href=#257 id=257 data-nosnippet>257</a>
<a href=#258 id=258 data-nosnippet>258</a>        <span class="prelude-val">Ok</span>(available_accounts.first().map(|a| (<span class="kw-2">*</span>a).clone()))
<a href=#259 id=259 data-nosnippet>259</a>    }
<a href=#260 id=260 data-nosnippet>260</a>
<a href=#261 id=261 data-nosnippet>261</a>    <span class="kw">pub async fn </span>start_rotation_loop(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#262 id=262 data-nosnippet>262</a>        <span class="kw">let </span>accounts = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.accounts);
<a href=#263 id=263 data-nosnippet>263</a>        <span class="kw">let </span>sessions = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.sessions);
<a href=#264 id=264 data-nosnippet>264</a>        <span class="kw">let </span>interval = <span class="self">self</span>.rotation_interval;
<a href=#265 id=265 data-nosnippet>265</a>
<a href=#266 id=266 data-nosnippet>266</a>        tokio::spawn(<span class="kw">async move </span>{
<a href=#267 id=267 data-nosnippet>267</a>            <span class="kw">let </span><span class="kw-2">mut </span>interval_timer = tokio::time::interval(interval);
<a href=#268 id=268 data-nosnippet>268</a>            
<a href=#269 id=269 data-nosnippet>269</a>            <span class="kw">loop </span>{
<a href=#270 id=270 data-nosnippet>270</a>                interval_timer.tick().<span class="kw">await</span>;
<a href=#271 id=271 data-nosnippet>271</a>                
<a href=#272 id=272 data-nosnippet>272</a>                <span class="comment">// 清理过期会话
<a href=#273 id=273 data-nosnippet>273</a>                </span>{
<a href=#274 id=274 data-nosnippet>274</a>                    <span class="kw">let </span><span class="kw-2">mut </span>sessions_guard = sessions.write().<span class="kw">await</span>;
<a href=#275 id=275 data-nosnippet>275</a>                    <span class="kw">let </span>now = chrono::Utc::now();
<a href=#276 id=276 data-nosnippet>276</a>                    sessions_guard.retain(|<span class="kw">_</span>, session| {
<a href=#277 id=277 data-nosnippet>277</a>                        <span class="kw">let </span>age = now.signed_duration_since(session.last_used);
<a href=#278 id=278 data-nosnippet>278</a>                        age.num_hours() &lt; <span class="number">24 </span><span class="comment">// 保留24小时内的会话
<a href=#279 id=279 data-nosnippet>279</a>                    </span>});
<a href=#280 id=280 data-nosnippet>280</a>                }
<a href=#281 id=281 data-nosnippet>281</a>
<a href=#282 id=282 data-nosnippet>282</a>                <span class="comment">// 降低账号风险评分
<a href=#283 id=283 data-nosnippet>283</a>                </span>{
<a href=#284 id=284 data-nosnippet>284</a>                    <span class="kw">let </span><span class="kw-2">mut </span>accounts_guard = accounts.write().<span class="kw">await</span>;
<a href=#285 id=285 data-nosnippet>285</a>                    <span class="kw">for </span>account <span class="kw">in </span>accounts_guard.values_mut() {
<a href=#286 id=286 data-nosnippet>286</a>                        <span class="kw">if </span>account.risk_score &gt; <span class="number">0.0 </span>{
<a href=#287 id=287 data-nosnippet>287</a>                            account.risk_score = (account.risk_score - <span class="number">0.05</span>).max(<span class="number">0.0</span>);
<a href=#288 id=288 data-nosnippet>288</a>                            
<a href=#289 id=289 data-nosnippet>289</a>                            <span class="comment">// 如果风险评分降低，可能恢复正常状态
<a href=#290 id=290 data-nosnippet>290</a>                            </span><span class="kw">if </span><span class="macro">matches!</span>(account.status, AccountStatus::Abnormal) &amp;&amp; account.risk_score &lt; <span class="number">0.3 </span>{
<a href=#291 id=291 data-nosnippet>291</a>                                account.status = AccountStatus::Normal;
<a href=#292 id=292 data-nosnippet>292</a>                            }
<a href=#293 id=293 data-nosnippet>293</a>                        }
<a href=#294 id=294 data-nosnippet>294</a>                    }
<a href=#295 id=295 data-nosnippet>295</a>                }
<a href=#296 id=296 data-nosnippet>296</a>
<a href=#297 id=297 data-nosnippet>297</a>                <span class="macro">info!</span>(<span class="string">"账号轮换和风险评分更新完成"</span>);
<a href=#298 id=298 data-nosnippet>298</a>            }
<a href=#299 id=299 data-nosnippet>299</a>        });
<a href=#300 id=300 data-nosnippet>300</a>    }
<a href=#301 id=301 data-nosnippet>301</a>
<a href=#302 id=302 data-nosnippet>302</a>    <span class="kw">pub async fn </span>get_pool_status(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; (usize, usize, usize, usize) {
<a href=#303 id=303 data-nosnippet>303</a>        <span class="kw">let </span>accounts = <span class="self">self</span>.accounts.read().<span class="kw">await</span>;
<a href=#304 id=304 data-nosnippet>304</a>        <span class="kw">let </span>sessions = <span class="self">self</span>.sessions.read().<span class="kw">await</span>;
<a href=#305 id=305 data-nosnippet>305</a>        
<a href=#306 id=306 data-nosnippet>306</a>        <span class="kw">let </span>total = accounts.len();
<a href=#307 id=307 data-nosnippet>307</a>        <span class="kw">let </span>active = accounts.values()
<a href=#308 id=308 data-nosnippet>308</a>            .filter(|a| <span class="macro">matches!</span>(a.status, AccountStatus::Normal))
<a href=#309 id=309 data-nosnippet>309</a>            .count();
<a href=#310 id=310 data-nosnippet>310</a>        <span class="kw">let </span>logged_in = sessions.values()
<a href=#311 id=311 data-nosnippet>311</a>            .filter(|s| s.is_valid)
<a href=#312 id=312 data-nosnippet>312</a>            .count();
<a href=#313 id=313 data-nosnippet>313</a>        <span class="kw">let </span>healthy = accounts.values()
<a href=#314 id=314 data-nosnippet>314</a>            .filter(|a| <span class="macro">matches!</span>(a.status, AccountStatus::Normal) &amp;&amp; a.risk_score &lt; <span class="number">0.3</span>)
<a href=#315 id=315 data-nosnippet>315</a>            .count();
<a href=#316 id=316 data-nosnippet>316</a>
<a href=#317 id=317 data-nosnippet>317</a>        (total, active, logged_in, healthy)
<a href=#318 id=318 data-nosnippet>318</a>    }
<a href=#319 id=319 data-nosnippet>319</a>
<a href=#320 id=320 data-nosnippet>320</a>    <span class="kw">pub async fn </span>get_all_accounts(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;Account&gt; {
<a href=#321 id=321 data-nosnippet>321</a>        <span class="kw">let </span>accounts = <span class="self">self</span>.accounts.read().<span class="kw">await</span>;
<a href=#322 id=322 data-nosnippet>322</a>        accounts.values().cloned().collect()
<a href=#323 id=323 data-nosnippet>323</a>    }
<a href=#324 id=324 data-nosnippet>324</a>}</code></pre></div></section></main></body></html>