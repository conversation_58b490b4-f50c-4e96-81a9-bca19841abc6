-- 代理使用统计表
CREATE TABLE proxy_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proxy_id TEXT NOT NULL,
    task_id TEXT,
    usage_type INTEGER NOT NULL, -- 1: Task, 2: Health Check, 3: Test
    success BOOLEAN NOT NULL,
    response_time INTEGER, -- 响应时间(毫秒)
    bytes_transferred INTEGER DEFAULT 0, -- 传输字节数
    error_message TEXT,
    used_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (proxy_id) REFERENCES proxies(proxy_id),
    FOREIGN KEY (task_id) REFERENCES crawl_tasks(task_id)
);

-- 代理性能统计表 (按小时聚合)
CREATE TABLE proxy_performance_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proxy_id TEXT NOT NULL,
    hour_timestamp DATETIME NOT NULL, -- 小时级时间戳
    total_requests INTEGER DEFAULT 0,
    successful_requests INTEGER DEFAULT 0,
    failed_requests INTEGER DEFAULT 0,
    avg_response_time REAL DEFAULT 0.0,
    total_bytes_transferred INTEGER DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (proxy_id) REFERENCES proxies(proxy_id),
    UNIQUE(proxy_id, hour_timestamp)
);

-- 创建索引
CREATE INDEX idx_proxy_usage_proxy_id ON proxy_usage(proxy_id);
CREATE INDEX idx_proxy_usage_task_id ON proxy_usage(task_id);
CREATE INDEX idx_proxy_usage_used_at ON proxy_usage(used_at);
CREATE INDEX idx_proxy_usage_success ON proxy_usage(success);
CREATE INDEX idx_proxy_performance_stats_proxy_id ON proxy_performance_stats(proxy_id);
CREATE INDEX idx_proxy_performance_stats_hour_timestamp ON proxy_performance_stats(hour_timestamp);

-- 创建更新时间触发器
CREATE TRIGGER update_proxy_performance_stats_updated_at
    AFTER UPDATE ON proxy_performance_stats
    FOR EACH ROW
BEGIN
    UPDATE proxy_performance_stats SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
