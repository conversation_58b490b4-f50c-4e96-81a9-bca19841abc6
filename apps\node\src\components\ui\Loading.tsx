import React from 'react';
import { cn } from '../../lib/utils';

// 加载动画类型
export type LoadingType = 'spinner' | 'dots' | 'pulse' | 'skeleton' | 'wave';

export interface LoadingProps {
  type?: LoadingType;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  text?: string;
}

// 旋转加载器
const Spinner = ({ size = 'md', className }: { size: string; className?: string }) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  return (
    <div
      className={cn(
        'animate-spin rounded-full border-2 border-current border-t-transparent',
        sizeClasses[size as keyof typeof sizeClasses],
        className
      )}
    />
  );
};

// 点状加载器
const Dots = ({ size = 'md', className }: { size: string; className?: string }) => {
  const sizeClasses = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3',
    xl: 'w-4 h-4',
  };

  const dotClass = cn(
    'rounded-full bg-current animate-pulse',
    sizeClasses[size as keyof typeof sizeClasses]
  );

  return (
    <div className={cn('flex space-x-1', className)}>
      <div className={dotClass} style={{ animationDelay: '0ms' }} />
      <div className={dotClass} style={{ animationDelay: '150ms' }} />
      <div className={dotClass} style={{ animationDelay: '300ms' }} />
    </div>
  );
};

// 脉冲加载器
const Pulse = ({ size = 'md', className }: { size: string; className?: string }) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-20 h-20',
  };

  return (
    <div
      className={cn(
        'rounded-full bg-primary/20 animate-pulse',
        sizeClasses[size as keyof typeof sizeClasses],
        className
      )}
    />
  );
};

// 骨架屏加载器
const Skeleton = ({ className }: { className?: string }) => {
  return (
    <div className={cn('animate-shimmer bg-muted rounded', className)} />
  );
};

// 波浪加载器
const Wave = ({ size = 'md', className }: { size: string; className?: string }) => {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-10',
    xl: 'h-12',
  };

  const barClass = cn(
    'w-1 bg-current animate-pulse rounded-full',
    sizeClasses[size as keyof typeof sizeClasses]
  );

  return (
    <div className={cn('flex items-end space-x-1', className)}>
      {[...Array(5)].map((_, i) => (
        <div
          key={i}
          className={barClass}
          style={{
            animationDelay: `${i * 100}ms`,
            animationDuration: '1s',
          }}
        />
      ))}
    </div>
  );
};

// 主加载组件
export function Loading({ type = 'spinner', size = 'md', className, text }: LoadingProps) {
  const renderLoader = () => {
    switch (type) {
      case 'dots':
        return <Dots size={size} className={className} />;
      case 'pulse':
        return <Pulse size={size} className={className} />;
      case 'skeleton':
        return <Skeleton className={className} />;
      case 'wave':
        return <Wave size={size} className={className} />;
      default:
        return <Spinner size={size} className={className} />;
    }
  };

  return (
    <div className="flex flex-col items-center justify-center space-y-2">
      {renderLoader()}
      {text && (
        <p className="text-sm text-muted-foreground animate-pulse">{text}</p>
      )}
    </div>
  );
}

// 全屏加载组件
export interface FullScreenLoadingProps {
  show: boolean;
  text?: string;
  type?: LoadingType;
}

export function FullScreenLoading({ show, text = '加载中...', type = 'spinner' }: FullScreenLoadingProps) {
  if (!show) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm animate-fade-in">
      <div className="flex flex-col items-center space-y-4 p-8 bg-card rounded-xl shadow-lg">
        <Loading type={type} size="lg" />
        <p className="text-lg font-medium text-foreground">{text}</p>
      </div>
    </div>
  );
}

// 骨架屏组件集合
export const SkeletonComponents = {
  Card: ({ className }: { className?: string }) => (
    <div className={cn('card animate-pulse', className)}>
      <div className="card-content p-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </div>
      </div>
    </div>
  ),

  Table: ({ rows = 5, className }: { rows?: number; className?: string }) => (
    <div className={cn('space-y-3', className)}>
      {[...Array(rows)].map((_, i) => (
        <div key={i} className="flex items-center space-x-4 animate-pulse">
          <Skeleton className="h-4 w-4 rounded" />
          <Skeleton className="h-4 flex-1" />
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-16" />
        </div>
      ))}
    </div>
  ),

  Text: ({ lines = 3, className }: { lines?: number; className?: string }) => (
    <div className={cn('space-y-2', className)}>
      {[...Array(lines)].map((_, i) => (
        <Skeleton
          key={i}
          className={cn(
            'h-4',
            i === lines - 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  ),

  Avatar: ({ className }: { className?: string }) => (
    <Skeleton className={cn('h-10 w-10 rounded-full', className)} />
  ),

  Button: ({ className }: { className?: string }) => (
    <Skeleton className={cn('h-10 w-24 rounded-lg', className)} />
  ),
};

export { Skeleton };
