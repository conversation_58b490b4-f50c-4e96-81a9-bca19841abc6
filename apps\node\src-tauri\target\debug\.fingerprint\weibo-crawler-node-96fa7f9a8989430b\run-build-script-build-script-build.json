{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9430400796423198903, "build_script_build", false, 15288209031122813234]], "local": [{"RerunIfChanged": {"output": "debug\\build\\weibo-crawler-node-96fa7f9a8989430b\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}