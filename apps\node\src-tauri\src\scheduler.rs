use crate::error::{AppError, Result};
use crate::crawler::{CrawlTask, TaskType};
use serde::{Deserialize, Serialize};
use std::collections::{BinaryHeap, HashMap};
use std::cmp::Ordering;
use std::sync::Arc;
use tokio::sync::{RwLock, Semaphore};
use tokio::time::{Duration, Instant};
use tracing::{info, error};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriorityTask {
    pub task: CrawlTask,
    pub priority: u8,
    pub scheduled_at: chrono::DateTime<chrono::Utc>,
}

impl PartialEq for PriorityTask {
    fn eq(&self, other: &Self) -> bool {
        self.priority == other.priority
    }
}

impl Eq for PriorityTask {}

impl PartialOrd for PriorityTask {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for PriorityTask {
    fn cmp(&self, other: &Self) -> Ordering {
        // 优先级高的任务排在前面（数字越大优先级越高）
        other.priority.cmp(&self.priority)
            .then_with(|| self.scheduled_at.cmp(&other.scheduled_at))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStatistics {
    pub total_received: u64,
    pub total_processed: u64,
    pub total_success: u64,
    pub total_failed: u64,
    pub average_processing_time: f64,
    pub tasks_per_minute: f64,
    pub queue_length: usize,
    pub active_tasks: usize,
}

pub struct TaskScheduler {
    task_queue: Arc<RwLock<BinaryHeap<PriorityTask>>>,
    active_tasks: Arc<RwLock<HashMap<String, PriorityTask>>>,
    completed_tasks: Arc<RwLock<Vec<String>>>,
    failed_tasks: Arc<RwLock<Vec<String>>>,
    semaphore: Arc<Semaphore>,
    #[allow(dead_code)]
    max_concurrent: usize,
    is_processing: Arc<RwLock<bool>>,
    statistics: Arc<RwLock<TaskStatistics>>,
}

impl TaskScheduler {
    pub async fn new(max_concurrent: usize) -> Result<Self> {
        Ok(Self {
            task_queue: Arc::new(RwLock::new(BinaryHeap::new())),
            active_tasks: Arc::new(RwLock::new(HashMap::new())),
            completed_tasks: Arc::new(RwLock::new(Vec::new())),
            failed_tasks: Arc::new(RwLock::new(Vec::new())),
            semaphore: Arc::new(Semaphore::new(max_concurrent)),
            max_concurrent,
            is_processing: Arc::new(RwLock::new(false)),
            statistics: Arc::new(RwLock::new(TaskStatistics {
                total_received: 0,
                total_processed: 0,
                total_success: 0,
                total_failed: 0,
                average_processing_time: 0.0,
                tasks_per_minute: 0.0,
                queue_length: 0,
                active_tasks: 0,
            })),
        })
    }

    pub async fn add_task(&self, task_type: TaskType, target_url: String, priority: u8) -> Result<String> {
        let task_id = Uuid::new_v4().to_string();
        
        let task = CrawlTask {
            id: task_id.clone(),
            task_type,
            target_url,
            priority,
            retry_count: 0,
            max_retries: 3,
            metadata: HashMap::new(),
            created_at: chrono::Utc::now(),
        };

        let priority_task = PriorityTask {
            task,
            priority,
            scheduled_at: chrono::Utc::now(),
        };

        // 添加到队列
        {
            let mut queue = self.task_queue.write().await;
            queue.push(priority_task);
        }

        // 更新统计信息
        {
            let mut stats = self.statistics.write().await;
            stats.total_received += 1;
            stats.queue_length = {
                let queue = self.task_queue.read().await;
                queue.len()
            };
        }

        info!("任务 {} 已添加到队列，优先级: {}", task_id, priority);
        Ok(task_id)
    }

    pub async fn start_processing(&self) -> Result<()> {
        let mut is_processing = self.is_processing.write().await;
        if *is_processing {
            return Err(AppError::Scheduler("任务处理已在运行中".to_string()));
        }
        *is_processing = true;

        info!("启动任务处理循环");
        self.spawn_processing_loop().await;
        Ok(())
    }

    pub async fn stop_processing(&self) -> Result<()> {
        let mut is_processing = self.is_processing.write().await;
        if !*is_processing {
            return Err(AppError::Scheduler("任务处理未在运行".to_string()));
        }
        *is_processing = false;

        info!("停止任务处理循环");
        Ok(())
    }

    async fn spawn_processing_loop(&self) {
        let task_queue = Arc::clone(&self.task_queue);
        let active_tasks = Arc::clone(&self.active_tasks);
        let completed_tasks = Arc::clone(&self.completed_tasks);
        let failed_tasks = Arc::clone(&self.failed_tasks);
        let semaphore = Arc::clone(&self.semaphore);
        let is_processing = Arc::clone(&self.is_processing);
        let statistics = Arc::clone(&self.statistics);

        tokio::spawn(async move {
            while *is_processing.read().await {
                // 从队列中获取任务
                let task = {
                    let mut queue = task_queue.write().await;
                    queue.pop()
                };

                if let Some(priority_task) = task {
                    // 获取信号量许可
                    let permit = Arc::clone(&semaphore).acquire_owned().await.unwrap();

                    // 添加到活跃任务列表
                    {
                        let mut active = active_tasks.write().await;
                        active.insert(priority_task.task.id.clone(), priority_task.clone());
                    }

                    // 更新统计信息
                    {
                        let mut stats = statistics.write().await;
                        stats.active_tasks = active_tasks.read().await.len();
                        stats.queue_length = task_queue.read().await.len();
                    }

                    // 异步处理任务
                    let task_id = priority_task.task.id.clone();
                    let active_tasks_clone = Arc::clone(&active_tasks);
                    let completed_tasks_clone = Arc::clone(&completed_tasks);
                    let failed_tasks_clone = Arc::clone(&failed_tasks);
                    let statistics_clone = Arc::clone(&statistics);

                    tokio::spawn(async move {
                        let start_time = Instant::now();

                        // 模拟任务处理
                        let success = Self::process_task(&priority_task.task).await;

                        let processing_time = start_time.elapsed().as_millis() as f64;

                        // 从活跃任务列表移除
                        {
                            let mut active = active_tasks_clone.write().await;
                            active.remove(&task_id);
                        }

                        // 更新完成或失败列表
                        if success {
                            let mut completed = completed_tasks_clone.write().await;
                            completed.push(task_id.clone());
                            info!("任务 {} 处理成功，耗时: {:.2}ms", task_id, processing_time);
                        } else {
                            let mut failed = failed_tasks_clone.write().await;
                            failed.push(task_id.clone());
                            error!("任务 {} 处理失败，耗时: {:.2}ms", task_id, processing_time);
                        }

                        // 更新统计信息
                        {
                            let mut stats = statistics_clone.write().await;
                            stats.total_processed += 1;
                            if success {
                                stats.total_success += 1;
                            } else {
                                stats.total_failed += 1;
                            }

                            // 更新平均处理时间
                            stats.average_processing_time =
                                (stats.average_processing_time * (stats.total_processed - 1) as f64 + processing_time)
                                / stats.total_processed as f64;

                            stats.active_tasks = active_tasks_clone.read().await.len();
                        }

                        // 释放信号量许可
                        drop(permit);
                    });
                } else {
                    // 队列为空，等待一段时间
                    tokio::time::sleep(Duration::from_millis(100)).await;
                }
            }
        });
    }

    async fn process_task(task: &CrawlTask) -> bool {
        // 模拟任务处理时间
        let processing_time = match task.task_type {
            TaskType::User => Duration::from_millis(1000),
            TaskType::Post => Duration::from_millis(800),
            TaskType::Comment => Duration::from_millis(600),
            TaskType::Topic => Duration::from_millis(1200),
        };

        tokio::time::sleep(processing_time).await;

        // 模拟90%的成功率
        rand::random::<f64>() > 0.1
    }

    pub async fn get_queue_status(&self) -> crate::commands::TaskQueueStatus {
        let queue_length = {
            let queue = self.task_queue.read().await;
            queue.len() as u32
        };

        let active_count = {
            let active = self.active_tasks.read().await;
            active.len() as u32
        };

        let (completed_count, failed_count) = {
            let completed = self.completed_tasks.read().await;
            let failed = self.failed_tasks.read().await;
            (completed.len() as u64, failed.len() as u64)
        };

        let stats = self.statistics.read().await;

        crate::commands::TaskQueueStatus {
            pending_tasks: queue_length,
            running_tasks: active_count,
            completed_tasks: completed_count,
            failed_tasks: failed_count,
            queue_length,
            processing_speed: stats.tasks_per_minute,
        }
    }

    pub async fn get_statistics(&self) -> crate::commands::TaskStatistics {
        let stats = self.statistics.read().await;
        
        crate::commands::TaskStatistics {
            total_processed: stats.total_processed,
            success_count: stats.total_success,
            failure_count: stats.total_failed,
            average_duration: stats.average_processing_time / 1000.0, // 转换为秒
            success_rate: if stats.total_processed > 0 {
                (stats.total_success as f64 / stats.total_processed as f64) * 100.0
            } else {
                0.0
            },
            tasks_per_hour: stats.tasks_per_minute * 60.0,
        }
    }

    pub async fn retry_failed_task(&self, task_id: &str) -> Result<()> {
        // 这里应该实现重试失败任务的逻辑
        // 目前先返回成功
        info!("重试任务: {}", task_id);
        Ok(())
    }

    pub async fn cancel_task(&self, task_id: &str) -> Result<()> {
        // 从队列中移除任务
        {
            let mut queue = self.task_queue.write().await;
            let mut temp_queue = BinaryHeap::new();
            let mut found = false;

            while let Some(priority_task) = queue.pop() {
                if priority_task.task.id == task_id {
                    found = true;
                    info!("任务 {} 已从队列中取消", task_id);
                } else {
                    temp_queue.push(priority_task);
                }
            }

            *queue = temp_queue;

            if !found {
                return Err(AppError::Scheduler(format!("任务 {} 不在队列中", task_id)));
            }
        }

        Ok(())
    }

    pub async fn clear_completed_tasks(&self) -> Result<usize> {
        let mut completed = self.completed_tasks.write().await;
        let count = completed.len();
        completed.clear();
        
        info!("清理了 {} 个已完成任务", count);
        Ok(count)
    }

    pub async fn clear_failed_tasks(&self) -> Result<usize> {
        let mut failed = self.failed_tasks.write().await;
        let count = failed.len();
        failed.clear();
        
        info!("清理了 {} 个失败任务", count);
        Ok(count)
    }
}
