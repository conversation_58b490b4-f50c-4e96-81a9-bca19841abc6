import React from 'react'
import { render, screen } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { <PERSON><PERSON>hart, SimpleLineChart, MultiLineChart } from '../LineChart'
import { mockChartData } from '../../../test/utils'

// Mock Recharts
vi.mock('recharts', () => ({
  LineChart: ({ children, ...props }: any) => (
    <div data-testid="recharts-line-chart" {...props}>
      {children}
    </div>
  ),
  Line: (props: any) => <div data-testid="recharts-line" {...props} />,
  XAxis: (props: any) => <div data-testid="recharts-xaxis" {...props} />,
  YAxis: (props: any) => <div data-testid="recharts-yaxis" {...props} />,
  CartesianGrid: (props: any) => <div data-testid="recharts-grid" {...props} />,
  Tooltip: (props: any) => <div data-testid="recharts-tooltip" {...props} />,
  Legend: (props: any) => <div data-testid="recharts-legend" {...props} />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="recharts-responsive-container">{children}</div>
  ),
}))

describe('LineChart', () => {
  const mockData = mockChartData.lineChart

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render line chart with data', () => {
    render(
      <LineChart
        data={mockData}
        title="Test Chart"
        subtitle="Test Subtitle"
      />
    )

    expect(screen.getByText('Test Chart')).toBeInTheDocument()
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument()
    expect(screen.getByTestId('recharts-line-chart')).toBeInTheDocument()
    expect(screen.getByTestId('recharts-responsive-container')).toBeInTheDocument()
  })

  it('should render with custom lines configuration', () => {
    const lines = [
      { key: 'value', name: 'Value 1', color: '#ff0000' },
      { key: 'value2', name: 'Value 2', color: '#00ff00' },
    ]

    render(
      <LineChart
        data={mockData}
        lines={lines}
        showLegend={true}
      />
    )

    const lineElements = screen.getAllByTestId('recharts-line')
    expect(lineElements).toHaveLength(2)
    expect(screen.getByTestId('recharts-legend')).toBeInTheDocument()
  })

  it('should show loading state', () => {
    render(
      <LineChart
        data={[]}
        loading={true}
        height={300}
      />
    )

    const loadingSpinner = screen.getByRole('status', { hidden: true })
    expect(loadingSpinner).toBeInTheDocument()
    expect(loadingSpinner).toHaveClass('animate-spin')
  })

  it('should show error state', () => {
    render(
      <LineChart
        data={[]}
        error="Failed to load data"
        height={300}
      />
    )

    expect(screen.getByText('Failed to load data')).toBeInTheDocument()
    expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument() // Error icon
  })

  it('should render grid when showGrid is true', () => {
    render(
      <LineChart
        data={mockData}
        showGrid={true}
      />
    )

    expect(screen.getByTestId('recharts-grid')).toBeInTheDocument()
  })

  it('should not render grid when showGrid is false', () => {
    render(
      <LineChart
        data={mockData}
        showGrid={false}
      />
    )

    expect(screen.queryByTestId('recharts-grid')).not.toBeInTheDocument()
  })

  it('should render axes when enabled', () => {
    render(
      <LineChart
        data={mockData}
        showXAxis={true}
        showYAxis={true}
      />
    )

    expect(screen.getByTestId('recharts-xaxis')).toBeInTheDocument()
    expect(screen.getByTestId('recharts-yaxis')).toBeInTheDocument()
  })

  it('should not render axes when disabled', () => {
    render(
      <LineChart
        data={mockData}
        showXAxis={false}
        showYAxis={false}
      />
    )

    expect(screen.queryByTestId('recharts-xaxis')).not.toBeInTheDocument()
    expect(screen.queryByTestId('recharts-yaxis')).not.toBeInTheDocument()
  })

  it('should render tooltip when enabled', () => {
    render(
      <LineChart
        data={mockData}
        showTooltip={true}
      />
    )

    expect(screen.getByTestId('recharts-tooltip')).toBeInTheDocument()
  })

  it('should apply custom className', () => {
    render(
      <LineChart
        data={mockData}
        className="custom-chart-class"
      />
    )

    const chartContainer = screen.getByTestId('recharts-responsive-container').parentElement
    expect(chartContainer).toHaveClass('custom-chart-class')
  })
})

describe('SimpleLineChart', () => {
  const mockData = mockChartData.lineChart

  it('should render simple line chart with single line', () => {
    render(
      <SimpleLineChart
        data={mockData}
        yKey="value"
        title="Simple Chart"
      />
    )

    expect(screen.getByText('Simple Chart')).toBeInTheDocument()
    expect(screen.getByTestId('recharts-line-chart')).toBeInTheDocument()
    
    const lineElements = screen.getAllByTestId('recharts-line')
    expect(lineElements).toHaveLength(1)
  })

  it('should use custom color', () => {
    render(
      <SimpleLineChart
        data={mockData}
        yKey="value"
        color="#ff0000"
      />
    )

    const lineElement = screen.getByTestId('recharts-line')
    expect(lineElement).toHaveAttribute('stroke', '#ff0000')
  })
})

describe('MultiLineChart', () => {
  const mockData = mockChartData.lineChart
  const metrics = [
    { key: 'value', name: 'Metric 1', unit: '%' },
    { key: 'value2', name: 'Metric 2', unit: 'ms' },
  ]

  it('should render multiple lines with metrics', () => {
    render(
      <MultiLineChart
        data={mockData}
        metrics={metrics}
        title="Multi Line Chart"
      />
    )

    expect(screen.getByText('Multi Line Chart')).toBeInTheDocument()
    expect(screen.getByTestId('recharts-legend')).toBeInTheDocument()
    
    const lineElements = screen.getAllByTestId('recharts-line')
    expect(lineElements).toHaveLength(2)
  })

  it('should apply custom colors to metrics', () => {
    const metricsWithColors = [
      { key: 'value', name: 'Metric 1', color: '#ff0000' },
      { key: 'value2', name: 'Metric 2', color: '#00ff00' },
    ]

    render(
      <MultiLineChart
        data={mockData}
        metrics={metricsWithColors}
      />
    )

    const lineElements = screen.getAllByTestId('recharts-line')
    expect(lineElements[0]).toHaveAttribute('stroke', '#ff0000')
    expect(lineElements[1]).toHaveAttribute('stroke', '#00ff00')
  })
})

describe('Chart Container', () => {
  it('should render empty state when no data', () => {
    render(
      <LineChart
        data={[]}
        title="Empty Chart"
      />
    )

    expect(screen.getByText('Empty Chart')).toBeInTheDocument()
    // 应该显示空状态或者至少不崩溃
    expect(screen.getByTestId('recharts-line-chart')).toBeInTheDocument()
  })

  it('should handle responsive container', () => {
    render(
      <LineChart
        data={mockChartData.lineChart}
        height={400}
      />
    )

    const responsiveContainer = screen.getByTestId('recharts-responsive-container')
    expect(responsiveContainer).toBeInTheDocument()
  })

  it('should render with custom height', () => {
    render(
      <LineChart
        data={mockChartData.lineChart}
        height={500}
      />
    )

    // 检查容器高度设置
    const container = screen.getByTestId('recharts-responsive-container').parentElement
    expect(container).toHaveStyle({ height: '500px' })
  })
})
