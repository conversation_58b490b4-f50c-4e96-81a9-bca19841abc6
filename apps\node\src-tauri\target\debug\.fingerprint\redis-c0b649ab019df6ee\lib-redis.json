{"rustc": 1842507548689473721, "features": "[\"acl\", \"aio\", \"async-trait\", \"bytes\", \"default\", \"futures-util\", \"geospatial\", \"keep-alive\", \"pin-project-lite\", \"script\", \"sha1_smol\", \"socket2\", \"streams\", \"tokio\", \"tokio-comp\", \"tokio-util\"]", "declared_features": "[\"acl\", \"ahash\", \"aio\", \"arc-swap\", \"async-native-tls\", \"async-std\", \"async-std-comp\", \"async-std-native-tls-comp\", \"async-std-rustls-comp\", \"async-std-tls-comp\", \"async-trait\", \"bytes\", \"cluster\", \"cluster-async\", \"connection-manager\", \"crc16\", \"default\", \"futures\", \"futures-rustls\", \"futures-util\", \"geospatial\", \"json\", \"keep-alive\", \"log\", \"native-tls\", \"pin-project-lite\", \"r2d2\", \"rand\", \"rustls\", \"rustls-native-certs\", \"rustls-pemfile\", \"rustls-webpki\", \"script\", \"sentinel\", \"serde\", \"serde_json\", \"sha1_smol\", \"socket2\", \"streams\", \"tcp_nodelay\", \"tls\", \"tls-native-tls\", \"tls-rustls\", \"tls-rustls-insecure\", \"tls-rustls-webpki-roots\", \"tokio\", \"tokio-comp\", \"tokio-native-tls\", \"tokio-native-tls-comp\", \"tokio-retry\", \"tokio-rustls\", \"tokio-rustls-comp\", \"tokio-util\", \"webpki-roots\"]", "target": 5936222214539778515, "profile": 15657897354478470176, "path": 819742389124406106, "deps": [[40386456601120721, "percent_encoding", false, 14003801311530373528], [917570942013697716, "sha1_smol", false, 3212613450129279067], [1211321333142909612, "socket2", false, 7113017371464419162], [1216309103264968120, "ryu", false, 5387813824775335765], [1288403060204016458, "tokio_util", false, 11334306296847631233], [1906322745568073236, "pin_project_lite", false, 18131270670966278584], [3150220818285335163, "url", false, 1746054398145793718], [7695812897323945497, "itoa", false, 12556285801682292415], [10629569228670356391, "futures_util", false, 15644822958053016495], [11946729385090170470, "async_trait", false, 1405635847080043314], [12944427623413450645, "tokio", false, 13941386680418085679], [16066129441945555748, "bytes", false, 256859762049119040], [17915660048393766120, "combine", false, 12555918157802167956]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\redis-c0b649ab019df6ee\\dep-lib-redis", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}