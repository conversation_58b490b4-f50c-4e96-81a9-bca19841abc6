# 节点配置
NODE_ID=crawler_node_001
NODE_NAME=爬虫节点001
NODE_TYPE=crawler

# 数据库配置
SQLITE_DATABASE_URL=sqlite:./data/crawler_node.db
REDIS_URL=redis://localhost:6379
RABBITMQ_URL=amqp://admin:password@localhost:5672

# 管理节点配置
MASTER_NODE_URL=http://localhost:8080
HEARTBEAT_INTERVAL=30

# 爬虫配置
MAX_CONCURRENT_TASKS=100
REQUEST_TIMEOUT=30
RETRY_MAX_ATTEMPTS=3

# 代理配置
PROXY_POOL_SIZE=50
PROXY_HEALTH_CHECK_INTERVAL=300

# 账号配置
ACCOUNT_POOL_SIZE=20
ACCOUNT_ROTATION_INTERVAL=3600

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/crawler_node.log
