{"rustc": 1842507548689473721, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 2241668132362809309, "path": 15307606537240818418, "deps": [[1288403060204016458, "tokio_util", false, 5339149626986303070], [1906322745568073236, "pin_project_lite", false, 8108972857832786586], [7620660491849607393, "futures_core_03", false, 10280731575054067511], [12944427623413450645, "tokio_dep", false, 5789438060576990513], [15932120279885307830, "memchr", false, 12618675062812920877], [16066129441945555748, "bytes", false, 2398977389649674023]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\combine-da6050ab0e8fdb09\\dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}