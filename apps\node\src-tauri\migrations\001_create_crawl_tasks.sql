-- 爬取任务表
CREATE TABLE crawl_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT UNIQUE NOT NULL,
    task_type INTEGER NOT NULL, -- 1: User, 2: Post, 3: Comment, 4: Topic
    target_url TEXT NOT NULL,
    priority INTEGER NOT NULL DEFAULT 5,
    status INTEGER NOT NULL DEFAULT 0, -- 0: Pending, 1: Running, 2: Completed, 3: Failed
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retries INTEGER NOT NULL DEFAULT 3,
    metadata TEXT, -- JSON格式的元数据
    assigned_at DATETIME,
    started_at DATETIME,
    completed_at DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_crawl_tasks_task_id ON crawl_tasks(task_id);
CREATE INDEX idx_crawl_tasks_status ON crawl_tasks(status);
CREATE INDEX idx_crawl_tasks_priority ON crawl_tasks(priority);
CREATE INDEX idx_crawl_tasks_created_at ON crawl_tasks(created_at);

-- 创建更新时间触发器
CREATE TRIGGER update_crawl_tasks_updated_at
    AFTER UPDATE ON crawl_tasks
    FOR EACH ROW
BEGIN
    UPDATE crawl_tasks SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
