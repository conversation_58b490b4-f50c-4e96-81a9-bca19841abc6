{"rustc": 1842507548689473721, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 15657897354478470176, "path": 4209321403230616227, "deps": [[1906322745568073236, "pin_project_lite", false, 18131270670966278584], [7620660491849607393, "futures_core", false, 6897113057850168815], [9556762810601084293, "brotli", false, 2686292599745509306], [12944427623413450645, "tokio", false, 13941386680418085679], [15932120279885307830, "memchr", false, 13280053228737166842], [17772299992546037086, "flate2", false, 2124762649658070698]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-compression-1e645d9ab44ba6df\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}