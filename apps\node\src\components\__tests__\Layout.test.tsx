import React from 'react'
import { screen, fireEvent } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { Layout } from '../Layout'
import { renderWithProviders, setupTauriMocks } from '../../test/utils'

// Mock react-router-dom
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/' }),
  }
})

describe('Layout', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    setupTauriMocks()
  })

  it('should render layout with navigation', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    // 检查导航元素
    expect(screen.getByRole('navigation')).toBeInTheDocument()
    expect(screen.getByText('微博爬虫节点')).toBeInTheDocument()
    
    // 检查主要导航链接
    expect(screen.getByText('仪表板')).toBeInTheDocument()
    expect(screen.getByText('任务管理')).toBeInTheDocument()
    expect(screen.getByText('代理管理')).toBeInTheDocument()
    expect(screen.getByText('账号管理')).toBeInTheDocument()
    expect(screen.getByText('系统监控')).toBeInTheDocument()
    
    // 检查子内容
    expect(screen.getByText('Test Content')).toBeInTheDocument()
  })

  it('should toggle sidebar', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    const sidebar = screen.getByTestId('sidebar')
    const toggleButton = screen.getByTestId('sidebar-toggle')

    // 初始状态应该是展开的
    expect(sidebar).not.toHaveClass('collapsed')

    // 点击切换按钮
    fireEvent.click(toggleButton)
    expect(sidebar).toHaveClass('collapsed')

    // 再次点击应该展开
    fireEvent.click(toggleButton)
    expect(sidebar).not.toHaveClass('collapsed')
  })

  it('should highlight active navigation item', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>,
      { initialEntries: ['/tasks'] }
    )

    const tasksLink = screen.getByText('任务管理').closest('a')
    expect(tasksLink).toHaveClass('active')
  })

  it('should handle navigation clicks', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    const tasksLink = screen.getByText('任务管理')
    fireEvent.click(tasksLink)

    expect(mockNavigate).toHaveBeenCalledWith('/tasks')
  })

  it('should display user menu', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    const userMenuButton = screen.getByTestId('user-menu-button')
    expect(userMenuButton).toBeInTheDocument()

    // 点击用户菜单
    fireEvent.click(userMenuButton)

    // 检查菜单项
    expect(screen.getByText('设置')).toBeInTheDocument()
    expect(screen.getByText('关于')).toBeInTheDocument()
    expect(screen.getByText('退出')).toBeInTheDocument()
  })

  it('should handle theme toggle', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    const themeToggle = screen.getByTestId('theme-toggle')
    expect(themeToggle).toBeInTheDocument()

    // 点击主题切换
    fireEvent.click(themeToggle)

    // 检查主题是否切换（这里需要根据实际实现调整）
    expect(document.documentElement).toHaveClass('dark')
  })

  it('should display system status', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    // 检查系统状态指示器
    const statusIndicator = screen.getByTestId('system-status')
    expect(statusIndicator).toBeInTheDocument()
  })

  it('should handle window controls on desktop', () => {
    // 模拟桌面环境
    Object.defineProperty(window, '__TAURI__', {
      value: { invoke: vi.fn() },
      writable: true,
    })

    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    // 检查窗口控制按钮
    const minimizeButton = screen.getByTestId('window-minimize')
    const maximizeButton = screen.getByTestId('window-maximize')
    const closeButton = screen.getByTestId('window-close')

    expect(minimizeButton).toBeInTheDocument()
    expect(maximizeButton).toBeInTheDocument()
    expect(closeButton).toBeInTheDocument()

    // 测试窗口控制
    fireEvent.click(minimizeButton)
    expect(window.__TAURI__.invoke).toHaveBeenCalledWith('minimize_window')

    fireEvent.click(maximizeButton)
    expect(window.__TAURI__.invoke).toHaveBeenCalledWith('maximize_window')

    fireEvent.click(closeButton)
    expect(window.__TAURI__.invoke).toHaveBeenCalledWith('close_window')
  })

  it('should be responsive', () => {
    // 模拟移动端视口
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 768,
    })

    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    const sidebar = screen.getByTestId('sidebar')
    
    // 在移动端，侧边栏应该默认折叠
    expect(sidebar).toHaveClass('mobile-collapsed')
  })

  it('should handle keyboard shortcuts', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    // 测试快捷键
    fireEvent.keyDown(document, { key: 'b', ctrlKey: true })
    
    const sidebar = screen.getByTestId('sidebar')
    expect(sidebar).toHaveClass('collapsed')
  })

  it('should display notifications', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    const notificationButton = screen.getByTestId('notifications-button')
    expect(notificationButton).toBeInTheDocument()

    // 点击通知按钮
    fireEvent.click(notificationButton)

    // 检查通知面板
    expect(screen.getByTestId('notifications-panel')).toBeInTheDocument()
  })

  it('should handle search functionality', () => {
    renderWithProviders(
      <Layout>
        <div>Test Content</div>
      </Layout>
    )

    const searchInput = screen.getByPlaceholderText('搜索...')
    expect(searchInput).toBeInTheDocument()

    // 输入搜索内容
    fireEvent.change(searchInput, { target: { value: 'test search' } })
    expect(searchInput).toHaveValue('test search')

    // 按回车搜索
    fireEvent.keyDown(searchInput, { key: 'Enter' })
    
    // 检查搜索结果面板
    expect(screen.getByTestId('search-results')).toBeInTheDocument()
  })
})
