import React, { useState, useEffect, useRef } from 'react';
import { LineChart, AreaChart, chartColors } from './ModernChart';
import { cn } from '../../lib/utils';

// 实时数据点接口
export interface DataPoint {
  timestamp: number;
  value: number;
  label?: string;
}

// 实时图表属性
export interface RealTimeChartProps {
  data: DataPoint[];
  maxDataPoints?: number;
  updateInterval?: number;
  height?: number;
  className?: string;
  type?: 'line' | 'area';
  color?: string;
  title?: string;
  unit?: string;
  showCurrentValue?: boolean;
  animate?: boolean;
}

export function RealTimeChart({
  data,
  maxDataPoints = 50,
  updateInterval = 1000,
  height = 200,
  className,
  type = 'line',
  color = chartColors.primary,
  title,
  unit = '',
  showCurrentValue = true,
  animate = true,
}: RealTimeChartProps) {
  const [chartData, setChartData] = useState<any[]>([]);
  const [currentValue, setCurrentValue] = useState<number>(0);
  const intervalRef = useRef<NodeJS.Timeout>();

  // 格式化时间戳
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // 更新图表数据
  useEffect(() => {
    if (data.length === 0) return;

    const formattedData = data
      .slice(-maxDataPoints)
      .map(point => ({
        name: formatTime(point.timestamp),
        value: point.value,
        timestamp: point.timestamp,
      }));

    setChartData(formattedData);
    setCurrentValue(data[data.length - 1]?.value || 0);
  }, [data, maxDataPoints]);

  // 自动滚动效果
  useEffect(() => {
    if (!animate) return;

    intervalRef.current = setInterval(() => {
      setChartData(prev => {
        if (prev.length === 0) return prev;
        
        // 添加轻微的动画效果
        return prev.map(item => ({
          ...item,
          _animated: true,
        }));
      });
    }, updateInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [updateInterval, animate]);

  const ChartComponent = type === 'area' ? AreaChart : LineChart;

  return (
    <div className={cn('space-y-4', className)}>
      {/* 标题和当前值 */}
      {(title || showCurrentValue) && (
        <div className="flex items-center justify-between">
          {title && (
            <h3 className="text-lg font-semibold text-foreground">{title}</h3>
          )}
          {showCurrentValue && (
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">
                {currentValue.toLocaleString()}
                {unit && <span className="text-sm text-muted-foreground ml-1">{unit}</span>}
              </div>
              <div className="text-xs text-muted-foreground">
                实时数据
              </div>
            </div>
          )}
        </div>
      )}

      {/* 图表 */}
      <div className="relative">
        <ChartComponent
          data={chartData}
          height={height}
          xKey="name"
          yKey="value"
          color={color}
          showGrid={true}
          showTooltip={true}
          showLegend={false}
          animate={animate}
        />
        
        {/* 实时指示器 */}
        <div className="absolute top-2 right-2 flex items-center space-x-2">
          <div className="w-2 h-2 bg-success rounded-full animate-pulse" />
          <span className="text-xs text-muted-foreground">实时</span>
        </div>
      </div>
    </div>
  );
}

// 多指标实时图表
export interface MultiMetricChartProps {
  metrics: {
    name: string;
    data: DataPoint[];
    color: string;
    unit?: string;
  }[];
  maxDataPoints?: number;
  height?: number;
  className?: string;
  type?: 'line' | 'area';
}

export function MultiMetricChart({
  metrics,
  maxDataPoints = 50,
  height = 300,
  className,
  type = 'line',
}: MultiMetricChartProps) {
  const [chartData, setChartData] = useState<any[]>([]);

  // 格式化时间戳
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  // 合并多个指标的数据
  useEffect(() => {
    if (metrics.length === 0) return;

    // 获取所有时间戳
    const allTimestamps = new Set<number>();
    metrics.forEach(metric => {
      metric.data.forEach(point => {
        allTimestamps.add(point.timestamp);
      });
    });

    // 按时间戳排序并限制数量
    const sortedTimestamps = Array.from(allTimestamps)
      .sort((a, b) => a - b)
      .slice(-maxDataPoints);

    // 构建图表数据
    const formattedData = sortedTimestamps.map(timestamp => {
      const dataPoint: any = {
        name: formatTime(timestamp),
        timestamp,
      };

      metrics.forEach(metric => {
        const point = metric.data.find(p => p.timestamp === timestamp);
        dataPoint[metric.name] = point?.value || 0;
      });

      return dataPoint;
    });

    setChartData(formattedData);
  }, [metrics, maxDataPoints]);

  return (
    <div className={cn('space-y-4', className)}>
      {/* 指标概览 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => {
          const currentValue = metric.data[metric.data.length - 1]?.value || 0;
          return (
            <div key={index} className="flex items-center space-x-3 p-3 bg-muted/30 rounded-lg">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: metric.color }}
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground truncate">
                  {metric.name}
                </p>
                <p className="text-lg font-bold" style={{ color: metric.color }}>
                  {currentValue.toLocaleString()}
                  {metric.unit && (
                    <span className="text-xs text-muted-foreground ml-1">
                      {metric.unit}
                    </span>
                  )}
                </p>
              </div>
            </div>
          );
        })}
      </div>

      {/* 多线图表 */}
      <div className="relative">
        <LineChart
          data={chartData}
          height={height}
          xKey="name"
          yKey={metrics[0]?.name}
          showGrid={true}
          showTooltip={true}
          showLegend={true}
          animate={true}
        />
        
        {/* 实时指示器 */}
        <div className="absolute top-2 right-2 flex items-center space-x-2">
          <div className="w-2 h-2 bg-success rounded-full animate-pulse" />
          <span className="text-xs text-muted-foreground">实时监控</span>
        </div>
      </div>
    </div>
  );
}

// 性能指标仪表板
export interface PerformanceDashboardProps {
  cpuData: DataPoint[];
  memoryData: DataPoint[];
  networkData: DataPoint[];
  className?: string;
}

export function PerformanceDashboard({
  cpuData,
  memoryData,
  networkData,
  className,
}: PerformanceDashboardProps) {
  return (
    <div className={cn('space-y-6', className)}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <RealTimeChart
          data={cpuData}
          title="CPU 使用率"
          unit="%"
          color={chartColors.info}
          type="area"
          height={150}
        />
        <RealTimeChart
          data={memoryData}
          title="内存使用率"
          unit="%"
          color={chartColors.warning}
          type="area"
          height={150}
        />
        <RealTimeChart
          data={networkData}
          title="网络流量"
          unit="MB/s"
          color={chartColors.success}
          type="line"
          height={150}
        />
      </div>
    </div>
  );
}
