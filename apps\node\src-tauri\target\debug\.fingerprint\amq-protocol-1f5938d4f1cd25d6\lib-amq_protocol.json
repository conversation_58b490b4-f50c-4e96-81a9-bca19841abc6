{"rustc": 1842507548689473721, "features": "[\"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\", \"verbose-errors\"]", "target": 12659125817264579830, "profile": 15657897354478470176, "path": 2622419605622029895, "deps": [[2985572863888970315, "amq_protocol_types", false, 16780648480481141825], [4886105269790530060, "cookie_factory", false, 14189293752459852100], [6502365400774175331, "nom", false, 7053466019656569712], [7048981225526245511, "build_script_build", false, 8167599371888164942], [9689903380558560274, "serde", false, 4389908846715814937], [11096876330329401515, "amq_protocol_uri", false, 2937034675637483620], [12404004217544788841, "amq_protocol_tcp", false, 13639081686720247237]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\amq-protocol-1f5938d4f1cd25d6\\dep-lib-amq_protocol", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}