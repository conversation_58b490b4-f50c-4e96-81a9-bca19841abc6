{"rustc": 1842507548689473721, "features": "[\"client\", \"h2\", \"http1\", \"http2\", \"runtime\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 15657897354478470176, "path": 883317369159468112, "deps": [[784494742817713399, "tower_service", false, 12359912582921736970], [1569313478171189446, "want", false, 4691988333867635760], [1811549171721445101, "futures_channel", false, 3188382322166671117], [1906322745568073236, "pin_project_lite", false, 18131270670966278584], [4405182208873388884, "http", false, 5891631273443115995], [6163892036024256188, "httparse", false, 12158478972230890943], [6304235478050270880, "httpdate", false, 4364480488144126888], [7620660491849607393, "futures_core", false, 6897113057850168815], [7695812897323945497, "itoa", false, 12556285801682292415], [8606274917505247608, "tracing", false, 12569555633877986202], [8915503303801890683, "http_body", false, 5424949408989136933], [10629569228670356391, "futures_util", false, 12856374576410295979], [12614995553916589825, "socket2", false, 16085688965518363858], [12944427623413450645, "tokio", false, 13195404102495884738], [13763625454224483636, "h2", false, 1007915763500126833], [16066129441945555748, "bytes", false, 256859762049119040]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-730b548df8988629\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}