{"rustc": 1842507548689473721, "features": "[\"parking\", \"std\"]", "declared_features": "[\"critical-section\", \"default\", \"loom\", \"parking\", \"portable-atomic\", \"portable-atomic-util\", \"portable_atomic_crate\", \"std\"]", "target": 8831420706606120547, "profile": 5585765287293540646, "path": 2403256403827496564, "deps": [[189982446159473706, "parking", false, 9068259759801728583], [1906322745568073236, "pin_project_lite", false, 18131270670966278584], [12100481297174703255, "concurrent_queue", false, 16448109489529989683]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\event-listener-fd20c17a83be4a49\\dep-lib-event_listener", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}