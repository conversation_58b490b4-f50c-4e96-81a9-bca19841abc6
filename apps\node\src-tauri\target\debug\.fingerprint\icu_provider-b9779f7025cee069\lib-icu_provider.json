{"rustc": 1842507548689473721, "features": "[\"baked\", \"zerotrie\"]", "declared_features": "[\"alloc\", \"baked\", \"deserialize_bincode_1\", \"deserialize_json\", \"deserialize_postcard_1\", \"export\", \"logging\", \"serde\", \"std\", \"sync\", \"zerotrie\"]", "target": 8134314816311233441, "profile": 15657897354478470176, "path": *******************, "deps": [[577007972892873560, "icu_locale_core", false, *******************], [1537006514548139957, "zerovec", false, 2020730650671356773], [1720717020211068583, "writeable", false, 14585533363100240951], [2094002304596326048, "zerotrie", false, 12250944776004386960], [*******************, "stable_deref_trait", false, 11639012276821805707], [5298260564258778412, "displaydoc", false, 18406754746530483604], [10706449961930108323, "yoke", false, 14563440385815289377], [17046516144589451410, "zerofrom", false, 2326963030566692795], [18328566729972757851, "tinystr", false, 16330281579309855897]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\icu_provider-b9779f7025cee069\\dep-lib-icu_provider", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}