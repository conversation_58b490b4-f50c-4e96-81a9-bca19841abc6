{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 15657897354478470176, "path": 9658408667360391262, "deps": [[9620753569207166497, "zerovec_derive", false, 9865083115276346288], [10706449961930108323, "yoke", false, 14563440385815289377], [17046516144589451410, "zerofrom", false, 2326963030566692795]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-0ddfd6c0bac90eb6\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}