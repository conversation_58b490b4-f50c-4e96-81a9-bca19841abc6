-- 系统性能监控表
CREATE TABLE system_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id TEXT NOT NULL,
    cpu_usage REAL NOT NULL, -- CPU使用率 0.0-1.0
    memory_usage REAL NOT NULL, -- 内存使用率 0.0-1.0
    memory_total INTEGER NOT NULL, -- 总内存(字节)
    memory_used INTEGER NOT NULL, -- 已用内存(字节)
    disk_usage REAL NOT NULL, -- 磁盘使用率 0.0-1.0
    disk_total INTEGER NOT NULL, -- 总磁盘空间(字节)
    disk_used INTEGER NOT NULL, -- 已用磁盘空间(字节)
    network_in INTEGER DEFAULT 0, -- 网络入流量(字节)
    network_out INTEGER DEFAULT 0, -- 网络出流量(字节)
    active_connections INTEGER DEFAULT 0, -- 活跃连接数
    recorded_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 任务执行统计表 (按小时聚合)
CREATE TABLE task_statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    hour_timestamp DATETIME NOT NULL, -- 小时级时间戳
    total_tasks INTEGER DEFAULT 0,
    pending_tasks INTEGER DEFAULT 0,
    running_tasks INTEGER DEFAULT 0,
    completed_tasks INTEGER DEFAULT 0,
    failed_tasks INTEGER DEFAULT 0,
    skipped_tasks INTEGER DEFAULT 0,
    avg_execution_time REAL DEFAULT 0.0, -- 平均执行时间(毫秒)
    success_rate REAL DEFAULT 0.0, -- 成功率 0.0-1.0
    tasks_per_minute REAL DEFAULT 0.0, -- 每分钟处理任务数
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(hour_timestamp)
);

-- 错误日志表
CREATE TABLE error_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    error_type INTEGER NOT NULL, -- 1: System, 2: Network, 3: Database, 4: Business
    error_level INTEGER NOT NULL, -- 1: Debug, 2: Info, 3: Warn, 4: Error, 5: Fatal
    error_code TEXT,
    error_message TEXT NOT NULL,
    error_context TEXT, -- JSON格式的错误上下文
    stack_trace TEXT,
    component TEXT, -- 发生错误的组件
    task_id TEXT, -- 相关任务ID
    account_id TEXT, -- 相关账号ID
    proxy_id TEXT, -- 相关代理ID
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 节点状态表
CREATE TABLE node_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    node_id TEXT UNIQUE NOT NULL,
    node_name TEXT NOT NULL,
    node_version TEXT,
    status INTEGER NOT NULL, -- 1: Online, 2: Offline, 3: Maintenance, 4: Error
    last_heartbeat DATETIME NOT NULL,
    uptime INTEGER DEFAULT 0, -- 运行时间(秒)
    total_tasks_processed INTEGER DEFAULT 0,
    current_load REAL DEFAULT 0.0, -- 当前负载 0.0-1.0
    capabilities TEXT, -- JSON格式的节点能力描述
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_system_metrics_node_id ON system_metrics(node_id);
CREATE INDEX idx_system_metrics_recorded_at ON system_metrics(recorded_at);
CREATE INDEX idx_task_statistics_hour_timestamp ON task_statistics(hour_timestamp);
CREATE INDEX idx_error_logs_error_type ON error_logs(error_type);
CREATE INDEX idx_error_logs_error_level ON error_logs(error_level);
CREATE INDEX idx_error_logs_created_at ON error_logs(created_at);
CREATE INDEX idx_error_logs_task_id ON error_logs(task_id);
CREATE INDEX idx_node_status_node_id ON node_status(node_id);
CREATE INDEX idx_node_status_status ON node_status(status);
CREATE INDEX idx_node_status_last_heartbeat ON node_status(last_heartbeat);

-- 创建更新时间触发器
CREATE TRIGGER update_task_statistics_updated_at
    AFTER UPDATE ON task_statistics
    FOR EACH ROW
BEGIN
    UPDATE task_statistics SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER update_node_status_updated_at
    AFTER UPDATE ON node_status
    FOR EACH ROW
BEGIN
    UPDATE node_status SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
