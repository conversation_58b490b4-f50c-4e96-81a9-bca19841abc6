{"rustc": 1842507548689473721, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"default\", \"fresh-rust\", \"nightly\", \"serde\", \"std\"]", "target": 5388200169723499962, "profile": 8277339565235241299, "path": 18039700936156463448, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\allocator-api2-8fa2aa61800af23f\\dep-lib-allocator_api2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}