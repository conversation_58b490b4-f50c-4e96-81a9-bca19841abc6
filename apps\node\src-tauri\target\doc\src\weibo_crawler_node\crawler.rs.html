<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\crawler.rs`."><title>crawler.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>crawler.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{AppError, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>reqwest::{Client, header::HeaderMap};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>scraper::Html;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>std::collections::HashMap;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>std::sync::Arc;
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span>std::time::Duration;
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">use </span>tokio::sync::RwLock;
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">use </span>tracing::{info, error};
<a href=#10 id=10 data-nosnippet>10</a>
<a href=#11 id=11 data-nosnippet>11</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#12 id=12 data-nosnippet>12</a></span><span class="kw">pub struct </span>CrawlTask {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>id: String,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>task_type: TaskType,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>target_url: String,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>priority: u8,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>retry_count: u32,
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">pub </span>max_retries: u32,
<a href=#19 id=19 data-nosnippet>19</a>    <span class="kw">pub </span>metadata: HashMap&lt;String, String&gt;,
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#21 id=21 data-nosnippet>21</a>}
<a href=#22 id=22 data-nosnippet>22</a>
<a href=#23 id=23 data-nosnippet>23</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#24 id=24 data-nosnippet>24</a></span><span class="kw">pub enum </span>TaskType {
<a href=#25 id=25 data-nosnippet>25</a>    User,
<a href=#26 id=26 data-nosnippet>26</a>    Post,
<a href=#27 id=27 data-nosnippet>27</a>    Comment,
<a href=#28 id=28 data-nosnippet>28</a>    Topic,
<a href=#29 id=29 data-nosnippet>29</a>}
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#32 id=32 data-nosnippet>32</a></span><span class="kw">pub struct </span>CrawlResult {
<a href=#33 id=33 data-nosnippet>33</a>    <span class="kw">pub </span>task_id: String,
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">pub </span>success: bool,
<a href=#35 id=35 data-nosnippet>35</a>    <span class="kw">pub </span>data: <span class="prelude-ty">Option</span>&lt;serde_json::Value&gt;,
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>error_message: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#37 id=37 data-nosnippet>37</a>    <span class="kw">pub </span>response_time: u64,
<a href=#38 id=38 data-nosnippet>38</a>    <span class="kw">pub </span>completed_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#39 id=39 data-nosnippet>39</a>}
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a><span class="kw">pub struct </span>CrawlerEngine {
<a href=#42 id=42 data-nosnippet>42</a>    client: Client,
<a href=#43 id=43 data-nosnippet>43</a>    active_tasks: Arc&lt;RwLock&lt;HashMap&lt;String, CrawlTask&gt;&gt;&gt;,
<a href=#44 id=44 data-nosnippet>44</a>    <span class="attr">#[allow(dead_code)]
<a href=#45 id=45 data-nosnippet>45</a>    </span>max_concurrent: usize,
<a href=#46 id=46 data-nosnippet>46</a>    request_timeout: Duration,
<a href=#47 id=47 data-nosnippet>47</a>}
<a href=#48 id=48 data-nosnippet>48</a>
<a href=#49 id=49 data-nosnippet>49</a><span class="kw">impl </span>CrawlerEngine {
<a href=#50 id=50 data-nosnippet>50</a>    <span class="kw">pub async fn </span>new() -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#51 id=51 data-nosnippet>51</a>        <span class="kw">let </span><span class="kw-2">mut </span>headers = HeaderMap::new();
<a href=#52 id=52 data-nosnippet>52</a>        headers.insert(<span class="string">"User-Agent"</span>, <span class="string">"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"</span>.parse().unwrap());
<a href=#53 id=53 data-nosnippet>53</a>        headers.insert(<span class="string">"Accept"</span>, <span class="string">"text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"</span>.parse().unwrap());
<a href=#54 id=54 data-nosnippet>54</a>        headers.insert(<span class="string">"Accept-Language"</span>, <span class="string">"zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3"</span>.parse().unwrap());
<a href=#55 id=55 data-nosnippet>55</a>        headers.insert(<span class="string">"Accept-Encoding"</span>, <span class="string">"gzip, deflate"</span>.parse().unwrap());
<a href=#56 id=56 data-nosnippet>56</a>        headers.insert(<span class="string">"Connection"</span>, <span class="string">"keep-alive"</span>.parse().unwrap());
<a href=#57 id=57 data-nosnippet>57</a>
<a href=#58 id=58 data-nosnippet>58</a>        <span class="kw">let </span>client = Client::builder()
<a href=#59 id=59 data-nosnippet>59</a>            .default_headers(headers)
<a href=#60 id=60 data-nosnippet>60</a>            .timeout(Duration::from_secs(<span class="number">30</span>))
<a href=#61 id=61 data-nosnippet>61</a>            .cookie_store(<span class="bool-val">true</span>)
<a href=#62 id=62 data-nosnippet>62</a>            .gzip(<span class="bool-val">true</span>)
<a href=#63 id=63 data-nosnippet>63</a>            .build()
<a href=#64 id=64 data-nosnippet>64</a>            .map_err(|e| AppError::Crawler(<span class="macro">format!</span>(<span class="string">"创建HTTP客户端失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#65 id=65 data-nosnippet>65</a>
<a href=#66 id=66 data-nosnippet>66</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#67 id=67 data-nosnippet>67</a>            client,
<a href=#68 id=68 data-nosnippet>68</a>            active_tasks: Arc::new(RwLock::new(HashMap::new())),
<a href=#69 id=69 data-nosnippet>69</a>            max_concurrent: <span class="number">100</span>,
<a href=#70 id=70 data-nosnippet>70</a>            request_timeout: Duration::from_secs(<span class="number">30</span>),
<a href=#71 id=71 data-nosnippet>71</a>        })
<a href=#72 id=72 data-nosnippet>72</a>    }
<a href=#73 id=73 data-nosnippet>73</a>
<a href=#74 id=74 data-nosnippet>74</a>    <span class="kw">pub async fn </span>start(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#75 id=75 data-nosnippet>75</a>        <span class="macro">info!</span>(<span class="string">"爬虫引擎启动"</span>);
<a href=#76 id=76 data-nosnippet>76</a>        <span class="comment">// 这里可以启动任务处理循环
<a href=#77 id=77 data-nosnippet>77</a>        </span><span class="prelude-val">Ok</span>(())
<a href=#78 id=78 data-nosnippet>78</a>    }
<a href=#79 id=79 data-nosnippet>79</a>
<a href=#80 id=80 data-nosnippet>80</a>    <span class="kw">pub async fn </span>stop(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#81 id=81 data-nosnippet>81</a>        <span class="macro">info!</span>(<span class="string">"爬虫引擎停止"</span>);
<a href=#82 id=82 data-nosnippet>82</a>        <span class="comment">// 这里可以停止任务处理循环
<a href=#83 id=83 data-nosnippet>83</a>        </span><span class="prelude-val">Ok</span>(())
<a href=#84 id=84 data-nosnippet>84</a>    }
<a href=#85 id=85 data-nosnippet>85</a>
<a href=#86 id=86 data-nosnippet>86</a>    <span class="kw">pub async fn </span>execute_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task: CrawlTask) -&gt; <span class="prelude-ty">Result</span>&lt;CrawlResult&gt; {
<a href=#87 id=87 data-nosnippet>87</a>        <span class="kw">let </span>start_time = std::time::Instant::now();
<a href=#88 id=88 data-nosnippet>88</a>        
<a href=#89 id=89 data-nosnippet>89</a>        <span class="comment">// 添加到活跃任务列表
<a href=#90 id=90 data-nosnippet>90</a>        </span>{
<a href=#91 id=91 data-nosnippet>91</a>            <span class="kw">let </span><span class="kw-2">mut </span>active_tasks = <span class="self">self</span>.active_tasks.write().<span class="kw">await</span>;
<a href=#92 id=92 data-nosnippet>92</a>            active_tasks.insert(task.id.clone(), task.clone());
<a href=#93 id=93 data-nosnippet>93</a>        }
<a href=#94 id=94 data-nosnippet>94</a>
<a href=#95 id=95 data-nosnippet>95</a>        <span class="kw">let </span>result = <span class="kw">match </span>task.task_type {
<a href=#96 id=96 data-nosnippet>96</a>            TaskType::User =&gt; <span class="self">self</span>.crawl_user_info(<span class="kw-2">&amp;</span>task).<span class="kw">await</span>,
<a href=#97 id=97 data-nosnippet>97</a>            TaskType::Post =&gt; <span class="self">self</span>.crawl_post_content(<span class="kw-2">&amp;</span>task).<span class="kw">await</span>,
<a href=#98 id=98 data-nosnippet>98</a>            TaskType::Comment =&gt; <span class="self">self</span>.crawl_comments(<span class="kw-2">&amp;</span>task).<span class="kw">await</span>,
<a href=#99 id=99 data-nosnippet>99</a>            TaskType::Topic =&gt; <span class="self">self</span>.crawl_topic_info(<span class="kw-2">&amp;</span>task).<span class="kw">await</span>,
<a href=#100 id=100 data-nosnippet>100</a>        };
<a href=#101 id=101 data-nosnippet>101</a>
<a href=#102 id=102 data-nosnippet>102</a>        <span class="comment">// 从活跃任务列表移除
<a href=#103 id=103 data-nosnippet>103</a>        </span>{
<a href=#104 id=104 data-nosnippet>104</a>            <span class="kw">let </span><span class="kw-2">mut </span>active_tasks = <span class="self">self</span>.active_tasks.write().<span class="kw">await</span>;
<a href=#105 id=105 data-nosnippet>105</a>            active_tasks.remove(<span class="kw-2">&amp;</span>task.id);
<a href=#106 id=106 data-nosnippet>106</a>        }
<a href=#107 id=107 data-nosnippet>107</a>
<a href=#108 id=108 data-nosnippet>108</a>        <span class="kw">let </span>response_time = start_time.elapsed().as_millis() <span class="kw">as </span>u64;
<a href=#109 id=109 data-nosnippet>109</a>
<a href=#110 id=110 data-nosnippet>110</a>        <span class="kw">match </span>result {
<a href=#111 id=111 data-nosnippet>111</a>            <span class="prelude-val">Ok</span>(data) =&gt; {
<a href=#112 id=112 data-nosnippet>112</a>                <span class="macro">info!</span>(<span class="string">"任务 {} 执行成功，耗时: {}ms"</span>, task.id, response_time);
<a href=#113 id=113 data-nosnippet>113</a>                <span class="prelude-val">Ok</span>(CrawlResult {
<a href=#114 id=114 data-nosnippet>114</a>                    task_id: task.id,
<a href=#115 id=115 data-nosnippet>115</a>                    success: <span class="bool-val">true</span>,
<a href=#116 id=116 data-nosnippet>116</a>                    data: <span class="prelude-val">Some</span>(data),
<a href=#117 id=117 data-nosnippet>117</a>                    error_message: <span class="prelude-val">None</span>,
<a href=#118 id=118 data-nosnippet>118</a>                    response_time,
<a href=#119 id=119 data-nosnippet>119</a>                    completed_at: chrono::Utc::now(),
<a href=#120 id=120 data-nosnippet>120</a>                })
<a href=#121 id=121 data-nosnippet>121</a>            }
<a href=#122 id=122 data-nosnippet>122</a>            <span class="prelude-val">Err</span>(e) =&gt; {
<a href=#123 id=123 data-nosnippet>123</a>                <span class="macro">error!</span>(<span class="string">"任务 {} 执行失败: {}"</span>, task.id, e);
<a href=#124 id=124 data-nosnippet>124</a>                <span class="prelude-val">Ok</span>(CrawlResult {
<a href=#125 id=125 data-nosnippet>125</a>                    task_id: task.id,
<a href=#126 id=126 data-nosnippet>126</a>                    success: <span class="bool-val">false</span>,
<a href=#127 id=127 data-nosnippet>127</a>                    data: <span class="prelude-val">None</span>,
<a href=#128 id=128 data-nosnippet>128</a>                    error_message: <span class="prelude-val">Some</span>(e.to_string()),
<a href=#129 id=129 data-nosnippet>129</a>                    response_time,
<a href=#130 id=130 data-nosnippet>130</a>                    completed_at: chrono::Utc::now(),
<a href=#131 id=131 data-nosnippet>131</a>                })
<a href=#132 id=132 data-nosnippet>132</a>            }
<a href=#133 id=133 data-nosnippet>133</a>        }
<a href=#134 id=134 data-nosnippet>134</a>    }
<a href=#135 id=135 data-nosnippet>135</a>
<a href=#136 id=136 data-nosnippet>136</a>    <span class="kw">async fn </span>crawl_user_info(<span class="kw-2">&amp;</span><span class="self">self</span>, task: <span class="kw-2">&amp;</span>CrawlTask) -&gt; <span class="prelude-ty">Result</span>&lt;serde_json::Value&gt; {
<a href=#137 id=137 data-nosnippet>137</a>        <span class="kw">let </span>response = <span class="self">self</span>.client
<a href=#138 id=138 data-nosnippet>138</a>            .get(<span class="kw-2">&amp;</span>task.target_url)
<a href=#139 id=139 data-nosnippet>139</a>            .timeout(<span class="self">self</span>.request_timeout)
<a href=#140 id=140 data-nosnippet>140</a>            .send()
<a href=#141 id=141 data-nosnippet>141</a>            .<span class="kw">await
<a href=#142 id=142 data-nosnippet>142</a>            </span>.map_err(|e| AppError::Crawler(<span class="macro">format!</span>(<span class="string">"请求失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#143 id=143 data-nosnippet>143</a>
<a href=#144 id=144 data-nosnippet>144</a>        <span class="kw">let </span>html = response.text().<span class="kw">await
<a href=#145 id=145 data-nosnippet>145</a>            </span>.map_err(|e| AppError::Crawler(<span class="macro">format!</span>(<span class="string">"读取响应失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#146 id=146 data-nosnippet>146</a>
<a href=#147 id=147 data-nosnippet>147</a>        <span class="comment">// 解析用户信息
<a href=#148 id=148 data-nosnippet>148</a>        </span><span class="kw">let </span>_document = Html::parse_document(<span class="kw-2">&amp;</span>html);
<a href=#149 id=149 data-nosnippet>149</a>        
<a href=#150 id=150 data-nosnippet>150</a>        <span class="comment">// 这里应该根据实际的微博页面结构来解析
<a href=#151 id=151 data-nosnippet>151</a>        // 目前返回模拟数据
<a href=#152 id=152 data-nosnippet>152</a>        </span><span class="prelude-val">Ok</span>(<span class="macro">serde_json::json!</span>({
<a href=#153 id=153 data-nosnippet>153</a>            <span class="string">"user_id"</span>: <span class="string">"123456789"</span>,
<a href=#154 id=154 data-nosnippet>154</a>            <span class="string">"username"</span>: <span class="string">"test_user"</span>,
<a href=#155 id=155 data-nosnippet>155</a>            <span class="string">"nickname"</span>: <span class="string">"测试用户"</span>,
<a href=#156 id=156 data-nosnippet>156</a>            <span class="string">"followers_count"</span>: <span class="number">1000</span>,
<a href=#157 id=157 data-nosnippet>157</a>            <span class="string">"following_count"</span>: <span class="number">500</span>,
<a href=#158 id=158 data-nosnippet>158</a>            <span class="string">"posts_count"</span>: <span class="number">200</span>,
<a href=#159 id=159 data-nosnippet>159</a>            <span class="string">"verified"</span>: <span class="bool-val">false</span>,
<a href=#160 id=160 data-nosnippet>160</a>            <span class="string">"description"</span>: <span class="string">"这是一个测试用户"</span>,
<a href=#161 id=161 data-nosnippet>161</a>            <span class="string">"location"</span>: <span class="string">"北京"</span>,
<a href=#162 id=162 data-nosnippet>162</a>            <span class="string">"crawled_at"</span>: chrono::Utc::now()
<a href=#163 id=163 data-nosnippet>163</a>        }))
<a href=#164 id=164 data-nosnippet>164</a>    }
<a href=#165 id=165 data-nosnippet>165</a>
<a href=#166 id=166 data-nosnippet>166</a>    <span class="kw">async fn </span>crawl_post_content(<span class="kw-2">&amp;</span><span class="self">self</span>, task: <span class="kw-2">&amp;</span>CrawlTask) -&gt; <span class="prelude-ty">Result</span>&lt;serde_json::Value&gt; {
<a href=#167 id=167 data-nosnippet>167</a>        <span class="kw">let </span>response = <span class="self">self</span>.client
<a href=#168 id=168 data-nosnippet>168</a>            .get(<span class="kw-2">&amp;</span>task.target_url)
<a href=#169 id=169 data-nosnippet>169</a>            .timeout(<span class="self">self</span>.request_timeout)
<a href=#170 id=170 data-nosnippet>170</a>            .send()
<a href=#171 id=171 data-nosnippet>171</a>            .<span class="kw">await
<a href=#172 id=172 data-nosnippet>172</a>            </span>.map_err(|e| AppError::Crawler(<span class="macro">format!</span>(<span class="string">"请求失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#173 id=173 data-nosnippet>173</a>
<a href=#174 id=174 data-nosnippet>174</a>        <span class="kw">let </span>html = response.text().<span class="kw">await
<a href=#175 id=175 data-nosnippet>175</a>            </span>.map_err(|e| AppError::Crawler(<span class="macro">format!</span>(<span class="string">"读取响应失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#176 id=176 data-nosnippet>176</a>
<a href=#177 id=177 data-nosnippet>177</a>        <span class="comment">// 解析微博内容
<a href=#178 id=178 data-nosnippet>178</a>        </span><span class="kw">let </span>_document = Html::parse_document(<span class="kw-2">&amp;</span>html);
<a href=#179 id=179 data-nosnippet>179</a>
<a href=#180 id=180 data-nosnippet>180</a>        <span class="comment">// 这里应该根据实际的微博页面结构来解析
<a href=#181 id=181 data-nosnippet>181</a>        // 目前返回模拟数据
<a href=#182 id=182 data-nosnippet>182</a>        </span><span class="prelude-val">Ok</span>(<span class="macro">serde_json::json!</span>({
<a href=#183 id=183 data-nosnippet>183</a>            <span class="string">"post_id"</span>: <span class="string">"987654321"</span>,
<a href=#184 id=184 data-nosnippet>184</a>            <span class="string">"user_id"</span>: <span class="string">"123456789"</span>,
<a href=#185 id=185 data-nosnippet>185</a>            <span class="string">"content"</span>: <span class="string">"这是一条测试微博内容"</span>,
<a href=#186 id=186 data-nosnippet>186</a>            <span class="string">"like_count"</span>: <span class="number">100</span>,
<a href=#187 id=187 data-nosnippet>187</a>            <span class="string">"comment_count"</span>: <span class="number">50</span>,
<a href=#188 id=188 data-nosnippet>188</a>            <span class="string">"repost_count"</span>: <span class="number">25</span>,
<a href=#189 id=189 data-nosnippet>189</a>            <span class="string">"published_at"</span>: <span class="string">"2024-01-15T10:30:00Z"</span>,
<a href=#190 id=190 data-nosnippet>190</a>            <span class="string">"crawled_at"</span>: chrono::Utc::now()
<a href=#191 id=191 data-nosnippet>191</a>        }))
<a href=#192 id=192 data-nosnippet>192</a>    }
<a href=#193 id=193 data-nosnippet>193</a>
<a href=#194 id=194 data-nosnippet>194</a>    <span class="kw">async fn </span>crawl_comments(<span class="kw-2">&amp;</span><span class="self">self</span>, task: <span class="kw-2">&amp;</span>CrawlTask) -&gt; <span class="prelude-ty">Result</span>&lt;serde_json::Value&gt; {
<a href=#195 id=195 data-nosnippet>195</a>        <span class="kw">let </span>response = <span class="self">self</span>.client
<a href=#196 id=196 data-nosnippet>196</a>            .get(<span class="kw-2">&amp;</span>task.target_url)
<a href=#197 id=197 data-nosnippet>197</a>            .timeout(<span class="self">self</span>.request_timeout)
<a href=#198 id=198 data-nosnippet>198</a>            .send()
<a href=#199 id=199 data-nosnippet>199</a>            .<span class="kw">await
<a href=#200 id=200 data-nosnippet>200</a>            </span>.map_err(|e| AppError::Crawler(<span class="macro">format!</span>(<span class="string">"请求失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#201 id=201 data-nosnippet>201</a>
<a href=#202 id=202 data-nosnippet>202</a>        <span class="kw">let </span>html = response.text().<span class="kw">await
<a href=#203 id=203 data-nosnippet>203</a>            </span>.map_err(|e| AppError::Crawler(<span class="macro">format!</span>(<span class="string">"读取响应失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#204 id=204 data-nosnippet>204</a>
<a href=#205 id=205 data-nosnippet>205</a>        <span class="comment">// 解析评论数据
<a href=#206 id=206 data-nosnippet>206</a>        </span><span class="kw">let </span>_document = Html::parse_document(<span class="kw-2">&amp;</span>html);
<a href=#207 id=207 data-nosnippet>207</a>
<a href=#208 id=208 data-nosnippet>208</a>        <span class="comment">// 这里应该根据实际的微博页面结构来解析
<a href=#209 id=209 data-nosnippet>209</a>        // 目前返回模拟数据
<a href=#210 id=210 data-nosnippet>210</a>        </span><span class="prelude-val">Ok</span>(<span class="macro">serde_json::json!</span>({
<a href=#211 id=211 data-nosnippet>211</a>            <span class="string">"comments"</span>: [
<a href=#212 id=212 data-nosnippet>212</a>                {
<a href=#213 id=213 data-nosnippet>213</a>                    <span class="string">"comment_id"</span>: <span class="string">"comment_001"</span>,
<a href=#214 id=214 data-nosnippet>214</a>                    <span class="string">"post_id"</span>: <span class="string">"987654321"</span>,
<a href=#215 id=215 data-nosnippet>215</a>                    <span class="string">"user_id"</span>: <span class="string">"user_001"</span>,
<a href=#216 id=216 data-nosnippet>216</a>                    <span class="string">"content"</span>: <span class="string">"这是一条测试评论"</span>,
<a href=#217 id=217 data-nosnippet>217</a>                    <span class="string">"like_count"</span>: <span class="number">10</span>,
<a href=#218 id=218 data-nosnippet>218</a>                    <span class="string">"published_at"</span>: <span class="string">"2024-01-15T10:35:00Z"
<a href=#219 id=219 data-nosnippet>219</a>                </span>}
<a href=#220 id=220 data-nosnippet>220</a>            ],
<a href=#221 id=221 data-nosnippet>221</a>            <span class="string">"total_count"</span>: <span class="number">1</span>,
<a href=#222 id=222 data-nosnippet>222</a>            <span class="string">"crawled_at"</span>: chrono::Utc::now()
<a href=#223 id=223 data-nosnippet>223</a>        }))
<a href=#224 id=224 data-nosnippet>224</a>    }
<a href=#225 id=225 data-nosnippet>225</a>
<a href=#226 id=226 data-nosnippet>226</a>    <span class="kw">async fn </span>crawl_topic_info(<span class="kw-2">&amp;</span><span class="self">self</span>, task: <span class="kw-2">&amp;</span>CrawlTask) -&gt; <span class="prelude-ty">Result</span>&lt;serde_json::Value&gt; {
<a href=#227 id=227 data-nosnippet>227</a>        <span class="kw">let </span>response = <span class="self">self</span>.client
<a href=#228 id=228 data-nosnippet>228</a>            .get(<span class="kw-2">&amp;</span>task.target_url)
<a href=#229 id=229 data-nosnippet>229</a>            .timeout(<span class="self">self</span>.request_timeout)
<a href=#230 id=230 data-nosnippet>230</a>            .send()
<a href=#231 id=231 data-nosnippet>231</a>            .<span class="kw">await
<a href=#232 id=232 data-nosnippet>232</a>            </span>.map_err(|e| AppError::Crawler(<span class="macro">format!</span>(<span class="string">"请求失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#233 id=233 data-nosnippet>233</a>
<a href=#234 id=234 data-nosnippet>234</a>        <span class="kw">let </span>html = response.text().<span class="kw">await
<a href=#235 id=235 data-nosnippet>235</a>            </span>.map_err(|e| AppError::Crawler(<span class="macro">format!</span>(<span class="string">"读取响应失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#236 id=236 data-nosnippet>236</a>
<a href=#237 id=237 data-nosnippet>237</a>        <span class="comment">// 解析话题信息
<a href=#238 id=238 data-nosnippet>238</a>        </span><span class="kw">let </span>_document = Html::parse_document(<span class="kw-2">&amp;</span>html);
<a href=#239 id=239 data-nosnippet>239</a>
<a href=#240 id=240 data-nosnippet>240</a>        <span class="comment">// 这里应该根据实际的微博页面结构来解析
<a href=#241 id=241 data-nosnippet>241</a>        // 目前返回模拟数据
<a href=#242 id=242 data-nosnippet>242</a>        </span><span class="prelude-val">Ok</span>(<span class="macro">serde_json::json!</span>({
<a href=#243 id=243 data-nosnippet>243</a>            <span class="string">"topic_id"</span>: <span class="string">"topic_001"</span>,
<a href=#244 id=244 data-nosnippet>244</a>            <span class="string">"name"</span>: <span class="string">"测试话题"</span>,
<a href=#245 id=245 data-nosnippet>245</a>            <span class="string">"description"</span>: <span class="string">"这是一个测试话题"</span>,
<a href=#246 id=246 data-nosnippet>246</a>            <span class="string">"post_count"</span>: <span class="number">1000</span>,
<a href=#247 id=247 data-nosnippet>247</a>            <span class="string">"participant_count"</span>: <span class="number">500</span>,
<a href=#248 id=248 data-nosnippet>248</a>            <span class="string">"hot_posts"</span>: [],
<a href=#249 id=249 data-nosnippet>249</a>            <span class="string">"crawled_at"</span>: chrono::Utc::now()
<a href=#250 id=250 data-nosnippet>250</a>        }))
<a href=#251 id=251 data-nosnippet>251</a>    }
<a href=#252 id=252 data-nosnippet>252</a>
<a href=#253 id=253 data-nosnippet>253</a>    <span class="kw">pub async fn </span>get_active_tasks(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;CrawlTask&gt; {
<a href=#254 id=254 data-nosnippet>254</a>        <span class="kw">let </span>active_tasks = <span class="self">self</span>.active_tasks.read().<span class="kw">await</span>;
<a href=#255 id=255 data-nosnippet>255</a>        active_tasks.values().cloned().collect()
<a href=#256 id=256 data-nosnippet>256</a>    }
<a href=#257 id=257 data-nosnippet>257</a>
<a href=#258 id=258 data-nosnippet>258</a>    <span class="kw">pub async fn </span>get_active_task_count(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; usize {
<a href=#259 id=259 data-nosnippet>259</a>        <span class="kw">let </span>active_tasks = <span class="self">self</span>.active_tasks.read().<span class="kw">await</span>;
<a href=#260 id=260 data-nosnippet>260</a>        active_tasks.len()
<a href=#261 id=261 data-nosnippet>261</a>    }
<a href=#262 id=262 data-nosnippet>262</a>}</code></pre></div></section></main></body></html>