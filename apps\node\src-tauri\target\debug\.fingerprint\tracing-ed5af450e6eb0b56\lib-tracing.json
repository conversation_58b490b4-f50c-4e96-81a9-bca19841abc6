{"rustc": 1842507548689473721, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 11202463608144111571, "path": 13173524339016769992, "deps": [[325572602735163265, "tracing_attributes", false, 6648120420862857763], [1906322745568073236, "pin_project_lite", false, 8108972857832786586], [3424551429995674438, "tracing_core", false, 6586885485974029526], [5986029879202738730, "log", false, 3004741351825838512]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-ed5af450e6eb0b56\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}