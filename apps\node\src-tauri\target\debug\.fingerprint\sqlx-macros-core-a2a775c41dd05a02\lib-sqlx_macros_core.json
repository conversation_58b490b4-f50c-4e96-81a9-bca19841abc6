{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2225463790103693989, "path": 1772539668647958475, "deps": [[530211389790465181, "hex", false, 12229943825153590659], [996810380461694889, "sqlx_core", false, 17065667732324117254], [1441306149310335789, "tempfile", false, 18220066594357074812], [2713742371683562785, "syn", false, 9891413255480890497], [3060637413840920116, "proc_macro2", false, 10993881616374897205], [3150220818285335163, "url", false, 12924313294338006410], [3405707034081185165, "dotenvy", false, 6305364608311466902], [3722963349756955755, "once_cell", false, 3092077161891181902], [8045585743974080694, "heck", false, 7625333963528033452], [8569119365930580996, "serde_json", false, 6464436191210645041], [9689903380558560274, "serde", false, 4389908846715814937], [9857275760291862238, "sha2", false, 3599881831848126162], [11838249260056359578, "sqlx_sqlite", false, 3087588939316175790], [12170264697963848012, "either", false, 18004636883487716086], [12944427623413450645, "tokio", false, 1766398034649706782], [17990358020177143287, "quote", false, 8157755205024365785]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-a2a775c41dd05a02\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}