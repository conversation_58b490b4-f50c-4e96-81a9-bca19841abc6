{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2225463790103693989, "path": 13632869191356886395, "deps": [[4022439902832367970, "zerofrom_derive", false, 8534365858912733267]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-736368635c6b6f1e\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}