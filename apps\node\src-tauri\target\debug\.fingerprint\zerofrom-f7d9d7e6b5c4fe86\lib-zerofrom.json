{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\"]", "declared_features": "[\"alloc\", \"default\", \"derive\"]", "target": 723370850876025358, "profile": 2241668132362809309, "path": 13632869191356886395, "deps": [[4022439902832367970, "zerofrom_derive", false, 8534365858912733267]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerofrom-f7d9d7e6b5c4fe86\\dep-lib-zerofrom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}