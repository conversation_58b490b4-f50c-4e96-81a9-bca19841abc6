import { useEffect, memo, useMemo, useCallback } from "react";
import {
  Activity,
  Users,
  Globe,
  ListTodo,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock
} from "lucide-react";
import {
  useNodeStore,
  useTaskStore,
  useProxyStore,
  useAccountStore
} from "../lib/tauri";
import { formatDuration, formatNumber, getStatusColor } from "../lib/utils";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  StatCard,
  MetricCard,
  InfoCard
} from "../components/ui";
import { LoadingSkeleton } from "../components/Layout";

interface StatCard {
  title: string;
  value: string | number;
  change?: string;
  icon: React.ComponentType<any>;
  color: string;
}

function DashboardComponent() {
  // 使用 Zustand stores
  const {
    nodeInfo,
    systemMetrics,
    activeTasks,
    totalTasks,
    successRate,
    loading: nodeLoading,
    fetchNodeStatus
  } = useNodeStore();

  const {
    queueInfo,
    loading: taskLoading,
    fetchQueueStatus,
    fetchTaskStatistics
  } = useTaskStore();

  const {
    statistics: proxyStats,
    loading: proxyLoading,
    fetchProxyStatistics
  } = useProxyStore();

  const {
    statistics: accountStats,
    loading: accountLoading,
    fetchAccountStatistics
  } = useAccountStore();

  const loading = nodeLoading || taskLoading || proxyLoading || accountLoading;

  // 将所有hooks放在早期return之前
  const statCards: StatCard[] = useMemo(() => [
    {
      title: "活跃任务",
      value: activeTasks || 0,
      change: `总计 ${formatNumber(totalTasks || 0)}`,
      icon: ListTodo,
      color: "text-blue-600",
    },
    {
      title: "代理池状态",
      value: `${proxyStats?.healthy_proxies || 0}/${proxyStats?.total_proxies || 0}`,
      change: `健康率 ${((proxyStats?.healthy_proxies || 0) / (proxyStats?.total_proxies || 1) * 100).toFixed(1)}%`,
      icon: Globe,
      color: "text-green-600",
    },
    {
      title: "账号池状态",
      value: `${accountStats?.active_accounts || 0}/${accountStats?.total_accounts || 0}`,
      change: `登录 ${accountStats?.logged_in_accounts || 0}`,
      icon: Users,
      color: "text-purple-600",
    },
    {
      title: "成功率",
      value: `${successRate?.toFixed(1) || 0}%`,
      change: `处理速度 ${queueInfo?.processing_speed?.toFixed(1) || 0}/min`,
      icon: TrendingUp,
      color: "text-orange-600",
    },
  ], [activeTasks, totalTasks, proxyStats, accountStats, successRate, queueInfo]);

  const loadDashboardData = useCallback(async () => {
    try {
      await Promise.all([
        fetchNodeStatus(),
        fetchQueueStatus(),
        fetchTaskStatistics(),
        fetchProxyStatistics(),
        fetchAccountStatistics(),
      ]);
    } catch (error) {
      console.error("加载仪表板数据失败:", error);
    }
  }, [fetchNodeStatus, fetchQueueStatus, fetchTaskStatistics, fetchProxyStatistics, fetchAccountStatistics]);

  useEffect(() => {
    loadDashboardData();
    const interval = setInterval(loadDashboardData, 10000); // 每10秒刷新
    return () => clearInterval(interval);
  }, [loadDashboardData]);

  if (loading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="animate-slide-in-left">
        <h1 className="text-2xl sm:text-3xl xl:text-4xl font-bold text-gradient">节点仪表板</h1>
        <p className="text-muted-foreground mt-2 text-sm sm:text-base xl:text-lg">
          实时监控微博爬虫节点运行状态和性能指标
        </p>
      </div>

      {/* 节点状态卡片 - 现代化设计 */}
      <InfoCard
        title="节点状态"
        subtitle={`${nodeInfo?.nodeName || 'Unknown'} (${nodeInfo?.nodeId || 'N/A'})`}
        status={nodeInfo?.status === "online" ? "success" : "error"}
        actions={
          <div className={`status-indicator ${nodeInfo?.status === "online" ? "status-online" : "status-offline"}`}>
            <div className="w-2 h-2 rounded-full bg-current animate-pulse mr-2" />
            <span className="text-sm font-medium">
              {nodeInfo?.status === "online" ? "在线" : "离线"}
            </span>
          </div>
        }
        content={
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6">
            <MetricCard
              title="运行时间"
              value={formatDuration(nodeInfo?.uptime || 0)}
              color="hsl(var(--primary))"
              description="系统持续运行时间"
            />
            <MetricCard
              title="CPU使用率"
              value={systemMetrics?.cpu_usage?.toFixed(1) || 0}
              unit="%"
              progress={systemMetrics?.cpu_usage || 0}
              color="hsl(var(--info))"
              description="当前处理器使用情况"
            />
            <MetricCard
              title="内存使用率"
              value={systemMetrics?.memory_usage?.toFixed(1) || 0}
              unit="%"
              progress={systemMetrics?.memory_usage || 0}
              color="hsl(var(--warning))"
              description="系统内存占用情况"
            />
          </div>
        }
        className="animate-scale-in"
      />

      {/* 统计卡片网格 - 现代化响应式设计 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 xl:gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <StatCard
              key={index}
              title={card.title}
              value={card.value}
              change={card.change}
              icon={<Icon className={`h-5 w-5 sm:h-6 sm:w-6 ${card.color}`} />}
              className="animate-scale-in"
              style={{ animationDelay: `${index * 100}ms` }}
            />
          );
        })}
      </div>

      {/* 任务队列状态 - 优化移动端布局 */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6">
        <Card>
          <CardHeader>
            <CardTitle>任务队列</CardTitle>
            <CardDescription>当前任务处理状态</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm">等待中</span>
                </div>
                <span className="font-medium">{queueInfo?.pending_tasks || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Activity className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">处理中</span>
                </div>
                <span className="font-medium">{queueInfo?.running_tasks || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm">已完成</span>
                </div>
                <span className="font-medium">{formatNumber(queueInfo?.completed_tasks || 0)}</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <span className="text-sm">失败</span>
                </div>
                <span className="font-medium">{formatNumber(queueInfo?.failed_tasks || 0)}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 系统资源使用情况 */}
        <InfoCard
          title="系统资源"
          subtitle="当前资源使用情况"
          content={
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <MetricCard
                title="CPU"
                value={systemMetrics?.cpu_usage?.toFixed(1) || 0}
                unit="%"
                progress={systemMetrics?.cpu_usage || 0}
                color="hsl(var(--info))"
                description="处理器使用率"
              />
              <MetricCard
                title="内存"
                value={systemMetrics?.memory_usage?.toFixed(1) || 0}
                unit="%"
                progress={systemMetrics?.memory_usage || 0}
                color="hsl(var(--success))"
                description="内存占用率"
              />
              <MetricCard
                title="磁盘"
                value={systemMetrics?.disk_usage?.toFixed(1) || 0}
                unit="%"
                progress={systemMetrics?.disk_usage || 0}
                color="hsl(var(--warning))"
                description="存储空间使用"
              />
            </div>
          }
        />
      </div>
    </div>
  );
}

// 使用 React.memo 优化性能
const Dashboard = memo(DashboardComponent);

// 默认导出
export default Dashboard;

// 命名导出（保持向后兼容）
export { Dashboard };
