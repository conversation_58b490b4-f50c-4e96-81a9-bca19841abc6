use sqlx::SqlitePool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 从环境变量或默认值获取数据库URL
    let database_url = env::var("SQLITE_DATABASE_URL")
        .unwrap_or_else(|_| "sqlite:./data/crawler_node.db".to_string());

    println!("连接数据库: {}", database_url);

    // 确保数据目录存在
    if let Some(parent) = std::path::Path::new(database_url.trim_start_matches("sqlite:")).parent() {
        tokio::fs::create_dir_all(parent).await?;
    }

    // 连接数据库
    let pool = SqlitePool::connect(&database_url).await?;

    println!("运行数据库迁移...");

    // 运行迁移
    sqlx::migrate!("./migrations").run(&pool).await?;

    println!("数据库迁移完成！");

    pool.close().await;
    Ok(())
}
