use thiserror::Error;

pub type Result<T> = std::result::Result<T, AppError>;

#[derive(Error, Debug)]
pub enum AppError {
    #[error("配置错误: {0}")]
    Config(String),

    #[error("数据库错误: {0}")]
    Database(#[from] sqlx::Error),

    #[error("数据库迁移错误: {0}")]
    Migration(String),

    #[error("网络请求错误: {0}")]
    Request(#[from] reqwest::Error),

    #[error("Redis错误: {0}")]
    Redis(#[from] redis::RedisError),

    #[error("消息队列错误: {0}")]
    MessageQueue(String),

    #[error("代理错误: {0}")]
    Proxy(String),

    #[error("账号错误: {0}")]
    Account(String),

    #[error("爬虫错误: {0}")]
    Crawler(String),

    #[error("任务调度错误: {0}")]
    Scheduler(String),

    #[error("系统监控错误: {0}")]
    Monitor(String),

    #[error("浏览器错误: {0}")]
    Browser(String),

    #[error("IO错误: {0}")]
    Io(#[from] std::io::Error),

    #[error("序列化错误: {0}")]
    Serialization(#[from] serde_json::Error),

    #[error("其他错误: {0}")]
    Other(String),
}

impl From<AppError> for String {
    fn from(error: AppError) -> Self {
        error.to_string()
    }
}

// 为Tauri命令提供错误转换
impl serde::Serialize for AppError {
    fn serialize<S>(&self, serializer: S) -> std::result::Result<S::Ok, S::Error>
    where
        S: serde::ser::Serializer,
    {
        serializer.serialize_str(self.to_string().as_ref())
    }
}

// 从anyhow::Error转换
impl From<anyhow::Error> for AppError {
    fn from(error: anyhow::Error) -> Self {
        AppError::Browser(error.to_string())
    }
}

// 从playwright::Error转换
impl From<playwright::Error> for AppError {
    fn from(error: playwright::Error) -> Self {
        AppError::Browser(error.to_string())
    }
}

// 从Arc<playwright::Error>转换
impl From<std::sync::Arc<playwright::Error>> for AppError {
    fn from(error: std::sync::Arc<playwright::Error>) -> Self {
        AppError::Browser(error.to_string())
    }
}
