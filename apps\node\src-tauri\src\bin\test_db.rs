use sqlx::SqlitePool;
use std::path::Path;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let database_url = "sqlite:./data/crawler_node.db";
    
    println!("测试数据库连接: {}", database_url);
    
    // 检查数据库文件是否存在
    let db_path = database_url.trim_start_matches("sqlite:");
    if Path::new(db_path).exists() {
        println!("✓ 数据库文件存在: {}", db_path);
    } else {
        println!("✗ 数据库文件不存在: {}", db_path);
        return Ok(());
    }
    
    // 尝试连接数据库
    match SqlitePool::connect(database_url).await {
        Ok(pool) => {
            println!("✓ 数据库连接成功");
            
            // 测试查询
            match sqlx::query!("SELECT name FROM sqlite_master WHERE type='table'")
                .fetch_all(&pool)
                .await 
            {
                Ok(rows) => {
                    println!("✓ 数据库表列表:");
                    for row in rows {
                        println!("  - {:?}", row.name);
                    }
                }
                Err(e) => {
                    println!("✗ 查询表失败: {}", e);
                }
            }
            
            pool.close().await;
        }
        Err(e) => {
            println!("✗ 数据库连接失败: {}", e);
        }
    }
    
    Ok(())
}
