<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\config.rs`."><title>config.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>config.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{AppError, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::env;
<a href=#4 id=4 data-nosnippet>4</a>
<a href=#5 id=5 data-nosnippet>5</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#6 id=6 data-nosnippet>6</a></span><span class="kw">pub struct </span>AppConfig {
<a href=#7 id=7 data-nosnippet>7</a>    <span class="comment">// 节点配置
<a href=#8 id=8 data-nosnippet>8</a>    </span><span class="kw">pub </span>node_id: String,
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">pub </span>node_name: String,
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub </span>node_type: String,
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a>    <span class="comment">// 数据库配置
<a href=#13 id=13 data-nosnippet>13</a>    </span><span class="kw">pub </span>database_url: String,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>redis_url: String,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>rabbitmq_url: String,
<a href=#16 id=16 data-nosnippet>16</a>
<a href=#17 id=17 data-nosnippet>17</a>    <span class="comment">// 管理节点配置
<a href=#18 id=18 data-nosnippet>18</a>    </span><span class="kw">pub </span>master_node_url: String,
<a href=#19 id=19 data-nosnippet>19</a>    <span class="kw">pub </span>heartbeat_interval: u64,
<a href=#20 id=20 data-nosnippet>20</a>
<a href=#21 id=21 data-nosnippet>21</a>    <span class="comment">// 爬虫配置
<a href=#22 id=22 data-nosnippet>22</a>    </span><span class="kw">pub </span>max_concurrent_tasks: usize,
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">pub </span>request_timeout: u64,
<a href=#24 id=24 data-nosnippet>24</a>    <span class="kw">pub </span>retry_max_attempts: u32,
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a>    <span class="comment">// 代理配置
<a href=#27 id=27 data-nosnippet>27</a>    </span><span class="kw">pub </span>proxy_pool_size: usize,
<a href=#28 id=28 data-nosnippet>28</a>    <span class="kw">pub </span>proxy_health_check_interval: u64,
<a href=#29 id=29 data-nosnippet>29</a>
<a href=#30 id=30 data-nosnippet>30</a>    <span class="comment">// 账号配置
<a href=#31 id=31 data-nosnippet>31</a>    </span><span class="kw">pub </span>account_pool_size: usize,
<a href=#32 id=32 data-nosnippet>32</a>    <span class="kw">pub </span>account_rotation_interval: u64,
<a href=#33 id=33 data-nosnippet>33</a>
<a href=#34 id=34 data-nosnippet>34</a>    <span class="comment">// 日志配置
<a href=#35 id=35 data-nosnippet>35</a>    </span><span class="kw">pub </span>log_level: String,
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>log_file: String,
<a href=#37 id=37 data-nosnippet>37</a>}
<a href=#38 id=38 data-nosnippet>38</a>
<a href=#39 id=39 data-nosnippet>39</a><span class="kw">impl </span>Default <span class="kw">for </span>AppConfig {
<a href=#40 id=40 data-nosnippet>40</a>    <span class="kw">fn </span>default() -&gt; <span class="self">Self </span>{
<a href=#41 id=41 data-nosnippet>41</a>        <span class="self">Self </span>{
<a href=#42 id=42 data-nosnippet>42</a>            node_id: <span class="string">"crawler_node_001"</span>.to_string(),
<a href=#43 id=43 data-nosnippet>43</a>            node_name: <span class="string">"爬虫节点001"</span>.to_string(),
<a href=#44 id=44 data-nosnippet>44</a>            node_type: <span class="string">"crawler"</span>.to_string(),
<a href=#45 id=45 data-nosnippet>45</a>            database_url: <span class="string">"sqlite:./data/crawler_node.db"</span>.to_string(),
<a href=#46 id=46 data-nosnippet>46</a>            redis_url: <span class="string">"redis://localhost:6379"</span>.to_string(),
<a href=#47 id=47 data-nosnippet>47</a>            rabbitmq_url: <span class="string">"amqp://admin:password@localhost:5672"</span>.to_string(),
<a href=#48 id=48 data-nosnippet>48</a>            master_node_url: <span class="string">"http://localhost:8080"</span>.to_string(),
<a href=#49 id=49 data-nosnippet>49</a>            heartbeat_interval: <span class="number">30</span>,
<a href=#50 id=50 data-nosnippet>50</a>            max_concurrent_tasks: <span class="number">100</span>,
<a href=#51 id=51 data-nosnippet>51</a>            request_timeout: <span class="number">30</span>,
<a href=#52 id=52 data-nosnippet>52</a>            retry_max_attempts: <span class="number">3</span>,
<a href=#53 id=53 data-nosnippet>53</a>            proxy_pool_size: <span class="number">50</span>,
<a href=#54 id=54 data-nosnippet>54</a>            proxy_health_check_interval: <span class="number">300</span>,
<a href=#55 id=55 data-nosnippet>55</a>            account_pool_size: <span class="number">20</span>,
<a href=#56 id=56 data-nosnippet>56</a>            account_rotation_interval: <span class="number">3600</span>,
<a href=#57 id=57 data-nosnippet>57</a>            log_level: <span class="string">"info"</span>.to_string(),
<a href=#58 id=58 data-nosnippet>58</a>            log_file: <span class="string">"./logs/crawler_node.log"</span>.to_string(),
<a href=#59 id=59 data-nosnippet>59</a>        }
<a href=#60 id=60 data-nosnippet>60</a>    }
<a href=#61 id=61 data-nosnippet>61</a>}
<a href=#62 id=62 data-nosnippet>62</a>
<a href=#63 id=63 data-nosnippet>63</a><span class="kw">impl </span>AppConfig {
<a href=#64 id=64 data-nosnippet>64</a>    <span class="kw">pub async fn </span>load() -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#65 id=65 data-nosnippet>65</a>        <span class="kw">let </span><span class="kw-2">mut </span>config = <span class="self">Self</span>::default();
<a href=#66 id=66 data-nosnippet>66</a>
<a href=#67 id=67 data-nosnippet>67</a>        <span class="comment">// 从环境变量加载配置
<a href=#68 id=68 data-nosnippet>68</a>        </span><span class="kw">if let </span><span class="prelude-val">Ok</span>(node_id) = env::var(<span class="string">"NODE_ID"</span>) {
<a href=#69 id=69 data-nosnippet>69</a>            config.node_id = node_id;
<a href=#70 id=70 data-nosnippet>70</a>        }
<a href=#71 id=71 data-nosnippet>71</a>
<a href=#72 id=72 data-nosnippet>72</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(node_name) = env::var(<span class="string">"NODE_NAME"</span>) {
<a href=#73 id=73 data-nosnippet>73</a>            config.node_name = node_name;
<a href=#74 id=74 data-nosnippet>74</a>        }
<a href=#75 id=75 data-nosnippet>75</a>
<a href=#76 id=76 data-nosnippet>76</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(node_type) = env::var(<span class="string">"NODE_TYPE"</span>) {
<a href=#77 id=77 data-nosnippet>77</a>            config.node_type = node_type;
<a href=#78 id=78 data-nosnippet>78</a>        }
<a href=#79 id=79 data-nosnippet>79</a>
<a href=#80 id=80 data-nosnippet>80</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(database_url) = env::var(<span class="string">"SQLITE_DATABASE_URL"</span>) {
<a href=#81 id=81 data-nosnippet>81</a>            config.database_url = database_url;
<a href=#82 id=82 data-nosnippet>82</a>        }
<a href=#83 id=83 data-nosnippet>83</a>
<a href=#84 id=84 data-nosnippet>84</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(redis_url) = env::var(<span class="string">"REDIS_URL"</span>) {
<a href=#85 id=85 data-nosnippet>85</a>            config.redis_url = redis_url;
<a href=#86 id=86 data-nosnippet>86</a>        }
<a href=#87 id=87 data-nosnippet>87</a>
<a href=#88 id=88 data-nosnippet>88</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(rabbitmq_url) = env::var(<span class="string">"RABBITMQ_URL"</span>) {
<a href=#89 id=89 data-nosnippet>89</a>            config.rabbitmq_url = rabbitmq_url;
<a href=#90 id=90 data-nosnippet>90</a>        }
<a href=#91 id=91 data-nosnippet>91</a>
<a href=#92 id=92 data-nosnippet>92</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(master_node_url) = env::var(<span class="string">"MASTER_NODE_URL"</span>) {
<a href=#93 id=93 data-nosnippet>93</a>            config.master_node_url = master_node_url;
<a href=#94 id=94 data-nosnippet>94</a>        }
<a href=#95 id=95 data-nosnippet>95</a>
<a href=#96 id=96 data-nosnippet>96</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(heartbeat_interval) = env::var(<span class="string">"HEARTBEAT_INTERVAL"</span>) {
<a href=#97 id=97 data-nosnippet>97</a>            config.heartbeat_interval = heartbeat_interval.parse().unwrap_or(<span class="number">30</span>);
<a href=#98 id=98 data-nosnippet>98</a>        }
<a href=#99 id=99 data-nosnippet>99</a>
<a href=#100 id=100 data-nosnippet>100</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(max_concurrent_tasks) = env::var(<span class="string">"MAX_CONCURRENT_TASKS"</span>) {
<a href=#101 id=101 data-nosnippet>101</a>            config.max_concurrent_tasks = max_concurrent_tasks.parse().unwrap_or(<span class="number">100</span>);
<a href=#102 id=102 data-nosnippet>102</a>        }
<a href=#103 id=103 data-nosnippet>103</a>
<a href=#104 id=104 data-nosnippet>104</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(request_timeout) = env::var(<span class="string">"REQUEST_TIMEOUT"</span>) {
<a href=#105 id=105 data-nosnippet>105</a>            config.request_timeout = request_timeout.parse().unwrap_or(<span class="number">30</span>);
<a href=#106 id=106 data-nosnippet>106</a>        }
<a href=#107 id=107 data-nosnippet>107</a>
<a href=#108 id=108 data-nosnippet>108</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(retry_max_attempts) = env::var(<span class="string">"RETRY_MAX_ATTEMPTS"</span>) {
<a href=#109 id=109 data-nosnippet>109</a>            config.retry_max_attempts = retry_max_attempts.parse().unwrap_or(<span class="number">3</span>);
<a href=#110 id=110 data-nosnippet>110</a>        }
<a href=#111 id=111 data-nosnippet>111</a>
<a href=#112 id=112 data-nosnippet>112</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(proxy_pool_size) = env::var(<span class="string">"PROXY_POOL_SIZE"</span>) {
<a href=#113 id=113 data-nosnippet>113</a>            config.proxy_pool_size = proxy_pool_size.parse().unwrap_or(<span class="number">50</span>);
<a href=#114 id=114 data-nosnippet>114</a>        }
<a href=#115 id=115 data-nosnippet>115</a>
<a href=#116 id=116 data-nosnippet>116</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(proxy_health_check_interval) = env::var(<span class="string">"PROXY_HEALTH_CHECK_INTERVAL"</span>) {
<a href=#117 id=117 data-nosnippet>117</a>            config.proxy_health_check_interval = proxy_health_check_interval.parse().unwrap_or(<span class="number">300</span>);
<a href=#118 id=118 data-nosnippet>118</a>        }
<a href=#119 id=119 data-nosnippet>119</a>
<a href=#120 id=120 data-nosnippet>120</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(account_pool_size) = env::var(<span class="string">"ACCOUNT_POOL_SIZE"</span>) {
<a href=#121 id=121 data-nosnippet>121</a>            config.account_pool_size = account_pool_size.parse().unwrap_or(<span class="number">20</span>);
<a href=#122 id=122 data-nosnippet>122</a>        }
<a href=#123 id=123 data-nosnippet>123</a>
<a href=#124 id=124 data-nosnippet>124</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(account_rotation_interval) = env::var(<span class="string">"ACCOUNT_ROTATION_INTERVAL"</span>) {
<a href=#125 id=125 data-nosnippet>125</a>            config.account_rotation_interval = account_rotation_interval.parse().unwrap_or(<span class="number">3600</span>);
<a href=#126 id=126 data-nosnippet>126</a>        }
<a href=#127 id=127 data-nosnippet>127</a>
<a href=#128 id=128 data-nosnippet>128</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(log_level) = env::var(<span class="string">"LOG_LEVEL"</span>) {
<a href=#129 id=129 data-nosnippet>129</a>            config.log_level = log_level;
<a href=#130 id=130 data-nosnippet>130</a>        }
<a href=#131 id=131 data-nosnippet>131</a>
<a href=#132 id=132 data-nosnippet>132</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(log_file) = env::var(<span class="string">"LOG_FILE"</span>) {
<a href=#133 id=133 data-nosnippet>133</a>            config.log_file = log_file;
<a href=#134 id=134 data-nosnippet>134</a>        }
<a href=#135 id=135 data-nosnippet>135</a>
<a href=#136 id=136 data-nosnippet>136</a>        <span class="prelude-val">Ok</span>(config)
<a href=#137 id=137 data-nosnippet>137</a>    }
<a href=#138 id=138 data-nosnippet>138</a>
<a href=#139 id=139 data-nosnippet>139</a>    <span class="kw">pub async fn </span>save(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#140 id=140 data-nosnippet>140</a>        <span class="comment">// 这里可以实现配置保存到文件的逻辑
<a href=#141 id=141 data-nosnippet>141</a>        // 目前先返回Ok
<a href=#142 id=142 data-nosnippet>142</a>        </span><span class="prelude-val">Ok</span>(())
<a href=#143 id=143 data-nosnippet>143</a>    }
<a href=#144 id=144 data-nosnippet>144</a>
<a href=#145 id=145 data-nosnippet>145</a>    <span class="kw">pub fn </span>validate(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#146 id=146 data-nosnippet>146</a>        <span class="kw">if </span><span class="self">self</span>.node_id.is_empty() {
<a href=#147 id=147 data-nosnippet>147</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Config(<span class="string">"节点ID不能为空"</span>.to_string()));
<a href=#148 id=148 data-nosnippet>148</a>        }
<a href=#149 id=149 data-nosnippet>149</a>
<a href=#150 id=150 data-nosnippet>150</a>        <span class="kw">if </span><span class="self">self</span>.node_name.is_empty() {
<a href=#151 id=151 data-nosnippet>151</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Config(<span class="string">"节点名称不能为空"</span>.to_string()));
<a href=#152 id=152 data-nosnippet>152</a>        }
<a href=#153 id=153 data-nosnippet>153</a>
<a href=#154 id=154 data-nosnippet>154</a>        <span class="kw">if </span><span class="self">self</span>.max_concurrent_tasks == <span class="number">0 </span>{
<a href=#155 id=155 data-nosnippet>155</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Config(<span class="string">"最大并发任务数必须大于0"</span>.to_string()));
<a href=#156 id=156 data-nosnippet>156</a>        }
<a href=#157 id=157 data-nosnippet>157</a>
<a href=#158 id=158 data-nosnippet>158</a>        <span class="kw">if </span><span class="self">self</span>.proxy_pool_size == <span class="number">0 </span>{
<a href=#159 id=159 data-nosnippet>159</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Config(<span class="string">"代理池大小必须大于0"</span>.to_string()));
<a href=#160 id=160 data-nosnippet>160</a>        }
<a href=#161 id=161 data-nosnippet>161</a>
<a href=#162 id=162 data-nosnippet>162</a>        <span class="kw">if </span><span class="self">self</span>.account_pool_size == <span class="number">0 </span>{
<a href=#163 id=163 data-nosnippet>163</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Config(<span class="string">"账号池大小必须大于0"</span>.to_string()));
<a href=#164 id=164 data-nosnippet>164</a>        }
<a href=#165 id=165 data-nosnippet>165</a>
<a href=#166 id=166 data-nosnippet>166</a>        <span class="prelude-val">Ok</span>(())
<a href=#167 id=167 data-nosnippet>167</a>    }
<a href=#168 id=168 data-nosnippet>168</a>}</code></pre></div></section></main></body></html>