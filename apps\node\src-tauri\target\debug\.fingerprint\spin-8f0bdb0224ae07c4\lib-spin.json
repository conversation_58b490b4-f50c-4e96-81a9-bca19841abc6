{"rustc": 1842507548689473721, "features": "[\"barrier\", \"default\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"rwlock\", \"spin_mutex\"]", "declared_features": "[\"barrier\", \"default\", \"fair_mutex\", \"lazy\", \"lock_api\", \"lock_api_crate\", \"mutex\", \"once\", \"portable-atomic\", \"portable_atomic\", \"rwlock\", \"spin_mutex\", \"std\", \"ticket_mutex\", \"use_ticket_mutex\"]", "target": 4260413527236709406, "profile": 2225463790103693989, "path": 17706335662159069642, "deps": [[8081351675046095464, "lock_api_crate", false, 4354837006951398139]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\spin-8f0bdb0224ae07c4\\dep-lib-spin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}