<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `TaskService` struct in crate `weibo_crawler_node`."><title>TaskService in weibo_crawler_node::service::task_service - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc struct"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Task<wbr>Service</a></h2><h3><a href="#fields">Fields</a></h3><ul class="block structfield"><li><a href="#structfield.failed_count" title="failed_count">failed_count</a></li><li><a href="#structfield.is_running" title="is_running">is_running</a></li><li><a href="#structfield.processed_count" title="processed_count">processed_count</a></li><li><a href="#structfield.repository_manager" title="repository_manager">repository_manager</a></li><li><a href="#structfield.start_time" title="start_time">start_time</a></li></ul><h3><a href="#implementations">Methods</a></h3><ul class="block method"><li><a href="#method.cleanup_old_tasks" title="cleanup_old_tasks">cleanup_old_tasks</a></li><li><a href="#method.complete_task" title="complete_task">complete_task</a></li><li><a href="#method.create_task" title="create_task">create_task</a></li><li><a href="#method.create_tasks_batch" title="create_tasks_batch">create_tasks_batch</a></li><li><a href="#method.delete_task" title="delete_task">delete_task</a></li><li><a href="#method.fail_task" title="fail_task">fail_task</a></li><li><a href="#method.get_pending_tasks" title="get_pending_tasks">get_pending_tasks</a></li><li><a href="#method.get_task" title="get_task">get_task</a></li><li><a href="#method.get_task_statistics" title="get_task_statistics">get_task_statistics</a></li><li><a href="#method.list_tasks" title="list_tasks">list_tasks</a></li><li><a href="#method.new" title="new">new</a></li><li><a href="#method.query_tasks" title="query_tasks">query_tasks</a></li><li><a href="#method.reset_task" title="reset_task">reset_task</a></li><li><a href="#method.start_task" title="start_task">start_task</a></li><li><a href="#method.update_task" title="update_task">update_task</a></li></ul><h3><a href="#trait-implementations">Trait Implementations</a></h3><ul class="block trait-implementation"><li><a href="#impl-MonitorableService-for-TaskService" title="MonitorableService">MonitorableService</a></li><li><a href="#impl-Service-for-TaskService" title="Service">Service</a></li><li><a href="#impl-StartableService-for-TaskService" title="StartableService">StartableService</a></li></ul><h3><a href="#synthetic-implementations">Auto Trait Implementations</a></h3><ul class="block synthetic-implementation"><li><a href="#impl-Freeze-for-TaskService" title="!Freeze">!Freeze</a></li><li><a href="#impl-RefUnwindSafe-for-TaskService" title="!RefUnwindSafe">!RefUnwindSafe</a></li><li><a href="#impl-UnwindSafe-for-TaskService" title="!UnwindSafe">!UnwindSafe</a></li><li><a href="#impl-Send-for-TaskService" title="Send">Send</a></li><li><a href="#impl-Sync-for-TaskService" title="Sync">Sync</a></li><li><a href="#impl-Unpin-for-TaskService" title="Unpin">Unpin</a></li></ul><h3><a href="#blanket-implementations">Blanket Implementations</a></h3><ul class="block blanket-implementation"><li><a href="#impl-Any-for-T" title="Any">Any</a></li><li><a href="#impl-Borrow%3CT%3E-for-T" title="Borrow&#60;T&#62;">Borrow&#60;T&#62;</a></li><li><a href="#impl-BorrowMut%3CT%3E-for-T" title="BorrowMut&#60;T&#62;">BorrowMut&#60;T&#62;</a></li><li><a href="#impl-ErasedDestructor-for-T" title="ErasedDestructor">ErasedDestructor</a></li><li><a href="#impl-From%3CT%3E-for-T" title="From&#60;T&#62;">From&#60;T&#62;</a></li><li><a href="#impl-Instrument-for-T" title="Instrument">Instrument</a></li><li><a href="#impl-Into%3CU%3E-for-T" title="Into&#60;U&#62;">Into&#60;U&#62;</a></li><li><a href="#impl-IntoEither-for-T" title="IntoEither">IntoEither</a></li><li><a href="#impl-Pointable-for-T" title="Pointable">Pointable</a></li><li><a href="#impl-Same-for-T" title="Same">Same</a></li><li><a href="#impl-TryFrom%3CU%3E-for-T" title="TryFrom&#60;U&#62;">TryFrom&#60;U&#62;</a></li><li><a href="#impl-TryInto%3CU%3E-for-T" title="TryInto&#60;U&#62;">TryInto&#60;U&#62;</a></li><li><a href="#impl-VZip%3CV%3E-for-T" title="VZip&#60;V&#62;">VZip&#60;V&#62;</a></li><li><a href="#impl-WithSubscriber-for-T" title="WithSubscriber">WithSubscriber</a></li></ul></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>service::<wbr>task_<wbr>service</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../../index.html">weibo_crawler_node</a>::<wbr><a href="../index.html">service</a>::<wbr><a href="index.html">task_service</a></div><h1>Struct <span class="struct">TaskService</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../../src/weibo_crawler_node/service/task_service.rs.html#52-58">Source</a> </span></div><pre class="rust item-decl"><code>pub struct TaskService {
    repository_manager: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;<a class="struct" href="../../repository/struct.RepositoryManager.html" title="struct weibo_crawler_node::repository::RepositoryManager">RepositoryManager</a>&gt;,
    is_running: <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/sync/atomic/struct.AtomicBool.html" title="struct core::sync::atomic::AtomicBool">AtomicBool</a>,
    processed_count: <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/sync/atomic/struct.AtomicU64.html" title="struct core::sync::atomic::AtomicU64">AtomicU64</a>,
    failed_count: <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/sync/atomic/struct.AtomicU64.html" title="struct core::sync::atomic::AtomicU64">AtomicU64</a>,
    start_time: RwLock&lt;<a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="struct" href="https://docs.rs/chrono/latest/chrono/datetime/struct.DateTime.html" title="struct chrono::datetime::DateTime">DateTime</a>&lt;<a class="struct" href="https://docs.rs/chrono/latest/chrono/offset/utc/struct.Utc.html" title="struct chrono::offset::utc::Utc">Utc</a>&gt;&gt;&gt;,
}</code></pre><h2 id="fields" class="fields section-header">Fields<a href="#fields" class="anchor">§</a></h2><span id="structfield.repository_manager" class="structfield section-header"><a href="#structfield.repository_manager" class="anchor field">§</a><code>repository_manager: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;<a class="struct" href="../../repository/struct.RepositoryManager.html" title="struct weibo_crawler_node::repository::RepositoryManager">RepositoryManager</a>&gt;</code></span><span id="structfield.is_running" class="structfield section-header"><a href="#structfield.is_running" class="anchor field">§</a><code>is_running: <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/sync/atomic/struct.AtomicBool.html" title="struct core::sync::atomic::AtomicBool">AtomicBool</a></code></span><span id="structfield.processed_count" class="structfield section-header"><a href="#structfield.processed_count" class="anchor field">§</a><code>processed_count: <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/sync/atomic/struct.AtomicU64.html" title="struct core::sync::atomic::AtomicU64">AtomicU64</a></code></span><span id="structfield.failed_count" class="structfield section-header"><a href="#structfield.failed_count" class="anchor field">§</a><code>failed_count: <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/sync/atomic/struct.AtomicU64.html" title="struct core::sync::atomic::AtomicU64">AtomicU64</a></code></span><span id="structfield.start_time" class="structfield section-header"><a href="#structfield.start_time" class="anchor field">§</a><code>start_time: RwLock&lt;<a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="struct" href="https://docs.rs/chrono/latest/chrono/datetime/struct.DateTime.html" title="struct chrono::datetime::DateTime">DateTime</a>&lt;<a class="struct" href="https://docs.rs/chrono/latest/chrono/offset/utc/struct.Utc.html" title="struct chrono::offset::utc::Utc">Utc</a>&gt;&gt;&gt;</code></span><h2 id="implementations" class="section-header">Implementations<a href="#implementations" class="anchor">§</a></h2><div id="implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-TaskService" class="impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#60-250">Source</a><a href="#impl-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section></summary><div class="impl-items"><section id="method.new" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#61-69">Source</a><h4 class="code-header">pub fn <a href="#method.new" class="fn">new</a>(repository_manager: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;<a class="struct" href="../../repository/struct.RepositoryManager.html" title="struct weibo_crawler_node::repository::RepositoryManager">RepositoryManager</a>&gt;) -&gt; Self</h4></section><details class="toggle method-toggle" open><summary><section id="method.create_task" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#72-97">Source</a><h4 class="code-header">pub async fn <a href="#method.create_task" class="fn">create_task</a>(
    &amp;self,
    request: <a class="struct" href="struct.CreateTaskRequest.html" title="struct weibo_crawler_node::service::task_service::CreateTaskRequest">CreateTaskRequest</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="../../storage/struct.TaskRecord.html" title="struct weibo_crawler_node::storage::TaskRecord">TaskRecord</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>创建新任务</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.create_tasks_batch" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#100-114">Source</a><h4 class="code-header">pub async fn <a href="#method.create_tasks_batch" class="fn">create_tasks_batch</a>(
    &amp;self,
    requests: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="struct.CreateTaskRequest.html" title="struct weibo_crawler_node::service::task_service::CreateTaskRequest">CreateTaskRequest</a>&gt;,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="../../storage/struct.TaskRecord.html" title="struct weibo_crawler_node::storage::TaskRecord">TaskRecord</a>&gt;, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>批量创建任务</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.get_task" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#117-122">Source</a><h4 class="code-header">pub async fn <a href="#method.get_task" class="fn">get_task</a>(
    &amp;self,
    task_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="struct" href="../../storage/struct.TaskRecord.html" title="struct weibo_crawler_node::storage::TaskRecord">TaskRecord</a>&gt;, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>获取任务详情</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.update_task" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#125-140">Source</a><h4 class="code-header">pub async fn <a href="#method.update_task" class="fn">update_task</a>(
    &amp;self,
    task_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
    request: <a class="struct" href="struct.UpdateTaskRequest.html" title="struct weibo_crawler_node::service::task_service::UpdateTaskRequest">UpdateTaskRequest</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="../../storage/struct.TaskRecord.html" title="struct weibo_crawler_node::storage::TaskRecord">TaskRecord</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>更新任务</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.delete_task" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#143-151">Source</a><h4 class="code-header">pub async fn <a href="#method.delete_task" class="fn">delete_task</a>(&amp;self, task_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>删除任务</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.list_tasks" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#154-156">Source</a><h4 class="code-header">pub async fn <a href="#method.list_tasks" class="fn">list_tasks</a>(
    &amp;self,
    page: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>,
    limit: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="../../repository/struct.PaginatedResult.html" title="struct weibo_crawler_node::repository::PaginatedResult">PaginatedResult</a>&lt;<a class="struct" href="../../storage/struct.TaskRecord.html" title="struct weibo_crawler_node::storage::TaskRecord">TaskRecord</a>&gt;, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>获取任务列表（分页）</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.query_tasks" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#159-169">Source</a><h4 class="code-header">pub async fn <a href="#method.query_tasks" class="fn">query_tasks</a>(
    &amp;self,
    query: <a class="struct" href="struct.TaskQuery.html" title="struct weibo_crawler_node::service::task_service::TaskQuery">TaskQuery</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="../../storage/struct.TaskRecord.html" title="struct weibo_crawler_node::storage::TaskRecord">TaskRecord</a>&gt;, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>根据条件查询任务</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.get_pending_tasks" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#172-174">Source</a><h4 class="code-header">pub async fn <a href="#method.get_pending_tasks" class="fn">get_pending_tasks</a>(
    &amp;self,
    limit: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="../../storage/struct.TaskRecord.html" title="struct weibo_crawler_node::storage::TaskRecord">TaskRecord</a>&gt;, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>获取待处理任务</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.start_task" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#177-181">Source</a><h4 class="code-header">pub async fn <a href="#method.start_task" class="fn">start_task</a>(&amp;self, task_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>开始处理任务</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.complete_task" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#184-189">Source</a><h4 class="code-header">pub async fn <a href="#method.complete_task" class="fn">complete_task</a>(&amp;self, task_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>完成任务</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.fail_task" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#192-197">Source</a><h4 class="code-header">pub async fn <a href="#method.fail_task" class="fn">fail_task</a>(&amp;self, task_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>任务失败</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.reset_task" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#200-204">Source</a><h4 class="code-header">pub async fn <a href="#method.reset_task" class="fn">reset_task</a>(&amp;self, task_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>重置任务状态</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.get_task_statistics" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#207-242">Source</a><h4 class="code-header">pub async fn <a href="#method.get_task_statistics" class="fn">get_task_statistics</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="struct.TaskMetrics.html" title="struct weibo_crawler_node::service::task_service::TaskMetrics">TaskMetrics</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>获取任务统计信息</p>
</div></details><details class="toggle method-toggle" open><summary><section id="method.cleanup_old_tasks" class="method"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#245-249">Source</a><h4 class="code-header">pub async fn <a href="#method.cleanup_old_tasks" class="fn">cleanup_old_tasks</a>(&amp;self, days: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.i32.html">i32</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u64.html">u64</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></summary><div class="docblock"><p>清理旧任务</p>
</div></details></div></details></div><h2 id="trait-implementations" class="section-header">Trait Implementations<a href="#trait-implementations" class="anchor">§</a></h2><div id="trait-implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-MonitorableService-for-TaskService" class="impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#290-320">Source</a><a href="#impl-MonitorableService-for-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="../trait.MonitorableService.html" title="trait weibo_crawler_node::service::MonitorableService">MonitorableService</a> for <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section></summary><div class="impl-items"><section id="associatedtype.Metrics" class="associatedtype trait-impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#291">Source</a><a href="#associatedtype.Metrics" class="anchor">§</a><h4 class="code-header">type <a href="../trait.MonitorableService.html#associatedtype.Metrics" class="associatedtype">Metrics</a> = <a class="struct" href="struct.TaskMetrics.html" title="struct weibo_crawler_node::service::task_service::TaskMetrics">TaskMetrics</a></h4></section><section id="method.get_metrics" class="method trait-impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#293-295">Source</a><a href="#method.get_metrics" class="anchor">§</a><h4 class="code-header">fn <a href="../trait.MonitorableService.html#tymethod.get_metrics" class="fn">get_metrics</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self::<a class="associatedtype" href="../trait.MonitorableService.html#associatedtype.Metrics" title="type weibo_crawler_node::service::MonitorableService::Metrics">Metrics</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="method.get_health_status" class="method trait-impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#297-319">Source</a><a href="#method.get_health_status" class="anchor">§</a><h4 class="code-header">fn <a href="../trait.MonitorableService.html#tymethod.get_health_status" class="fn">get_health_status</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="enum" href="../enum.HealthStatus.html" title="enum weibo_crawler_node::service::HealthStatus">HealthStatus</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-Service-for-TaskService" class="impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#252-256">Source</a><a href="#impl-Service-for-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="../trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a> for <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section></summary><div class="impl-items"><section id="method.name" class="method trait-impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#253-255">Source</a><a href="#method.name" class="anchor">§</a><h4 class="code-header">fn <a href="../trait.Service.html#tymethod.name" class="fn">name</a>(&amp;self) -&gt; &amp;'static <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a></h4></section></div></details><details class="toggle implementors-toggle" open><summary><section id="impl-StartableService-for-TaskService" class="impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#259-287">Source</a><a href="#impl-StartableService-for-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="../trait.StartableService.html" title="trait weibo_crawler_node::service::StartableService">StartableService</a> for <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section></summary><div class="impl-items"><section id="method.start" class="method trait-impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#260-270">Source</a><a href="#method.start" class="anchor">§</a><h4 class="code-header">fn <a href="../trait.StartableService.html#tymethod.start" class="fn">start</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="method.stop" class="method trait-impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#272-282">Source</a><a href="#method.stop" class="anchor">§</a><h4 class="code-header">fn <a href="../trait.StartableService.html#tymethod.stop" class="fn">stop</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="method.is_running" class="method trait-impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/task_service.rs.html#284-286">Source</a><a href="#method.is_running" class="anchor">§</a><h4 class="code-header">fn <a href="../trait.StartableService.html#tymethod.is_running" class="fn">is_running</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a></h4></section><section id="method.restart" class="method trait-impl"><a class="src rightside" href="../../../src/weibo_crawler_node/service/mod.rs.html#48-51">Source</a><a href="#method.restart" class="anchor">§</a><h4 class="code-header">fn <a href="../trait.StartableService.html#method.restart" class="fn">restart</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> + 'async_trait,
    'life0: 'async_trait,</div></h4></section></div></details></div><h2 id="synthetic-implementations" class="section-header">Auto Trait Implementations<a href="#synthetic-implementations" class="anchor">§</a></h2><div id="synthetic-implementations-list"><section id="impl-Freeze-for-TaskService" class="impl"><a href="#impl-Freeze-for-TaskService" class="anchor">§</a><h3 class="code-header">impl !<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Freeze.html" title="trait core::marker::Freeze">Freeze</a> for <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section><section id="impl-RefUnwindSafe-for-TaskService" class="impl"><a href="#impl-RefUnwindSafe-for-TaskService" class="anchor">§</a><h3 class="code-header">impl !<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/panic/unwind_safe/trait.RefUnwindSafe.html" title="trait core::panic::unwind_safe::RefUnwindSafe">RefUnwindSafe</a> for <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section><section id="impl-Send-for-TaskService" class="impl"><a href="#impl-Send-for-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> for <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section><section id="impl-Sync-for-TaskService" class="impl"><a href="#impl-Sync-for-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> for <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section><section id="impl-Unpin-for-TaskService" class="impl"><a href="#impl-Unpin-for-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Unpin.html" title="trait core::marker::Unpin">Unpin</a> for <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section><section id="impl-UnwindSafe-for-TaskService" class="impl"><a href="#impl-UnwindSafe-for-TaskService" class="anchor">§</a><h3 class="code-header">impl !<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/panic/unwind_safe/trait.UnwindSafe.html" title="trait core::panic::unwind_safe::UnwindSafe">UnwindSafe</a> for <a class="struct" href="struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section></div><h2 id="blanket-implementations" class="section-header">Blanket Implementations<a href="#blanket-implementations" class="anchor">§</a></h2><div id="blanket-implementations-list"><details class="toggle implementors-toggle"><summary><section id="impl-Any-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/any.rs.html#138">Source</a><a href="#impl-Any-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/any/trait.Any.html" title="trait core::any::Any">Any</a> for T<div class="where">where
    T: 'static + ?<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.type_id" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/any.rs.html#139">Source</a><a href="#method.type_id" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/any/trait.Any.html#tymethod.type_id" class="fn">type_id</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/any/struct.TypeId.html" title="struct core::any::TypeId">TypeId</a></h4></section></summary><div class='docblock'>Gets the <code>TypeId</code> of <code>self</code>. <a href="https://doc.rust-lang.org/1.88.0/core/any/trait.Any.html#tymethod.type_id">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Borrow%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#209">Source</a><a href="#impl-Borrow%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.Borrow.html" title="trait core::borrow::Borrow">Borrow</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#211">Source</a><a href="#method.borrow" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.Borrow.html#tymethod.borrow" class="fn">borrow</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;T</a></h4></section></summary><div class='docblock'>Immutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.Borrow.html#tymethod.borrow">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-BorrowMut%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#217">Source</a><a href="#impl-BorrowMut%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.BorrowMut.html" title="trait core::borrow::BorrowMut">BorrowMut</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow_mut" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#218">Source</a><a href="#method.borrow_mut" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut" class="fn">borrow_mut</a>(&amp;mut self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;mut T</a></h4></section></summary><div class='docblock'>Mutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-From%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#767">Source</a><a href="#impl-From%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#770">Source</a><a href="#method.from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html#tymethod.from" class="fn">from</a>(t: T) -&gt; T</h4></section></summary><div class="docblock"><p>Returns the argument unchanged.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Instrument-for-T" class="impl"><a href="#impl-Instrument-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; Instrument for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.instrument" class="method trait-impl"><a href="#method.instrument" class="anchor">§</a><h4 class="code-header">fn <a class="fn">instrument</a>(self, span: Span) -&gt; Instrumented&lt;Self&gt;</h4></section></summary><div class='docblock'>Instruments this type with the provided [<code>Span</code>], returning an
<code>Instrumented</code> wrapper. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.in_current_span" class="method trait-impl"><a href="#method.in_current_span" class="anchor">§</a><h4 class="code-header">fn <a class="fn">in_current_span</a>(self) -&gt; Instrumented&lt;Self&gt;</h4></section></summary><div class='docblock'>Instruments this type with the <a href="super::Span::current()">current</a> <a href="crate::Span"><code>Span</code></a>, returning an
<code>Instrumented</code> wrapper. <a>Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Into%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#750-752">Source</a><a href="#impl-Into%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#760">Source</a><a href="#method.into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html#tymethod.into" class="fn">into</a>(self) -&gt; U</h4></section></summary><div class="docblock"><p>Calls <code>U::from(self)</code>.</p>
<p>That is, this conversion is whatever the implementation of
<code><a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for U</code> chooses to do.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-IntoEither-for-T" class="impl"><a class="src rightside" href="https://docs.rs/either/1/src/either/into_either.rs.html#64">Source</a><a href="#impl-IntoEither-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html" title="trait either::into_either::IntoEither">IntoEither</a> for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into_either" class="method trait-impl"><a class="src rightside" href="https://docs.rs/either/1/src/either/into_either.rs.html#29">Source</a><a href="#method.into_either" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either" class="fn">into_either</a>(self, into_left: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a>) -&gt; <a class="enum" href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either">Either</a>&lt;Self, Self&gt;</h4></section></summary><div class='docblock'>Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Left" title="variant either::Either::Left"><code>Left</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
if <code>into_left</code> is <code>true</code>.
Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Right" title="variant either::Either::Right"><code>Right</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
otherwise. <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.into_either_with" class="method trait-impl"><a class="src rightside" href="https://docs.rs/either/1/src/either/into_either.rs.html#55-57">Source</a><a href="#method.into_either_with" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either_with" class="fn">into_either_with</a>&lt;F&gt;(self, into_left: F) -&gt; <a class="enum" href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either">Either</a>&lt;Self, Self&gt;<div class="where">where
    F: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html" title="trait core::ops::function::FnOnce">FnOnce</a>(&amp;Self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a>,</div></h4></section></summary><div class='docblock'>Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Left" title="variant either::Either::Left"><code>Left</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
if <code>into_left(&amp;self)</code> returns <code>true</code>.
Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Right" title="variant either::Either::Right"><code>Right</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
otherwise. <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either_with">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Pointable-for-T" class="impl"><a href="#impl-Pointable-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; Pointable for T</h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedconstant.ALIGN" class="associatedconstant trait-impl"><a href="#associatedconstant.ALIGN" class="anchor">§</a><h4 class="code-header">const <a class="constant">ALIGN</a>: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a></h4></section></summary><div class='docblock'>The alignment of pointer.</div></details><details class="toggle" open><summary><section id="associatedtype.Init" class="associatedtype trait-impl"><a href="#associatedtype.Init" class="anchor">§</a><h4 class="code-header">type <a class="associatedtype">Init</a> = T</h4></section></summary><div class='docblock'>The type for initializers.</div></details><details class="toggle method-toggle" open><summary><section id="method.init" class="method trait-impl"><a href="#method.init" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">init</a>(init: &lt;T as Pointable&gt;::Init) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a></h4></section></summary><div class='docblock'>Initializes a with the given initializer. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.deref" class="method trait-impl"><a href="#method.deref" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">deref</a>&lt;'a&gt;(ptr: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;'a T</a></h4></section></summary><div class='docblock'>Dereferences the given pointer. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.deref_mut" class="method trait-impl"><a href="#method.deref_mut" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">deref_mut</a>&lt;'a&gt;(ptr: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;'a mut T</a></h4></section></summary><div class='docblock'>Mutably dereferences the given pointer. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.drop" class="method trait-impl"><a href="#method.drop" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">drop</a>(ptr: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>)</h4></section></summary><div class='docblock'>Drops the object pointed to by the given pointer. <a>Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Same-for-T" class="impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#34">Source</a><a href="#impl-Same-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html" title="trait typenum::type_operators::Same">Same</a> for T</h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Output" class="associatedtype trait-impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#35">Source</a><a href="#associatedtype.Output" class="anchor">§</a><h4 class="code-header">type <a href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html#associatedtype.Output" class="associatedtype">Output</a> = T</h4></section></summary><div class='docblock'>Should always be <code>Self</code></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryFrom%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#806-808">Source</a><a href="#impl-TryFrom%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error-1" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#810">Source</a><a href="#associatedtype.Error-1" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" class="associatedtype">Error</a> = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/convert/enum.Infallible.html" title="enum core::convert::Infallible">Infallible</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#813">Source</a><a href="#method.try_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#tymethod.try_from" class="fn">try_from</a>(value: U) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, &lt;T as <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryInto%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#791-793">Source</a><a href="#impl-TryInto%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryInto.html" title="trait core::convert::TryInto">TryInto</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#795">Source</a><a href="#associatedtype.Error" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryInto.html#associatedtype.Error" class="associatedtype">Error</a> = &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#798">Source</a><a href="#method.try_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryInto.html#tymethod.try_into" class="fn">try_into</a>(self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;U, &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-VZip%3CV%3E-for-T" class="impl"><a href="#impl-VZip%3CV%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;V, T&gt; VZip&lt;V&gt; for T<div class="where">where
    V: MultiLane&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><section id="method.vzip" class="method trait-impl"><a href="#method.vzip" class="anchor">§</a><h4 class="code-header">fn <a class="fn">vzip</a>(self) -&gt; V</h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-WithSubscriber-for-T" class="impl"><a href="#impl-WithSubscriber-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; WithSubscriber for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.with_subscriber" class="method trait-impl"><a href="#method.with_subscriber" class="anchor">§</a><h4 class="code-header">fn <a class="fn">with_subscriber</a>&lt;S&gt;(self, subscriber: S) -&gt; WithDispatch&lt;Self&gt;<div class="where">where
    S: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;Dispatch&gt;,</div></h4></section></summary><div class='docblock'>Attaches the provided <a href="super::Subscriber"><code>Subscriber</code></a> to this type, returning a
[<code>WithDispatch</code>] wrapper. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.with_current_subscriber" class="method trait-impl"><a href="#method.with_current_subscriber" class="anchor">§</a><h4 class="code-header">fn <a class="fn">with_current_subscriber</a>(self) -&gt; WithDispatch&lt;Self&gt;</h4></section></summary><div class='docblock'>Attaches the current <a href="crate::dispatcher#setting-the-default-subscriber">default</a> <a href="super::Subscriber"><code>Subscriber</code></a> to this type, returning a
[<code>WithDispatch</code>] wrapper. <a>Read more</a></div></details></div></details><section id="impl-ErasedDestructor-for-T" class="impl"><a href="#impl-ErasedDestructor-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; ErasedDestructor for T<div class="where">where
    T: 'static,</div></h3></section></div></section></div></main></body></html>