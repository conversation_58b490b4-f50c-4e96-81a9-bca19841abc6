{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"macros\", \"std\"]", "declared_features": "[\"alloc\", \"base64\", \"chrono\", \"chrono_0_4\", \"default\", \"guide\", \"hashbrown_0_14\", \"hashbrown_0_15\", \"hex\", \"indexmap\", \"indexmap_1\", \"indexmap_2\", \"json\", \"macros\", \"schemars_0_8\", \"schemars_0_9\", \"schemars_1\", \"std\", \"time_0_3\"]", "target": 10448421281463538527, "profile": 6834063317110192372, "path": 7886800299839333671, "deps": [[7026957619838884710, "serde_with_macros", false, 15145717770606680787], [9689903380558560274, "serde", false, 7307778708627749338], [16257276029081467297, "serde_derive", false, 1825622199956065576]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\serde_with-5f749c2564aed4e8\\dep-lib-serde_with", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}