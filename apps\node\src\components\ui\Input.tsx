import React from 'react';
import { cn } from '../../lib/utils';

// Input变体类型定义
export type InputVariant = 'default' | 'filled' | 'ghost';
export type InputSize = 'default' | 'sm' | 'lg';
export type InputState = 'default' | 'error' | 'success' | 'warning';

// Input变体样式函数
const getInputVariantStyles = (variant: InputVariant = 'default') => {
  const variants = {
    default: "hover:border-ring/50",
    filled: "bg-muted border-transparent hover:bg-muted/80 focus-visible:bg-background",
    ghost: "border-transparent hover:border-input focus-visible:border-ring",
  };
  return variants[variant];
};

const getInputSizeStyles = (size: InputSize = 'default') => {
  const sizes = {
    default: "h-10 px-3 py-2",
    sm: "h-9 px-3 text-xs",
    lg: "h-12 px-4 text-base",
  };
  return sizes[size];
};

const getInputStateStyles = (state: InputState = 'default') => {
  const states = {
    default: "",
    error: "border-destructive focus-visible:ring-destructive",
    success: "border-success focus-visible:ring-success",
    warning: "border-warning focus-visible:ring-warning",
  };
  return states[state];
};

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: InputVariant;
  size?: InputSize;
  state?: InputState;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  error?: string;
  success?: string;
  helperText?: string;
  label?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    variant = 'default',
    size = 'default',
    state = 'default',
    type,
    leftIcon,
    rightIcon,
    error,
    success,
    helperText,
    label,
    id,
    ...props
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const finalState = error ? 'error' : success ? 'success' : state;

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={inputId}
            className="block text-sm font-medium text-foreground mb-2"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {leftIcon}
            </div>
          )}
          
          <input
            type={type}
            id={inputId}
            className={cn(
              "flex w-full rounded-lg border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200",
              getInputVariantStyles(variant),
              getInputSizeStyles(size),
              getInputStateStyles(finalState),
              leftIcon && "pl-10",
              rightIcon && "pr-10",
              className
            )}
            ref={ref}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {rightIcon}
            </div>
          )}
        </div>
        
        {(error || success || helperText) && (
          <div className="mt-2 text-sm">
            {error && (
              <p className="text-destructive flex items-center gap-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {error}
              </p>
            )}
            {success && (
              <p className="text-success flex items-center gap-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                {success}
              </p>
            )}
            {helperText && !error && !success && (
              <p className="text-muted-foreground">{helperText}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

// 搜索输入框组件
export interface SearchInputProps extends Omit<InputProps, 'leftIcon' | 'type'> {
  onSearch?: (value: string) => void;
  onClear?: () => void;
}

const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ onSearch, onClear, className, ...props }, ref) => {
    const [value, setValue] = React.useState(props.value || '');

    const handleSearch = () => {
      onSearch?.(value as string);
    };

    const handleClear = () => {
      setValue('');
      onClear?.();
    };

    return (
      <Input
        ref={ref}
        type="search"
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
        leftIcon={
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        }
        rightIcon={
          value && (
            <button
              type="button"
              onClick={handleClear}
              className="hover:text-foreground transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )
        }
        className={className}
        {...props}
      />
    );
  }
);

SearchInput.displayName = "SearchInput";

export { Input, SearchInput, getInputVariantStyles, getInputSizeStyles, getInputStateStyles };
