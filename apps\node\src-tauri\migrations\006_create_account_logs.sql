-- 账号操作日志表
CREATE TABLE account_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id TEXT NOT NULL,
    operation_type INTEGER NOT NULL, -- 1: <PERSON><PERSON>, 2: <PERSON>gout, 3: Task, 4: <PERSON><PERSON>, 5: Risk Check
    operation_result INTEGER NOT NULL, -- 1: Success, 2: Failed, 3: Warning
    operation_details TEXT, -- JSON格式的操作详情
    ip_address TEXT,
    user_agent TEXT,
    proxy_used TEXT, -- 使用的代理ID
    error_message TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
);

-- 账号风险评估记录表
CREATE TABLE account_risk_assessments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id TEXT NOT NULL,
    risk_factors TEXT NOT NULL, -- JSON格式的风险因子
    risk_score REAL NOT NULL, -- 风险评分 0.0-1.0
    assessment_reason TEXT,
    recommended_action INTEGER, -- 1: Continue, 2: Pause, 3: Switch, 4: Ban
    assessed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
);

-- 账号使用统计表 (按小时聚合)
CREATE TABLE account_usage_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id TEXT NOT NULL,
    hour_timestamp DATETIME NOT NULL, -- 小时级时间戳
    total_tasks INTEGER DEFAULT 0,
    successful_tasks INTEGER DEFAULT 0,
    failed_tasks INTEGER DEFAULT 0,
    login_attempts INTEGER DEFAULT 0,
    successful_logins INTEGER DEFAULT 0,
    risk_incidents INTEGER DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id),
    UNIQUE(account_id, hour_timestamp)
);

-- 创建索引
CREATE INDEX idx_account_logs_account_id ON account_logs(account_id);
CREATE INDEX idx_account_logs_operation_type ON account_logs(operation_type);
CREATE INDEX idx_account_logs_created_at ON account_logs(created_at);
CREATE INDEX idx_account_risk_assessments_account_id ON account_risk_assessments(account_id);
CREATE INDEX idx_account_risk_assessments_risk_score ON account_risk_assessments(risk_score);
CREATE INDEX idx_account_risk_assessments_assessed_at ON account_risk_assessments(assessed_at);
CREATE INDEX idx_account_usage_stats_account_id ON account_usage_stats(account_id);
CREATE INDEX idx_account_usage_stats_hour_timestamp ON account_usage_stats(hour_timestamp);

-- 创建更新时间触发器
CREATE TRIGGER update_account_usage_stats_updated_at
    AFTER UPDATE ON account_usage_stats
    FOR EACH ROW
BEGIN
    UPDATE account_usage_stats SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
