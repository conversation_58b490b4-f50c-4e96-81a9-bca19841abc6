{"rustc": 1842507548689473721, "features": "[\"global-shortcut\", \"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 676420316161376534, "deps": [[4381063397040571828, "webview2_com", false, 1159489376968944555], [7653476968652377684, "windows", false, 5238405700565607631], [8292277814562636972, "tauri_utils", false, 17150142075726711639], [8319709847752024821, "uuid", false, 1140400842430028499], [8391357152270261188, "wry", false, 14006098027577611024], [11693073011723388840, "raw_window_handle", false, 7780770964976528412], [13208667028893622512, "rand", false, 6363632852697834881], [14162324460024849578, "tauri_runtime", false, 13527492245305971298], [16228250612241359704, "build_script_build", false, 10838618406746771457]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-5e21150ce496f736\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}