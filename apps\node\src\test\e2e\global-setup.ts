import { chromium, FullConfig } from '@playwright/test'

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting E2E test setup...')
  
  // 启动浏览器进行预热
  const browser = await chromium.launch()
  const page = await browser.newPage()
  
  try {
    // 等待应用启动
    console.log('⏳ Waiting for application to start...')
    await page.goto('http://localhost:1420', { 
      waitUntil: 'networkidle',
      timeout: 60000 
    })
    
    // 检查应用是否正常加载
    await page.waitForSelector('[data-testid="app-root"]', { timeout: 30000 })
    console.log('✅ Application is ready')
    
    // 执行任何必要的初始化
    await page.evaluate(() => {
      // 清理本地存储
      localStorage.clear()
      sessionStorage.clear()
      
      // 设置测试环境标识
      localStorage.setItem('test-mode', 'true')
    })
    
    console.log('✅ E2E test setup completed')
    
  } catch (error) {
    console.error('❌ E2E test setup failed:', error)
    throw error
  } finally {
    await browser.close()
  }
}

export default globalSetup
