import { useState, useEffect, useCallback } from 'react'

interface UseApiOptions<T> {
  initialData?: T
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
  enabled?: boolean
}

interface UseApiReturn<T> {
  data: T | null
  loading: boolean
  error: Error | null
  refetch: () => Promise<void>
  mutate: (newData: T) => void
}

export function useApi<T>(
  url: string,
  options: UseApiOptions<T> = {}
): UseApiReturn<T> {
  const { initialData = null, onSuccess, onError, enabled = true } = options
  
  const [data, setData] = useState<T | null>(initialData)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const fetchData = useCallback(async () => {
    if (!enabled) return
    
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      setData(result)
      onSuccess?.(result)
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      onError?.(error)
    } finally {
      setLoading(false)
    }
  }, [url, enabled, onSuccess, onError])
  
  const mutate = useCallback((newData: T) => {
    setData(newData)
  }, [])
  
  useEffect(() => {
    if (enabled) {
      fetchData()
    }
  }, [fetchData, enabled])
  
  return {
    data,
    loading,
    error,
    refetch: fetchData,
    mutate
  }
}

// Tauri API 专用 Hook
import { invoke } from '@tauri-apps/api/tauri'

interface UseTauriCommandOptions<T> {
  initialData?: T
  onSuccess?: (data: T) => void
  onError?: (error: Error) => void
  enabled?: boolean
}

export function useTauriCommand<T, P = any>(
  command: string,
  params?: P,
  options: UseTauriCommandOptions<T> = {}
): UseApiReturn<T> {
  const { initialData = null, onSuccess, onError, enabled = true } = options
  
  const [data, setData] = useState<T | null>(initialData)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  
  const executeCommand = useCallback(async () => {
    if (!enabled) return
    
    setLoading(true)
    setError(null)
    
    try {
      const result = await invoke<T>(command, params)
      setData(result)
      onSuccess?.(result)
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      onError?.(error)
    } finally {
      setLoading(false)
    }
  }, [command, params, enabled, onSuccess, onError])
  
  const mutate = useCallback((newData: T) => {
    setData(newData)
  }, [])
  
  useEffect(() => {
    if (enabled) {
      executeCommand()
    }
  }, [executeCommand, enabled])
  
  return {
    data,
    loading,
    error,
    refetch: executeCommand,
    mutate
  }
}

// 异步操作 Hook
export function useAsyncOperation<T, P extends any[] = []>(
  operation: (...args: P) => Promise<T>
) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [data, setData] = useState<T | null>(null)
  
  const execute = useCallback(async (...args: P) => {
    setLoading(true)
    setError(null)
    
    try {
      const result = await operation(...args)
      setData(result)
      return result
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      throw error
    } finally {
      setLoading(false)
    }
  }, [operation])
  
  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
  }, [])
  
  return {
    execute,
    loading,
    error,
    data,
    reset
  }
}
