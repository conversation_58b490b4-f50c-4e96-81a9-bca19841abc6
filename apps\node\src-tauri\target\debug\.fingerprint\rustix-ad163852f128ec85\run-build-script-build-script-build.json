{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6246679968272628950, "build_script_build", false, 11754080893855971962]], "local": [{"RerunIfChanged": {"output": "debug\\build\\rustix-ad163852f128ec85\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_EXPERIMENTAL_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_RUSTC_DEP_OF_STD", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_MIRI", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}