import { useState, useEffect, useRef } from 'react'
import React from 'react'

/**
 * 媒体查询 Hook - 响应式设计支持
 */
export function useMediaQuery(query: string): boolean {
  const [matches, setMatches] = useState(false)

  useEffect(() => {
    const media = window.matchMedia(query)
    
    // 设置初始值
    setMatches(media.matches)

    // 创建监听器
    const listener = (event: MediaQueryListEvent) => {
      setMatches(event.matches)
    }

    // 添加监听器
    if (media.addEventListener) {
      media.addEventListener('change', listener)
    } else {
      // 兼容旧版本浏览器
      media.addListener(listener)
    }

    // 清理函数
    return () => {
      if (media.removeEventListener) {
        media.removeEventListener('change', listener)
      } else {
        media.removeListener(listener)
      }
    }
  }, [query])

  return matches
}

/**
 * 断点 Hook - 常用断点检测
 */
export function useBreakpoint() {
  const isMobile = useMediaQuery('(max-width: 768px)')
  const isTablet = useMediaQuery('(min-width: 769px) and (max-width: 1024px)')
  const isDesktop = useMediaQuery('(min-width: 1025px)')
  const isLarge = useMediaQuery('(min-width: 1440px)')

  return {
    isMobile,
    isTablet,
    isDesktop,
    isLarge,
    // 便利属性
    isSmallScreen: isMobile,
    isMediumScreen: isTablet,
    isLargeScreen: isDesktop || isLarge
  }
}

/**
 * 屏幕方向 Hook
 */
export function useOrientation() {
  const isPortrait = useMediaQuery('(orientation: portrait)')
  const isLandscape = useMediaQuery('(orientation: landscape)')

  return {
    isPortrait,
    isLandscape
  }
}

/**
 * 深色模式 Hook
 */
export function useDarkMode() {
  const prefersDark = useMediaQuery('(prefers-color-scheme: dark)')
  const [isDark, setIsDark] = useState(prefersDark)

  useEffect(() => {
    setIsDark(prefersDark)
  }, [prefersDark])

  const toggle = () => setIsDark(!isDark)
  const enable = () => setIsDark(true)
  const disable = () => setIsDark(false)

  return {
    isDark,
    toggle,
    enable,
    disable
  }
}

/**
 * 减少动画偏好 Hook
 */
export function usePrefersReducedMotion(): boolean {
  return useMediaQuery('(prefers-reduced-motion: reduce)')
}

/**
 * 高对比度偏好 Hook
 */
export function usePrefersHighContrast(): boolean {
  return useMediaQuery('(prefers-contrast: high)')
}

/**
 * 视窗尺寸 Hook
 */
export function useViewportSize() {
  const [size, setSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0
  })

  useEffect(() => {
    const handleResize = () => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight
      })
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return size
}

/**
 * 响应式值 Hook - 根据断点返回不同值
 */
export function useResponsiveValue<T>(values: {
  mobile?: T
  tablet?: T
  desktop?: T
  default: T
}): T {
  const { isMobile, isTablet, isDesktop } = useBreakpoint()

  if (isMobile && values.mobile !== undefined) {
    return values.mobile
  }
  
  if (isTablet && values.tablet !== undefined) {
    return values.tablet
  }
  
  if (isDesktop && values.desktop !== undefined) {
    return values.desktop
  }

  return values.default
}

/**
 * 容器查询 Hook - 基于元素尺寸的响应式
 */
export function useContainerQuery<T extends HTMLElement>(
  queries: Record<string, string>
): {
  ref: React.RefObject<T>
  matches: Record<string, boolean>
} {
  const ref = React.useRef<T>(null)
  const [matches, setMatches] = useState<Record<string, boolean>>({})

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0]
      if (!entry) return

      const { width, height } = entry.contentRect
      const newMatches: Record<string, boolean> = {}

      Object.entries(queries).forEach(([key, query]) => {
        // 简单的宽度查询解析
        const widthMatch = query.match(/\(min-width:\s*(\d+)px\)/)
        const maxWidthMatch = query.match(/\(max-width:\s*(\d+)px\)/)
        
        if (widthMatch) {
          const minWidth = parseInt(widthMatch[1])
          newMatches[key] = width >= minWidth
        } else if (maxWidthMatch) {
          const maxWidth = parseInt(maxWidthMatch[1])
          newMatches[key] = width <= maxWidth
        }
      })

      setMatches(newMatches)
    })

    resizeObserver.observe(element)
    return () => resizeObserver.disconnect()
  }, [queries])

  return { ref, matches }
}
