@echo off
echo 正在初始化数据库...

REM 确保数据目录存在
if not exist "data" mkdir data

REM 创建数据库文件（如果不存在）
if not exist "data\crawler_node.db" (
    echo 创建数据库文件...
    type nul > "data\crawler_node.db"
)

REM 检查是否安装了sqlite3
where sqlite3 >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到 sqlite3 命令
    echo 请安装 SQLite 或确保 sqlite3.exe 在 PATH 中
    pause
    exit /b 1
)

REM 运行初始化脚本
echo 运行数据库初始化脚本...
sqlite3 data\crawler_node.db < init_db.sql

if %ERRORLEVEL% EQU 0 (
    echo ✓ 数据库初始化成功！
) else (
    echo ✗ 数据库初始化失败！
    pause
    exit /b 1
)

REM 验证表是否创建成功
echo 验证数据库表...
sqlite3 data\crawler_node.db ".tables"

echo.
echo 数据库初始化完成！
echo 数据库文件位置: %CD%\data\crawler_node.db
pause
