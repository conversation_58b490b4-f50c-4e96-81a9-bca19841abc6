cargo:rerun-if-changed=build.rs
cargo:rerun-if-changed=Cargo.toml
cargo:rustc-check-cfg=cfg(zerocopy_aarch64_simd_1_59_0)
cargo:rustc-check-cfg=cfg(rust, values("1.59.0"))
cargo:rustc-check-cfg=cfg(zerocopy_core_error_1_81_0)
cargo:rustc-check-cfg=cfg(rust, values("1.81.0"))
cargo:rustc-check-cfg=cfg(zerocopy_diagnostic_on_unimplemented_1_78_0)
cargo:rustc-check-cfg=cfg(rust, values("1.78.0"))
cargo:rustc-check-cfg=cfg(zerocopy_generic_bounds_in_const_fn_1_61_0)
cargo:rustc-check-cfg=cfg(rust, values("1.61.0"))
cargo:rustc-check-cfg=cfg(zerocopy_panic_in_const_and_vec_try_reserve_1_57_0)
cargo:rustc-check-cfg=cfg(rust, values("1.57.0"))
cargo:rustc-check-cfg=cfg(zerocopy_target_has_atomics_1_60_0)
cargo:rustc-check-cfg=cfg(rust, values("1.60.0"))
cargo:rustc-check-cfg=cfg(doc_cfg)
cargo:rustc-check-cfg=cfg(kani)
cargo:rustc-check-cfg=cfg(__ZEROCOPY_INTERNAL_USE_ONLY_NIGHTLY_FEATURES_IN_TESTS)
cargo:rustc-check-cfg=cfg(coverage_nightly)
cargo:rustc-cfg=zerocopy_aarch64_simd_1_59_0
cargo:rustc-cfg=zerocopy_core_error_1_81_0
cargo:rustc-cfg=zerocopy_diagnostic_on_unimplemented_1_78_0
cargo:rustc-cfg=zerocopy_generic_bounds_in_const_fn_1_61_0
cargo:rustc-cfg=zerocopy_panic_in_const_and_vec_try_reserve_1_57_0
cargo:rustc-cfg=zerocopy_target_has_atomics_1_60_0
