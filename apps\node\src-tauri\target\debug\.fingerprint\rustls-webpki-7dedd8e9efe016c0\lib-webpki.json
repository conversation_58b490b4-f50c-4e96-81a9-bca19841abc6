{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ring\", \"std\"]", "declared_features": "[\"alloc\", \"aws-lc-rs\", \"aws-lc-rs-fips\", \"aws-lc-rs-unstable\", \"default\", \"ring\", \"std\"]", "target": 5054897795206437336, "profile": 15657897354478470176, "path": 17794303679293903145, "deps": [[2883436298747778685, "pki_types", false, 17984470032282637163], [5491919304041016563, "ring", false, 16503626731405694287], [8995469080876806959, "untrusted", false, 4975939749537646384]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-webpki-7dedd8e9efe016c0\\dep-lib-webpki", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}