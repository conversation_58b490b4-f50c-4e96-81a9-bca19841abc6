// API 相关 Hooks
export {
  useApi,
  useTauriCommand,
  useAsyncOperation
} from './useApi'

// 防抖节流 Hooks
export {
  useDebounce,
  useDebouncedCallback,
  useThrottle,
  useThrottledCallback,
  useSearchDebounce,
  useInputDebounce
} from './useDebounce'

// 存储相关 Hooks
export {
  useLocalStorage,
  useSessionStorage,
  usePersistentState,
  useStorageListener,
  useStorageCapacity
} from './useLocalStorage'

// 定时器相关 Hooks
export {
  useInterval,
  useControllableInterval,
  useCountdown,
  usePolling
} from './useInterval'

// 媒体查询和响应式 Hooks
export {
  useMediaQuery,
  useBreakpoint,
  useOrientation,
  useDarkMode,
  usePrefersReducedMotion,
  usePrefersHighContrast,
  useViewportSize,
  useResponsiveValue,
  useContainerQuery
} from './useMediaQuery'

// 重新导出常用组合
export const useResponsive = () => {
  const breakpoint = useBreakpoint()
  const orientation = useOrientation()
  const viewportSize = useViewportSize()

  return {
    ...breakpoint,
    ...orientation,
    ...viewportSize
  }
}

// 常用的组合 Hook
export const useAppState = () => {
  const darkMode = useDarkMode()
  const responsive = useResponsive()
  const prefersReducedMotion = usePrefersReducedMotion()

  return {
    ...darkMode,
    ...responsive,
    prefersReducedMotion
  }
}
