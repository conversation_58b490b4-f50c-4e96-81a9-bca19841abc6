{"rustc": 1842507548689473721, "features": "[\"default\", \"rustls\", \"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"serde_json\", \"vendored-openssl\"]", "target": 5047893427188600641, "profile": 2241668132362809309, "path": 11396702735158082904, "deps": [[5103565458935487, "futures_io", false, 483540127987625433], [1951382276944535576, "executor_trait", false, 7899436297065855059], [3395626199960367565, "pinky_swear", false, 11813926103184338762], [4495526598637097934, "parking_lot", false, 12177880921016921824], [4656928804077918400, "flume", false, 15791438370974859873], [4713603720635702932, "build_script_build", false, 7710635043041730301], [7048981225526245511, "amq_protocol", false, 52613720300963707], [7620660491849607393, "futures_core", false, 10280731575054067511], [8606274917505247608, "tracing", false, 5492527985859474487], [8864093321401338808, "waker_fn", false, 15813420805235342189], [9689903380558560274, "serde", false, 4202820352788480372], [11946729385090170470, "async_trait", false, 1405635847080043314], [14332133141799632110, "reactor_trait", false, 8793674127975386401], [15121870802873242844, "async_global_executor_trait", false, 9000559667465225219], [16454787060997881635, "async_reactor_trait", false, 15828137135086792430]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\lapin-19aa89ceb6226a22\\dep-lib-lapin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}