<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="缓存特征"><title>Cacheable in weibo_crawler_node::repository - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc trait"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Cacheable</a></h2><h3><a href="#required-methods">Required Methods</a></h3><ul class="block"><li><a href="#tymethod.get_from_cache" title="get_from_cache">get_from_cache</a></li><li><a href="#tymethod.invalidate_cache" title="invalidate_cache">invalidate_cache</a></li><li><a href="#tymethod.invalidate_cache_pattern" title="invalidate_cache_pattern">invalidate_cache_pattern</a></li><li><a href="#tymethod.set_cache" title="set_cache">set_cache</a></li></ul><h3><a href="#implementors">Implementors</a></h3></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>repository</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">repository</a></div><h1>Trait <span class="trait">Cacheable</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/repository/mod.rs.html#223-228">Source</a> </span></div><pre class="rust item-decl"><code>pub trait Cacheable&lt;T&gt; {
    // Required methods
    fn <a href="#tymethod.get_from_cache" class="fn">get_from_cache</a>&lt;'life0, 'life1, 'async_trait&gt;(
        &amp;'life0 self,
        key: &amp;'life1 <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;T&gt;, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait,
             'life1: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.set_cache" class="fn">set_cache</a>&lt;'life0, 'life1, 'life2, 'async_trait&gt;(
        &amp;'life0 self,
        key: &amp;'life1 <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
        value: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;'life2 T</a>,
        ttl: <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u64.html">u64</a>&gt;,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait,
             'life1: 'async_trait,
             'life2: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.invalidate_cache" class="fn">invalidate_cache</a>&lt;'life0, 'life1, 'async_trait&gt;(
        &amp;'life0 self,
        key: &amp;'life1 <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait,
             'life1: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.invalidate_cache_pattern" class="fn">invalidate_cache_pattern</a>&lt;'life0, 'life1, 'async_trait&gt;(
        &amp;'life0 self,
        pattern: &amp;'life1 <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait,
             'life1: 'async_trait</span>;
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>缓存特征</p>
</div></details><h2 id="required-methods" class="section-header">Required Methods<a href="#required-methods" class="anchor">§</a></h2><div class="methods"><section id="tymethod.get_from_cache" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#224">Source</a><h4 class="code-header">fn <a href="#tymethod.get_from_cache" class="fn">get_from_cache</a>&lt;'life0, 'life1, 'async_trait&gt;(
    &amp;'life0 self,
    key: &amp;'life1 <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;T&gt;, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,
    'life1: 'async_trait,</div></h4></section><section id="tymethod.set_cache" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#225">Source</a><h4 class="code-header">fn <a href="#tymethod.set_cache" class="fn">set_cache</a>&lt;'life0, 'life1, 'life2, 'async_trait&gt;(
    &amp;'life0 self,
    key: &amp;'life1 <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
    value: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;'life2 T</a>,
    ttl: <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u64.html">u64</a>&gt;,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,
    'life1: 'async_trait,
    'life2: 'async_trait,</div></h4></section><section id="tymethod.invalidate_cache" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#226">Source</a><h4 class="code-header">fn <a href="#tymethod.invalidate_cache" class="fn">invalidate_cache</a>&lt;'life0, 'life1, 'async_trait&gt;(
    &amp;'life0 self,
    key: &amp;'life1 <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,
    'life1: 'async_trait,</div></h4></section><section id="tymethod.invalidate_cache_pattern" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#227">Source</a><h4 class="code-header">fn <a href="#tymethod.invalidate_cache_pattern" class="fn">invalidate_cache_pattern</a>&lt;'life0, 'life1, 'async_trait&gt;(
    &amp;'life0 self,
    pattern: &amp;'life1 <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,
    'life1: 'async_trait,</div></h4></section></div><h2 id="implementors" class="section-header">Implementors<a href="#implementors" class="anchor">§</a></h2><div id="implementors-list"></div><script src="../../trait.impl/weibo_crawler_node/repository/trait.Cacheable.js" async></script></section></div></main></body></html>