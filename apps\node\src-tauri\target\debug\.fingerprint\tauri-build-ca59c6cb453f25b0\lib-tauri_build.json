{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 8181899942129726463, "deps": [[4450062412064442726, "dirs_next", false, 6519417936915856773], [4899080583175475170, "semver", false, 2022204265236511886], [7468248713591957673, "cargo_toml", false, 1436602286294663029], [8292277814562636972, "tauri_utils", false, 14917711522055890592], [8569119365930580996, "serde_json", false, 15813735211382407096], [9689903380558560274, "serde", false, 7307778708627749338], [10301936376833819828, "json_patch", false, 15513032560915199314], [13077543566650298139, "heck", false, 723539331256257772], [13625485746686963219, "anyhow", false, 13076012925280761826], [14189313126492979171, "tauri_winres", false, 12897938375004973141], [15622660310229662834, "walkdir", false, 11884419142722294321]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-ca59c6cb453f25b0\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}