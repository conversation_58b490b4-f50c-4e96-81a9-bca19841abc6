{"rustc": 1842507548689473721, "features": "[\"default\", \"rustls\", \"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"serde_json\", \"vendored-openssl\"]", "target": 5047893427188600641, "profile": 15657897354478470176, "path": 11396702735158082904, "deps": [[5103565458935487, "futures_io", false, 487272345117200944], [1951382276944535576, "executor_trait", false, 4626106166338756968], [3395626199960367565, "pinky_swear", false, 7439154225499088299], [4495526598637097934, "parking_lot", false, 5753253266357357226], [4656928804077918400, "flume", false, 14892895667108268381], [4713603720635702932, "build_script_build", false, 7710635043041730301], [7048981225526245511, "amq_protocol", false, 7731126474313318282], [7620660491849607393, "futures_core", false, 6897113057850168815], [8606274917505247608, "tracing", false, 17646992783032211729], [8864093321401338808, "waker_fn", false, 2289374646354926171], [9689903380558560274, "serde", false, 4389908846715814937], [11946729385090170470, "async_trait", false, 1405635847080043314], [14332133141799632110, "reactor_trait", false, 291301327424145344], [15121870802873242844, "async_global_executor_trait", false, 218445502946828595], [16454787060997881635, "async_reactor_trait", false, 18086619117800314851]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\lapin-ae51757ccbaa215a\\dep-lib-lapin", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}