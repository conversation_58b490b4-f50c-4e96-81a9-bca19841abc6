import React from 'react';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent,
  StatCard,
  MetricCard,
  InfoCard
} from './Card';
import { Button } from './Button';
import { Input, SearchInput } from './Input';
import { Badge, StatusBadge, CountBadge, TrendBadge } from './Badge';
import { Activity, Users, Globe } from 'lucide-react';

// 测试组件，验证所有组件是否正常工作
export function TestComponents() {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">UI组件测试</h1>
      
      {/* 测试Card组件 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">Card组件</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Card variant="default">
            <CardHeader>
              <CardTitle>默认卡片</CardTitle>
              <CardDescription>这是一个默认样式的卡片</CardDescription>
            </CardHeader>
            <CardContent>
              <p>卡片内容区域</p>
            </CardContent>
          </Card>
          
          <Card variant="elevated">
            <CardHeader>
              <CardTitle>高级卡片</CardTitle>
              <CardDescription>带有阴影效果的卡片</CardDescription>
            </CardHeader>
            <CardContent>
              <p>卡片内容区域</p>
            </CardContent>
          </Card>
          
          <Card variant="interactive">
            <CardHeader>
              <CardTitle>交互卡片</CardTitle>
              <CardDescription>可点击的卡片</CardDescription>
            </CardHeader>
            <CardContent>
              <p>卡片内容区域</p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* 测试StatCard组件 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">StatCard组件</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="活跃用户"
            value="1,234"
            change="+12% 较上月"
            trend="up"
            icon={<Users className="w-6 h-6 text-primary" />}
          />
          <StatCard
            title="系统负载"
            value="45%"
            change="正常范围"
            trend="neutral"
            icon={<Activity className="w-6 h-6 text-success" />}
          />
          <StatCard
            title="网络连接"
            value="856"
            change="-3% 较昨日"
            trend="down"
            icon={<Globe className="w-6 h-6 text-info" />}
          />
        </div>
      </section>

      {/* 测试MetricCard组件 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">MetricCard组件</h2>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <MetricCard
            title="CPU使用率"
            value={75.5}
            unit="%"
            progress={75.5}
            color="hsl(var(--info))"
            description="当前处理器使用情况"
          />
          <MetricCard
            title="内存使用率"
            value={60.2}
            unit="%"
            progress={60.2}
            color="hsl(var(--warning))"
            description="系统内存占用情况"
          />
          <MetricCard
            title="磁盘使用率"
            value={45.8}
            unit="%"
            progress={45.8}
            color="hsl(var(--success))"
            description="存储空间使用情况"
          />
        </div>
      </section>

      {/* 测试Button组件 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">Button组件</h2>
        <div className="flex flex-wrap gap-2">
          <Button variant="default">默认按钮</Button>
          <Button variant="secondary">次要按钮</Button>
          <Button variant="outline">边框按钮</Button>
          <Button variant="ghost">幽灵按钮</Button>
          <Button variant="destructive">危险按钮</Button>
          <Button variant="success">成功按钮</Button>
          <Button variant="warning">警告按钮</Button>
          <Button variant="info">信息按钮</Button>
          <Button loading>加载中</Button>
          <Button leftIcon={<Activity className="w-4 h-4" />}>带图标</Button>
        </div>
      </section>

      {/* 测试Input组件 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">Input组件</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
          <Input
            label="用户名"
            placeholder="请输入用户名"
            leftIcon={<Users className="w-4 h-4" />}
          />
          <Input
            label="密码"
            type="password"
            placeholder="请输入密码"
            helperText="密码长度至少8位"
          />
          <SearchInput
            placeholder="搜索..."
            onSearch={(value) => console.log('搜索:', value)}
          />
          <Input
            label="邮箱"
            type="email"
            placeholder="请输入邮箱"
            error="邮箱格式不正确"
          />
        </div>
      </section>

      {/* 测试Badge组件 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">Badge组件</h2>
        <div className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Badge variant="default">默认</Badge>
            <Badge variant="secondary">次要</Badge>
            <Badge variant="success">成功</Badge>
            <Badge variant="warning">警告</Badge>
            <Badge variant="destructive">错误</Badge>
            <Badge variant="info">信息</Badge>
            <Badge variant="outline">边框</Badge>
            <Badge variant="ghost">幽灵</Badge>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <StatusBadge status="online" />
            <StatusBadge status="offline" />
            <StatusBadge status="warning" />
            <StatusBadge status="maintenance" />
            <StatusBadge status="error" />
          </div>
          
          <div className="flex flex-wrap gap-2">
            <TrendBadge trend="up" value="+12%" />
            <TrendBadge trend="down" value="-5%" />
            <TrendBadge trend="neutral" value="0%" />
            <CountBadge count={5} />
            <CountBadge count={99} />
            <CountBadge count={100} max={99} />
          </div>
        </div>
      </section>

      {/* 测试InfoCard组件 */}
      <section>
        <h2 className="text-xl font-semibold mb-4">InfoCard组件</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <InfoCard
            title="系统状态"
            subtitle="节点运行信息"
            status="success"
            content={
              <div className="space-y-2">
                <p>系统运行正常</p>
                <p>所有服务已启动</p>
              </div>
            }
            actions={
              <Button size="sm">查看详情</Button>
            }
          />
          
          <InfoCard
            title="警告信息"
            subtitle="需要注意的问题"
            status="warning"
            content={
              <div className="space-y-2">
                <p>磁盘空间不足</p>
                <p>建议清理临时文件</p>
              </div>
            }
            actions={
              <Button variant="warning" size="sm">立即处理</Button>
            }
          />
        </div>
      </section>
    </div>
  );
}
