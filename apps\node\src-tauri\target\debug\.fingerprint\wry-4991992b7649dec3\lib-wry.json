{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 15657897354478470176, "path": 16482571177845734215, "deps": [[3007252114546291461, "tao", false, 3970238144360104634], [3150220818285335163, "url", false, 1746054398145793718], [3540822385484940109, "windows_implement", false, 3473423417027385092], [3722963349756955755, "once_cell", false, 3092077161891181902], [4381063397040571828, "webview2_com", false, 2334546348545801766], [4405182208873388884, "http", false, 5891631273443115995], [4684437522915235464, "libc", false, 14405428633553126106], [5986029879202738730, "log", false, 13216658706447473378], [7653476968652377684, "windows", false, 4435937259384173537], [8008191657135824715, "thiserror", false, 5374037758448816721], [8391357152270261188, "build_script_build", false, 15010752959823956639], [8569119365930580996, "serde_json", false, 3777298738341353814], [9689903380558560274, "serde", false, 4389908846715814937], [11989259058781683633, "dunce", false, 1955325503927001036]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-4991992b7649dec3\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}