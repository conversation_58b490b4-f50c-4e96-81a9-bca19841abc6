<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\window_manager.rs`."><title>window_manager.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>window_manager.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{AppError, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::collections::HashMap;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>tauri::{AppHandle, Manager, Window, WindowBuilder, WindowUrl};
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>tracing::{info};
<a href=#6 id=6 data-nosnippet>6</a>
<a href=#7 id=7 data-nosnippet>7</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#8 id=8 data-nosnippet>8</a></span><span class="kw">pub struct </span>WindowConfig {
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">pub </span>label: String,
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub </span>title: String,
<a href=#11 id=11 data-nosnippet>11</a>    <span class="kw">pub </span>url: String,
<a href=#12 id=12 data-nosnippet>12</a>    <span class="kw">pub </span>width: f64,
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>height: f64,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>min_width: <span class="prelude-ty">Option</span>&lt;f64&gt;,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>min_height: <span class="prelude-ty">Option</span>&lt;f64&gt;,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>max_width: <span class="prelude-ty">Option</span>&lt;f64&gt;,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>max_height: <span class="prelude-ty">Option</span>&lt;f64&gt;,
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">pub </span>resizable: bool,
<a href=#19 id=19 data-nosnippet>19</a>    <span class="kw">pub </span>maximizable: bool,
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">pub </span>minimizable: bool,
<a href=#21 id=21 data-nosnippet>21</a>    <span class="kw">pub </span>closable: bool,
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">pub </span>decorations: bool,
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">pub </span>always_on_top: bool,
<a href=#24 id=24 data-nosnippet>24</a>    <span class="kw">pub </span>center: bool,
<a href=#25 id=25 data-nosnippet>25</a>    <span class="kw">pub </span>fullscreen: bool,
<a href=#26 id=26 data-nosnippet>26</a>    <span class="kw">pub </span>transparent: bool,
<a href=#27 id=27 data-nosnippet>27</a>    <span class="kw">pub </span>visible: bool,
<a href=#28 id=28 data-nosnippet>28</a>}
<a href=#29 id=29 data-nosnippet>29</a>
<a href=#30 id=30 data-nosnippet>30</a><span class="kw">impl </span>Default <span class="kw">for </span>WindowConfig {
<a href=#31 id=31 data-nosnippet>31</a>    <span class="kw">fn </span>default() -&gt; <span class="self">Self </span>{
<a href=#32 id=32 data-nosnippet>32</a>        <span class="self">Self </span>{
<a href=#33 id=33 data-nosnippet>33</a>            label: <span class="string">"window"</span>.to_string(),
<a href=#34 id=34 data-nosnippet>34</a>            title: <span class="string">"Window"</span>.to_string(),
<a href=#35 id=35 data-nosnippet>35</a>            url: <span class="string">"/"</span>.to_string(),
<a href=#36 id=36 data-nosnippet>36</a>            width: <span class="number">800.0</span>,
<a href=#37 id=37 data-nosnippet>37</a>            height: <span class="number">600.0</span>,
<a href=#38 id=38 data-nosnippet>38</a>            min_width: <span class="prelude-val">Some</span>(<span class="number">400.0</span>),
<a href=#39 id=39 data-nosnippet>39</a>            min_height: <span class="prelude-val">Some</span>(<span class="number">300.0</span>),
<a href=#40 id=40 data-nosnippet>40</a>            max_width: <span class="prelude-val">None</span>,
<a href=#41 id=41 data-nosnippet>41</a>            max_height: <span class="prelude-val">None</span>,
<a href=#42 id=42 data-nosnippet>42</a>            resizable: <span class="bool-val">true</span>,
<a href=#43 id=43 data-nosnippet>43</a>            maximizable: <span class="bool-val">true</span>,
<a href=#44 id=44 data-nosnippet>44</a>            minimizable: <span class="bool-val">true</span>,
<a href=#45 id=45 data-nosnippet>45</a>            closable: <span class="bool-val">true</span>,
<a href=#46 id=46 data-nosnippet>46</a>            decorations: <span class="bool-val">true</span>,
<a href=#47 id=47 data-nosnippet>47</a>            always_on_top: <span class="bool-val">false</span>,
<a href=#48 id=48 data-nosnippet>48</a>            center: <span class="bool-val">true</span>,
<a href=#49 id=49 data-nosnippet>49</a>            fullscreen: <span class="bool-val">false</span>,
<a href=#50 id=50 data-nosnippet>50</a>            transparent: <span class="bool-val">false</span>,
<a href=#51 id=51 data-nosnippet>51</a>            visible: <span class="bool-val">true</span>,
<a href=#52 id=52 data-nosnippet>52</a>        }
<a href=#53 id=53 data-nosnippet>53</a>    }
<a href=#54 id=54 data-nosnippet>54</a>}
<a href=#55 id=55 data-nosnippet>55</a>
<a href=#56 id=56 data-nosnippet>56</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#57 id=57 data-nosnippet>57</a></span><span class="kw">pub struct </span>WindowInfo {
<a href=#58 id=58 data-nosnippet>58</a>    <span class="kw">pub </span>label: String,
<a href=#59 id=59 data-nosnippet>59</a>    <span class="kw">pub </span>title: String,
<a href=#60 id=60 data-nosnippet>60</a>    <span class="kw">pub </span>url: String,
<a href=#61 id=61 data-nosnippet>61</a>    <span class="kw">pub </span>is_visible: bool,
<a href=#62 id=62 data-nosnippet>62</a>    <span class="kw">pub </span>is_focused: bool,
<a href=#63 id=63 data-nosnippet>63</a>    <span class="kw">pub </span>is_maximized: bool,
<a href=#64 id=64 data-nosnippet>64</a>    <span class="kw">pub </span>is_minimized: bool,
<a href=#65 id=65 data-nosnippet>65</a>    <span class="kw">pub </span>is_fullscreen: bool,
<a href=#66 id=66 data-nosnippet>66</a>    <span class="kw">pub </span>position: (i32, i32),
<a href=#67 id=67 data-nosnippet>67</a>    <span class="kw">pub </span>size: (u32, u32),
<a href=#68 id=68 data-nosnippet>68</a>}
<a href=#69 id=69 data-nosnippet>69</a>
<a href=#70 id=70 data-nosnippet>70</a><span class="kw">pub struct </span>WindowManager {
<a href=#71 id=71 data-nosnippet>71</a>    app_handle: AppHandle,
<a href=#72 id=72 data-nosnippet>72</a>    window_configs: HashMap&lt;String, WindowConfig&gt;,
<a href=#73 id=73 data-nosnippet>73</a>}
<a href=#74 id=74 data-nosnippet>74</a>
<a href=#75 id=75 data-nosnippet>75</a><span class="kw">impl </span>WindowManager {
<a href=#76 id=76 data-nosnippet>76</a>    <span class="kw">pub fn </span>new(app_handle: AppHandle) -&gt; <span class="self">Self </span>{
<a href=#77 id=77 data-nosnippet>77</a>        <span class="self">Self </span>{
<a href=#78 id=78 data-nosnippet>78</a>            app_handle,
<a href=#79 id=79 data-nosnippet>79</a>            window_configs: HashMap::new(),
<a href=#80 id=80 data-nosnippet>80</a>        }
<a href=#81 id=81 data-nosnippet>81</a>    }
<a href=#82 id=82 data-nosnippet>82</a>
<a href=#83 id=83 data-nosnippet>83</a>    <span class="doccomment">/// 创建新窗口
<a href=#84 id=84 data-nosnippet>84</a>    </span><span class="kw">pub fn </span>create_window(<span class="kw-2">&amp;mut </span><span class="self">self</span>, config: WindowConfig) -&gt; <span class="prelude-ty">Result</span>&lt;Window&gt; {
<a href=#85 id=85 data-nosnippet>85</a>        <span class="kw">let </span><span class="kw-2">mut </span>builder = WindowBuilder::new(
<a href=#86 id=86 data-nosnippet>86</a>            <span class="kw-2">&amp;</span><span class="self">self</span>.app_handle,
<a href=#87 id=87 data-nosnippet>87</a>            <span class="kw-2">&amp;</span>config.label,
<a href=#88 id=88 data-nosnippet>88</a>            WindowUrl::App(config.url.parse().unwrap())
<a href=#89 id=89 data-nosnippet>89</a>        )
<a href=#90 id=90 data-nosnippet>90</a>        .title(<span class="kw-2">&amp;</span>config.title)
<a href=#91 id=91 data-nosnippet>91</a>        .inner_size(config.width, config.height)
<a href=#92 id=92 data-nosnippet>92</a>        .resizable(config.resizable)
<a href=#93 id=93 data-nosnippet>93</a>        .maximizable(config.maximizable)
<a href=#94 id=94 data-nosnippet>94</a>        .minimizable(config.minimizable)
<a href=#95 id=95 data-nosnippet>95</a>        .closable(config.closable)
<a href=#96 id=96 data-nosnippet>96</a>        .decorations(config.decorations)
<a href=#97 id=97 data-nosnippet>97</a>        .always_on_top(config.always_on_top)
<a href=#98 id=98 data-nosnippet>98</a>        .center()
<a href=#99 id=99 data-nosnippet>99</a>        .fullscreen(config.fullscreen)
<a href=#100 id=100 data-nosnippet>100</a>        .transparent(config.transparent)
<a href=#101 id=101 data-nosnippet>101</a>        .visible(config.visible);
<a href=#102 id=102 data-nosnippet>102</a>
<a href=#103 id=103 data-nosnippet>103</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(min_width) = config.min_width {
<a href=#104 id=104 data-nosnippet>104</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>(min_height) = config.min_height {
<a href=#105 id=105 data-nosnippet>105</a>                builder = builder.min_inner_size(min_width, min_height);
<a href=#106 id=106 data-nosnippet>106</a>            }
<a href=#107 id=107 data-nosnippet>107</a>        }
<a href=#108 id=108 data-nosnippet>108</a>
<a href=#109 id=109 data-nosnippet>109</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(max_width) = config.max_width {
<a href=#110 id=110 data-nosnippet>110</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>(max_height) = config.max_height {
<a href=#111 id=111 data-nosnippet>111</a>                builder = builder.max_inner_size(max_width, max_height);
<a href=#112 id=112 data-nosnippet>112</a>            }
<a href=#113 id=113 data-nosnippet>113</a>        }
<a href=#114 id=114 data-nosnippet>114</a>
<a href=#115 id=115 data-nosnippet>115</a>        <span class="kw">let </span>window = builder.build()
<a href=#116 id=116 data-nosnippet>116</a>            .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"创建窗口失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#117 id=117 data-nosnippet>117</a>
<a href=#118 id=118 data-nosnippet>118</a>        <span class="comment">// 保存窗口配置
<a href=#119 id=119 data-nosnippet>119</a>        </span><span class="self">self</span>.window_configs.insert(config.label.clone(), config.clone());
<a href=#120 id=120 data-nosnippet>120</a>
<a href=#121 id=121 data-nosnippet>121</a>        <span class="comment">// 设置窗口事件监听
<a href=#122 id=122 data-nosnippet>122</a>        </span><span class="self">self</span>.setup_window_events(<span class="kw-2">&amp;</span>window)<span class="question-mark">?</span>;
<a href=#123 id=123 data-nosnippet>123</a>
<a href=#124 id=124 data-nosnippet>124</a>        <span class="macro">info!</span>(<span class="string">"创建窗口: {}"</span>, config.label);
<a href=#125 id=125 data-nosnippet>125</a>        <span class="prelude-val">Ok</span>(window)
<a href=#126 id=126 data-nosnippet>126</a>    }
<a href=#127 id=127 data-nosnippet>127</a>
<a href=#128 id=128 data-nosnippet>128</a>    <span class="doccomment">/// 获取窗口
<a href=#129 id=129 data-nosnippet>129</a>    </span><span class="kw">pub fn </span>get_window(<span class="kw-2">&amp;</span><span class="self">self</span>, label: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;Window&gt; {
<a href=#130 id=130 data-nosnippet>130</a>        <span class="self">self</span>.app_handle.get_window(label)
<a href=#131 id=131 data-nosnippet>131</a>    }
<a href=#132 id=132 data-nosnippet>132</a>
<a href=#133 id=133 data-nosnippet>133</a>    <span class="doccomment">/// 关闭窗口
<a href=#134 id=134 data-nosnippet>134</a>    </span><span class="kw">pub fn </span>close_window(<span class="kw-2">&amp;mut </span><span class="self">self</span>, label: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#135 id=135 data-nosnippet>135</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(window) = <span class="self">self</span>.get_window(label) {
<a href=#136 id=136 data-nosnippet>136</a>            window.close().map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"关闭窗口失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#137 id=137 data-nosnippet>137</a>            <span class="self">self</span>.window_configs.remove(label);
<a href=#138 id=138 data-nosnippet>138</a>            <span class="macro">info!</span>(<span class="string">"关闭窗口: {}"</span>, label);
<a href=#139 id=139 data-nosnippet>139</a>        }
<a href=#140 id=140 data-nosnippet>140</a>        <span class="prelude-val">Ok</span>(())
<a href=#141 id=141 data-nosnippet>141</a>    }
<a href=#142 id=142 data-nosnippet>142</a>
<a href=#143 id=143 data-nosnippet>143</a>    <span class="doccomment">/// 显示窗口
<a href=#144 id=144 data-nosnippet>144</a>    </span><span class="kw">pub fn </span>show_window(<span class="kw-2">&amp;</span><span class="self">self</span>, label: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#145 id=145 data-nosnippet>145</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(window) = <span class="self">self</span>.get_window(label) {
<a href=#146 id=146 data-nosnippet>146</a>            window.show().map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"显示窗口失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#147 id=147 data-nosnippet>147</a>            window.set_focus().map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"设置窗口焦点失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#148 id=148 data-nosnippet>148</a>            <span class="macro">info!</span>(<span class="string">"显示窗口: {}"</span>, label);
<a href=#149 id=149 data-nosnippet>149</a>        }
<a href=#150 id=150 data-nosnippet>150</a>        <span class="prelude-val">Ok</span>(())
<a href=#151 id=151 data-nosnippet>151</a>    }
<a href=#152 id=152 data-nosnippet>152</a>
<a href=#153 id=153 data-nosnippet>153</a>    <span class="doccomment">/// 隐藏窗口
<a href=#154 id=154 data-nosnippet>154</a>    </span><span class="kw">pub fn </span>hide_window(<span class="kw-2">&amp;</span><span class="self">self</span>, label: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#155 id=155 data-nosnippet>155</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(window) = <span class="self">self</span>.get_window(label) {
<a href=#156 id=156 data-nosnippet>156</a>            window.hide().map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"隐藏窗口失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#157 id=157 data-nosnippet>157</a>            <span class="macro">info!</span>(<span class="string">"隐藏窗口: {}"</span>, label);
<a href=#158 id=158 data-nosnippet>158</a>        }
<a href=#159 id=159 data-nosnippet>159</a>        <span class="prelude-val">Ok</span>(())
<a href=#160 id=160 data-nosnippet>160</a>    }
<a href=#161 id=161 data-nosnippet>161</a>
<a href=#162 id=162 data-nosnippet>162</a>    <span class="doccomment">/// 最大化窗口
<a href=#163 id=163 data-nosnippet>163</a>    </span><span class="kw">pub fn </span>maximize_window(<span class="kw-2">&amp;</span><span class="self">self</span>, label: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#164 id=164 data-nosnippet>164</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(window) = <span class="self">self</span>.get_window(label) {
<a href=#165 id=165 data-nosnippet>165</a>            window.maximize().map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"最大化窗口失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#166 id=166 data-nosnippet>166</a>            <span class="macro">info!</span>(<span class="string">"最大化窗口: {}"</span>, label);
<a href=#167 id=167 data-nosnippet>167</a>        }
<a href=#168 id=168 data-nosnippet>168</a>        <span class="prelude-val">Ok</span>(())
<a href=#169 id=169 data-nosnippet>169</a>    }
<a href=#170 id=170 data-nosnippet>170</a>
<a href=#171 id=171 data-nosnippet>171</a>    <span class="doccomment">/// 最小化窗口
<a href=#172 id=172 data-nosnippet>172</a>    </span><span class="kw">pub fn </span>minimize_window(<span class="kw-2">&amp;</span><span class="self">self</span>, label: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#173 id=173 data-nosnippet>173</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(window) = <span class="self">self</span>.get_window(label) {
<a href=#174 id=174 data-nosnippet>174</a>            window.minimize().map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"最小化窗口失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#175 id=175 data-nosnippet>175</a>            <span class="macro">info!</span>(<span class="string">"最小化窗口: {}"</span>, label);
<a href=#176 id=176 data-nosnippet>176</a>        }
<a href=#177 id=177 data-nosnippet>177</a>        <span class="prelude-val">Ok</span>(())
<a href=#178 id=178 data-nosnippet>178</a>    }
<a href=#179 id=179 data-nosnippet>179</a>
<a href=#180 id=180 data-nosnippet>180</a>    <span class="doccomment">/// 切换窗口全屏状态
<a href=#181 id=181 data-nosnippet>181</a>    </span><span class="kw">pub fn </span>toggle_fullscreen(<span class="kw-2">&amp;</span><span class="self">self</span>, label: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#182 id=182 data-nosnippet>182</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(window) = <span class="self">self</span>.get_window(label) {
<a href=#183 id=183 data-nosnippet>183</a>            <span class="kw">let </span>is_fullscreen = window.is_fullscreen()
<a href=#184 id=184 data-nosnippet>184</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"获取窗口全屏状态失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#185 id=185 data-nosnippet>185</a>
<a href=#186 id=186 data-nosnippet>186</a>            window.set_fullscreen(!is_fullscreen)
<a href=#187 id=187 data-nosnippet>187</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"切换窗口全屏状态失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#188 id=188 data-nosnippet>188</a>
<a href=#189 id=189 data-nosnippet>189</a>            <span class="macro">info!</span>(<span class="string">"切换窗口全屏状态: {} -&gt; {}"</span>, label, !is_fullscreen);
<a href=#190 id=190 data-nosnippet>190</a>        }
<a href=#191 id=191 data-nosnippet>191</a>        <span class="prelude-val">Ok</span>(())
<a href=#192 id=192 data-nosnippet>192</a>    }
<a href=#193 id=193 data-nosnippet>193</a>
<a href=#194 id=194 data-nosnippet>194</a>    <span class="doccomment">/// 获取窗口信息
<a href=#195 id=195 data-nosnippet>195</a>    </span><span class="kw">pub fn </span>get_window_info(<span class="kw-2">&amp;</span><span class="self">self</span>, label: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;WindowInfo&gt;&gt; {
<a href=#196 id=196 data-nosnippet>196</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(window) = <span class="self">self</span>.get_window(label) {
<a href=#197 id=197 data-nosnippet>197</a>            <span class="kw">let </span>title = window.title()
<a href=#198 id=198 data-nosnippet>198</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"获取窗口标题失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#199 id=199 data-nosnippet>199</a>            
<a href=#200 id=200 data-nosnippet>200</a>            <span class="kw">let </span>is_visible = window.is_visible()
<a href=#201 id=201 data-nosnippet>201</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"获取窗口可见性失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#202 id=202 data-nosnippet>202</a>            
<a href=#203 id=203 data-nosnippet>203</a>            <span class="kw">let </span>is_focused = window.is_focused()
<a href=#204 id=204 data-nosnippet>204</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"获取窗口焦点状态失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#205 id=205 data-nosnippet>205</a>            
<a href=#206 id=206 data-nosnippet>206</a>            <span class="kw">let </span>is_maximized = window.is_maximized()
<a href=#207 id=207 data-nosnippet>207</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"获取窗口最大化状态失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#208 id=208 data-nosnippet>208</a>            
<a href=#209 id=209 data-nosnippet>209</a>            <span class="kw">let </span>is_minimized = window.is_minimized()
<a href=#210 id=210 data-nosnippet>210</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"获取窗口最小化状态失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#211 id=211 data-nosnippet>211</a>            
<a href=#212 id=212 data-nosnippet>212</a>            <span class="kw">let </span>is_fullscreen = window.is_fullscreen()
<a href=#213 id=213 data-nosnippet>213</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"获取窗口全屏状态失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#214 id=214 data-nosnippet>214</a>            
<a href=#215 id=215 data-nosnippet>215</a>            <span class="kw">let </span>position = window.outer_position()
<a href=#216 id=216 data-nosnippet>216</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"获取窗口位置失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#217 id=217 data-nosnippet>217</a>            
<a href=#218 id=218 data-nosnippet>218</a>            <span class="kw">let </span>size = window.outer_size()
<a href=#219 id=219 data-nosnippet>219</a>                .map_err(|e| AppError::Other(<span class="macro">format!</span>(<span class="string">"获取窗口大小失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#220 id=220 data-nosnippet>220</a>
<a href=#221 id=221 data-nosnippet>221</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(WindowInfo {
<a href=#222 id=222 data-nosnippet>222</a>                label: label.to_string(),
<a href=#223 id=223 data-nosnippet>223</a>                title,
<a href=#224 id=224 data-nosnippet>224</a>                url: <span class="self">self</span>.window_configs.get(label)
<a href=#225 id=225 data-nosnippet>225</a>                    .map(|c| c.url.clone())
<a href=#226 id=226 data-nosnippet>226</a>                    .unwrap_or_default(),
<a href=#227 id=227 data-nosnippet>227</a>                is_visible,
<a href=#228 id=228 data-nosnippet>228</a>                is_focused,
<a href=#229 id=229 data-nosnippet>229</a>                is_maximized,
<a href=#230 id=230 data-nosnippet>230</a>                is_minimized,
<a href=#231 id=231 data-nosnippet>231</a>                is_fullscreen,
<a href=#232 id=232 data-nosnippet>232</a>                position: (position.x, position.y),
<a href=#233 id=233 data-nosnippet>233</a>                size: (size.width, size.height),
<a href=#234 id=234 data-nosnippet>234</a>            }))
<a href=#235 id=235 data-nosnippet>235</a>        } <span class="kw">else </span>{
<a href=#236 id=236 data-nosnippet>236</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>)
<a href=#237 id=237 data-nosnippet>237</a>        }
<a href=#238 id=238 data-nosnippet>238</a>    }
<a href=#239 id=239 data-nosnippet>239</a>
<a href=#240 id=240 data-nosnippet>240</a>    <span class="doccomment">/// 获取所有窗口信息
<a href=#241 id=241 data-nosnippet>241</a>    </span><span class="kw">pub fn </span>get_all_windows(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;WindowInfo&gt;&gt; {
<a href=#242 id=242 data-nosnippet>242</a>        <span class="kw">let </span><span class="kw-2">mut </span>windows = Vec::new();
<a href=#243 id=243 data-nosnippet>243</a>        
<a href=#244 id=244 data-nosnippet>244</a>        <span class="kw">for </span>label <span class="kw">in </span><span class="self">self</span>.window_configs.keys() {
<a href=#245 id=245 data-nosnippet>245</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(info)) = <span class="self">self</span>.get_window_info(label) {
<a href=#246 id=246 data-nosnippet>246</a>                windows.push(info);
<a href=#247 id=247 data-nosnippet>247</a>            }
<a href=#248 id=248 data-nosnippet>248</a>        }
<a href=#249 id=249 data-nosnippet>249</a>        
<a href=#250 id=250 data-nosnippet>250</a>        <span class="prelude-val">Ok</span>(windows)
<a href=#251 id=251 data-nosnippet>251</a>    }
<a href=#252 id=252 data-nosnippet>252</a>
<a href=#253 id=253 data-nosnippet>253</a>    <span class="doccomment">/// 设置窗口事件监听
<a href=#254 id=254 data-nosnippet>254</a>    </span><span class="kw">fn </span>setup_window_events(<span class="kw-2">&amp;</span><span class="self">self</span>, window: <span class="kw-2">&amp;</span>Window) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#255 id=255 data-nosnippet>255</a>        <span class="kw">let </span>window_label = window.label().to_string();
<a href=#256 id=256 data-nosnippet>256</a>        <span class="kw">let </span>app_handle = <span class="self">self</span>.app_handle.clone();
<a href=#257 id=257 data-nosnippet>257</a>        
<a href=#258 id=258 data-nosnippet>258</a>        window.on_window_event(<span class="kw">move </span>|event| {
<a href=#259 id=259 data-nosnippet>259</a>            <span class="kw">match </span>event {
<a href=#260 id=260 data-nosnippet>260</a>                tauri::WindowEvent::Focused(focused) =&gt; {
<a href=#261 id=261 data-nosnippet>261</a>                    <span class="kw">let _ </span>= app_handle.emit_all(<span class="string">"window-focus-changed"</span>, (window_label.clone(), <span class="kw-2">*</span>focused));
<a href=#262 id=262 data-nosnippet>262</a>                }
<a href=#263 id=263 data-nosnippet>263</a>                tauri::WindowEvent::Resized(size) =&gt; {
<a href=#264 id=264 data-nosnippet>264</a>                    <span class="kw">let _ </span>= app_handle.emit_all(<span class="string">"window-resized"</span>, (window_label.clone(), size));
<a href=#265 id=265 data-nosnippet>265</a>                }
<a href=#266 id=266 data-nosnippet>266</a>                tauri::WindowEvent::Moved(position) =&gt; {
<a href=#267 id=267 data-nosnippet>267</a>                    <span class="kw">let _ </span>= app_handle.emit_all(<span class="string">"window-moved"</span>, (window_label.clone(), position));
<a href=#268 id=268 data-nosnippet>268</a>                }
<a href=#269 id=269 data-nosnippet>269</a>                tauri::WindowEvent::CloseRequested { api, .. } =&gt; {
<a href=#270 id=270 data-nosnippet>270</a>                    <span class="comment">// 可以在这里添加关闭确认逻辑
<a href=#271 id=271 data-nosnippet>271</a>                    </span><span class="kw">let _ </span>= app_handle.emit_all(<span class="string">"window-close-requested"</span>, window_label.clone());
<a href=#272 id=272 data-nosnippet>272</a>                }
<a href=#273 id=273 data-nosnippet>273</a>                <span class="kw">_ </span>=&gt; {}
<a href=#274 id=274 data-nosnippet>274</a>            }
<a href=#275 id=275 data-nosnippet>275</a>        });
<a href=#276 id=276 data-nosnippet>276</a>        
<a href=#277 id=277 data-nosnippet>277</a>        <span class="prelude-val">Ok</span>(())
<a href=#278 id=278 data-nosnippet>278</a>    }
<a href=#279 id=279 data-nosnippet>279</a>
<a href=#280 id=280 data-nosnippet>280</a>    <span class="doccomment">/// 创建预定义窗口
<a href=#281 id=281 data-nosnippet>281</a>    </span><span class="kw">pub fn </span>create_settings_window(<span class="kw-2">&amp;mut </span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Window&gt; {
<a href=#282 id=282 data-nosnippet>282</a>        <span class="kw">let </span>config = WindowConfig {
<a href=#283 id=283 data-nosnippet>283</a>            label: <span class="string">"settings"</span>.to_string(),
<a href=#284 id=284 data-nosnippet>284</a>            title: <span class="string">"设置"</span>.to_string(),
<a href=#285 id=285 data-nosnippet>285</a>            url: <span class="string">"#/settings"</span>.to_string(),
<a href=#286 id=286 data-nosnippet>286</a>            width: <span class="number">600.0</span>,
<a href=#287 id=287 data-nosnippet>287</a>            height: <span class="number">500.0</span>,
<a href=#288 id=288 data-nosnippet>288</a>            min_width: <span class="prelude-val">Some</span>(<span class="number">500.0</span>),
<a href=#289 id=289 data-nosnippet>289</a>            min_height: <span class="prelude-val">Some</span>(<span class="number">400.0</span>),
<a href=#290 id=290 data-nosnippet>290</a>            center: <span class="bool-val">true</span>,
<a href=#291 id=291 data-nosnippet>291</a>            ..Default::default()
<a href=#292 id=292 data-nosnippet>292</a>        };
<a href=#293 id=293 data-nosnippet>293</a>        
<a href=#294 id=294 data-nosnippet>294</a>        <span class="self">self</span>.create_window(config)
<a href=#295 id=295 data-nosnippet>295</a>    }
<a href=#296 id=296 data-nosnippet>296</a>
<a href=#297 id=297 data-nosnippet>297</a>    <span class="kw">pub fn </span>create_about_window(<span class="kw-2">&amp;mut </span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Window&gt; {
<a href=#298 id=298 data-nosnippet>298</a>        <span class="kw">let </span>config = WindowConfig {
<a href=#299 id=299 data-nosnippet>299</a>            label: <span class="string">"about"</span>.to_string(),
<a href=#300 id=300 data-nosnippet>300</a>            title: <span class="string">"关于"</span>.to_string(),
<a href=#301 id=301 data-nosnippet>301</a>            url: <span class="string">"#/about"</span>.to_string(),
<a href=#302 id=302 data-nosnippet>302</a>            width: <span class="number">400.0</span>,
<a href=#303 id=303 data-nosnippet>303</a>            height: <span class="number">300.0</span>,
<a href=#304 id=304 data-nosnippet>304</a>            resizable: <span class="bool-val">false</span>,
<a href=#305 id=305 data-nosnippet>305</a>            maximizable: <span class="bool-val">false</span>,
<a href=#306 id=306 data-nosnippet>306</a>            center: <span class="bool-val">true</span>,
<a href=#307 id=307 data-nosnippet>307</a>            ..Default::default()
<a href=#308 id=308 data-nosnippet>308</a>        };
<a href=#309 id=309 data-nosnippet>309</a>        
<a href=#310 id=310 data-nosnippet>310</a>        <span class="self">self</span>.create_window(config)
<a href=#311 id=311 data-nosnippet>311</a>    }
<a href=#312 id=312 data-nosnippet>312</a>
<a href=#313 id=313 data-nosnippet>313</a>    <span class="kw">pub fn </span>create_monitor_window(<span class="kw-2">&amp;mut </span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Window&gt; {
<a href=#314 id=314 data-nosnippet>314</a>        <span class="kw">let </span>config = WindowConfig {
<a href=#315 id=315 data-nosnippet>315</a>            label: <span class="string">"monitor"</span>.to_string(),
<a href=#316 id=316 data-nosnippet>316</a>            title: <span class="string">"系统监控"</span>.to_string(),
<a href=#317 id=317 data-nosnippet>317</a>            url: <span class="string">"#/monitor"</span>.to_string(),
<a href=#318 id=318 data-nosnippet>318</a>            width: <span class="number">1000.0</span>,
<a href=#319 id=319 data-nosnippet>319</a>            height: <span class="number">700.0</span>,
<a href=#320 id=320 data-nosnippet>320</a>            min_width: <span class="prelude-val">Some</span>(<span class="number">800.0</span>),
<a href=#321 id=321 data-nosnippet>321</a>            min_height: <span class="prelude-val">Some</span>(<span class="number">600.0</span>),
<a href=#322 id=322 data-nosnippet>322</a>            center: <span class="bool-val">true</span>,
<a href=#323 id=323 data-nosnippet>323</a>            ..Default::default()
<a href=#324 id=324 data-nosnippet>324</a>        };
<a href=#325 id=325 data-nosnippet>325</a>        
<a href=#326 id=326 data-nosnippet>326</a>        <span class="self">self</span>.create_window(config)
<a href=#327 id=327 data-nosnippet>327</a>    }
<a href=#328 id=328 data-nosnippet>328</a>}
<a href=#329 id=329 data-nosnippet>329</a>
<a href=#330 id=330 data-nosnippet>330</a><span class="comment">// Tauri命令
<a href=#331 id=331 data-nosnippet>331</a></span><span class="attr">#[tauri::command]
<a href=#332 id=332 data-nosnippet>332</a></span><span class="kw">pub fn </span>create_window(
<a href=#333 id=333 data-nosnippet>333</a>    app_handle: AppHandle,
<a href=#334 id=334 data-nosnippet>334</a>    config: WindowConfig,
<a href=#335 id=335 data-nosnippet>335</a>) -&gt; std::result::Result&lt;(), String&gt; {
<a href=#336 id=336 data-nosnippet>336</a>    <span class="kw">let </span><span class="kw-2">mut </span>manager = WindowManager::new(app_handle);
<a href=#337 id=337 data-nosnippet>337</a>    manager.create_window(config).map_err(|e| e.to_string())<span class="question-mark">?</span>;
<a href=#338 id=338 data-nosnippet>338</a>    <span class="prelude-val">Ok</span>(())
<a href=#339 id=339 data-nosnippet>339</a>}
<a href=#340 id=340 data-nosnippet>340</a>
<a href=#341 id=341 data-nosnippet>341</a><span class="attr">#[tauri::command]
<a href=#342 id=342 data-nosnippet>342</a></span><span class="kw">pub fn </span>close_window(
<a href=#343 id=343 data-nosnippet>343</a>    app_handle: AppHandle,
<a href=#344 id=344 data-nosnippet>344</a>    label: String,
<a href=#345 id=345 data-nosnippet>345</a>) -&gt; std::result::Result&lt;(), String&gt; {
<a href=#346 id=346 data-nosnippet>346</a>    <span class="kw">let </span><span class="kw-2">mut </span>manager = WindowManager::new(app_handle);
<a href=#347 id=347 data-nosnippet>347</a>    manager.close_window(<span class="kw-2">&amp;</span>label).map_err(|e| e.to_string())
<a href=#348 id=348 data-nosnippet>348</a>}
<a href=#349 id=349 data-nosnippet>349</a>
<a href=#350 id=350 data-nosnippet>350</a><span class="attr">#[tauri::command]
<a href=#351 id=351 data-nosnippet>351</a></span><span class="kw">pub fn </span>show_window(
<a href=#352 id=352 data-nosnippet>352</a>    app_handle: AppHandle,
<a href=#353 id=353 data-nosnippet>353</a>    label: String,
<a href=#354 id=354 data-nosnippet>354</a>) -&gt; std::result::Result&lt;(), String&gt; {
<a href=#355 id=355 data-nosnippet>355</a>    <span class="kw">let </span>manager = WindowManager::new(app_handle);
<a href=#356 id=356 data-nosnippet>356</a>    manager.show_window(<span class="kw-2">&amp;</span>label).map_err(|e| e.to_string())
<a href=#357 id=357 data-nosnippet>357</a>}
<a href=#358 id=358 data-nosnippet>358</a>
<a href=#359 id=359 data-nosnippet>359</a><span class="attr">#[tauri::command]
<a href=#360 id=360 data-nosnippet>360</a></span><span class="kw">pub fn </span>hide_window(
<a href=#361 id=361 data-nosnippet>361</a>    app_handle: AppHandle,
<a href=#362 id=362 data-nosnippet>362</a>    label: String,
<a href=#363 id=363 data-nosnippet>363</a>) -&gt; std::result::Result&lt;(), String&gt; {
<a href=#364 id=364 data-nosnippet>364</a>    <span class="kw">let </span>manager = WindowManager::new(app_handle);
<a href=#365 id=365 data-nosnippet>365</a>    manager.hide_window(<span class="kw-2">&amp;</span>label).map_err(|e| e.to_string())
<a href=#366 id=366 data-nosnippet>366</a>}
<a href=#367 id=367 data-nosnippet>367</a>
<a href=#368 id=368 data-nosnippet>368</a><span class="attr">#[tauri::command]
<a href=#369 id=369 data-nosnippet>369</a></span><span class="kw">pub fn </span>get_window_info(
<a href=#370 id=370 data-nosnippet>370</a>    app_handle: AppHandle,
<a href=#371 id=371 data-nosnippet>371</a>    label: String,
<a href=#372 id=372 data-nosnippet>372</a>) -&gt; std::result::Result&lt;<span class="prelude-ty">Option</span>&lt;WindowInfo&gt;, String&gt; {
<a href=#373 id=373 data-nosnippet>373</a>    <span class="kw">let </span>manager = WindowManager::new(app_handle);
<a href=#374 id=374 data-nosnippet>374</a>    manager.get_window_info(<span class="kw-2">&amp;</span>label).map_err(|e| e.to_string())
<a href=#375 id=375 data-nosnippet>375</a>}
<a href=#376 id=376 data-nosnippet>376</a>
<a href=#377 id=377 data-nosnippet>377</a><span class="attr">#[tauri::command]
<a href=#378 id=378 data-nosnippet>378</a></span><span class="kw">pub fn </span>get_all_windows(
<a href=#379 id=379 data-nosnippet>379</a>    app_handle: AppHandle,
<a href=#380 id=380 data-nosnippet>380</a>) -&gt; std::result::Result&lt;Vec&lt;WindowInfo&gt;, String&gt; {
<a href=#381 id=381 data-nosnippet>381</a>    <span class="kw">let </span>manager = WindowManager::new(app_handle);
<a href=#382 id=382 data-nosnippet>382</a>    manager.get_all_windows().map_err(|e| e.to_string())
<a href=#383 id=383 data-nosnippet>383</a>}</code></pre></div></section></main></body></html>