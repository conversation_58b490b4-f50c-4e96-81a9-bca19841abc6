version: '3.8'

services:
  crawler-node:
    build: .
    container_name: weibo-crawler-node
    ports:
      - "8084:8084"
    environment:
      - NODE_ID=crawler_node_001
      - NODE_NAME=爬虫节点001
      - NODE_TYPE=crawler
      - SQLITE_DATABASE_URL=sqlite:/app/data/crawler_node.db
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
      - MASTER_NODE_URL=http://master-node:8080
      - HEARTBEAT_INTERVAL=30
      - MAX_CONCURRENT_TASKS=100
      - REQUEST_TIMEOUT=30
      - RETRY_MAX_ATTEMPTS=3
      - PROXY_POOL_SIZE=50
      - PROXY_HEALTH_CHECK_INTERVAL=300
      - ACCOUNT_POOL_SIZE=20
      - ACCOUNT_ROTATION_INTERVAL=3600
      - LOG_LEVEL=info
      - LOG_FILE=/app/logs/crawler_node.log
    volumes:
      - crawler_data:/app/data
      - crawler_logs:/app/logs
    depends_on:
      - redis
      - rabbitmq
    restart: unless-stopped
    networks:
      - weibo-network

  redis:
    image: redis:7-alpine
    container_name: weibo-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - weibo-network

  rabbitmq:
    image: rabbitmq:3.12-management
    container_name: weibo-rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=password
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    restart: unless-stopped
    networks:
      - weibo-network

volumes:
  crawler_data:
  crawler_logs:
  redis_data:
  rabbitmq_data:

networks:
  weibo-network:
    driver: bridge
