{"rustc": 1842507548689473721, "features": "[\"chrono\", \"default\", \"rt-tokio\"]", "declared_features": "[\"actix-rt\", \"async-std\", \"chrono\", \"default\", \"only-for-docs-rs\", \"rt-actix\", \"rt-async-std\", \"rt-tokio\"]", "target": 17092474573756926029, "profile": 15657897354478470176, "path": 3942210427294595234, "deps": [[2283771217451780507, "serde_with", false, 17654187646144666733], [2644515958598432451, "dirs", false, 11066231654282401233], [2706460456408817945, "futures", false, 10470981809282521867], [5986029879202738730, "log", false, 13216658706447473378], [7626844547088148622, "build_script_build", false, 402048014862467254], [8008191657135824715, "thiserror", false, 5374037758448816721], [8569119365930580996, "serde_json", false, 3777298738341353814], [9689903380558560274, "serde", false, 4389908846715814937], [9897246384292347999, "chrono", false, 8524163041540453310], [11903278875415370753, "itertools", false, 8620271989780121153], [12944427623413450645, "tokio", false, 13941386680418085679], [15822952346600863224, "strong", false, 12832096568547200923], [16973251432615581304, "tokio_stream", false, 2949582084737634361], [17205548227036139086, "zip", false, 10489907256285052659], [17282734725213053079, "base64", false, 6304729949308084380], [17605717126308396068, "paste", false, 265303868298634720]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\playwright-e8332cd5c9cbbcde\\dep-lib-playwright", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}