use crate::error::{AppError, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Window, WindowBuilder, WindowUrl};
use tracing::{info};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowConfig {
    pub label: String,
    pub title: String,
    pub url: String,
    pub width: f64,
    pub height: f64,
    pub min_width: Option<f64>,
    pub min_height: Option<f64>,
    pub max_width: Option<f64>,
    pub max_height: Option<f64>,
    pub resizable: bool,
    pub maximizable: bool,
    pub minimizable: bool,
    pub closable: bool,
    pub decorations: bool,
    pub always_on_top: bool,
    pub center: bool,
    pub fullscreen: bool,
    pub transparent: bool,
    pub visible: bool,
}

impl Default for WindowConfig {
    fn default() -> Self {
        Self {
            label: "window".to_string(),
            title: "Window".to_string(),
            url: "/".to_string(),
            width: 800.0,
            height: 600.0,
            min_width: Some(400.0),
            min_height: Some(300.0),
            max_width: None,
            max_height: None,
            resizable: true,
            maximizable: true,
            minimizable: true,
            closable: true,
            decorations: true,
            always_on_top: false,
            center: true,
            fullscreen: false,
            transparent: false,
            visible: true,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WindowInfo {
    pub label: String,
    pub title: String,
    pub url: String,
    pub is_visible: bool,
    pub is_focused: bool,
    pub is_maximized: bool,
    pub is_minimized: bool,
    pub is_fullscreen: bool,
    pub position: (i32, i32),
    pub size: (u32, u32),
}

pub struct WindowManager {
    app_handle: AppHandle,
    window_configs: HashMap<String, WindowConfig>,
}

impl WindowManager {
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            app_handle,
            window_configs: HashMap::new(),
        }
    }

    /// 创建新窗口
    pub fn create_window(&mut self, config: WindowConfig) -> Result<Window> {
        let mut builder = WindowBuilder::new(
            &self.app_handle,
            &config.label,
            WindowUrl::App(config.url.parse().unwrap())
        )
        .title(&config.title)
        .inner_size(config.width, config.height)
        .resizable(config.resizable)
        .maximizable(config.maximizable)
        .minimizable(config.minimizable)
        .closable(config.closable)
        .decorations(config.decorations)
        .always_on_top(config.always_on_top)
        .center()
        .fullscreen(config.fullscreen)
        .transparent(config.transparent)
        .visible(config.visible);

        if let Some(min_width) = config.min_width {
            if let Some(min_height) = config.min_height {
                builder = builder.min_inner_size(min_width, min_height);
            }
        }

        if let Some(max_width) = config.max_width {
            if let Some(max_height) = config.max_height {
                builder = builder.max_inner_size(max_width, max_height);
            }
        }

        let window = builder.build()
            .map_err(|e| AppError::Other(format!("创建窗口失败: {}", e)))?;

        // 保存窗口配置
        self.window_configs.insert(config.label.clone(), config.clone());

        // 设置窗口事件监听
        self.setup_window_events(&window)?;

        info!("创建窗口: {}", config.label);
        Ok(window)
    }

    /// 获取窗口
    pub fn get_window(&self, label: &str) -> Option<Window> {
        self.app_handle.get_window(label)
    }

    /// 关闭窗口
    pub fn close_window(&mut self, label: &str) -> Result<()> {
        if let Some(window) = self.get_window(label) {
            window.close().map_err(|e| AppError::Other(format!("关闭窗口失败: {}", e)))?;
            self.window_configs.remove(label);
            info!("关闭窗口: {}", label);
        }
        Ok(())
    }

    /// 显示窗口
    pub fn show_window(&self, label: &str) -> Result<()> {
        if let Some(window) = self.get_window(label) {
            window.show().map_err(|e| AppError::Other(format!("显示窗口失败: {}", e)))?;
            window.set_focus().map_err(|e| AppError::Other(format!("设置窗口焦点失败: {}", e)))?;
            info!("显示窗口: {}", label);
        }
        Ok(())
    }

    /// 隐藏窗口
    pub fn hide_window(&self, label: &str) -> Result<()> {
        if let Some(window) = self.get_window(label) {
            window.hide().map_err(|e| AppError::Other(format!("隐藏窗口失败: {}", e)))?;
            info!("隐藏窗口: {}", label);
        }
        Ok(())
    }

    /// 最大化窗口
    pub fn maximize_window(&self, label: &str) -> Result<()> {
        if let Some(window) = self.get_window(label) {
            window.maximize().map_err(|e| AppError::Other(format!("最大化窗口失败: {}", e)))?;
            info!("最大化窗口: {}", label);
        }
        Ok(())
    }

    /// 最小化窗口
    pub fn minimize_window(&self, label: &str) -> Result<()> {
        if let Some(window) = self.get_window(label) {
            window.minimize().map_err(|e| AppError::Other(format!("最小化窗口失败: {}", e)))?;
            info!("最小化窗口: {}", label);
        }
        Ok(())
    }

    /// 切换窗口全屏状态
    pub fn toggle_fullscreen(&self, label: &str) -> Result<()> {
        if let Some(window) = self.get_window(label) {
            let is_fullscreen = window.is_fullscreen()
                .map_err(|e| AppError::Other(format!("获取窗口全屏状态失败: {}", e)))?;

            window.set_fullscreen(!is_fullscreen)
                .map_err(|e| AppError::Other(format!("切换窗口全屏状态失败: {}", e)))?;

            info!("切换窗口全屏状态: {} -> {}", label, !is_fullscreen);
        }
        Ok(())
    }

    /// 获取窗口信息
    pub fn get_window_info(&self, label: &str) -> Result<Option<WindowInfo>> {
        if let Some(window) = self.get_window(label) {
            let title = window.title()
                .map_err(|e| AppError::Other(format!("获取窗口标题失败: {}", e)))?;
            
            let is_visible = window.is_visible()
                .map_err(|e| AppError::Other(format!("获取窗口可见性失败: {}", e)))?;
            
            let is_focused = window.is_focused()
                .map_err(|e| AppError::Other(format!("获取窗口焦点状态失败: {}", e)))?;
            
            let is_maximized = window.is_maximized()
                .map_err(|e| AppError::Other(format!("获取窗口最大化状态失败: {}", e)))?;
            
            let is_minimized = window.is_minimized()
                .map_err(|e| AppError::Other(format!("获取窗口最小化状态失败: {}", e)))?;
            
            let is_fullscreen = window.is_fullscreen()
                .map_err(|e| AppError::Other(format!("获取窗口全屏状态失败: {}", e)))?;
            
            let position = window.outer_position()
                .map_err(|e| AppError::Other(format!("获取窗口位置失败: {}", e)))?;
            
            let size = window.outer_size()
                .map_err(|e| AppError::Other(format!("获取窗口大小失败: {}", e)))?;

            Ok(Some(WindowInfo {
                label: label.to_string(),
                title,
                url: self.window_configs.get(label)
                    .map(|c| c.url.clone())
                    .unwrap_or_default(),
                is_visible,
                is_focused,
                is_maximized,
                is_minimized,
                is_fullscreen,
                position: (position.x, position.y),
                size: (size.width, size.height),
            }))
        } else {
            Ok(None)
        }
    }

    /// 获取所有窗口信息
    pub fn get_all_windows(&self) -> Result<Vec<WindowInfo>> {
        let mut windows = Vec::new();
        
        for label in self.window_configs.keys() {
            if let Ok(Some(info)) = self.get_window_info(label) {
                windows.push(info);
            }
        }
        
        Ok(windows)
    }

    /// 设置窗口事件监听
    fn setup_window_events(&self, window: &Window) -> Result<()> {
        let window_label = window.label().to_string();
        let app_handle = self.app_handle.clone();
        
        window.on_window_event(move |event| {
            match event {
                tauri::WindowEvent::Focused(focused) => {
                    let _ = app_handle.emit_all("window-focus-changed", (window_label.clone(), *focused));
                }
                tauri::WindowEvent::Resized(size) => {
                    let _ = app_handle.emit_all("window-resized", (window_label.clone(), size));
                }
                tauri::WindowEvent::Moved(position) => {
                    let _ = app_handle.emit_all("window-moved", (window_label.clone(), position));
                }
                tauri::WindowEvent::CloseRequested { api, .. } => {
                    // 可以在这里添加关闭确认逻辑
                    let _ = app_handle.emit_all("window-close-requested", window_label.clone());
                }
                _ => {}
            }
        });
        
        Ok(())
    }

    /// 创建预定义窗口
    pub fn create_settings_window(&mut self) -> Result<Window> {
        let config = WindowConfig {
            label: "settings".to_string(),
            title: "设置".to_string(),
            url: "#/settings".to_string(),
            width: 600.0,
            height: 500.0,
            min_width: Some(500.0),
            min_height: Some(400.0),
            center: true,
            ..Default::default()
        };
        
        self.create_window(config)
    }

    pub fn create_about_window(&mut self) -> Result<Window> {
        let config = WindowConfig {
            label: "about".to_string(),
            title: "关于".to_string(),
            url: "#/about".to_string(),
            width: 400.0,
            height: 300.0,
            resizable: false,
            maximizable: false,
            center: true,
            ..Default::default()
        };
        
        self.create_window(config)
    }

    pub fn create_monitor_window(&mut self) -> Result<Window> {
        let config = WindowConfig {
            label: "monitor".to_string(),
            title: "系统监控".to_string(),
            url: "#/monitor".to_string(),
            width: 1000.0,
            height: 700.0,
            min_width: Some(800.0),
            min_height: Some(600.0),
            center: true,
            ..Default::default()
        };
        
        self.create_window(config)
    }
}

// Tauri命令
#[tauri::command]
pub fn create_window(
    app_handle: AppHandle,
    config: WindowConfig,
) -> std::result::Result<(), String> {
    let mut manager = WindowManager::new(app_handle);
    manager.create_window(config).map_err(|e| e.to_string())?;
    Ok(())
}

#[tauri::command]
pub fn close_window(
    app_handle: AppHandle,
    label: String,
) -> std::result::Result<(), String> {
    let mut manager = WindowManager::new(app_handle);
    manager.close_window(&label).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn show_window(
    app_handle: AppHandle,
    label: String,
) -> std::result::Result<(), String> {
    let manager = WindowManager::new(app_handle);
    manager.show_window(&label).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn hide_window(
    app_handle: AppHandle,
    label: String,
) -> std::result::Result<(), String> {
    let manager = WindowManager::new(app_handle);
    manager.hide_window(&label).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_window_info(
    app_handle: AppHandle,
    label: String,
) -> std::result::Result<Option<WindowInfo>, String> {
    let manager = WindowManager::new(app_handle);
    manager.get_window_info(&label).map_err(|e| e.to_string())
}

#[tauri::command]
pub fn get_all_windows(
    app_handle: AppHandle,
) -> std::result::Result<Vec<WindowInfo>, String> {
    let manager = WindowManager::new(app_handle);
    manager.get_all_windows().map_err(|e| e.to_string())
}
