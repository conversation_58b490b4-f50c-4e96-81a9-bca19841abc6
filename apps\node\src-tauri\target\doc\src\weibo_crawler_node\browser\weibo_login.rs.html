<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\browser\weibo_login.rs`."><title>weibo_login.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node\browser/</div>weibo_login.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{<span class="prelude-ty">Result</span>, AppError};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>playwright::{Playwright, api::{page::Page, browser::Browser, browser_context::BrowserContext}};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>tracing::{info, warn, error, debug};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::time::Duration;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>tokio::time::sleep;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use super</span>::{BrowserConfig, LoginCredentials, LoginResult, UserInfo};
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">const </span>WEIBO_LOGIN_URL: <span class="kw-2">&amp;</span>str = <span class="string">"https://s.weibo.com/"</span>;
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">const </span>LOGIN_TIMEOUT: u64 = <span class="number">30000</span>; <span class="comment">// 30秒
<a href=#10 id=10 data-nosnippet>10</a>
<a href=#11 id=11 data-nosnippet>11</a></span><span class="kw">pub async fn </span>login_with_playwright(
<a href=#12 id=12 data-nosnippet>12</a>    playwright: <span class="kw-2">&amp;</span>Playwright,
<a href=#13 id=13 data-nosnippet>13</a>    config: <span class="kw-2">&amp;</span>BrowserConfig,
<a href=#14 id=14 data-nosnippet>14</a>    credentials: LoginCredentials,
<a href=#15 id=15 data-nosnippet>15</a>) -&gt; <span class="prelude-ty">Result</span>&lt;LoginResult&gt; {
<a href=#16 id=16 data-nosnippet>16</a>    <span class="macro">info!</span>(<span class="string">"开始微博登录流程，用户名: {}"</span>, credentials.username);
<a href=#17 id=17 data-nosnippet>17</a>
<a href=#18 id=18 data-nosnippet>18</a>    <span class="comment">// 启动浏览器
<a href=#19 id=19 data-nosnippet>19</a>    </span><span class="kw">let </span>chromium = playwright.chromium();
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">let </span>browser = chromium
<a href=#21 id=21 data-nosnippet>21</a>        .launcher()
<a href=#22 id=22 data-nosnippet>22</a>        .headless(config.headless)
<a href=#23 id=23 data-nosnippet>23</a>        .launch()
<a href=#24 id=24 data-nosnippet>24</a>        .<span class="kw">await
<a href=#25 id=25 data-nosnippet>25</a>        </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"启动浏览器失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#26 id=26 data-nosnippet>26</a>
<a href=#27 id=27 data-nosnippet>27</a>    <span class="kw">let </span>result = <span class="kw">async </span>{
<a href=#28 id=28 data-nosnippet>28</a>        <span class="comment">// 创建浏览器上下文
<a href=#29 id=29 data-nosnippet>29</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>context_builder = browser.context_builder();
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(user_agent) = <span class="kw-2">&amp;</span>config.user_agent {
<a href=#32 id=32 data-nosnippet>32</a>            context_builder = context_builder.user_agent(user_agent);
<a href=#33 id=33 data-nosnippet>33</a>        }
<a href=#34 id=34 data-nosnippet>34</a>
<a href=#35 id=35 data-nosnippet>35</a>        <span class="kw">let </span>context = context_builder
<a href=#36 id=36 data-nosnippet>36</a>            .viewport(config.viewport_width, config.viewport_height)
<a href=#37 id=37 data-nosnippet>37</a>            .build()
<a href=#38 id=38 data-nosnippet>38</a>            .<span class="kw">await
<a href=#39 id=39 data-nosnippet>39</a>            </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"创建浏览器上下文失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a>        <span class="comment">// 创建新页面
<a href=#42 id=42 data-nosnippet>42</a>        </span><span class="kw">let </span>page = context
<a href=#43 id=43 data-nosnippet>43</a>            .new_page()
<a href=#44 id=44 data-nosnippet>44</a>            .<span class="kw">await
<a href=#45 id=45 data-nosnippet>45</a>            </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"创建页面失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#46 id=46 data-nosnippet>46</a>
<a href=#47 id=47 data-nosnippet>47</a>        <span class="comment">// 执行登录流程
<a href=#48 id=48 data-nosnippet>48</a>        </span>perform_login(<span class="kw-2">&amp;</span>page, config, credentials).<span class="kw">await
<a href=#49 id=49 data-nosnippet>49</a>    </span>}.<span class="kw">await</span>;
<a href=#50 id=50 data-nosnippet>50</a>
<a href=#51 id=51 data-nosnippet>51</a>    <span class="comment">// 关闭浏览器
<a href=#52 id=52 data-nosnippet>52</a>    </span><span class="kw">if let </span><span class="prelude-val">Err</span>(e) = browser.close().<span class="kw">await </span>{
<a href=#53 id=53 data-nosnippet>53</a>        <span class="macro">warn!</span>(<span class="string">"关闭浏览器时出错: {}"</span>, e);
<a href=#54 id=54 data-nosnippet>54</a>    }
<a href=#55 id=55 data-nosnippet>55</a>
<a href=#56 id=56 data-nosnippet>56</a>    result
<a href=#57 id=57 data-nosnippet>57</a>}
<a href=#58 id=58 data-nosnippet>58</a>
<a href=#59 id=59 data-nosnippet>59</a><span class="kw">async fn </span>perform_login(
<a href=#60 id=60 data-nosnippet>60</a>    page: <span class="kw-2">&amp;</span>Page,
<a href=#61 id=61 data-nosnippet>61</a>    config: <span class="kw-2">&amp;</span>BrowserConfig,
<a href=#62 id=62 data-nosnippet>62</a>    credentials: LoginCredentials,
<a href=#63 id=63 data-nosnippet>63</a>) -&gt; <span class="prelude-ty">Result</span>&lt;LoginResult&gt; {
<a href=#64 id=64 data-nosnippet>64</a>    <span class="comment">// 导航到微博搜索页面
<a href=#65 id=65 data-nosnippet>65</a>    </span><span class="macro">info!</span>(<span class="string">"导航到微博搜索页面..."</span>);
<a href=#66 id=66 data-nosnippet>66</a>    page.goto_builder(WEIBO_LOGIN_URL)
<a href=#67 id=67 data-nosnippet>67</a>        .timeout(config.timeout <span class="kw">as </span>f64)
<a href=#68 id=68 data-nosnippet>68</a>        .goto()
<a href=#69 id=69 data-nosnippet>69</a>        .<span class="kw">await
<a href=#70 id=70 data-nosnippet>70</a>        </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"导航到微博页面失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#71 id=71 data-nosnippet>71</a>
<a href=#72 id=72 data-nosnippet>72</a>    <span class="comment">// 等待页面加载
<a href=#73 id=73 data-nosnippet>73</a>    </span>sleep(Duration::from_millis(<span class="number">2000</span>)).<span class="kw">await</span>;
<a href=#74 id=74 data-nosnippet>74</a>
<a href=#75 id=75 data-nosnippet>75</a>    <span class="comment">// 检查是否已经登录
<a href=#76 id=76 data-nosnippet>76</a>    </span><span class="kw">if </span>is_already_logged_in(page).<span class="kw">await</span><span class="question-mark">? </span>{
<a href=#77 id=77 data-nosnippet>77</a>        <span class="macro">info!</span>(<span class="string">"检测到已登录状态"</span>);
<a href=#78 id=78 data-nosnippet>78</a>        <span class="kw">return </span>extract_login_info(page).<span class="kw">await</span>;
<a href=#79 id=79 data-nosnippet>79</a>    }
<a href=#80 id=80 data-nosnippet>80</a>
<a href=#81 id=81 data-nosnippet>81</a>    <span class="comment">// 查找并点击登录按钮 
<a href=#82 id=82 data-nosnippet>82</a>    // #searchapps &gt; div &gt; div.Nav_wrap_gHB1a &gt; div &gt; div &gt; div.woo-box-flex.woo-box-alignCenter.woo-box-justifyEnd.Nav_right_pDw0F &gt; div:nth-child(1) &gt; div &gt; a.LoginBtn_btn_10QRY.LoginBtn_btna_1hH9H
<a href=#83 id=83 data-nosnippet>83</a>    </span><span class="macro">info!</span>(<span class="string">"查找登录入口..."</span>);
<a href=#84 id=84 data-nosnippet>84</a>    <span class="kw">let </span>login_selectors = <span class="macro">vec!</span>[
<a href=#85 id=85 data-nosnippet>85</a>        <span class="string">"a[href*='login']"</span>,
<a href=#86 id=86 data-nosnippet>86</a>        <span class="string">".gn_login"</span>,
<a href=#87 id=87 data-nosnippet>87</a>        <span class="string">".login"</span>,
<a href=#88 id=88 data-nosnippet>88</a>        <span class="string">"a:has-text('登录')"</span>,
<a href=#89 id=89 data-nosnippet>89</a>        <span class="string">"button:has-text('登录')"</span>,
<a href=#90 id=90 data-nosnippet>90</a>    ];
<a href=#91 id=91 data-nosnippet>91</a>
<a href=#92 id=92 data-nosnippet>92</a>    <span class="kw">let </span><span class="kw-2">mut </span>login_clicked = <span class="bool-val">false</span>;
<a href=#93 id=93 data-nosnippet>93</a>    <span class="kw">for </span>selector <span class="kw">in </span>login_selectors {
<a href=#94 id=94 data-nosnippet>94</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(<span class="kw">_</span>) = page.click_builder(selector).timeout(<span class="number">1000.0</span>).click().<span class="kw">await </span>{
<a href=#95 id=95 data-nosnippet>95</a>            <span class="macro">info!</span>(<span class="string">"点击登录按钮: {}"</span>, selector);
<a href=#96 id=96 data-nosnippet>96</a>            login_clicked = <span class="bool-val">true</span>;
<a href=#97 id=97 data-nosnippet>97</a>            <span class="kw">break</span>;
<a href=#98 id=98 data-nosnippet>98</a>        }
<a href=#99 id=99 data-nosnippet>99</a>    }
<a href=#100 id=100 data-nosnippet>100</a>
<a href=#101 id=101 data-nosnippet>101</a>    <span class="kw">if </span>!login_clicked {
<a href=#102 id=102 data-nosnippet>102</a>        <span class="kw">return </span><span class="prelude-val">Ok</span>(LoginResult {
<a href=#103 id=103 data-nosnippet>103</a>            success: <span class="bool-val">false</span>,
<a href=#104 id=104 data-nosnippet>104</a>            message: <span class="string">"未找到登录入口"</span>.to_string(),
<a href=#105 id=105 data-nosnippet>105</a>            cookies: <span class="prelude-val">None</span>,
<a href=#106 id=106 data-nosnippet>106</a>            user_info: <span class="prelude-val">None</span>,
<a href=#107 id=107 data-nosnippet>107</a>        });
<a href=#108 id=108 data-nosnippet>108</a>    }
<a href=#109 id=109 data-nosnippet>109</a>
<a href=#110 id=110 data-nosnippet>110</a>    <span class="comment">// 等待登录表单出现
<a href=#111 id=111 data-nosnippet>111</a>    </span>sleep(Duration::from_millis(<span class="number">2000</span>)).<span class="kw">await</span>;
<a href=#112 id=112 data-nosnippet>112</a>
<a href=#113 id=113 data-nosnippet>113</a>    <span class="comment">// 填写用户名
<a href=#114 id=114 data-nosnippet>114</a>    </span><span class="macro">info!</span>(<span class="string">"填写登录信息..."</span>);
<a href=#115 id=115 data-nosnippet>115</a>    <span class="kw">let </span>username_selectors = <span class="macro">vec!</span>[
<a href=#116 id=116 data-nosnippet>116</a>        <span class="string">"input[name='username']"</span>,
<a href=#117 id=117 data-nosnippet>117</a>        <span class="string">"input[placeholder*='手机']"</span>,
<a href=#118 id=118 data-nosnippet>118</a>        <span class="string">"input[placeholder*='邮箱']"</span>,
<a href=#119 id=119 data-nosnippet>119</a>        <span class="string">"input[placeholder*='用户名']"</span>,
<a href=#120 id=120 data-nosnippet>120</a>        <span class="string">"#loginname"</span>,
<a href=#121 id=121 data-nosnippet>121</a>        <span class="string">".username input"</span>,
<a href=#122 id=122 data-nosnippet>122</a>    ];
<a href=#123 id=123 data-nosnippet>123</a>
<a href=#124 id=124 data-nosnippet>124</a>    <span class="kw">let </span><span class="kw-2">mut </span>username_filled = <span class="bool-val">false</span>;
<a href=#125 id=125 data-nosnippet>125</a>    <span class="kw">for </span>selector <span class="kw">in </span>username_selectors {
<a href=#126 id=126 data-nosnippet>126</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(<span class="kw">_</span>) = page.fill_builder(selector, <span class="kw-2">&amp;</span>credentials.username).fill().<span class="kw">await </span>{
<a href=#127 id=127 data-nosnippet>127</a>            <span class="macro">info!</span>(<span class="string">"填写用户名成功: {}"</span>, selector);
<a href=#128 id=128 data-nosnippet>128</a>            username_filled = <span class="bool-val">true</span>;
<a href=#129 id=129 data-nosnippet>129</a>            <span class="kw">break</span>;
<a href=#130 id=130 data-nosnippet>130</a>        }
<a href=#131 id=131 data-nosnippet>131</a>    }
<a href=#132 id=132 data-nosnippet>132</a>
<a href=#133 id=133 data-nosnippet>133</a>    <span class="kw">if </span>!username_filled {
<a href=#134 id=134 data-nosnippet>134</a>        <span class="kw">return </span><span class="prelude-val">Ok</span>(LoginResult {
<a href=#135 id=135 data-nosnippet>135</a>            success: <span class="bool-val">false</span>,
<a href=#136 id=136 data-nosnippet>136</a>            message: <span class="string">"未找到用户名输入框"</span>.to_string(),
<a href=#137 id=137 data-nosnippet>137</a>            cookies: <span class="prelude-val">None</span>,
<a href=#138 id=138 data-nosnippet>138</a>            user_info: <span class="prelude-val">None</span>,
<a href=#139 id=139 data-nosnippet>139</a>        });
<a href=#140 id=140 data-nosnippet>140</a>    }
<a href=#141 id=141 data-nosnippet>141</a>
<a href=#142 id=142 data-nosnippet>142</a>    <span class="comment">// 填写密码
<a href=#143 id=143 data-nosnippet>143</a>    </span><span class="kw">let </span>password_selectors = <span class="macro">vec!</span>[
<a href=#144 id=144 data-nosnippet>144</a>        <span class="string">"input[name='password']"</span>,
<a href=#145 id=145 data-nosnippet>145</a>        <span class="string">"input[type='password']"</span>,
<a href=#146 id=146 data-nosnippet>146</a>        <span class="string">"#password"</span>,
<a href=#147 id=147 data-nosnippet>147</a>        <span class="string">".password input"</span>,
<a href=#148 id=148 data-nosnippet>148</a>    ];
<a href=#149 id=149 data-nosnippet>149</a>
<a href=#150 id=150 data-nosnippet>150</a>    <span class="kw">let </span><span class="kw-2">mut </span>password_filled = <span class="bool-val">false</span>;
<a href=#151 id=151 data-nosnippet>151</a>    <span class="kw">for </span>selector <span class="kw">in </span>password_selectors {
<a href=#152 id=152 data-nosnippet>152</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(<span class="kw">_</span>) = page.fill_builder(selector, <span class="kw-2">&amp;</span>credentials.password).fill().<span class="kw">await </span>{
<a href=#153 id=153 data-nosnippet>153</a>            <span class="macro">info!</span>(<span class="string">"填写密码成功: {}"</span>, selector);
<a href=#154 id=154 data-nosnippet>154</a>            password_filled = <span class="bool-val">true</span>;
<a href=#155 id=155 data-nosnippet>155</a>            <span class="kw">break</span>;
<a href=#156 id=156 data-nosnippet>156</a>        }
<a href=#157 id=157 data-nosnippet>157</a>    }
<a href=#158 id=158 data-nosnippet>158</a>
<a href=#159 id=159 data-nosnippet>159</a>    <span class="kw">if </span>!password_filled {
<a href=#160 id=160 data-nosnippet>160</a>        <span class="kw">return </span><span class="prelude-val">Ok</span>(LoginResult {
<a href=#161 id=161 data-nosnippet>161</a>            success: <span class="bool-val">false</span>,
<a href=#162 id=162 data-nosnippet>162</a>            message: <span class="string">"未找到密码输入框"</span>.to_string(),
<a href=#163 id=163 data-nosnippet>163</a>            cookies: <span class="prelude-val">None</span>,
<a href=#164 id=164 data-nosnippet>164</a>            user_info: <span class="prelude-val">None</span>,
<a href=#165 id=165 data-nosnippet>165</a>        });
<a href=#166 id=166 data-nosnippet>166</a>    }
<a href=#167 id=167 data-nosnippet>167</a>
<a href=#168 id=168 data-nosnippet>168</a>    <span class="comment">// 点击登录按钮
<a href=#169 id=169 data-nosnippet>169</a>    </span><span class="kw">let </span>submit_selectors = <span class="macro">vec!</span>[
<a href=#170 id=170 data-nosnippet>170</a>        <span class="string">"button[type='submit']"</span>,
<a href=#171 id=171 data-nosnippet>171</a>        <span class="string">"input[type='submit']"</span>,
<a href=#172 id=172 data-nosnippet>172</a>        <span class="string">".btn_login"</span>,
<a href=#173 id=173 data-nosnippet>173</a>        <span class="string">"button:has-text('登录')"</span>,
<a href=#174 id=174 data-nosnippet>174</a>        <span class="string">"a:has-text('登录')"</span>,
<a href=#175 id=175 data-nosnippet>175</a>    ];
<a href=#176 id=176 data-nosnippet>176</a>
<a href=#177 id=177 data-nosnippet>177</a>    <span class="kw">let </span><span class="kw-2">mut </span>submit_clicked = <span class="bool-val">false</span>;
<a href=#178 id=178 data-nosnippet>178</a>    <span class="kw">for </span>selector <span class="kw">in </span>submit_selectors {
<a href=#179 id=179 data-nosnippet>179</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(<span class="kw">_</span>) = page.click_builder(selector).timeout(<span class="number">1000.0</span>).click().<span class="kw">await </span>{
<a href=#180 id=180 data-nosnippet>180</a>            <span class="macro">info!</span>(<span class="string">"点击登录提交按钮: {}"</span>, selector);
<a href=#181 id=181 data-nosnippet>181</a>            submit_clicked = <span class="bool-val">true</span>;
<a href=#182 id=182 data-nosnippet>182</a>            <span class="kw">break</span>;
<a href=#183 id=183 data-nosnippet>183</a>        }
<a href=#184 id=184 data-nosnippet>184</a>    }
<a href=#185 id=185 data-nosnippet>185</a>
<a href=#186 id=186 data-nosnippet>186</a>    <span class="kw">if </span>!submit_clicked {
<a href=#187 id=187 data-nosnippet>187</a>        <span class="kw">return </span><span class="prelude-val">Ok</span>(LoginResult {
<a href=#188 id=188 data-nosnippet>188</a>            success: <span class="bool-val">false</span>,
<a href=#189 id=189 data-nosnippet>189</a>            message: <span class="string">"未找到登录提交按钮"</span>.to_string(),
<a href=#190 id=190 data-nosnippet>190</a>            cookies: <span class="prelude-val">None</span>,
<a href=#191 id=191 data-nosnippet>191</a>            user_info: <span class="prelude-val">None</span>,
<a href=#192 id=192 data-nosnippet>192</a>        });
<a href=#193 id=193 data-nosnippet>193</a>    }
<a href=#194 id=194 data-nosnippet>194</a>
<a href=#195 id=195 data-nosnippet>195</a>    <span class="comment">// 等待登录结果
<a href=#196 id=196 data-nosnippet>196</a>    </span><span class="macro">info!</span>(<span class="string">"等待登录结果..."</span>);
<a href=#197 id=197 data-nosnippet>197</a>    sleep(Duration::from_millis(<span class="number">3000</span>)).<span class="kw">await</span>;
<a href=#198 id=198 data-nosnippet>198</a>
<a href=#199 id=199 data-nosnippet>199</a>    <span class="comment">// 检查是否需要验证码
<a href=#200 id=200 data-nosnippet>200</a>    </span><span class="kw">if </span>is_captcha_required(page).<span class="kw">await</span><span class="question-mark">? </span>{
<a href=#201 id=201 data-nosnippet>201</a>        <span class="kw">return </span><span class="prelude-val">Ok</span>(LoginResult {
<a href=#202 id=202 data-nosnippet>202</a>            success: <span class="bool-val">false</span>,
<a href=#203 id=203 data-nosnippet>203</a>            message: <span class="string">"需要验证码，请手动完成登录"</span>.to_string(),
<a href=#204 id=204 data-nosnippet>204</a>            cookies: <span class="prelude-val">None</span>,
<a href=#205 id=205 data-nosnippet>205</a>            user_info: <span class="prelude-val">None</span>,
<a href=#206 id=206 data-nosnippet>206</a>        });
<a href=#207 id=207 data-nosnippet>207</a>    }
<a href=#208 id=208 data-nosnippet>208</a>
<a href=#209 id=209 data-nosnippet>209</a>    <span class="comment">// 检查登录是否成功
<a href=#210 id=210 data-nosnippet>210</a>    </span><span class="kw">if </span>is_login_successful(page).<span class="kw">await</span><span class="question-mark">? </span>{
<a href=#211 id=211 data-nosnippet>211</a>        <span class="macro">info!</span>(<span class="string">"登录成功"</span>);
<a href=#212 id=212 data-nosnippet>212</a>        extract_login_info(page).<span class="kw">await
<a href=#213 id=213 data-nosnippet>213</a>    </span>} <span class="kw">else </span>{
<a href=#214 id=214 data-nosnippet>214</a>        <span class="kw">let </span>error_msg = get_login_error_message(page).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#215 id=215 data-nosnippet>215</a>        <span class="prelude-val">Ok</span>(LoginResult {
<a href=#216 id=216 data-nosnippet>216</a>            success: <span class="bool-val">false</span>,
<a href=#217 id=217 data-nosnippet>217</a>            message: error_msg,
<a href=#218 id=218 data-nosnippet>218</a>            cookies: <span class="prelude-val">None</span>,
<a href=#219 id=219 data-nosnippet>219</a>            user_info: <span class="prelude-val">None</span>,
<a href=#220 id=220 data-nosnippet>220</a>        })
<a href=#221 id=221 data-nosnippet>221</a>    }
<a href=#222 id=222 data-nosnippet>222</a>}
<a href=#223 id=223 data-nosnippet>223</a>
<a href=#224 id=224 data-nosnippet>224</a><span class="kw">async fn </span>is_already_logged_in(page: <span class="kw-2">&amp;</span>Page) -&gt; <span class="prelude-ty">Result</span>&lt;bool&gt; {
<a href=#225 id=225 data-nosnippet>225</a>    <span class="comment">// 检查是否存在用户信息相关元素
<a href=#226 id=226 data-nosnippet>226</a>    </span><span class="kw">let </span>logged_in_selectors = <span class="macro">vec!</span>[
<a href=#227 id=227 data-nosnippet>227</a>        <span class="string">".gn_name"</span>,
<a href=#228 id=228 data-nosnippet>228</a>        <span class="string">".user-info"</span>,
<a href=#229 id=229 data-nosnippet>229</a>        <span class="string">"a[href*='u/']"</span>,
<a href=#230 id=230 data-nosnippet>230</a>        <span class="string">".avatar"</span>,
<a href=#231 id=231 data-nosnippet>231</a>    ];
<a href=#232 id=232 data-nosnippet>232</a>
<a href=#233 id=233 data-nosnippet>233</a>    <span class="kw">for </span>selector <span class="kw">in </span>logged_in_selectors {
<a href=#234 id=234 data-nosnippet>234</a>        <span class="kw">if </span>page.query_selector(selector).<span class="kw">await</span>.is_ok() {
<a href=#235 id=235 data-nosnippet>235</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="bool-val">true</span>);
<a href=#236 id=236 data-nosnippet>236</a>        }
<a href=#237 id=237 data-nosnippet>237</a>    }
<a href=#238 id=238 data-nosnippet>238</a>
<a href=#239 id=239 data-nosnippet>239</a>    <span class="prelude-val">Ok</span>(<span class="bool-val">false</span>)
<a href=#240 id=240 data-nosnippet>240</a>}
<a href=#241 id=241 data-nosnippet>241</a>
<a href=#242 id=242 data-nosnippet>242</a><span class="kw">async fn </span>is_captcha_required(page: <span class="kw-2">&amp;</span>Page) -&gt; <span class="prelude-ty">Result</span>&lt;bool&gt; {
<a href=#243 id=243 data-nosnippet>243</a>    <span class="kw">let </span>captcha_selectors = <span class="macro">vec!</span>[
<a href=#244 id=244 data-nosnippet>244</a>        <span class="string">".captcha"</span>,
<a href=#245 id=245 data-nosnippet>245</a>        <span class="string">".verify"</span>,
<a href=#246 id=246 data-nosnippet>246</a>        <span class="string">"img[src*='captcha']"</span>,
<a href=#247 id=247 data-nosnippet>247</a>        <span class="string">".verification"</span>,
<a href=#248 id=248 data-nosnippet>248</a>    ];
<a href=#249 id=249 data-nosnippet>249</a>
<a href=#250 id=250 data-nosnippet>250</a>    <span class="kw">for </span>selector <span class="kw">in </span>captcha_selectors {
<a href=#251 id=251 data-nosnippet>251</a>        <span class="kw">if </span>page.query_selector(selector).<span class="kw">await</span>.is_ok() {
<a href=#252 id=252 data-nosnippet>252</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="bool-val">true</span>);
<a href=#253 id=253 data-nosnippet>253</a>        }
<a href=#254 id=254 data-nosnippet>254</a>    }
<a href=#255 id=255 data-nosnippet>255</a>
<a href=#256 id=256 data-nosnippet>256</a>    <span class="prelude-val">Ok</span>(<span class="bool-val">false</span>)
<a href=#257 id=257 data-nosnippet>257</a>}
<a href=#258 id=258 data-nosnippet>258</a>
<a href=#259 id=259 data-nosnippet>259</a><span class="kw">async fn </span>is_login_successful(page: <span class="kw-2">&amp;</span>Page) -&gt; <span class="prelude-ty">Result</span>&lt;bool&gt; {
<a href=#260 id=260 data-nosnippet>260</a>    <span class="comment">// 检查URL变化或页面元素变化来判断登录是否成功
<a href=#261 id=261 data-nosnippet>261</a>    </span><span class="kw">let </span>current_url = page.url().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#262 id=262 data-nosnippet>262</a>    
<a href=#263 id=263 data-nosnippet>263</a>    <span class="comment">// 如果URL包含用户相关信息，说明登录成功
<a href=#264 id=264 data-nosnippet>264</a>    </span><span class="kw">if </span>current_url.contains(<span class="string">"/u/"</span>) || current_url.contains(<span class="string">"home"</span>) {
<a href=#265 id=265 data-nosnippet>265</a>        <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="bool-val">true</span>);
<a href=#266 id=266 data-nosnippet>266</a>    }
<a href=#267 id=267 data-nosnippet>267</a>
<a href=#268 id=268 data-nosnippet>268</a>    <span class="comment">// 检查是否存在登录后的元素
<a href=#269 id=269 data-nosnippet>269</a>    </span>is_already_logged_in(page).<span class="kw">await
<a href=#270 id=270 data-nosnippet>270</a></span>}
<a href=#271 id=271 data-nosnippet>271</a>
<a href=#272 id=272 data-nosnippet>272</a><span class="kw">async fn </span>get_login_error_message(page: <span class="kw-2">&amp;</span>Page) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#273 id=273 data-nosnippet>273</a>    <span class="kw">let </span>error_selectors = <span class="macro">vec!</span>[
<a href=#274 id=274 data-nosnippet>274</a>        <span class="string">".error"</span>,
<a href=#275 id=275 data-nosnippet>275</a>        <span class="string">".alert"</span>,
<a href=#276 id=276 data-nosnippet>276</a>        <span class="string">".warning"</span>,
<a href=#277 id=277 data-nosnippet>277</a>        <span class="string">".message"</span>,
<a href=#278 id=278 data-nosnippet>278</a>        <span class="string">".tip"</span>,
<a href=#279 id=279 data-nosnippet>279</a>    ];
<a href=#280 id=280 data-nosnippet>280</a>
<a href=#281 id=281 data-nosnippet>281</a>    <span class="kw">for </span>selector <span class="kw">in </span>error_selectors {
<a href=#282 id=282 data-nosnippet>282</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(element) = page.query_selector(selector).<span class="kw">await </span>{
<a href=#283 id=283 data-nosnippet>283</a>            <span class="kw">if let </span><span class="prelude-val">Some</span>(element) = element {
<a href=#284 id=284 data-nosnippet>284</a>                <span class="kw">if let </span><span class="prelude-val">Ok</span>(text) = element.text_content().<span class="kw">await </span>{
<a href=#285 id=285 data-nosnippet>285</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(text) = text {
<a href=#286 id=286 data-nosnippet>286</a>                        <span class="kw">if </span>!text.trim().is_empty() {
<a href=#287 id=287 data-nosnippet>287</a>                            <span class="kw">return </span><span class="prelude-val">Ok</span>(text.trim().to_string());
<a href=#288 id=288 data-nosnippet>288</a>                        }
<a href=#289 id=289 data-nosnippet>289</a>                    }
<a href=#290 id=290 data-nosnippet>290</a>                }
<a href=#291 id=291 data-nosnippet>291</a>            }
<a href=#292 id=292 data-nosnippet>292</a>        }
<a href=#293 id=293 data-nosnippet>293</a>    }
<a href=#294 id=294 data-nosnippet>294</a>
<a href=#295 id=295 data-nosnippet>295</a>    <span class="prelude-val">Ok</span>(<span class="string">"登录失败，原因未知"</span>.to_string())
<a href=#296 id=296 data-nosnippet>296</a>}
<a href=#297 id=297 data-nosnippet>297</a>
<a href=#298 id=298 data-nosnippet>298</a><span class="kw">async fn </span>extract_login_info(page: <span class="kw-2">&amp;</span>Page) -&gt; <span class="prelude-ty">Result</span>&lt;LoginResult&gt; {
<a href=#299 id=299 data-nosnippet>299</a>    <span class="comment">// 获取cookies
<a href=#300 id=300 data-nosnippet>300</a>    </span><span class="kw">let </span>cookies = get_cookies_string(page).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#301 id=301 data-nosnippet>301</a>    
<a href=#302 id=302 data-nosnippet>302</a>    <span class="comment">// 获取用户信息
<a href=#303 id=303 data-nosnippet>303</a>    </span><span class="kw">let </span>user_info = extract_user_info(page).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#304 id=304 data-nosnippet>304</a>
<a href=#305 id=305 data-nosnippet>305</a>    <span class="prelude-val">Ok</span>(LoginResult {
<a href=#306 id=306 data-nosnippet>306</a>        success: <span class="bool-val">true</span>,
<a href=#307 id=307 data-nosnippet>307</a>        message: <span class="string">"登录成功"</span>.to_string(),
<a href=#308 id=308 data-nosnippet>308</a>        cookies: <span class="prelude-val">Some</span>(cookies),
<a href=#309 id=309 data-nosnippet>309</a>        user_info,
<a href=#310 id=310 data-nosnippet>310</a>    })
<a href=#311 id=311 data-nosnippet>311</a>}
<a href=#312 id=312 data-nosnippet>312</a>
<a href=#313 id=313 data-nosnippet>313</a><span class="kw">async fn </span>get_cookies_string(page: <span class="kw-2">&amp;</span>Page) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#314 id=314 data-nosnippet>314</a>    <span class="kw">let </span>context = page.context();
<a href=#315 id=315 data-nosnippet>315</a>    <span class="kw">let </span>cookies = context.cookies(<span class="prelude-val">None</span>).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#316 id=316 data-nosnippet>316</a>    
<a href=#317 id=317 data-nosnippet>317</a>    <span class="kw">let </span>cookie_strings: Vec&lt;String&gt; = cookies
<a href=#318 id=318 data-nosnippet>318</a>        .iter()
<a href=#319 id=319 data-nosnippet>319</a>        .map(|cookie| <span class="macro">format!</span>(<span class="string">"{}={}"</span>, cookie.name, cookie.value))
<a href=#320 id=320 data-nosnippet>320</a>        .collect();
<a href=#321 id=321 data-nosnippet>321</a>    
<a href=#322 id=322 data-nosnippet>322</a>    <span class="prelude-val">Ok</span>(cookie_strings.join(<span class="string">"; "</span>))
<a href=#323 id=323 data-nosnippet>323</a>}
<a href=#324 id=324 data-nosnippet>324</a>
<a href=#325 id=325 data-nosnippet>325</a><span class="kw">async fn </span>extract_user_info(page: <span class="kw-2">&amp;</span>Page) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;UserInfo&gt;&gt; {
<a href=#326 id=326 data-nosnippet>326</a>    <span class="comment">// 尝试提取用户信息
<a href=#327 id=327 data-nosnippet>327</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>uid = String::new();
<a href=#328 id=328 data-nosnippet>328</a>    <span class="kw">let </span><span class="kw-2">mut </span>nickname = String::new();
<a href=#329 id=329 data-nosnippet>329</a>    <span class="kw">let </span><span class="kw-2">mut </span>avatar_url: <span class="prelude-ty">Option</span>&lt;String&gt; = <span class="prelude-val">None</span>;
<a href=#330 id=330 data-nosnippet>330</a>
<a href=#331 id=331 data-nosnippet>331</a>    <span class="comment">// 从URL中提取UID
<a href=#332 id=332 data-nosnippet>332</a>    </span><span class="kw">let </span>current_url = page.url().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#333 id=333 data-nosnippet>333</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(uid_match) = current_url.split(<span class="string">"/u/"</span>).nth(<span class="number">1</span>) {
<a href=#334 id=334 data-nosnippet>334</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(uid_part) = uid_match.split(<span class="string">"?"</span>).next() {
<a href=#335 id=335 data-nosnippet>335</a>            uid = uid_part.to_string();
<a href=#336 id=336 data-nosnippet>336</a>        }
<a href=#337 id=337 data-nosnippet>337</a>    }
<a href=#338 id=338 data-nosnippet>338</a>
<a href=#339 id=339 data-nosnippet>339</a>    <span class="comment">// 提取昵称
<a href=#340 id=340 data-nosnippet>340</a>    </span><span class="kw">let </span>nickname_selectors = <span class="macro">vec!</span>[
<a href=#341 id=341 data-nosnippet>341</a>        <span class="string">".gn_name"</span>,
<a href=#342 id=342 data-nosnippet>342</a>        <span class="string">".user-name"</span>,
<a href=#343 id=343 data-nosnippet>343</a>        <span class="string">".nickname"</span>,
<a href=#344 id=344 data-nosnippet>344</a>        <span class="string">".username"</span>,
<a href=#345 id=345 data-nosnippet>345</a>    ];
<a href=#346 id=346 data-nosnippet>346</a>
<a href=#347 id=347 data-nosnippet>347</a>    <span class="kw">for </span>selector <span class="kw">in </span>nickname_selectors {
<a href=#348 id=348 data-nosnippet>348</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(element)) = page.query_selector(selector).<span class="kw">await </span>{
<a href=#349 id=349 data-nosnippet>349</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(text)) = element.text_content().<span class="kw">await </span>{
<a href=#350 id=350 data-nosnippet>350</a>                <span class="kw">if </span>!text.trim().is_empty() {
<a href=#351 id=351 data-nosnippet>351</a>                    nickname = text.trim().to_string();
<a href=#352 id=352 data-nosnippet>352</a>                    <span class="kw">break</span>;
<a href=#353 id=353 data-nosnippet>353</a>                }
<a href=#354 id=354 data-nosnippet>354</a>            }
<a href=#355 id=355 data-nosnippet>355</a>        }
<a href=#356 id=356 data-nosnippet>356</a>    }
<a href=#357 id=357 data-nosnippet>357</a>
<a href=#358 id=358 data-nosnippet>358</a>    <span class="comment">// 提取头像URL
<a href=#359 id=359 data-nosnippet>359</a>    </span><span class="kw">let </span>avatar_selectors = <span class="macro">vec!</span>[
<a href=#360 id=360 data-nosnippet>360</a>        <span class="string">".avatar img"</span>,
<a href=#361 id=361 data-nosnippet>361</a>        <span class="string">".user-avatar img"</span>,
<a href=#362 id=362 data-nosnippet>362</a>        <span class="string">".head-img img"</span>,
<a href=#363 id=363 data-nosnippet>363</a>    ];
<a href=#364 id=364 data-nosnippet>364</a>
<a href=#365 id=365 data-nosnippet>365</a>    <span class="kw">for </span>selector <span class="kw">in </span>avatar_selectors {
<a href=#366 id=366 data-nosnippet>366</a>        <span class="kw">if let </span><span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(element)) = page.query_selector(selector).<span class="kw">await </span>{
<a href=#367 id=367 data-nosnippet>367</a>            <span class="kw">if let </span><span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(src)) = element.get_attribute(<span class="string">"src"</span>).<span class="kw">await </span>{
<a href=#368 id=368 data-nosnippet>368</a>                avatar_url = <span class="prelude-val">Some</span>(src);
<a href=#369 id=369 data-nosnippet>369</a>                <span class="kw">break</span>;
<a href=#370 id=370 data-nosnippet>370</a>            }
<a href=#371 id=371 data-nosnippet>371</a>        }
<a href=#372 id=372 data-nosnippet>372</a>    }
<a href=#373 id=373 data-nosnippet>373</a>
<a href=#374 id=374 data-nosnippet>374</a>    <span class="kw">if </span>!uid.is_empty() || !nickname.is_empty() {
<a href=#375 id=375 data-nosnippet>375</a>        <span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(UserInfo {
<a href=#376 id=376 data-nosnippet>376</a>            uid,
<a href=#377 id=377 data-nosnippet>377</a>            nickname,
<a href=#378 id=378 data-nosnippet>378</a>            avatar_url,
<a href=#379 id=379 data-nosnippet>379</a>        }))
<a href=#380 id=380 data-nosnippet>380</a>    } <span class="kw">else </span>{
<a href=#381 id=381 data-nosnippet>381</a>        <span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>)
<a href=#382 id=382 data-nosnippet>382</a>    }
<a href=#383 id=383 data-nosnippet>383</a>}</code></pre></div></section></main></body></html>