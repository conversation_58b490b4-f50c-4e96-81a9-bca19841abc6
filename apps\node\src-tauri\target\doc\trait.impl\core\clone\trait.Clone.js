(function() {
    var implementors = Object.fromEntries([["weibo_crawler_node",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::<PERSON>lone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/account/enum.AccountStatus.html\" title=\"enum weibo_crawler_node::account::AccountStatus\">AccountStatus</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/crawler/enum.TaskType.html\" title=\"enum weibo_crawler_node::crawler::TaskType\">TaskType</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/notification/enum.ActionType.html\" title=\"enum weibo_crawler_node::notification::ActionType\">ActionType</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/notification/enum.NotificationType.html\" title=\"enum weibo_crawler_node::notification::NotificationType\">NotificationType</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/proxy/enum.ProxyProtocol.html\" title=\"enum weibo_crawler_node::proxy::ProxyProtocol\">ProxyProtocol</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/proxy/enum.ProxyStatus.html\" title=\"enum weibo_crawler_node::proxy::ProxyStatus\">ProxyStatus</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/repository/enum.FilterOperator.html\" title=\"enum weibo_crawler_node::repository::FilterOperator\">FilterOperator</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/repository/enum.FilterValue.html\" title=\"enum weibo_crawler_node::repository::FilterValue\">FilterValue</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/repository/enum.OrderDirection.html\" title=\"enum weibo_crawler_node::repository::OrderDirection\">OrderDirection</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/service/enum.HealthStatus.html\" title=\"enum weibo_crawler_node::service::HealthStatus\">HealthStatus</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"enum\" href=\"weibo_crawler_node/service/enum.ServiceEvent.html\" title=\"enum weibo_crawler_node::service::ServiceEvent\">ServiceEvent</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/account/struct.Account.html\" title=\"struct weibo_crawler_node::account::Account\">Account</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/account/struct.LoginSession.html\" title=\"struct weibo_crawler_node::account::LoginSession\">LoginSession</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.BrowserConfig.html\" title=\"struct weibo_crawler_node::browser::BrowserConfig\">BrowserConfig</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.LoginCredentials.html\" title=\"struct weibo_crawler_node::browser::LoginCredentials\">LoginCredentials</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.LoginResult.html\" title=\"struct weibo_crawler_node::browser::LoginResult\">LoginResult</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.UserInfo.html\" title=\"struct weibo_crawler_node::browser::UserInfo\">UserInfo</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/config/struct.AppConfig.html\" title=\"struct weibo_crawler_node::config::AppConfig\">AppConfig</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/crawler/struct.CrawlResult.html\" title=\"struct weibo_crawler_node::crawler::CrawlResult\">CrawlResult</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/crawler/struct.CrawlTask.html\" title=\"struct weibo_crawler_node::crawler::CrawlTask\">CrawlTask</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/monitor/struct.NodeHealth.html\" title=\"struct weibo_crawler_node::monitor::NodeHealth\">NodeHealth</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/monitor/struct.PerformanceMetrics.html\" title=\"struct weibo_crawler_node::monitor::PerformanceMetrics\">PerformanceMetrics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/monitor/struct.SystemMetrics.html\" title=\"struct weibo_crawler_node::monitor::SystemMetrics\">SystemMetrics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/notification/struct.Notification.html\" title=\"struct weibo_crawler_node::notification::Notification\">Notification</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/notification/struct.NotificationAction.html\" title=\"struct weibo_crawler_node::notification::NotificationAction\">NotificationAction</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/proxy/struct.Proxy.html\" title=\"struct weibo_crawler_node::proxy::Proxy\">Proxy</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/proxy/struct.ProxyHealthCheck.html\" title=\"struct weibo_crawler_node::proxy::ProxyHealthCheck\">ProxyHealthCheck</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/account_repository/struct.AccountRepository.html\" title=\"struct weibo_crawler_node::repository::account_repository::AccountRepository\">AccountRepository</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/proxy_repository/struct.ProxyRepository.html\" title=\"struct weibo_crawler_node::repository::proxy_repository::ProxyRepository\">ProxyRepository</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/struct.Filter.html\" title=\"struct weibo_crawler_node::repository::Filter\">Filter</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/struct.QueryParams.html\" title=\"struct weibo_crawler_node::repository::QueryParams\">QueryParams</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/task_repository/struct.TaskRepository.html\" title=\"struct weibo_crawler_node::repository::task_repository::TaskRepository\">TaskRepository</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/task_repository/struct.TaskStatistics.html\" title=\"struct weibo_crawler_node::repository::task_repository::TaskStatistics\">TaskStatistics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/scheduler/struct.PriorityTask.html\" title=\"struct weibo_crawler_node::scheduler::PriorityTask\">PriorityTask</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/scheduler/struct.TaskStatistics.html\" title=\"struct weibo_crawler_node::scheduler::TaskStatistics\">TaskStatistics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/account_service/struct.AccountMetrics.html\" title=\"struct weibo_crawler_node::service::account_service::AccountMetrics\">AccountMetrics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/crawler_service/struct.CrawlerMetrics.html\" title=\"struct weibo_crawler_node::service::crawler_service::CrawlerMetrics\">CrawlerMetrics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/monitor_service/struct.SystemMetrics.html\" title=\"struct weibo_crawler_node::service::monitor_service::SystemMetrics\">SystemMetrics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/proxy_service/struct.ProxyMetrics.html\" title=\"struct weibo_crawler_node::service::proxy_service::ProxyMetrics\">ProxyMetrics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/struct.ServiceConfig.html\" title=\"struct weibo_crawler_node::service::ServiceConfig\">ServiceConfig</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/task_service/struct.CreateTaskRequest.html\" title=\"struct weibo_crawler_node::service::task_service::CreateTaskRequest\">CreateTaskRequest</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/task_service/struct.TaskMetrics.html\" title=\"struct weibo_crawler_node::service::task_service::TaskMetrics\">TaskMetrics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/task_service/struct.TaskQuery.html\" title=\"struct weibo_crawler_node::service::task_service::TaskQuery\">TaskQuery</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/task_service/struct.UpdateTaskRequest.html\" title=\"struct weibo_crawler_node::service::task_service::UpdateTaskRequest\">UpdateTaskRequest</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.AccountLog.html\" title=\"struct weibo_crawler_node::storage::AccountLog\">AccountLog</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.AccountPoolStatus.html\" title=\"struct weibo_crawler_node::storage::AccountPoolStatus\">AccountPoolStatus</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.AccountRecord.html\" title=\"struct weibo_crawler_node::storage::AccountRecord\">AccountRecord</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.ErrorLog.html\" title=\"struct weibo_crawler_node::storage::ErrorLog\">ErrorLog</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.ProxyPoolStatus.html\" title=\"struct weibo_crawler_node::storage::ProxyPoolStatus\">ProxyPoolStatus</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.ProxyRecord.html\" title=\"struct weibo_crawler_node::storage::ProxyRecord\">ProxyRecord</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.ProxyUsage.html\" title=\"struct weibo_crawler_node::storage::ProxyUsage\">ProxyUsage</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.SystemMetrics.html\" title=\"struct weibo_crawler_node::storage::SystemMetrics\">SystemMetrics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.TaskLog.html\" title=\"struct weibo_crawler_node::storage::TaskLog\">TaskLog</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.TaskRecord.html\" title=\"struct weibo_crawler_node::storage::TaskRecord\">TaskRecord</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.TaskResult.html\" title=\"struct weibo_crawler_node::storage::TaskResult\">TaskResult</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.TaskStatistics.html\" title=\"struct weibo_crawler_node::storage::TaskStatistics\">TaskStatistics</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/updater/struct.UpdateInfo.html\" title=\"struct weibo_crawler_node::updater::UpdateInfo\">UpdateInfo</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/updater/struct.UpdateStatus.html\" title=\"struct weibo_crawler_node::updater::UpdateStatus\">UpdateStatus</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/window_manager/struct.WindowConfig.html\" title=\"struct weibo_crawler_node::window_manager::WindowConfig\">WindowConfig</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/window_manager/struct.WindowInfo.html\" title=\"struct weibo_crawler_node::window_manager::WindowInfo\">WindowInfo</a>"],["impl&lt;T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/struct.PaginatedResult.html\" title=\"struct weibo_crawler_node::repository::PaginatedResult\">PaginatedResult</a>&lt;T&gt;"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[19179]}