(function() {
    var type_impls = Object.fromEntries([["weibo_crawler_node",[["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Clone-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1752-1755\">Source</a></span><a href=\"#impl-Clone-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::<PERSON>lone\"><PERSON>lone</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.clone\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1758\">Source</a><a href=\"#method.clone\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html#tymethod.clone\" class=\"fn\">clone</a>(&amp;self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;</h4></section></summary><div class='docblock'>Returns a copy of the value. <a href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html#tymethod.clone\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.clone_from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1766\">Source</a><a href=\"#method.clone_from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html#method.clone_from\" class=\"fn\">clone_from</a>(&amp;mut self, source: &amp;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;)</h4></section></summary><div class='docblock'>Performs copy-assignment from <code>source</code>. <a href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html#method.clone_from\">Read more</a></div></details></div></details>","Clone","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Context%3CT,+E%3E-for-Result%3CT,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/anyhow/1.0.98/src/anyhow/context.rs.html#42-44\">Source</a><a href=\"#impl-Context%3CT,+E%3E-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://docs.rs/anyhow/1.0.98/anyhow/trait.Context.html\" title=\"trait anyhow::Context\">Context</a>&lt;T, E&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    E: <a class=\"trait\" href=\"https://docs.rs/anyhow/1.0.98/anyhow/context/ext/trait.StdError.html\" title=\"trait anyhow::context::ext::StdError\">StdError</a> + <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html\" title=\"trait core::marker::Send\">Send</a> + <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Sync.html\" title=\"trait core::marker::Sync\">Sync</a> + 'static,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.context\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/anyhow/1.0.98/src/anyhow/context.rs.html#46-48\">Source</a><a href=\"#method.context\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/anyhow/1.0.98/anyhow/trait.Context.html#tymethod.context\" class=\"fn\">context</a>&lt;C&gt;(self, context: C) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, <a class=\"struct\" href=\"https://docs.rs/anyhow/1.0.98/anyhow/struct.Error.html\" title=\"struct anyhow::Error\">Error</a>&gt;<div class=\"where\">where\n    C: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Display.html\" title=\"trait core::fmt::Display\">Display</a> + <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html\" title=\"trait core::marker::Send\">Send</a> + <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Sync.html\" title=\"trait core::marker::Sync\">Sync</a> + 'static,</div></h4></section></summary><div class='docblock'>Wrap the error value with additional context.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.with_context\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/anyhow/1.0.98/src/anyhow/context.rs.html#58-61\">Source</a><a href=\"#method.with_context\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/anyhow/1.0.98/anyhow/trait.Context.html#tymethod.with_context\" class=\"fn\">with_context</a>&lt;C, F&gt;(self, context: F) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, <a class=\"struct\" href=\"https://docs.rs/anyhow/1.0.98/anyhow/struct.Error.html\" title=\"struct anyhow::Error\">Error</a>&gt;<div class=\"where\">where\n    C: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Display.html\" title=\"trait core::fmt::Display\">Display</a> + <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html\" title=\"trait core::marker::Send\">Send</a> + <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Sync.html\" title=\"trait core::marker::Sync\">Sync</a> + 'static,\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>() -&gt; C,</div></h4></section></summary><div class='docblock'>Wrap the error value with additional context that is evaluated lazily\nonly once an error does occur.</div></details></div></details>","Context<T, E>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Debug-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a></span><a href=\"#impl-Debug-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.fmt\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a><a href=\"#method.fmt\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html#tymethod.fmt\" class=\"fn\">fmt</a>(&amp;self, f: &amp;mut <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/struct.Formatter.html\" title=\"struct core::fmt::Formatter\">Formatter</a>&lt;'_&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.unit.html\">()</a>, <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/struct.Error.html\" title=\"struct core::fmt::Error\">Error</a>&gt;</h4></section></summary><div class='docblock'>Formats the value using the given formatter. <a href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html#tymethod.fmt\">Read more</a></div></details></div></details>","Debug","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Deserialize%3C'de%3E-for-Result%3CT,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/de/impls.rs.html#2992-2995\">Source</a><a href=\"#impl-Deserialize%3C'de%3E-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;'de, T, E&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt;,\n    E: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html\" title=\"trait serde::de::Deserialize\">Deserialize</a>&lt;'de&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.deserialize\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/de/impls.rs.html#2997-2999\">Source</a><a href=\"#method.deserialize\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html#tymethod.deserialize\" class=\"fn\">deserialize</a>&lt;D&gt;(\n    deserializer: D,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;, &lt;D as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html\" title=\"trait serde::de::Deserializer\">Deserializer</a>&lt;'de&gt;&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html#associatedtype.Error\" title=\"type serde::de::Deserializer::Error\">Error</a>&gt;<div class=\"where\">where\n    D: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html\" title=\"trait serde::de::Deserializer\">Deserializer</a>&lt;'de&gt;,</div></h4></section></summary><div class='docblock'>Deserialize this value from the given Serde deserializer. <a href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserialize.html#tymethod.deserialize\">Read more</a></div></details></div></details>","Deserialize<'de>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-DeserializeAs%3C'de,+Result%3CT,+E%3E%3E-for-Result%3CTAs,+EAs%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde_with/1.14.0/src/serde_with/de/impls.rs.html#211-214\">Source</a><a href=\"#impl-DeserializeAs%3C'de,+Result%3CT,+E%3E%3E-for-Result%3CTAs,+EAs%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;'de, T, TAs, E, EAs&gt; <a class=\"trait\" href=\"https://docs.rs/serde_with/1.14.0/serde_with/de/trait.DeserializeAs.html\" title=\"trait serde_with::de::DeserializeAs\">DeserializeAs</a>&lt;'de, <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;TAs, EAs&gt;<div class=\"where\">where\n    TAs: <a class=\"trait\" href=\"https://docs.rs/serde_with/1.14.0/serde_with/de/trait.DeserializeAs.html\" title=\"trait serde_with::de::DeserializeAs\">DeserializeAs</a>&lt;'de, T&gt;,\n    EAs: <a class=\"trait\" href=\"https://docs.rs/serde_with/1.14.0/serde_with/de/trait.DeserializeAs.html\" title=\"trait serde_with::de::DeserializeAs\">DeserializeAs</a>&lt;'de, E&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.deserialize_as\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde_with/1.14.0/src/serde_with/de/impls.rs.html#216-218\">Source</a><a href=\"#method.deserialize_as\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/serde_with/1.14.0/serde_with/de/trait.DeserializeAs.html#tymethod.deserialize_as\" class=\"fn\">deserialize_as</a>&lt;D&gt;(\n    deserializer: D,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;, &lt;D as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html\" title=\"trait serde::de::Deserializer\">Deserializer</a>&lt;'de&gt;&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html#associatedtype.Error\" title=\"type serde::de::Deserializer::Error\">Error</a>&gt;<div class=\"where\">where\n    D: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html\" title=\"trait serde::de::Deserializer\">Deserializer</a>&lt;'de&gt;,</div></h4></section></summary><div class='docblock'>Deserialize this value from the given Serde deserializer.</div></details></div></details>","DeserializeAs<'de, Result<T, E>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-DeserializeAs%3C'de,+Result%3CT,+E%3E%3E-for-Result%3CTAs,+EAs%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde_with/3.14.0/src/serde_with/de/impls.rs.html#352-355\">Source</a><a href=\"#impl-DeserializeAs%3C'de,+Result%3CT,+E%3E%3E-for-Result%3CTAs,+EAs%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;'de, T, TAs, E, EAs&gt; <a class=\"trait\" href=\"https://docs.rs/serde_with/3.14.0/serde_with/de/trait.DeserializeAs.html\" title=\"trait serde_with::de::DeserializeAs\">DeserializeAs</a>&lt;'de, <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;TAs, EAs&gt;<div class=\"where\">where\n    TAs: <a class=\"trait\" href=\"https://docs.rs/serde_with/3.14.0/serde_with/de/trait.DeserializeAs.html\" title=\"trait serde_with::de::DeserializeAs\">DeserializeAs</a>&lt;'de, T&gt;,\n    EAs: <a class=\"trait\" href=\"https://docs.rs/serde_with/3.14.0/serde_with/de/trait.DeserializeAs.html\" title=\"trait serde_with::de::DeserializeAs\">DeserializeAs</a>&lt;'de, E&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.deserialize_as\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde_with/3.14.0/src/serde_with/de/impls.rs.html#357-359\">Source</a><a href=\"#method.deserialize_as\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/serde_with/3.14.0/serde_with/de/trait.DeserializeAs.html#tymethod.deserialize_as\" class=\"fn\">deserialize_as</a>&lt;D&gt;(\n    deserializer: D,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;, &lt;D as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html\" title=\"trait serde::de::Deserializer\">Deserializer</a>&lt;'de&gt;&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html#associatedtype.Error\" title=\"type serde::de::Deserializer::Error\">Error</a>&gt;<div class=\"where\">where\n    D: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/de/trait.Deserializer.html\" title=\"trait serde::de::Deserializer\">Deserializer</a>&lt;'de&gt;,</div></h4></section></summary><div class='docblock'>Deserialize this value from the given Serde deserializer.</div></details></div></details>","DeserializeAs<'de, Result<T, E>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-From%3CEither%3CL,+R%3E%3E-for-Result%3CR,+L%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/either/1/src/either/lib.rs.html#1132\">Source</a><a href=\"#impl-From%3CEither%3CL,+R%3E%3E-for-Result%3CR,+L%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;L, R&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"enum\" href=\"https://docs.rs/either/1/either/enum.Either.html\" title=\"enum either::Either\">Either</a>&lt;L, R&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;R, L&gt;</h3><div class=\"docblock\"><p>Convert from <code>Either</code> to <code>Result</code> with <code>Right =&gt; Ok</code> and <code>Left =&gt; Err</code>.</p>\n</div></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/either/1/src/either/lib.rs.html#1133\">Source</a><a href=\"#method.from\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html#tymethod.from\" class=\"fn\">from</a>(val: <a class=\"enum\" href=\"https://docs.rs/either/1/either/enum.Either.html\" title=\"enum either::Either\">Either</a>&lt;L, R&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;R, L&gt;</h4></section></summary><div class='docblock'>Converts to this type from the input type.</div></details></div></details>","From<Either<L, R>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-FromIterator%3CResult%3CA,+E%3E%3E-for-Result%3CV,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1976\">Source</a></span><a href=\"#impl-FromIterator%3CResult%3CA,+E%3E%3E-for-Result%3CV,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;A, E, V&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/collect/trait.FromIterator.html\" title=\"trait core::iter::traits::collect::FromIterator\">FromIterator</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;A, E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;V, E&gt;<div class=\"where\">where\n    V: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/collect/trait.FromIterator.html\" title=\"trait core::iter::traits::collect::FromIterator\">FromIterator</a>&lt;A&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from_iter\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2020\">Source</a><a href=\"#method.from_iter\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/collect/trait.FromIterator.html#tymethod.from_iter\" class=\"fn\">from_iter</a>&lt;I&gt;(iter: I) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;V, E&gt;<div class=\"where\">where\n    I: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a>&lt;Item = <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;A, E&gt;&gt;,</div></h4></section></summary><div class=\"docblock\"><p>Takes each element in the <code>Iterator</code>: if it is an <code>Err</code>, no further\nelements are taken, and the <code>Err</code> is returned. Should no <code>Err</code> occur, a\ncontainer with the values of each <code>Result</code> is returned.</p>\n<p>Here is an example which increments every integer in a vector,\nchecking for overflow:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>];\n<span class=\"kw\">let </span>res: <span class=\"prelude-ty\">Result</span>&lt;Vec&lt;u32&gt;, <span class=\"kw-2\">&amp;</span><span class=\"lifetime\">'static </span>str&gt; = v.iter().map(|x: <span class=\"kw-2\">&amp;</span>u32|\n    x.checked_add(<span class=\"number\">1</span>).ok_or(<span class=\"string\">\"Overflow!\"</span>)\n).collect();\n<span class=\"macro\">assert_eq!</span>(res, <span class=\"prelude-val\">Ok</span>(<span class=\"macro\">vec!</span>[<span class=\"number\">2</span>, <span class=\"number\">3</span>]));</code></pre></div>\n<p>Here is another example that tries to subtract one from another list\nof integers, this time checking for underflow:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>, <span class=\"number\">0</span>];\n<span class=\"kw\">let </span>res: <span class=\"prelude-ty\">Result</span>&lt;Vec&lt;u32&gt;, <span class=\"kw-2\">&amp;</span><span class=\"lifetime\">'static </span>str&gt; = v.iter().map(|x: <span class=\"kw-2\">&amp;</span>u32|\n    x.checked_sub(<span class=\"number\">1</span>).ok_or(<span class=\"string\">\"Underflow!\"</span>)\n).collect();\n<span class=\"macro\">assert_eq!</span>(res, <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Underflow!\"</span>));</code></pre></div>\n<p>Here is a variation on the previous example, showing that no\nfurther elements are taken from <code>iter</code> after the first <code>Err</code>.</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">3</span>, <span class=\"number\">2</span>, <span class=\"number\">1</span>, <span class=\"number\">10</span>];\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>shared = <span class=\"number\">0</span>;\n<span class=\"kw\">let </span>res: <span class=\"prelude-ty\">Result</span>&lt;Vec&lt;u32&gt;, <span class=\"kw-2\">&amp;</span><span class=\"lifetime\">'static </span>str&gt; = v.iter().map(|x: <span class=\"kw-2\">&amp;</span>u32| {\n    shared += x;\n    x.checked_sub(<span class=\"number\">2</span>).ok_or(<span class=\"string\">\"Underflow!\"</span>)\n}).collect();\n<span class=\"macro\">assert_eq!</span>(res, <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Underflow!\"</span>));\n<span class=\"macro\">assert_eq!</span>(shared, <span class=\"number\">6</span>);</code></pre></div>\n<p>Since the third element caused an underflow, no further elements were taken,\nso the final value of <code>shared</code> is 6 (= <code>3 + 2 + 1</code>), not 16.</p>\n</div></details></div></details>","FromIterator<Result<A, E>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-FromResidual%3CResult%3CInfallible,+E%3E%3E-for-Result%3CT,+F%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2045\">Source</a><a href=\"#impl-FromResidual%3CResult%3CInfallible,+E%3E%3E-for-Result%3CT,+F%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E, F&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.FromResidual.html\" title=\"trait core::ops::try_trait::FromResidual\">FromResidual</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/enum.Infallible.html\" title=\"enum core::convert::Infallible\">Infallible</a>, E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, F&gt;<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;E&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from_residual\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2048\">Source</a><a href=\"#method.from_residual\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.FromResidual.html#tymethod.from_residual\" class=\"fn\">from_residual</a>(residual: <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/enum.Infallible.html\" title=\"enum core::convert::Infallible\">Infallible</a>, E&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, F&gt;</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>try_trait_v2</code>)</span></div></span><div class='docblock'>Constructs the type from a compatible <code>Residual</code> type. <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.FromResidual.html#tymethod.from_residual\">Read more</a></div></details></div></details>","FromResidual<Result<Infallible, E>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-FromResidual%3CYeet%3CE%3E%3E-for-Result%3CT,+F%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2056\">Source</a><a href=\"#impl-FromResidual%3CYeet%3CE%3E%3E-for-Result%3CT,+F%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E, F&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.FromResidual.html\" title=\"trait core::ops::try_trait::FromResidual\">FromResidual</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/struct.Yeet.html\" title=\"struct core::ops::try_trait::Yeet\">Yeet</a>&lt;E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, F&gt;<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;E&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.from_residual\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2058\">Source</a><a href=\"#method.from_residual\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.FromResidual.html#tymethod.from_residual\" class=\"fn\">from_residual</a>(_: <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/struct.Yeet.html\" title=\"struct core::ops::try_trait::Yeet\">Yeet</a>&lt;E&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, F&gt;</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>try_trait_v2</code>)</span></div></span><div class='docblock'>Constructs the type from a compatible <code>Residual</code> type. <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.FromResidual.html#tymethod.from_residual\">Read more</a></div></details></div></details>","FromResidual<Yeet<E>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Hash-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a></span><a href=\"#impl-Hash-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hash.html\" title=\"trait core::hash::Hash\">Hash</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hash.html\" title=\"trait core::hash::Hash\">Hash</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hash.html\" title=\"trait core::hash::Hash\">Hash</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.hash\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a><a href=\"#method.hash\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hash.html#tymethod.hash\" class=\"fn\">hash</a>&lt;__H&gt;(&amp;self, state: <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;mut __H</a>)<div class=\"where\">where\n    __H: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hasher.html\" title=\"trait core::hash::Hasher\">Hasher</a>,</div></h4></section></summary><div class='docblock'>Feeds this value into the given <a href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hasher.html\" title=\"trait core::hash::Hasher\"><code>Hasher</code></a>. <a href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hash.html#tymethod.hash\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.hash_slice\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.3.0\">1.3.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/hash/mod.rs.html#235-237\">Source</a></span><a href=\"#method.hash_slice\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hash.html#method.hash_slice\" class=\"fn\">hash_slice</a>&lt;H&gt;(data: &amp;[Self], state: <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;mut H</a>)<div class=\"where\">where\n    H: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hasher.html\" title=\"trait core::hash::Hasher\">Hasher</a>,\n    Self: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Feeds a slice of this type into the given <a href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hasher.html\" title=\"trait core::hash::Hasher\"><code>Hasher</code></a>. <a href=\"https://doc.rust-lang.org/1.88.0/core/hash/trait.Hash.html#method.hash_slice\">Read more</a></div></details></div></details>","Hash","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-IntoIterator-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1784\">Source</a></span><a href=\"#impl-IntoIterator-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/collect/trait.IntoIterator.html\" title=\"trait core::iter::traits::collect::IntoIterator\">IntoIterator</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_iter\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1804\">Source</a><a href=\"#method.into_iter\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/collect/trait.IntoIterator.html#tymethod.into_iter\" class=\"fn\">into_iter</a>(self) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/result/struct.IntoIter.html\" title=\"struct core::result::IntoIter\">IntoIter</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Returns a consuming iterator over the possibly contained value.</p>\n<p>The iterator yields one value if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Result::Ok</code></a>, otherwise none.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">5</span>);\n<span class=\"kw\">let </span>v: Vec&lt;u32&gt; = x.into_iter().collect();\n<span class=\"macro\">assert_eq!</span>(v, [<span class=\"number\">5</span>]);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"nothing!\"</span>);\n<span class=\"kw\">let </span>v: Vec&lt;u32&gt; = x.into_iter().collect();\n<span class=\"macro\">assert_eq!</span>(v, []);</code></pre></div>\n</div></details><details class=\"toggle\" open><summary><section id=\"associatedtype.Item\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1785\">Source</a><a href=\"#associatedtype.Item\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/collect/trait.IntoIterator.html#associatedtype.Item\" class=\"associatedtype\">Item</a> = T</h4></section></summary><div class='docblock'>The type of the elements being iterated over.</div></details><details class=\"toggle\" open><summary><section id=\"associatedtype.IntoIter\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1786\">Source</a><a href=\"#associatedtype.IntoIter\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/collect/trait.IntoIterator.html#associatedtype.IntoIter\" class=\"associatedtype\">IntoIter</a> = <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/result/struct.IntoIter.html\" title=\"struct core::result::IntoIter\">IntoIter</a>&lt;T&gt;</h4></section></summary><div class='docblock'>Which kind of iterator are we turning this into?</div></details></div></details>","IntoIterator","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Ord-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a></span><a href=\"#impl-Ord-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html\" title=\"trait core::cmp::Ord\">Ord</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html\" title=\"trait core::cmp::Ord\">Ord</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html\" title=\"trait core::cmp::Ord\">Ord</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.cmp\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a><a href=\"#method.cmp\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html#tymethod.cmp\" class=\"fn\">cmp</a>(&amp;self, other: &amp;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/enum.Ordering.html\" title=\"enum core::cmp::Ordering\">Ordering</a></h4></section></summary><div class='docblock'>This method returns an <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/enum.Ordering.html\" title=\"enum core::cmp::Ordering\"><code>Ordering</code></a> between <code>self</code> and <code>other</code>. <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html#tymethod.cmp\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.max\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.21.0\">1.21.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/cmp.rs.html#1009-1011\">Source</a></span><a href=\"#method.max\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html#method.max\" class=\"fn\">max</a>(self, other: Self) -&gt; Self<div class=\"where\">where\n    Self: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Compares and returns the maximum of two values. <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html#method.max\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.min\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.21.0\">1.21.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/cmp.rs.html#1048-1050\">Source</a></span><a href=\"#method.min\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html#method.min\" class=\"fn\">min</a>(self, other: Self) -&gt; Self<div class=\"where\">where\n    Self: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Compares and returns the minimum of two values. <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html#method.min\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.clamp\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.50.0\">1.50.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/cmp.rs.html#1074-1076\">Source</a></span><a href=\"#method.clamp\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html#method.clamp\" class=\"fn\">clamp</a>(self, min: Self, max: Self) -&gt; Self<div class=\"where\">where\n    Self: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Restrict a value to a certain interval. <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Ord.html#method.clamp\">Read more</a></div></details></div></details>","Ord","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-PartialEq-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a></span><a href=\"#impl-PartialEq-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialEq.html\" title=\"trait core::cmp::PartialEq\">PartialEq</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.eq\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a><a href=\"#method.eq\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialEq.html#tymethod.eq\" class=\"fn\">eq</a>(&amp;self, other: &amp;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>self</code> and <code>other</code> values to be equal, and is used by <code>==</code>.</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ne\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/cmp.rs.html#262\">Source</a></span><a href=\"#method.ne\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialEq.html#method.ne\" class=\"fn\">ne</a>(&amp;self, other: <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;Rhs</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests for <code>!=</code>. The default implementation is almost always sufficient,\nand should not be overridden without very good reason.</div></details></div></details>","PartialEq","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-PartialOrd-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a></span><a href=\"#impl-PartialOrd-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html\" title=\"trait core::cmp::PartialOrd\">PartialOrd</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html\" title=\"trait core::cmp::PartialOrd\">PartialOrd</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html\" title=\"trait core::cmp::PartialOrd\">PartialOrd</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.partial_cmp\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a><a href=\"#method.partial_cmp\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#tymethod.partial_cmp\" class=\"fn\">partial_cmp</a>(&amp;self, other: &amp;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html\" title=\"enum core::option::Option\">Option</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/enum.Ordering.html\" title=\"enum core::cmp::Ordering\">Ordering</a>&gt;</h4></section></summary><div class='docblock'>This method returns an ordering between <code>self</code> and <code>other</code> values if one exists. <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#tymethod.partial_cmp\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.lt\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/cmp.rs.html#1382\">Source</a></span><a href=\"#method.lt\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#method.lt\" class=\"fn\">lt</a>(&amp;self, other: <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;Rhs</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests less than (for <code>self</code> and <code>other</code>) and is used by the <code>&lt;</code> operator. <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#method.lt\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.le\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/cmp.rs.html#1400\">Source</a></span><a href=\"#method.le\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#method.le\" class=\"fn\">le</a>(&amp;self, other: <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;Rhs</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests less than or equal to (for <code>self</code> and <code>other</code>) and is used by the\n<code>&lt;=</code> operator. <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#method.le\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.gt\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/cmp.rs.html#1418\">Source</a></span><a href=\"#method.gt\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#method.gt\" class=\"fn\">gt</a>(&amp;self, other: <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;Rhs</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests greater than (for <code>self</code> and <code>other</code>) and is used by the <code>&gt;</code>\noperator. <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#method.gt\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ge\" class=\"method trait-impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/cmp.rs.html#1436\">Source</a></span><a href=\"#method.ge\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#method.ge\" class=\"fn\">ge</a>(&amp;self, other: <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;Rhs</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class='docblock'>Tests greater than or equal to (for <code>self</code> and <code>other</code>) and is used by\nthe <code>&gt;=</code> operator. <a href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.PartialOrd.html#method.ge\">Read more</a></div></details></div></details>","PartialOrd","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Product%3CResult%3CU,+E%3E%3E-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.16.0\">1.16.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/iter\\traits/accum.rs.html#184-186\">Source</a></span><a href=\"#impl-Product%3CResult%3CU,+E%3E%3E-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, U, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/accum/trait.Product.html\" title=\"trait core::iter::traits::accum::Product\">Product</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/accum/trait.Product.html\" title=\"trait core::iter::traits::accum::Product\">Product</a>&lt;U&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.product\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/iter\\traits/accum.rs.html#205-207\">Source</a><a href=\"#method.product\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/accum/trait.Product.html#tymethod.product\" class=\"fn\">product</a>&lt;I&gt;(iter: I) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    I: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/iterator/trait.Iterator.html\" title=\"trait core::iter::traits::iterator::Iterator\">Iterator</a>&lt;Item = <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;&gt;,</div></h4></section></summary><div class=\"docblock\"><p>Takes each element in the <a href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/iterator/trait.Iterator.html\" title=\"trait core::iter::traits::iterator::Iterator\"><code>Iterator</code></a>: if it is an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>, no further\nelements are taken, and the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> is returned. Should no <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>\noccur, the product of all elements is returned.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<p>This multiplies each number in a vector of strings,\nif a string could not be parsed the operation returns <code>Err</code>:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>nums = <span class=\"macro\">vec!</span>[<span class=\"string\">\"5\"</span>, <span class=\"string\">\"10\"</span>, <span class=\"string\">\"1\"</span>, <span class=\"string\">\"2\"</span>];\n<span class=\"kw\">let </span>total: <span class=\"prelude-ty\">Result</span>&lt;usize, <span class=\"kw\">_</span>&gt; = nums.iter().map(|w| w.parse::&lt;usize&gt;()).product();\n<span class=\"macro\">assert_eq!</span>(total, <span class=\"prelude-val\">Ok</span>(<span class=\"number\">100</span>));\n<span class=\"kw\">let </span>nums = <span class=\"macro\">vec!</span>[<span class=\"string\">\"5\"</span>, <span class=\"string\">\"10\"</span>, <span class=\"string\">\"one\"</span>, <span class=\"string\">\"2\"</span>];\n<span class=\"kw\">let </span>total: <span class=\"prelude-ty\">Result</span>&lt;usize, <span class=\"kw\">_</span>&gt; = nums.iter().map(|w| w.parse::&lt;usize&gt;()).product();\n<span class=\"macro\">assert!</span>(total.is_err());</code></pre></div>\n</div></details></div></details>","Product<Result<U, E>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Residual%3CT%3E-for-Result%3CInfallible,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2064\">Source</a><a href=\"#impl-Residual%3CT%3E-for-Result%3CInfallible,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Residual.html\" title=\"trait core::ops::try_trait::Residual\">Residual</a>&lt;T&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/enum.Infallible.html\" title=\"enum core::convert::Infallible\">Infallible</a>, E&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle\" open><summary><section id=\"associatedtype.TryType\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2065\">Source</a><a href=\"#associatedtype.TryType\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Residual.html#associatedtype.TryType\" class=\"associatedtype\">TryType</a> = <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>try_trait_v2_residual</code>)</span></div></span><div class='docblock'>The “return” type of this meta-function.</div></details></div></details>","Residual<T>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Result%3C%26T,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1558\">Source</a><a href=\"#impl-Result%3C%26T,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;T</a>, E&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.copied\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.59.0, const since 1.83.0\">1.59.0 (const: 1.83.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1575-1577\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.copied\" class=\"fn\">copied</a>(self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Copy.html\" title=\"trait core::marker::Copy\">Copy</a>,</div></h4></section></summary><div class=\"docblock\"><p>Maps a <code>Result&lt;&amp;T, E&gt;</code> to a <code>Result&lt;T, E&gt;</code> by copying the contents of the\n<code>Ok</code> part.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>val = <span class=\"number\">12</span>;\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>i32, i32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;</span>val);\n<span class=\"macro\">assert_eq!</span>(x, <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;</span><span class=\"number\">12</span>));\n<span class=\"kw\">let </span>copied = x.copied();\n<span class=\"macro\">assert_eq!</span>(copied, <span class=\"prelude-val\">Ok</span>(<span class=\"number\">12</span>));</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.cloned\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.59.0\">1.59.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1601-1603\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.cloned\" class=\"fn\">cloned</a>(self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h4></section></summary><div class=\"docblock\"><p>Maps a <code>Result&lt;&amp;T, E&gt;</code> to a <code>Result&lt;T, E&gt;</code> by cloning the contents of the\n<code>Ok</code> part.</p>\n<h5 id=\"examples-1\"><a class=\"doc-anchor\" href=\"#examples-1\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>val = <span class=\"number\">12</span>;\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>i32, i32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;</span>val);\n<span class=\"macro\">assert_eq!</span>(x, <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;</span><span class=\"number\">12</span>));\n<span class=\"kw\">let </span>cloned = x.cloned();\n<span class=\"macro\">assert_eq!</span>(cloned, <span class=\"prelude-val\">Ok</span>(<span class=\"number\">12</span>));</code></pre></div>\n</div></details></div></details>",0,"weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Result%3C%26mut+T,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1609\">Source</a><a href=\"#impl-Result%3C%26mut+T,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;mut T</a>, E&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.copied\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.59.0, const since 1.83.0\">1.59.0 (const: 1.83.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1626-1628\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.copied\" class=\"fn\">copied</a>(self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Copy.html\" title=\"trait core::marker::Copy\">Copy</a>,</div></h4></section></summary><div class=\"docblock\"><p>Maps a <code>Result&lt;&amp;mut T, E&gt;</code> to a <code>Result&lt;T, E&gt;</code> by copying the contents of the\n<code>Ok</code> part.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>val = <span class=\"number\">12</span>;\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;mut </span>i32, i32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;mut </span>val);\n<span class=\"macro\">assert_eq!</span>(x, <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;mut </span><span class=\"number\">12</span>));\n<span class=\"kw\">let </span>copied = x.copied();\n<span class=\"macro\">assert_eq!</span>(copied, <span class=\"prelude-val\">Ok</span>(<span class=\"number\">12</span>));</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.cloned\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.59.0\">1.59.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1652-1654\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.cloned\" class=\"fn\">cloned</a>(self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h4></section></summary><div class=\"docblock\"><p>Maps a <code>Result&lt;&amp;mut T, E&gt;</code> to a <code>Result&lt;T, E&gt;</code> by cloning the contents of the\n<code>Ok</code> part.</p>\n<h5 id=\"examples-1\"><a class=\"doc-anchor\" href=\"#examples-1\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>val = <span class=\"number\">12</span>;\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;mut </span>i32, i32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;mut </span>val);\n<span class=\"macro\">assert_eq!</span>(x, <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;mut </span><span class=\"number\">12</span>));\n<span class=\"kw\">let </span>cloned = x.cloned();\n<span class=\"macro\">assert_eq!</span>(cloned, <span class=\"prelude-val\">Ok</span>(<span class=\"number\">12</span>));</code></pre></div>\n</div></details></div></details>",0,"weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Result%3COption%3CT%3E,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1660\">Source</a><a href=\"#impl-Result%3COption%3CT%3E,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html\" title=\"enum core::option::Option\">Option</a>&lt;T&gt;, E&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.transpose\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.33.0, const since 1.83.0\">1.33.0 (const: 1.83.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1680\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.transpose\" class=\"fn\">transpose</a>(self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html\" title=\"enum core::option::Option\">Option</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;&gt;</h4></section></summary><div class=\"docblock\"><p>Transposes a <code>Result</code> of an <code>Option</code> into an <code>Option</code> of a <code>Result</code>.</p>\n<p><code>Ok(None)</code> will be mapped to <code>None</code>.\n<code>Ok(Some(_))</code> and <code>Err(_)</code> will be mapped to <code>Some(Ok(_))</code> and <code>Some(Err(_))</code>.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#[derive(Debug, Eq, PartialEq)]\n</span><span class=\"kw\">struct </span>SomeErr;\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"prelude-ty\">Option</span>&lt;i32&gt;, SomeErr&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"prelude-val\">Some</span>(<span class=\"number\">5</span>));\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Option</span>&lt;<span class=\"prelude-ty\">Result</span>&lt;i32, SomeErr&gt;&gt; = <span class=\"prelude-val\">Some</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"number\">5</span>));\n<span class=\"macro\">assert_eq!</span>(x.transpose(), y);</code></pre></div>\n</div></details></div></details>",0,"weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Result%3CResult%3CT,+E%3E,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1689\">Source</a><a href=\"#impl-Result%3CResult%3CT,+E%3E,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;, E&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.flatten\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1717\">Source</a><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.flatten\" class=\"fn\">flatten</a>(self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;</h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>result_flattening</code>)</span></div></span></summary><div class=\"docblock\"><p>Converts from <code>Result&lt;Result&lt;T, E&gt;, E&gt;</code> to <code>Result&lt;T, E&gt;</code></p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(result_flattening)]\n</span><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span><span class=\"lifetime\">'static </span>str, u32&gt;, u32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"hello\"</span>));\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"hello\"</span>), x.flatten());\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span><span class=\"lifetime\">'static </span>str, u32&gt;, u32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"prelude-val\">Err</span>(<span class=\"number\">6</span>));\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Err</span>(<span class=\"number\">6</span>), x.flatten());\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span><span class=\"lifetime\">'static </span>str, u32&gt;, u32&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"number\">6</span>);\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Err</span>(<span class=\"number\">6</span>), x.flatten());</code></pre></div>\n<p>Flattening only removes one level of nesting at a time:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"attr\">#![feature(result_flattening)]\n</span><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"prelude-ty\">Result</span>&lt;<span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span><span class=\"lifetime\">'static </span>str, u32&gt;, u32&gt;, u32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"hello\"</span>)));\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"hello\"</span>)), x.flatten());\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"hello\"</span>), x.flatten().flatten());</code></pre></div>\n</div></details></div></details>",0,"weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Result%3CT,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#564\">Source</a><a href=\"#impl-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.is_ok\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0, const since 1.48.0\">1.0.0 (const: 1.48.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#584\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.is_ok\" class=\"fn\">is_ok</a>(&amp;self) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class=\"docblock\"><p>Returns <code>true</code> if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;i32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(-<span class=\"number\">3</span>);\n<span class=\"macro\">assert_eq!</span>(x.is_ok(), <span class=\"bool-val\">true</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;i32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Some error message\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.is_ok(), <span class=\"bool-val\">false</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.is_ok_and\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.70.0\">1.70.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#609\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.is_ok_and\" class=\"fn\">is_ok_and</a>(self, f: impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(T) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class=\"docblock\"><p>Returns <code>true</code> if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> and the value inside of it matches a predicate.</p>\n<h5 id=\"examples-1\"><a class=\"doc-anchor\" href=\"#examples-1\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(x.is_ok_and(|x| x &gt; <span class=\"number\">1</span>), <span class=\"bool-val\">true</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">0</span>);\n<span class=\"macro\">assert_eq!</span>(x.is_ok_and(|x| x &gt; <span class=\"number\">1</span>), <span class=\"bool-val\">false</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"hey\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.is_ok_and(|x| x &gt; <span class=\"number\">1</span>), <span class=\"bool-val\">false</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;String, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"ownership\"</span>.to_string());\n<span class=\"macro\">assert_eq!</span>(x.as_ref().is_ok_and(|x| x.len() &gt; <span class=\"number\">1</span>), <span class=\"bool-val\">true</span>);\n<span class=\"macro\">println!</span>(<span class=\"string\">\"still alive {:?}\"</span>, x);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.is_err\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0, const since 1.48.0\">1.0.0 (const: 1.48.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#631\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.is_err\" class=\"fn\">is_err</a>(&amp;self) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class=\"docblock\"><p>Returns <code>true</code> if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>.</p>\n<h5 id=\"examples-2\"><a class=\"doc-anchor\" href=\"#examples-2\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;i32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(-<span class=\"number\">3</span>);\n<span class=\"macro\">assert_eq!</span>(x.is_err(), <span class=\"bool-val\">false</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;i32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Some error message\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.is_err(), <span class=\"bool-val\">true</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.is_err_and\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.70.0\">1.70.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#658\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.is_err_and\" class=\"fn\">is_err_and</a>(self, f: impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(E) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a>) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></summary><div class=\"docblock\"><p>Returns <code>true</code> if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> and the value inside of it matches a predicate.</p>\n<h5 id=\"examples-3\"><a class=\"doc-anchor\" href=\"#examples-3\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::io::{Error, ErrorKind};\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, Error&gt; = <span class=\"prelude-val\">Err</span>(Error::new(ErrorKind::NotFound, <span class=\"string\">\"!\"</span>));\n<span class=\"macro\">assert_eq!</span>(x.is_err_and(|x| x.kind() == ErrorKind::NotFound), <span class=\"bool-val\">true</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, Error&gt; = <span class=\"prelude-val\">Err</span>(Error::new(ErrorKind::PermissionDenied, <span class=\"string\">\"!\"</span>));\n<span class=\"macro\">assert_eq!</span>(x.is_err_and(|x| x.kind() == ErrorKind::NotFound), <span class=\"bool-val\">false</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, Error&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">123</span>);\n<span class=\"macro\">assert_eq!</span>(x.is_err_and(|x| x.kind() == ErrorKind::NotFound), <span class=\"bool-val\">false</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, String&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"ownership\"</span>.to_string());\n<span class=\"macro\">assert_eq!</span>(x.as_ref().is_err_and(|x| x.len() &gt; <span class=\"number\">1</span>), <span class=\"bool-val\">true</span>);\n<span class=\"macro\">println!</span>(<span class=\"string\">\"still alive {:?}\"</span>, x);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.ok\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#686\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.ok\" class=\"fn\">ok</a>(self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html\" title=\"enum core::option::Option\">Option</a>&lt;T&gt;</h4></section></summary><div class=\"docblock\"><p>Converts from <code>Result&lt;T, E&gt;</code> to <a href=\"https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html\" title=\"enum core::option::Option\"><code>Option&lt;T&gt;</code></a>.</p>\n<p>Converts <code>self</code> into an <a href=\"https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html\" title=\"enum core::option::Option\"><code>Option&lt;T&gt;</code></a>, consuming <code>self</code>,\nand discarding the error, if any.</p>\n<h5 id=\"examples-4\"><a class=\"doc-anchor\" href=\"#examples-4\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(x.ok(), <span class=\"prelude-val\">Some</span>(<span class=\"number\">2</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Nothing here\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.ok(), <span class=\"prelude-val\">None</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.err\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#709\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.err\" class=\"fn\">err</a>(self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html\" title=\"enum core::option::Option\">Option</a>&lt;E&gt;</h4></section></summary><div class=\"docblock\"><p>Converts from <code>Result&lt;T, E&gt;</code> to <a href=\"https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html\" title=\"enum core::option::Option\"><code>Option&lt;E&gt;</code></a>.</p>\n<p>Converts <code>self</code> into an <a href=\"https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html\" title=\"enum core::option::Option\"><code>Option&lt;E&gt;</code></a>, consuming <code>self</code>,\nand discarding the success value, if any.</p>\n<h5 id=\"examples-5\"><a class=\"doc-anchor\" href=\"#examples-5\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(x.err(), <span class=\"prelude-val\">None</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Nothing here\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.err(), <span class=\"prelude-val\">Some</span>(<span class=\"string\">\"Nothing here\"</span>));</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_ref\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0, const since 1.48.0\">1.0.0 (const: 1.48.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#737\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.as_ref\" class=\"fn\">as_ref</a>(&amp;self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;T</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;E</a>&gt;</h4></section></summary><div class=\"docblock\"><p>Converts from <code>&amp;Result&lt;T, E&gt;</code> to <code>Result&lt;&amp;T, &amp;E&gt;</code>.</p>\n<p>Produces a new <code>Result</code>, containing a reference\ninto the original, leaving the original in place.</p>\n<h5 id=\"examples-6\"><a class=\"doc-anchor\" href=\"#examples-6\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(x.as_ref(), <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;</span><span class=\"number\">2</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Error\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.as_ref(), <span class=\"prelude-val\">Err</span>(<span class=\"kw-2\">&amp;</span><span class=\"string\">\"Error\"</span>));</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_mut\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0, const since 1.83.0\">1.0.0 (const: 1.83.0)</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#767\">Source</a></span><h4 class=\"code-header\">pub const fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.as_mut\" class=\"fn\">as_mut</a>(&amp;mut self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;mut T</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;mut E</a>&gt;</h4></section></summary><div class=\"docblock\"><p>Converts from <code>&amp;mut Result&lt;T, E&gt;</code> to <code>Result&lt;&amp;mut T, &amp;mut E&gt;</code>.</p>\n<h5 id=\"examples-7\"><a class=\"doc-anchor\" href=\"#examples-7\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">fn </span>mutate(r: <span class=\"kw-2\">&amp;mut </span><span class=\"prelude-ty\">Result</span>&lt;i32, i32&gt;) {\n    <span class=\"kw\">match </span>r.as_mut() {\n        <span class=\"prelude-val\">Ok</span>(v) =&gt; <span class=\"kw-2\">*</span>v = <span class=\"number\">42</span>,\n        <span class=\"prelude-val\">Err</span>(e) =&gt; <span class=\"kw-2\">*</span>e = <span class=\"number\">0</span>,\n    }\n}\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>x: <span class=\"prelude-ty\">Result</span>&lt;i32, i32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\nmutate(<span class=\"kw-2\">&amp;mut </span>x);\n<span class=\"macro\">assert_eq!</span>(x.unwrap(), <span class=\"number\">42</span>);\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>x: <span class=\"prelude-ty\">Result</span>&lt;i32, i32&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"number\">13</span>);\nmutate(<span class=\"kw-2\">&amp;mut </span>x);\n<span class=\"macro\">assert_eq!</span>(x.unwrap_err(), <span class=\"number\">0</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.map\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#799\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.map\" class=\"fn\">map</a>&lt;U, F&gt;(self, op: F) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(T) -&gt; U,</div></h4></section></summary><div class=\"docblock\"><p>Maps a <code>Result&lt;T, E&gt;</code> to <code>Result&lt;U, E&gt;</code> by applying a function to a\ncontained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value, leaving an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> value untouched.</p>\n<p>This function can be used to compose the results of two functions.</p>\n<h5 id=\"examples-8\"><a class=\"doc-anchor\" href=\"#examples-8\">§</a>Examples</h5>\n<p>Print the numbers on each line of a string multiplied by two.</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>line = <span class=\"string\">\"1\\n2\\n3\\n4\\n\"</span>;\n\n<span class=\"kw\">for </span>num <span class=\"kw\">in </span>line.lines() {\n    <span class=\"kw\">match </span>num.parse::&lt;i32&gt;().map(|i| i * <span class=\"number\">2</span>) {\n        <span class=\"prelude-val\">Ok</span>(n) =&gt; <span class=\"macro\">println!</span>(<span class=\"string\">\"{n}\"</span>),\n        <span class=\"prelude-val\">Err</span>(..) =&gt; {}\n    }\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.map_or\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.41.0\">1.41.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#827\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.map_or\" class=\"fn\">map_or</a>&lt;U, F&gt;(self, default: U, f: F) -&gt; U<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(T) -&gt; U,</div></h4></section></summary><div class=\"docblock\"><p>Returns the provided default (if <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>), or\napplies a function to the contained value (if <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>).</p>\n<p>Arguments passed to <code>map_or</code> are eagerly evaluated; if you are passing\nthe result of a function call, it is recommended to use <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.map_or_else\" title=\"method core::result::Result::map_or_else\"><code>map_or_else</code></a>,\nwhich is lazily evaluated.</p>\n<h5 id=\"examples-9\"><a class=\"doc-anchor\" href=\"#examples-9\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw\">_</span>, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"foo\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.map_or(<span class=\"number\">42</span>, |v| v.len()), <span class=\"number\">3</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>str, <span class=\"kw\">_</span>&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"bar\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.map_or(<span class=\"number\">42</span>, |v| v.len()), <span class=\"number\">42</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.map_or_else\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.41.0\">1.41.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#854\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.map_or_else\" class=\"fn\">map_or_else</a>&lt;U, D, F&gt;(self, default: D, f: F) -&gt; U<div class=\"where\">where\n    D: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(E) -&gt; U,\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(T) -&gt; U,</div></h4></section></summary><div class=\"docblock\"><p>Maps a <code>Result&lt;T, E&gt;</code> to <code>U</code> by applying fallback function <code>default</code> to\na contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> value, or function <code>f</code> to a contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value.</p>\n<p>This function can be used to unpack a successful result\nwhile handling an error.</p>\n<h5 id=\"examples-10\"><a class=\"doc-anchor\" href=\"#examples-10\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>k = <span class=\"number\">21</span>;\n\n<span class=\"kw\">let </span>x : <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw\">_</span>, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"foo\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.map_or_else(|e| k * <span class=\"number\">2</span>, |v| v.len()), <span class=\"number\">3</span>);\n\n<span class=\"kw\">let </span>x : <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>str, <span class=\"kw\">_</span>&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"bar\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.map_or_else(|e| k * <span class=\"number\">2</span>, |v| v.len()), <span class=\"number\">42</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.map_err\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#881\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.map_err\" class=\"fn\">map_err</a>&lt;F, O&gt;(self, op: O) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, F&gt;<div class=\"where\">where\n    O: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(E) -&gt; F,</div></h4></section></summary><div class=\"docblock\"><p>Maps a <code>Result&lt;T, E&gt;</code> to <code>Result&lt;T, F&gt;</code> by applying a function to a\ncontained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> value, leaving an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value untouched.</p>\n<p>This function can be used to pass through a successful result while handling\nan error.</p>\n<h5 id=\"examples-11\"><a class=\"doc-anchor\" href=\"#examples-11\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">fn </span>stringify(x: u32) -&gt; String { <span class=\"macro\">format!</span>(<span class=\"string\">\"error code: {x}\"</span>) }\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, u32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(x.map_err(stringify), <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, u32&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"number\">13</span>);\n<span class=\"macro\">assert_eq!</span>(x.map_err(stringify), <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"error code: 13\"</span>.to_string()));</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.inspect\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.76.0\">1.76.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#903\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.inspect\" class=\"fn\">inspect</a>&lt;F&gt;(self, f: F) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;T</a>),</div></h4></section></summary><div class=\"docblock\"><p>Calls a function with a reference to the contained value if <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>.</p>\n<p>Returns the original result.</p>\n<h5 id=\"examples-12\"><a class=\"doc-anchor\" href=\"#examples-12\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: u8 = <span class=\"string\">\"4\"\n    </span>.parse::&lt;u8&gt;()\n    .inspect(|x| <span class=\"macro\">println!</span>(<span class=\"string\">\"original: {x}\"</span>))\n    .map(|x| x.pow(<span class=\"number\">3</span>))\n    .expect(<span class=\"string\">\"failed to parse number\"</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.inspect_err\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.76.0\">1.76.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#927\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.inspect_err\" class=\"fn\">inspect_err</a>&lt;F&gt;(self, f: F) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;E</a>),</div></h4></section></summary><div class=\"docblock\"><p>Calls a function with a reference to the contained value if <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>.</p>\n<p>Returns the original result.</p>\n<h5 id=\"examples-13\"><a class=\"doc-anchor\" href=\"#examples-13\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::{fs, io};\n\n<span class=\"kw\">fn </span>read() -&gt; io::Result&lt;String&gt; {\n    fs::read_to_string(<span class=\"string\">\"address.txt\"</span>)\n        .inspect_err(|e| <span class=\"macro\">eprintln!</span>(<span class=\"string\">\"failed to read file: {e}\"</span>))\n}</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_deref\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.47.0\">1.47.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#953-955\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.as_deref\" class=\"fn\">as_deref</a>(&amp;self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;&amp;&lt;T as <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/deref/trait.Deref.html\" title=\"trait core::ops::deref::Deref\">Deref</a>&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/deref/trait.Deref.html#associatedtype.Target\" title=\"type core::ops::deref::Deref::Target\">Target</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;E</a>&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/deref/trait.Deref.html\" title=\"trait core::ops::deref::Deref\">Deref</a>,</div></h4></section></summary><div class=\"docblock\"><p>Converts from <code>Result&lt;T, E&gt;</code> (or <code>&amp;Result&lt;T, E&gt;</code>) to <code>Result&lt;&amp;&lt;T as Deref&gt;::Target, &amp;E&gt;</code>.</p>\n<p>Coerces the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> variant of the original <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\"><code>Result</code></a> via <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/deref/trait.Deref.html\" title=\"trait core::ops::deref::Deref\"><code>Deref</code></a>\nand returns the new <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\"><code>Result</code></a>.</p>\n<h5 id=\"examples-14\"><a class=\"doc-anchor\" href=\"#examples-14\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;String, u32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"hello\"</span>.to_string());\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>str, <span class=\"kw-2\">&amp;</span>u32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"hello\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.as_deref(), y);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;String, u32&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"number\">42</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>str, <span class=\"kw-2\">&amp;</span>u32&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"kw-2\">&amp;</span><span class=\"number\">42</span>);\n<span class=\"macro\">assert_eq!</span>(x.as_deref(), y);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.as_deref_mut\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.47.0\">1.47.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#980-982\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.as_deref_mut\" class=\"fn\">as_deref_mut</a>(&amp;mut self) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;&amp;mut &lt;T as <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/deref/trait.Deref.html\" title=\"trait core::ops::deref::Deref\">Deref</a>&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/deref/trait.Deref.html#associatedtype.Target\" title=\"type core::ops::deref::Deref::Target\">Target</a>, <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;mut E</a>&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/deref/trait.DerefMut.html\" title=\"trait core::ops::deref::DerefMut\">DerefMut</a>,</div></h4></section></summary><div class=\"docblock\"><p>Converts from <code>Result&lt;T, E&gt;</code> (or <code>&amp;mut Result&lt;T, E&gt;</code>) to <code>Result&lt;&amp;mut &lt;T as DerefMut&gt;::Target, &amp;mut E&gt;</code>.</p>\n<p>Coerces the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> variant of the original <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\"><code>Result</code></a> via <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/deref/trait.DerefMut.html\" title=\"trait core::ops::deref::DerefMut\"><code>DerefMut</code></a>\nand returns the new <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\"><code>Result</code></a>.</p>\n<h5 id=\"examples-15\"><a class=\"doc-anchor\" href=\"#examples-15\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>s = <span class=\"string\">\"HELLO\"</span>.to_string();\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>x: <span class=\"prelude-ty\">Result</span>&lt;String, u32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"hello\"</span>.to_string());\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;mut </span>str, <span class=\"kw-2\">&amp;mut </span>u32&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"kw-2\">&amp;mut </span>s);\n<span class=\"macro\">assert_eq!</span>(x.as_deref_mut().map(|x| { x.make_ascii_uppercase(); x }), y);\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>i = <span class=\"number\">42</span>;\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>x: <span class=\"prelude-ty\">Result</span>&lt;String, u32&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"number\">42</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;mut </span>str, <span class=\"kw-2\">&amp;mut </span>u32&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"kw-2\">&amp;mut </span>i);\n<span class=\"macro\">assert_eq!</span>(x.as_deref_mut().map(|x| { x.make_ascii_uppercase(); x }), y);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.iter\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1006\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.iter\" class=\"fn\">iter</a>(&amp;self) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/result/struct.Iter.html\" title=\"struct core::result::Iter\">Iter</a>&lt;'_, T&gt;</h4></section></summary><div class=\"docblock\"><p>Returns an iterator over the possibly contained value.</p>\n<p>The iterator yields one value if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Result::Ok</code></a>, otherwise none.</p>\n<h5 id=\"examples-16\"><a class=\"doc-anchor\" href=\"#examples-16\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">7</span>);\n<span class=\"macro\">assert_eq!</span>(x.iter().next(), <span class=\"prelude-val\">Some</span>(<span class=\"kw-2\">&amp;</span><span class=\"number\">7</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"nothing!\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.iter().next(), <span class=\"prelude-val\">None</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.iter_mut\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1029\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.iter_mut\" class=\"fn\">iter_mut</a>(&amp;mut self) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/result/struct.IterMut.html\" title=\"struct core::result::IterMut\">IterMut</a>&lt;'_, T&gt;</h4></section></summary><div class=\"docblock\"><p>Returns a mutable iterator over the possibly contained value.</p>\n<p>The iterator yields one value if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Result::Ok</code></a>, otherwise none.</p>\n<h5 id=\"examples-17\"><a class=\"doc-anchor\" href=\"#examples-17\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span><span class=\"kw-2\">mut </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">7</span>);\n<span class=\"kw\">match </span>x.iter_mut().next() {\n    <span class=\"prelude-val\">Some</span>(v) =&gt; <span class=\"kw-2\">*</span>v = <span class=\"number\">40</span>,\n    <span class=\"prelude-val\">None </span>=&gt; {},\n}\n<span class=\"macro\">assert_eq!</span>(x, <span class=\"prelude-val\">Ok</span>(<span class=\"number\">40</span>));\n\n<span class=\"kw\">let </span><span class=\"kw-2\">mut </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"nothing!\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.iter_mut().next(), <span class=\"prelude-val\">None</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.expect\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.4.0\">1.4.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1083-1085\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.expect\" class=\"fn\">expect</a>(self, msg: &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.str.html\">str</a>) -&gt; T<div class=\"where\">where\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a>,</div></h4></section></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value, consuming the <code>self</code> value.</p>\n<p>Because this function may panic, its use is generally discouraged.\nInstead, prefer to use pattern matching and handle the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>\ncase explicitly, or call <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.unwrap_or\" title=\"method core::result::Result::unwrap_or\"><code>unwrap_or</code></a>, <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.unwrap_or_else\" title=\"method core::result::Result::unwrap_or_else\"><code>unwrap_or_else</code></a>, or\n<a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.unwrap_or_default\" title=\"method core::result::Result::unwrap_or_default\"><code>unwrap_or_default</code></a>.</p>\n<h5 id=\"panics\"><a class=\"doc-anchor\" href=\"#panics\">§</a>Panics</h5>\n<p>Panics if the value is an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>, with a panic message including the\npassed message, and the content of the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>.</p>\n<h5 id=\"examples-18\"><a class=\"doc-anchor\" href=\"#examples-18\">§</a>Examples</h5>\n<div class=\"example-wrap should_panic\"><a href=\"#\" class=\"tooltip\" title=\"This example panics\">ⓘ</a><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"emergency failure\"</span>);\nx.expect(<span class=\"string\">\"Testing expect\"</span>); <span class=\"comment\">// panics with `Testing expect: emergency failure`</span></code></pre></div>\n<h5 id=\"recommended-message-style\"><a class=\"doc-anchor\" href=\"#recommended-message-style\">§</a>Recommended Message Style</h5>\n<p>We recommend that <code>expect</code> messages are used to describe the reason you\n<em>expect</em> the <code>Result</code> should be <code>Ok</code>.</p>\n\n<div class=\"example-wrap should_panic\"><a href=\"#\" class=\"tooltip\" title=\"This example panics\">ⓘ</a><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>path = std::env::var(<span class=\"string\">\"IMPORTANT_PATH\"</span>)\n    .expect(<span class=\"string\">\"env variable `IMPORTANT_PATH` should be set by `wrapper_script.sh`\"</span>);</code></pre></div>\n<p><strong>Hint</strong>: If you’re having trouble remembering how to phrase expect\nerror messages remember to focus on the word “should” as in “env\nvariable should be set by blah” or “the given binary should be available\nand executable by the current user”.</p>\n<p>For more detail on expect message styles and the reasoning behind our recommendation please\nrefer to the section on <a href=\"../../std/error/index.html#common-message-styles\">“Common Message\nStyles”</a> in the\n<a href=\"../../std/error/index.html\"><code>std::error</code></a> module docs.</p>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.unwrap\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1131-1133\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.unwrap\" class=\"fn\">unwrap</a>(self) -&gt; T<div class=\"where\">where\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a>,</div></h4></section></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value, consuming the <code>self</code> value.</p>\n<p>Because this function may panic, its use is generally discouraged.\nPanics are meant for unrecoverable errors, and\n<a href=\"https://doc.rust-lang.org/book/ch09-01-unrecoverable-errors-with-panic.html\">may abort the entire program</a>.</p>\n<p>Instead, prefer to use <a href=\"https://doc.rust-lang.org/book/ch09-02-recoverable-errors-with-result.html#a-shortcut-for-propagating-errors-the--operator\">the <code>?</code> (try) operator</a>, or pattern matching\nto handle the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> case explicitly, or call <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.unwrap_or\" title=\"method core::result::Result::unwrap_or\"><code>unwrap_or</code></a>,\n<a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.unwrap_or_else\" title=\"method core::result::Result::unwrap_or_else\"><code>unwrap_or_else</code></a>, or <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.unwrap_or_default\" title=\"method core::result::Result::unwrap_or_default\"><code>unwrap_or_default</code></a>.</p>\n<h5 id=\"panics-1\"><a class=\"doc-anchor\" href=\"#panics-1\">§</a>Panics</h5>\n<p>Panics if the value is an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>, with a panic message provided by the\n<a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>’s value.</p>\n<h5 id=\"examples-19\"><a class=\"doc-anchor\" href=\"#examples-19\">§</a>Examples</h5>\n<p>Basic usage:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(x.unwrap(), <span class=\"number\">2</span>);</code></pre></div>\n\n<div class=\"example-wrap should_panic\"><a href=\"#\" class=\"tooltip\" title=\"This example panics\">ⓘ</a><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"emergency failure\"</span>);\nx.unwrap(); <span class=\"comment\">// panics with `emergency failure`</span></code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.unwrap_or_default\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.16.0\">1.16.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1168-1170\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.unwrap_or_default\" class=\"fn\">unwrap_or_default</a>(self) -&gt; T<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a>,</div></h4></section></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value or a default</p>\n<p>Consumes the <code>self</code> argument then, if <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>, returns the contained\nvalue, otherwise if <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>, returns the default value for that\ntype.</p>\n<h5 id=\"examples-20\"><a class=\"doc-anchor\" href=\"#examples-20\">§</a>Examples</h5>\n<p>Converts a string to an integer, turning poorly-formed strings\ninto 0 (the default value for integers). <a href=\"https://doc.rust-lang.org/1.88.0/std/primitive.str.html#method.parse\" title=\"method str::parse\"><code>parse</code></a> converts\na string to any other type that implements <a href=\"https://doc.rust-lang.org/1.88.0/core/str/traits/trait.FromStr.html\" title=\"trait core::str::traits::FromStr\"><code>FromStr</code></a>, returning an\n<a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> on error.</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>good_year_from_input = <span class=\"string\">\"1909\"</span>;\n<span class=\"kw\">let </span>bad_year_from_input = <span class=\"string\">\"190blarg\"</span>;\n<span class=\"kw\">let </span>good_year = good_year_from_input.parse().unwrap_or_default();\n<span class=\"kw\">let </span>bad_year = bad_year_from_input.parse().unwrap_or_default();\n\n<span class=\"macro\">assert_eq!</span>(<span class=\"number\">1909</span>, good_year);\n<span class=\"macro\">assert_eq!</span>(<span class=\"number\">0</span>, bad_year);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.expect_err\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.17.0\">1.17.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1195-1197\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.expect_err\" class=\"fn\">expect_err</a>(self, msg: &amp;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.str.html\">str</a>) -&gt; E<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a>,</div></h4></section></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> value, consuming the <code>self</code> value.</p>\n<h5 id=\"panics-2\"><a class=\"doc-anchor\" href=\"#panics-2\">§</a>Panics</h5>\n<p>Panics if the value is an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>, with a panic message including the\npassed message, and the content of the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>.</p>\n<h5 id=\"examples-21\"><a class=\"doc-anchor\" href=\"#examples-21\">§</a>Examples</h5>\n<div class=\"example-wrap should_panic\"><a href=\"#\" class=\"tooltip\" title=\"This example panics\">ⓘ</a><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">10</span>);\nx.expect_err(<span class=\"string\">\"Testing expect_err\"</span>); <span class=\"comment\">// panics with `Testing expect_err: 10`</span></code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.unwrap_err\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1226-1228\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.unwrap_err\" class=\"fn\">unwrap_err</a>(self) -&gt; E<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a>,</div></h4></section></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> value, consuming the <code>self</code> value.</p>\n<h5 id=\"panics-3\"><a class=\"doc-anchor\" href=\"#panics-3\">§</a>Panics</h5>\n<p>Panics if the value is an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>, with a custom panic message provided\nby the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>’s value.</p>\n<h5 id=\"examples-22\"><a class=\"doc-anchor\" href=\"#examples-22\">§</a>Examples</h5>\n<div class=\"example-wrap should_panic\"><a href=\"#\" class=\"tooltip\" title=\"This example panics\">ⓘ</a><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\nx.unwrap_err(); <span class=\"comment\">// panics with `2`</span></code></pre></div>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"emergency failure\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.unwrap_err(), <span class=\"string\">\"emergency failure\"</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_ok\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1261-1263\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.into_ok\" class=\"fn\">into_ok</a>(self) -&gt; T<div class=\"where\">where\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html\" title=\"trait core::convert::Into\">Into</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.never.html\">!</a>&gt;,</div></h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>unwrap_infallible</code>)</span></div></span></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value, but never panics.</p>\n<p>Unlike <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.unwrap\" title=\"method core::result::Result::unwrap\"><code>unwrap</code></a>, this method is known to never panic on the\nresult types it is implemented for. Therefore, it can be used\ninstead of <code>unwrap</code> as a maintainability safeguard that will fail\nto compile if the error type of the <code>Result</code> is later changed\nto an error that can actually occur.</p>\n<h5 id=\"examples-23\"><a class=\"doc-anchor\" href=\"#examples-23\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code>\n<span class=\"kw\">fn </span>only_good_news() -&gt; <span class=\"prelude-ty\">Result</span>&lt;String, !&gt; {\n    <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"this is fine\"</span>.into())\n}\n\n<span class=\"kw\">let </span>s: String = only_good_news().into_ok();\n<span class=\"macro\">println!</span>(<span class=\"string\">\"{s}\"</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.into_err\" class=\"method\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1296-1298\">Source</a><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.into_err\" class=\"fn\">into_err</a>(self) -&gt; E<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html\" title=\"trait core::convert::Into\">Into</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.never.html\">!</a>&gt;,</div></h4></section><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>unwrap_infallible</code>)</span></div></span></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> value, but never panics.</p>\n<p>Unlike <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.unwrap_err\" title=\"method core::result::Result::unwrap_err\"><code>unwrap_err</code></a>, this method is known to never panic on the\nresult types it is implemented for. Therefore, it can be used\ninstead of <code>unwrap_err</code> as a maintainability safeguard that will fail\nto compile if the ok type of the <code>Result</code> is later changed\nto a type that can actually occur.</p>\n<h5 id=\"examples-24\"><a class=\"doc-anchor\" href=\"#examples-24\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code>\n<span class=\"kw\">fn </span>only_bad_news() -&gt; <span class=\"prelude-ty\">Result</span>&lt;!, String&gt; {\n    <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Oops, it failed\"</span>.into())\n}\n\n<span class=\"kw\">let </span>error: String = only_bad_news().into_err();\n<span class=\"macro\">println!</span>(<span class=\"string\">\"{error}\"</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.and\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1339\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.and\" class=\"fn\">and</a>&lt;U&gt;(self, res: <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;</h4></section></summary><div class=\"docblock\"><p>Returns <code>res</code> if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>, otherwise returns the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> value of <code>self</code>.</p>\n<p>Arguments passed to <code>and</code> are eagerly evaluated; if you are passing the\nresult of a function call, it is recommended to use <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.and_then\" title=\"method core::result::Result::and_then\"><code>and_then</code></a>, which is\nlazily evaluated.</p>\n<h5 id=\"examples-25\"><a class=\"doc-anchor\" href=\"#examples-25\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>str, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"late error\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.and(y), <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"late error\"</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"early error\"</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>str, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"foo\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.and(y), <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"early error\"</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"not a 2\"</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>str, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"late error\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.and(y), <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"not a 2\"</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;<span class=\"kw-2\">&amp;</span>str, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"different result type\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.and(y), <span class=\"prelude-val\">Ok</span>(<span class=\"string\">\"different result type\"</span>));</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.and_then\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1379\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.and_then\" class=\"fn\">and_then</a>&lt;U, F&gt;(self, op: F) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(T) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;,</div></h4></section></summary><div class=\"docblock\"><p>Calls <code>op</code> if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>, otherwise returns the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> value of <code>self</code>.</p>\n<p>This function can be used for control flow based on <code>Result</code> values.</p>\n<h5 id=\"examples-26\"><a class=\"doc-anchor\" href=\"#examples-26\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">fn </span>sq_then_to_string(x: u32) -&gt; <span class=\"prelude-ty\">Result</span>&lt;String, <span class=\"kw-2\">&amp;</span><span class=\"lifetime\">'static </span>str&gt; {\n    x.checked_mul(x).map(|sq| sq.to_string()).ok_or(<span class=\"string\">\"overflowed\"</span>)\n}\n\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>).and_then(sq_then_to_string), <span class=\"prelude-val\">Ok</span>(<span class=\"number\">4</span>.to_string()));\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"number\">1_000_000</span>).and_then(sq_then_to_string), <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"overflowed\"</span>));\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Err</span>(<span class=\"string\">\"not a number\"</span>).and_then(sq_then_to_string), <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"not a number\"</span>));</code></pre></div>\n<p>Often used to chain fallible operations that may return <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>.</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">use </span>std::{io::ErrorKind, path::Path};\n\n<span class=\"comment\">// Note: on Windows \"/\" maps to \"C:\\\"\n</span><span class=\"kw\">let </span>root_modified_time = Path::new(<span class=\"string\">\"/\"</span>).metadata().and_then(|md| md.modified());\n<span class=\"macro\">assert!</span>(root_modified_time.is_ok());\n\n<span class=\"kw\">let </span>should_fail = Path::new(<span class=\"string\">\"/bad/path\"</span>).metadata().and_then(|md| md.modified());\n<span class=\"macro\">assert!</span>(should_fail.is_err());\n<span class=\"macro\">assert_eq!</span>(should_fail.unwrap_err().kind(), ErrorKind::NotFound);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.or\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1415\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.or\" class=\"fn\">or</a>&lt;F&gt;(self, res: <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, F&gt;) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, F&gt;</h4></section></summary><div class=\"docblock\"><p>Returns <code>res</code> if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>, otherwise returns the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value of <code>self</code>.</p>\n<p>Arguments passed to <code>or</code> are eagerly evaluated; if you are passing the\nresult of a function call, it is recommended to use <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.or_else\" title=\"method core::result::Result::or_else\"><code>or_else</code></a>, which is\nlazily evaluated.</p>\n<h5 id=\"examples-27\"><a class=\"doc-anchor\" href=\"#examples-27\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"late error\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.or(y), <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"early error\"</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(x.or(y), <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"not a 2\"</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"late error\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.or(y), <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"late error\"</span>));\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"kw\">let </span>y: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">100</span>);\n<span class=\"macro\">assert_eq!</span>(x.or(y), <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>));</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.or_else\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1440\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.or_else\" class=\"fn\">or_else</a>&lt;F, O&gt;(self, op: O) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, F&gt;<div class=\"where\">where\n    O: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(E) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, F&gt;,</div></h4></section></summary><div class=\"docblock\"><p>Calls <code>op</code> if the result is <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>, otherwise returns the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value of <code>self</code>.</p>\n<p>This function can be used for control flow based on result values.</p>\n<h5 id=\"examples-28\"><a class=\"doc-anchor\" href=\"#examples-28\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">fn </span>sq(x: u32) -&gt; <span class=\"prelude-ty\">Result</span>&lt;u32, u32&gt; { <span class=\"prelude-val\">Ok</span>(x * x) }\n<span class=\"kw\">fn </span>err(x: u32) -&gt; <span class=\"prelude-ty\">Result</span>&lt;u32, u32&gt; { <span class=\"prelude-val\">Err</span>(x) }\n\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>).or_else(sq).or_else(sq), <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>));\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>).or_else(err).or_else(sq), <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>));\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Err</span>(<span class=\"number\">3</span>).or_else(sq).or_else(err), <span class=\"prelude-val\">Ok</span>(<span class=\"number\">9</span>));\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Err</span>(<span class=\"number\">3</span>).or_else(err).or_else(err), <span class=\"prelude-val\">Err</span>(<span class=\"number\">3</span>));</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.unwrap_or\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1467\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.unwrap_or\" class=\"fn\">unwrap_or</a>(self, default: T) -&gt; T</h4></section></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value or a provided default.</p>\n<p>Arguments passed to <code>unwrap_or</code> are eagerly evaluated; if you are passing\nthe result of a function call, it is recommended to use <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#method.unwrap_or_else\" title=\"method core::result::Result::unwrap_or_else\"><code>unwrap_or_else</code></a>,\nwhich is lazily evaluated.</p>\n<h5 id=\"examples-29\"><a class=\"doc-anchor\" href=\"#examples-29\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>default = <span class=\"number\">2</span>;\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">9</span>);\n<span class=\"macro\">assert_eq!</span>(x.unwrap_or(default), <span class=\"number\">9</span>);\n\n<span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"error\"</span>);\n<span class=\"macro\">assert_eq!</span>(x.unwrap_or(default), default);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.unwrap_or_else\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1488\">Source</a></span><h4 class=\"code-header\">pub fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.unwrap_or_else\" class=\"fn\">unwrap_or_else</a>&lt;F&gt;(self, op: F) -&gt; T<div class=\"where\">where\n    F: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html\" title=\"trait core::ops::function::FnOnce\">FnOnce</a>(E) -&gt; T,</div></h4></section></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value or computes it from a closure.</p>\n<h5 id=\"examples-30\"><a class=\"doc-anchor\" href=\"#examples-30\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">fn </span>count(x: <span class=\"kw-2\">&amp;</span>str) -&gt; usize { x.len() }\n\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>).unwrap_or_else(count), <span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(<span class=\"prelude-val\">Err</span>(<span class=\"string\">\"foo\"</span>).unwrap_or_else(count), <span class=\"number\">3</span>);</code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.unwrap_unchecked\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.58.0\">1.58.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1518\">Source</a></span><h4 class=\"code-header\">pub unsafe fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.unwrap_unchecked\" class=\"fn\">unwrap_unchecked</a>(self) -&gt; T</h4></section></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> value, consuming the <code>self</code> value,\nwithout checking that the value is not an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>.</p>\n<h5 id=\"safety\"><a class=\"doc-anchor\" href=\"#safety\">§</a>Safety</h5>\n<p>Calling this method on an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> is <em><a href=\"https://doc.rust-lang.org/reference/behavior-considered-undefined.html\">undefined behavior</a></em>.</p>\n<h5 id=\"examples-31\"><a class=\"doc-anchor\" href=\"#examples-31\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"macro\">assert_eq!</span>(<span class=\"kw\">unsafe </span>{ x.unwrap_unchecked() }, <span class=\"number\">2</span>);</code></pre></div>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"emergency failure\"</span>);\n<span class=\"kw\">unsafe </span>{ x.unwrap_unchecked(); } <span class=\"comment\">// Undefined behavior!</span></code></pre></div>\n</div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.unwrap_err_unchecked\" class=\"method\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.58.0\">1.58.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1549\">Source</a></span><h4 class=\"code-header\">pub unsafe fn <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#tymethod.unwrap_err_unchecked\" class=\"fn\">unwrap_err_unchecked</a>(self) -&gt; E</h4></section></summary><div class=\"docblock\"><p>Returns the contained <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> value, consuming the <code>self</code> value,\nwithout checking that the value is not an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a>.</p>\n<h5 id=\"safety-1\"><a class=\"doc-anchor\" href=\"#safety-1\">§</a>Safety</h5>\n<p>Calling this method on an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Ok\" title=\"variant core::result::Result::Ok\"><code>Ok</code></a> is <em><a href=\"https://doc.rust-lang.org/reference/behavior-considered-undefined.html\">undefined behavior</a></em>.</p>\n<h5 id=\"examples-32\"><a class=\"doc-anchor\" href=\"#examples-32\">§</a>Examples</h5>\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Ok</span>(<span class=\"number\">2</span>);\n<span class=\"kw\">unsafe </span>{ x.unwrap_err_unchecked() }; <span class=\"comment\">// Undefined behavior!</span></code></pre></div>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>x: <span class=\"prelude-ty\">Result</span>&lt;u32, <span class=\"kw-2\">&amp;</span>str&gt; = <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"emergency failure\"</span>);\n<span class=\"macro\">assert_eq!</span>(<span class=\"kw\">unsafe </span>{ x.unwrap_err_unchecked() }, <span class=\"string\">\"emergency failure\"</span>);</code></pre></div>\n</div></details></div></details>",0,"weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Serialize-for-Result%3CT,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/ser/impls.rs.html#716-719\">Source</a><a href=\"#impl-Serialize-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html\" title=\"trait serde::ser::Serialize\">Serialize</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html\" title=\"trait serde::ser::Serialize\">Serialize</a>,\n    E: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html\" title=\"trait serde::ser::Serialize\">Serialize</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.serialize\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde/1.0.219/src/serde/ser/impls.rs.html#721-723\">Source</a><a href=\"#method.serialize\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html#tymethod.serialize\" class=\"fn\">serialize</a>&lt;S&gt;(\n    &amp;self,\n    serializer: S,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;&lt;S as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Ok\" title=\"type serde::ser::Serializer::Ok\">Ok</a>, &lt;S as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Error\" title=\"type serde::ser::Serializer::Error\">Error</a>&gt;<div class=\"where\">where\n    S: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>,</div></h4></section></summary><div class='docblock'>Serialize this value into the given Serde serializer. <a href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serialize.html#tymethod.serialize\">Read more</a></div></details></div></details>","Serialize","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-SerializeAs%3CResult%3CT,+E%3E%3E-for-Result%3CTAs,+EAs%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde_with/1.14.0/src/serde_with/ser/impls.rs.html#195-198\">Source</a><a href=\"#impl-SerializeAs%3CResult%3CT,+E%3E%3E-for-Result%3CTAs,+EAs%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, TAs, E, EAs&gt; <a class=\"trait\" href=\"https://docs.rs/serde_with/1.14.0/serde_with/ser/trait.SerializeAs.html\" title=\"trait serde_with::ser::SerializeAs\">SerializeAs</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;TAs, EAs&gt;<div class=\"where\">where\n    TAs: <a class=\"trait\" href=\"https://docs.rs/serde_with/1.14.0/serde_with/ser/trait.SerializeAs.html\" title=\"trait serde_with::ser::SerializeAs\">SerializeAs</a>&lt;T&gt;,\n    EAs: <a class=\"trait\" href=\"https://docs.rs/serde_with/1.14.0/serde_with/ser/trait.SerializeAs.html\" title=\"trait serde_with::ser::SerializeAs\">SerializeAs</a>&lt;E&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.serialize_as\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde_with/1.14.0/src/serde_with/ser/impls.rs.html#200-202\">Source</a><a href=\"#method.serialize_as\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/serde_with/1.14.0/serde_with/ser/trait.SerializeAs.html#tymethod.serialize_as\" class=\"fn\">serialize_as</a>&lt;S&gt;(\n    source: &amp;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;,\n    serializer: S,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;&lt;S as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Ok\" title=\"type serde::ser::Serializer::Ok\">Ok</a>, &lt;S as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Error\" title=\"type serde::ser::Serializer::Error\">Error</a>&gt;<div class=\"where\">where\n    S: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>,</div></h4></section></summary><div class='docblock'>Serialize this value into the given Serde serializer.</div></details></div></details>","SerializeAs<Result<T, E>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-SerializeAs%3CResult%3CT,+E%3E%3E-for-Result%3CTAs,+EAs%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde_with/3.14.0/src/serde_with/ser/impls.rs.html#312-315\">Source</a><a href=\"#impl-SerializeAs%3CResult%3CT,+E%3E%3E-for-Result%3CTAs,+EAs%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, TAs, E, EAs&gt; <a class=\"trait\" href=\"https://docs.rs/serde_with/3.14.0/serde_with/ser/trait.SerializeAs.html\" title=\"trait serde_with::ser::SerializeAs\">SerializeAs</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;TAs, EAs&gt;<div class=\"where\">where\n    TAs: <a class=\"trait\" href=\"https://docs.rs/serde_with/3.14.0/serde_with/ser/trait.SerializeAs.html\" title=\"trait serde_with::ser::SerializeAs\">SerializeAs</a>&lt;T&gt;,\n    EAs: <a class=\"trait\" href=\"https://docs.rs/serde_with/3.14.0/serde_with/ser/trait.SerializeAs.html\" title=\"trait serde_with::ser::SerializeAs\">SerializeAs</a>&lt;E&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.serialize_as\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://docs.rs/serde_with/3.14.0/src/serde_with/ser/impls.rs.html#317-319\">Source</a><a href=\"#method.serialize_as\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://docs.rs/serde_with/3.14.0/serde_with/ser/trait.SerializeAs.html#tymethod.serialize_as\" class=\"fn\">serialize_as</a>&lt;S&gt;(\n    source: &amp;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;,\n    serializer: S,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;&lt;S as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Ok\" title=\"type serde::ser::Serializer::Ok\">Ok</a>, &lt;S as <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>&gt;::<a class=\"associatedtype\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html#associatedtype.Error\" title=\"type serde::ser::Serializer::Error\">Error</a>&gt;<div class=\"where\">where\n    S: <a class=\"trait\" href=\"https://docs.rs/serde/1.0.219/serde/ser/trait.Serializer.html\" title=\"trait serde::ser::Serializer\">Serializer</a>,</div></h4></section></summary><div class='docblock'>Serialize this value into the given Serde serializer.</div></details></div></details>","SerializeAs<Result<T, E>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Sum%3CResult%3CU,+E%3E%3E-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.16.0\">1.16.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/iter\\traits/accum.rs.html#153-155\">Source</a></span><a href=\"#impl-Sum%3CResult%3CU,+E%3E%3E-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, U, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/accum/trait.Sum.html\" title=\"trait core::iter::traits::accum::Sum\">Sum</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/accum/trait.Sum.html\" title=\"trait core::iter::traits::accum::Sum\">Sum</a>&lt;U&gt;,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.sum\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/iter\\traits/accum.rs.html#175-177\">Source</a><a href=\"#method.sum\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/accum/trait.Sum.html#tymethod.sum\" class=\"fn\">sum</a>&lt;I&gt;(iter: I) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    I: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/iterator/trait.Iterator.html\" title=\"trait core::iter::traits::iterator::Iterator\">Iterator</a>&lt;Item = <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;&gt;,</div></h4></section></summary><div class=\"docblock\"><p>Takes each element in the <a href=\"https://doc.rust-lang.org/1.88.0/core/iter/traits/iterator/trait.Iterator.html\" title=\"trait core::iter::traits::iterator::Iterator\"><code>Iterator</code></a>: if it is an <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>, no further\nelements are taken, and the <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a> is returned. Should no <a href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html#variant.Err\" title=\"variant core::result::Result::Err\"><code>Err</code></a>\noccur, the sum of all elements is returned.</p>\n<h5 id=\"examples\"><a class=\"doc-anchor\" href=\"#examples\">§</a>Examples</h5>\n<p>This sums up every integer in a vector, rejecting the sum if a negative\nelement is encountered:</p>\n\n<div class=\"example-wrap\"><pre class=\"rust rust-example-rendered\"><code><span class=\"kw\">let </span>f = |<span class=\"kw-2\">&amp;</span>x: <span class=\"kw-2\">&amp;</span>i32| <span class=\"kw\">if </span>x &lt; <span class=\"number\">0 </span>{ <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Negative element found\"</span>) } <span class=\"kw\">else </span>{ <span class=\"prelude-val\">Ok</span>(x) };\n<span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, <span class=\"number\">2</span>];\n<span class=\"kw\">let </span>res: <span class=\"prelude-ty\">Result</span>&lt;i32, <span class=\"kw\">_</span>&gt; = v.iter().map(f).sum();\n<span class=\"macro\">assert_eq!</span>(res, <span class=\"prelude-val\">Ok</span>(<span class=\"number\">3</span>));\n<span class=\"kw\">let </span>v = <span class=\"macro\">vec!</span>[<span class=\"number\">1</span>, -<span class=\"number\">2</span>];\n<span class=\"kw\">let </span>res: <span class=\"prelude-ty\">Result</span>&lt;i32, <span class=\"kw\">_</span>&gt; = v.iter().map(f).sum();\n<span class=\"macro\">assert_eq!</span>(res, <span class=\"prelude-val\">Err</span>(<span class=\"string\">\"Negative element found\"</span>));</code></pre></div>\n</div></details></div></details>","Sum<Result<U, E>>","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Termination-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.61.0\">1.61.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/std/process.rs.html#2575\">Source</a></span><a href=\"#impl-Termination-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/std/process/trait.Termination.html\" title=\"trait std::process::Termination\">Termination</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/std/process/trait.Termination.html\" title=\"trait std::process::Termination\">Termination</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Debug.html\" title=\"trait core::fmt::Debug\">Debug</a>,</div></h3></section></summary><div class=\"impl-items\"><details class=\"toggle method-toggle\" open><summary><section id=\"method.report\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/std/process.rs.html#2576\">Source</a><a href=\"#method.report\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/std/process/trait.Termination.html#tymethod.report\" class=\"fn\">report</a>(self) -&gt; <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/std/process/struct.ExitCode.html\" title=\"struct std::process::ExitCode\">ExitCode</a></h4></section></summary><div class='docblock'>Is called to get the representation of the value as status code.\nThis status code is returned to the operating system.</div></details></div></details>","Termination","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-TestTermination-for-Result%3CT,+E%3E\" class=\"impl\"><a href=\"#impl-TestTermination-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; TestTermination for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;</h3></section></summary><div class=\"impl-items\"><section id=\"method.is_success\" class=\"method trait-impl\"><a href=\"#method.is_success\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a class=\"fn\">is_success</a>(&amp;self) -&gt; <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.bool.html\">bool</a></h4></section></div></details>","TestTermination","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-Try-for-Result%3CT,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2026\">Source</a><a href=\"#impl-Try-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html\" title=\"trait core::ops::try_trait::Try\">Try</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;</h3></section></summary><div class=\"impl-items\"><details class=\"toggle\" open><summary><section id=\"associatedtype.Output\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2027\">Source</a><a href=\"#associatedtype.Output\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#associatedtype.Output\" class=\"associatedtype\">Output</a> = T</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>try_trait_v2</code>)</span></div></span><div class='docblock'>The type of the value produced by <code>?</code> when <em>not</em> short-circuiting.</div></details><details class=\"toggle\" open><summary><section id=\"associatedtype.Residual\" class=\"associatedtype trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2028\">Source</a><a href=\"#associatedtype.Residual\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#associatedtype.Residual\" class=\"associatedtype\">Residual</a> = <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/enum.Infallible.html\" title=\"enum core::convert::Infallible\">Infallible</a>, E&gt;</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>try_trait_v2</code>)</span></div></span><div class='docblock'>The type of the value passed to <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.FromResidual.html#tymethod.from_residual\" title=\"associated function core::ops::try_trait::FromResidual::from_residual\"><code>FromResidual::from_residual</code></a>\nas part of <code>?</code> when short-circuiting. <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#associatedtype.Residual\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.from_output\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2031\">Source</a><a href=\"#method.from_output\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#tymethod.from_output\" class=\"fn\">from_output</a>(output: &lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt; as <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html\" title=\"trait core::ops::try_trait::Try\">Try</a>&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#associatedtype.Output\" title=\"type core::ops::try_trait::Try::Output\">Output</a>) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>try_trait_v2</code>)</span></div></span><div class='docblock'>Constructs the type from its <code>Output</code> type. <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#tymethod.from_output\">Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.branch\" class=\"method trait-impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#2036\">Source</a><a href=\"#method.branch\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#tymethod.branch\" class=\"fn\">branch</a>(\n    self,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/control_flow/enum.ControlFlow.html\" title=\"enum core::ops::control_flow::ControlFlow\">ControlFlow</a>&lt;&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt; as <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html\" title=\"trait core::ops::try_trait::Try\">Try</a>&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#associatedtype.Residual\" title=\"type core::ops::try_trait::Try::Residual\">Residual</a>, &lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt; as <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html\" title=\"trait core::ops::try_trait::Try\">Try</a>&gt;::<a class=\"associatedtype\" href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#associatedtype.Output\" title=\"type core::ops::try_trait::Try::Output\">Output</a>&gt;</h4></section></summary><span class=\"item-info\"><div class=\"stab unstable\"><span class=\"emoji\">🔬</span><span>This is a nightly-only experimental API. (<code>try_trait_v2</code>)</span></div></span><div class='docblock'>Used in <code>?</code> to decide whether the operator should produce a value\n(because this returned <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/control_flow/enum.ControlFlow.html#variant.Continue\" title=\"variant core::ops::control_flow::ControlFlow::Continue\"><code>ControlFlow::Continue</code></a>)\nor propagate a value back to the caller\n(because this returned <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/control_flow/enum.ControlFlow.html#variant.Break\" title=\"variant core::ops::control_flow::ControlFlow::Break\"><code>ControlFlow::Break</code></a>). <a href=\"https://doc.rust-lang.org/1.88.0/core/ops/try_trait/trait.Try.html#tymethod.branch\">Read more</a></div></details></div></details>","Try","weibo_crawler_node::error::Result"],["<details class=\"toggle implementors-toggle\" open><summary><section id=\"impl-TryWriteable-for-Result%3CT,+E%3E\" class=\"impl\"><a href=\"#impl-TryWriteable-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; TryWriteable for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: Writeable,\n    E: Writeable + <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.Clone.html\" title=\"trait core::clone::Clone\">Clone</a>,</div></h3></section></summary><div class=\"impl-items\"><section id=\"associatedtype.Error\" class=\"associatedtype trait-impl\"><a href=\"#associatedtype.Error\" class=\"anchor\">§</a><h4 class=\"code-header\">type <a class=\"associatedtype\">Error</a> = E</h4></section><details class=\"toggle method-toggle\" open><summary><section id=\"method.try_write_to\" class=\"method trait-impl\"><a href=\"#method.try_write_to\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a class=\"fn\">try_write_to</a>&lt;W&gt;(\n    &amp;self,\n    sink: <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;mut W</a>,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.unit.html\">()</a>, &lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt; as TryWriteable&gt;::Error&gt;, <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/struct.Error.html\" title=\"struct core::fmt::Error\">Error</a>&gt;<div class=\"where\">where\n    W: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Write.html\" title=\"trait core::fmt::Write\">Write</a> + ?<a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Writes the content of this writeable to a sink. <a>Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.try_write_to_parts\" class=\"method trait-impl\"><a href=\"#method.try_write_to_parts\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a class=\"fn\">try_write_to_parts</a>&lt;S&gt;(\n    &amp;self,\n    sink: <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.reference.html\">&amp;mut S</a>,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.unit.html\">()</a>, &lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt; as TryWriteable&gt;::Error&gt;, <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/struct.Error.html\" title=\"struct core::fmt::Error\">Error</a>&gt;<div class=\"where\">where\n    S: PartsWrite + ?<a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html\" title=\"trait core::marker::Sized\">Sized</a>,</div></h4></section></summary><div class='docblock'>Writes the content of this writeable to a sink with parts (annotations). <a>Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.writeable_length_hint\" class=\"method trait-impl\"><a href=\"#method.writeable_length_hint\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a class=\"fn\">writeable_length_hint</a>(&amp;self) -&gt; LengthHint</h4></section></summary><div class='docblock'>Returns a hint for the number of UTF-8 bytes that will be written to the sink. <a>Read more</a></div></details><details class=\"toggle method-toggle\" open><summary><section id=\"method.try_write_to_string\" class=\"method trait-impl\"><a href=\"#method.try_write_to_string\" class=\"anchor\">§</a><h4 class=\"code-header\">fn <a class=\"fn\">try_write_to_string</a>(\n    &amp;self,\n) -&gt; <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/alloc/borrow/enum.Cow.html\" title=\"enum alloc::borrow::Cow\">Cow</a>&lt;'_, <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.str.html\">str</a>&gt;, (&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt; as TryWriteable&gt;::Error, <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/alloc/borrow/enum.Cow.html\" title=\"enum alloc::borrow::Cow\">Cow</a>&lt;'_, <a class=\"primitive\" href=\"https://doc.rust-lang.org/1.88.0/std/primitive.str.html\">str</a>&gt;)&gt;</h4></section></summary><div class='docblock'>Writes the content of this writeable to a string. <a>Read more</a></div></details></div></details>","TryWriteable","weibo_crawler_node::error::Result"],["<section id=\"impl-Copy-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a></span><a href=\"#impl-Copy-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Copy.html\" title=\"trait core::marker::Copy\">Copy</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Copy.html\" title=\"trait core::marker::Copy\">Copy</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.Copy.html\" title=\"trait core::marker::Copy\">Copy</a>,</div></h3></section>","Copy","weibo_crawler_node::error::Result"],["<section id=\"impl-Eq-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a></span><a href=\"#impl-Eq-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Eq.html\" title=\"trait core::cmp::Eq\">Eq</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Eq.html\" title=\"trait core::cmp::Eq\">Eq</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/cmp/trait.Eq.html\" title=\"trait core::cmp::Eq\">Eq</a>,</div></h3></section>","Eq","weibo_crawler_node::error::Result"],["<section id=\"impl-FromStream%3CResult%3CT,+E%3E%3E-for-Result%3CU,+E%3E\" class=\"impl\"><a href=\"#impl-FromStream%3CResult%3CT,+E%3E%3E-for-Result%3CU,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, U, E&gt; FromStream&lt;<a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;&gt; for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;U, E&gt;<div class=\"where\">where\n    U: FromStream&lt;T&gt;,</div></h3></section>","FromStream<Result<T, E>>","weibo_crawler_node::error::Result"],["<section id=\"impl-StructuralPartialEq-for-Result%3CT,+E%3E\" class=\"impl\"><span class=\"rightside\"><span class=\"since\" title=\"Stable since Rust version 1.0.0\">1.0.0</span> · <a class=\"src\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#544\">Source</a></span><a href=\"#impl-StructuralPartialEq-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.StructuralPartialEq.html\" title=\"trait core::marker::StructuralPartialEq\">StructuralPartialEq</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;</h3></section>","StructuralPartialEq","weibo_crawler_node::error::Result"],["<section id=\"impl-UseCloned-for-Result%3CT,+E%3E\" class=\"impl\"><a class=\"src rightside\" href=\"https://doc.rust-lang.org/1.88.0/src/core/result.rs.html#1776-1779\">Source</a><a href=\"#impl-UseCloned-for-Result%3CT,+E%3E\" class=\"anchor\">§</a><h3 class=\"code-header\">impl&lt;T, E&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.UseCloned.html\" title=\"trait core::clone::UseCloned\">UseCloned</a> for <a class=\"enum\" href=\"https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html\" title=\"enum core::result::Result\">Result</a>&lt;T, E&gt;<div class=\"where\">where\n    T: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.UseCloned.html\" title=\"trait core::clone::UseCloned\">UseCloned</a>,\n    E: <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/clone/trait.UseCloned.html\" title=\"trait core::clone::UseCloned\">UseCloned</a>,</div></h3></section>","UseCloned","weibo_crawler_node::error::Result"]]]]);
    if (window.register_type_impls) {
        window.register_type_impls(type_impls);
    } else {
        window.pending_type_impls = type_impls;
    }
})()
//{"start":55,"fragment_lengths":[192621]}