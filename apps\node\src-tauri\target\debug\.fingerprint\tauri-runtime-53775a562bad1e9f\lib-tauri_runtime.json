{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 7668817577240348489, "path": 18318639559245105225, "deps": [[3150220818285335163, "url", false, 8909501939063229777], [4381063397040571828, "webview2_com", false, 1159489376968944555], [4405182208873388884, "http", false, 5525313316207021045], [7653476968652377684, "windows", false, 5238405700565607631], [8008191657135824715, "thiserror", false, 4083214613751541842], [8292277814562636972, "tauri_utils", false, 17150142075726711639], [8319709847752024821, "uuid", false, 1140400842430028499], [8569119365930580996, "serde_json", false, 2740896963817221287], [8866577183823226611, "http_range", false, 16409662982349129673], [9689903380558560274, "serde", false, 4202820352788480372], [11693073011723388840, "raw_window_handle", false, 7780770964976528412], [13208667028893622512, "rand", false, 6363632852697834881], [14162324460024849578, "build_script_build", false, 11394918073269898204]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-53775a562bad1e9f\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}