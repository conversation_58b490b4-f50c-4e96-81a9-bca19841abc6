use crate::error::{AppError, Result};
use serde::{Deserialize, Serialize};
use std::time::Duration;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use tokio::time::sleep;
use tracing::{info, error};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateInfo {
    pub version: String,
    pub release_date: String,
    pub download_url: String,
    pub changelog: String,
    pub is_critical: bool,
    pub file_size: u64,
    pub checksum: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateStatus {
    pub checking: bool,
    pub available: bool,
    pub downloading: bool,
    pub progress: f64,
    pub error: Option<String>,
    pub info: Option<UpdateInfo>,
}

impl Default for UpdateStatus {
    fn default() -> Self {
        Self {
            checking: false,
            available: false,
            downloading: false,
            progress: 0.0,
            error: None,
            info: None,
        }
    }
}

pub struct UpdateManager {
    app_handle: AppHandle,
    status: std::sync::Arc<tokio::sync::RwLock<UpdateStatus>>,
    check_interval: Duration,
    auto_check: bool,
}

impl UpdateManager {
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            app_handle,
            status: std::sync::Arc::new(tokio::sync::RwLock::new(UpdateStatus::default())),
            check_interval: Duration::from_secs(3600), // 1小时检查一次
            auto_check: true,
        }
    }

    /// 启动自动更新检查
    pub async fn start_auto_check(&self) -> Result<()> {
        if !self.auto_check {
            return Ok(());
        }

        let status = self.status.clone();
        let app_handle = self.app_handle.clone();
        let interval = self.check_interval;

        tokio::spawn(async move {
            loop {
                sleep(interval).await;
                
                let updater = UpdateManager {
                    app_handle: app_handle.clone(),
                    status: status.clone(),
                    check_interval: interval,
                    auto_check: true,
                };

                if let Err(e) = updater.check_for_updates().await {
                    error!("自动检查更新失败: {}", e);
                }
            }
        });

        info!("自动更新检查已启动，检查间隔: {:?}", self.check_interval);
        Ok(())
    }

    /// 检查更新
    pub async fn check_for_updates(&self) -> Result<bool> {
        let mut status = self.status.write().await;
        status.checking = true;
        status.error = None;
        drop(status);

        // 发送状态更新事件
        self.emit_status_update().await;

        let result = self.fetch_update_info().await;

        let mut status = self.status.write().await;
        status.checking = false;

        match result {
            Ok(update_info) => {
                let current_version = self.app_handle.package_info().version.to_string();
                let has_update = self.compare_versions(&current_version, &update_info.version);
                
                status.available = has_update;
                if has_update {
                    status.info = Some(update_info.clone());
                    info!("发现新版本: {}", update_info.version);
                    
                    // 发送更新可用通知
                    self.notify_update_available(&update_info).await;
                } else {
                    info!("当前已是最新版本");
                }
                
                drop(status);
                self.emit_status_update().await;
                Ok(has_update)
            }
            Err(e) => {
                status.error = Some(e.to_string());
                drop(status);
                self.emit_status_update().await;
                Err(e)
            }
        }
    }

    /// 下载并安装更新
    pub async fn download_and_install(&self) -> Result<()> {
        let status = self.status.read().await;
        let update_info = status.info.clone()
            .ok_or_else(|| AppError::Other("没有可用的更新信息".to_string()))?;
        drop(status);

        let mut status = self.status.write().await;
        status.downloading = true;
        status.progress = 0.0;
        status.error = None;
        drop(status);

        self.emit_status_update().await;

        // 模拟下载过程
        for i in 1..=100 {
            sleep(Duration::from_millis(50)).await;
            
            let mut status = self.status.write().await;
            status.progress = i as f64;
            drop(status);
            
            self.emit_status_update().await;
        }

        // 实际的下载和安装逻辑应该在这里实现
        // 这里只是模拟
        info!("更新下载完成，准备安装...");

        let mut status = self.status.write().await;
        status.downloading = false;
        status.progress = 100.0;
        drop(status);

        self.emit_status_update().await;

        // 通知用户重启应用
        self.notify_restart_required().await;

        Ok(())
    }

    /// 获取当前更新状态
    pub async fn get_status(&self) -> UpdateStatus {
        self.status.read().await.clone()
    }

    /// 从服务器获取更新信息
    async fn fetch_update_info(&self) -> Result<UpdateInfo> {
        // 这里应该从实际的更新服务器获取信息
        // 现在返回模拟数据
        sleep(Duration::from_secs(2)).await; // 模拟网络延迟

        // 模拟有时候有更新，有时候没有
        let has_update = rand::random::<bool>();
        
        if has_update {
            Ok(UpdateInfo {
                version: "1.1.0".to_string(),
                release_date: "2024-01-15".to_string(),
                download_url: "https://example.com/update.zip".to_string(),
                changelog: "修复了一些bug，提升了性能".to_string(),
                is_critical: false,
                file_size: 1024 * 1024 * 50, // 50MB
                checksum: "abc123def456".to_string(),
            })
        } else {
            Err(AppError::Other("当前已是最新版本".to_string()))
        }
    }

    /// 比较版本号
    fn compare_versions(&self, current: &str, latest: &str) -> bool {
        // 简单的版本比较，实际应用中应该使用更复杂的版本比较逻辑
        current != latest
    }

    /// 发送状态更新事件
    async fn emit_status_update(&self) {
        let status = self.status.read().await;
        let _ = self.app_handle.emit_all("update-status", status.clone());
    }

    /// 通知更新可用
    async fn notify_update_available(&self, update_info: &UpdateInfo) {
        let _ = self.app_handle.emit_all("update-available", update_info);
        
        // 如果是关键更新，显示系统通知
        if update_info.is_critical {
            self.show_system_notification(
                "关键更新可用",
                &format!("发现关键更新 v{}，建议立即更新", update_info.version)
            ).await;
        }
    }

    /// 通知需要重启
    async fn notify_restart_required(&self) {
        let _ = self.app_handle.emit_all("restart-required", ());
        
        self.show_system_notification(
            "更新完成",
            "更新已下载完成，请重启应用以应用更新"
        ).await;
    }

    /// 显示系统通知
    async fn show_system_notification(&self, title: &str, body: &str) {
        // 使用Tauri的通知API
        if let Some(window) = self.app_handle.get_window("main") {
            let script = format!(
                r#"
                if ('Notification' in window && Notification.permission === 'granted') {{
                    new Notification('{}', {{ body: '{}' }});
                }}
                "#,
                title.replace('\'', "\\'"),
                body.replace('\'', "\\'")
            );
            let _ = window.eval(&script);
        }
    }

    /// 设置自动检查间隔
    pub fn set_check_interval(&mut self, interval: Duration) {
        self.check_interval = interval;
    }

    /// 启用/禁用自动检查
    pub fn set_auto_check(&mut self, enabled: bool) {
        self.auto_check = enabled;
    }
}

// Tauri命令
#[tauri::command]
pub async fn check_for_updates(
    app_handle: AppHandle,
) -> std::result::Result<bool, String> {
    let updater = UpdateManager::new(app_handle);
    updater.check_for_updates().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn download_update(
    app_handle: AppHandle,
) -> std::result::Result<(), String> {
    let updater = UpdateManager::new(app_handle);
    updater.download_and_install().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn get_update_status(
    app_handle: AppHandle,
) -> std::result::Result<UpdateStatus, String> {
    let updater = UpdateManager::new(app_handle);
    Ok(updater.get_status().await)
}
