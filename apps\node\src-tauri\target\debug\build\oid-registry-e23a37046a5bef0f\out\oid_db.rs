/// 1.3.133.16.840.63.0.2
pub const OID_KDF_SHA1_SINGLE: Oid<'static> = oid!(1.3.133.16.840.63.0.2);
/// *******.4.1.311.2.1.4
pub const SPC_INDIRECT_DATA_OBJID: Oid<'static> = oid!(*******.4.1.311.2.1.4);
/// *******.4.1.311.2.1.11
pub const SPC_STATEMENT_TYPE_OBJID: Oid<'static> = oid!(*******.4.1.311.2.1.11);
/// *******.4.1.311.2.1.12
pub const SPC_SP_OPUS_INFO_OBJID: Oid<'static> = oid!(*******.4.1.311.2.1.12);
/// *******.4.1.311.2.1.15
pub const SPC_PE_IMAGE_DATA: Oid<'static> = oid!(*******.4.1.311.2.1.15);
/// *******.4.1.311.2.1.21
pub const SPC_INDIVIDUAL_SP_KEY_PURPOSE_OBJID : Oid<'static> = oid!(*******.4.1.311.2.1.21);
/// *******.4.1.311.10.1
pub const MS_CTL: Oid<'static> = oid!(*******.4.1.311.10.1);
/// *********.34
pub const OID_NIST_EC_P384: Oid<'static> = oid!(*********.34);
/// *********.35
pub const OID_NIST_EC_P521: Oid<'static> = oid!(*********.35);
/// ********.2.25
pub const OID_MD5_WITH_RSA: Oid<'static> = oid!(********.2.25);
/// ********.2.26
pub const OID_HASH_SHA1: Oid<'static> = oid!(********.2.26);
/// ********.2.29
pub const OID_SHA1_WITH_RSA: Oid<'static> = oid!(********.2.29);
/// 2.16.840.*********.1.42
pub const OID_NIST_ENC_AES256_CBC: Oid<'static> = oid!(2.16.840.*********.1.42);
/// 2.16.840.*********.2.1
pub const OID_NIST_HASH_SHA256: Oid<'static> = oid!(2.16.840.*********.2.1);
/// 2.16.840.*********.2.2
pub const OID_NIST_HASH_SHA384: Oid<'static> = oid!(2.16.840.*********.2.2);
/// 2.16.840.*********.2.3
pub const OID_NIST_HASH_SHA512: Oid<'static> = oid!(2.16.840.*********.2.3);
/// 1.2.840.113549.1.1.1
pub const OID_PKCS1_RSAENCRYPTION: Oid<'static> = oid!(1.2.840.113549.1.1.1);
/// 1.2.840.113549.1.1.2
pub const OID_PKCS1_MD2WITHRSAENC: Oid<'static> = oid!(1.2.840.113549.1.1.2);
/// 1.2.840.113549.1.1.3
pub const OID_PKCS1_MD4WITHRSAENC: Oid<'static> = oid!(1.2.840.113549.1.1.3);
/// 1.2.840.113549.1.1.4
pub const OID_PKCS1_MD5WITHRSAENC: Oid<'static> = oid!(1.2.840.113549.1.1.4);
/// 1.2.840.113549.1.1.5
pub const OID_PKCS1_SHA1WITHRSA: Oid<'static> = oid!(1.2.840.113549.1.1.5);
/// 1.2.840.113549.1.1.10
pub const OID_PKCS1_RSASSAPSS: Oid<'static> = oid!(1.2.840.113549.1.1.10);
/// 1.2.840.113549.1.1.11
pub const OID_PKCS1_SHA256WITHRSA: Oid<'static> = oid!(1.2.840.113549.1.1.11);
/// 1.2.840.113549.1.1.12
pub const OID_PKCS1_SHA384WITHRSA: Oid<'static> = oid!(1.2.840.113549.1.1.12);
/// 1.2.840.113549.1.1.13
pub const OID_PKCS1_SHA512WITHRSA: Oid<'static> = oid!(1.2.840.113549.1.1.13);
/// 1.2.840.113549.1.1.14
pub const OID_PKCS1_SHA224WITHRSA: Oid<'static> = oid!(1.2.840.113549.1.1.14);
/// 1.2.840.113549.1.12
pub const OID_PKCS12: Oid<'static> = oid!(1.2.840.113549.1.12);
/// 1.2.840.113549.1.12.1
pub const OID_PKCS12_PBEIDS: Oid<'static> = oid!(1.2.840.113549.1.12.1);
/// 1.2.840.113549.********
pub const OID_PKCS12_PBE_SHA1_128RC4: Oid<'static> = oid!(1.2.840.113549.********);
/// 1.2.840.113549.********
pub const OID_PKCS12_PBE_SHA1_40RC4: Oid<'static> = oid!(1.2.840.113549.********);
/// 1.2.840.113549.********
pub const OID_PKCS12_PBE_SHA1_3K_3DES_CBC: Oid<'static> = oid!(1.2.840.113549.********);
/// 1.2.840.113549.********
pub const OID_PKCS12_PBE_SHA1_2K_3DES_CBC: Oid<'static> = oid!(1.2.840.113549.********);
/// 1.2.840.113549.********
pub const OID_PKCS12_PBE_SHA1_128RC2_CBC: Oid<'static> = oid!(1.2.840.113549.********);
/// 1.2.840.113549.********
pub const OID_PKCS12_PBE_SHA1_40RC2_CBC: Oid<'static> = oid!(1.2.840.113549.********);
/// 1.2.840.113549.1.7.1
pub const OID_PKCS7_ID_DATA: Oid<'static> = oid!(1.2.840.113549.1.7.1);
/// 1.2.840.113549.1.7.2
pub const OID_PKCS7_ID_SIGNED_DATA: Oid<'static> = oid!(1.2.840.113549.1.7.2);
/// 1.2.840.113549.1.7.3
pub const OID_PKCS7_ID_ENVELOPED_DATA: Oid<'static> = oid!(1.2.840.113549.1.7.3);
/// 1.2.840.113549.1.7.4
pub const OID_PKCS7_ID_SIGNED_ENVELOPED_DATA: Oid<'static> = oid!(1.2.840.113549.1.7.4);
/// 1.2.840.113549.1.7.5
pub const OID_PKCS7_ID_DIGESTED_DATA: Oid<'static> = oid!(1.2.840.113549.1.7.5);
/// 1.2.840.113549.1.7.6
pub const OID_PKCS7_ID_ENCRYPTED_DATA: Oid<'static> = oid!(1.2.840.113549.1.7.6);
/// 1.2.840.113549.1.9.1
pub const OID_PKCS9_EMAIL_ADDRESS: Oid<'static> = oid!(1.2.840.113549.1.9.1);
/// 1.2.840.113549.1.9.2
pub const OID_PKCS9_UNSTRUCTURED_NAME: Oid<'static> = oid!(1.2.840.113549.1.9.2);
/// 1.2.840.113549.1.9.3
pub const OID_PKCS9_CONTENT_TYPE: Oid<'static> = oid!(1.2.840.113549.1.9.3);
/// 1.2.840.113549.1.9.4
pub const OID_PKCS9_ID_MESSAGE_DIGEST: Oid<'static> = oid!(1.2.840.113549.1.9.4);
/// 1.2.840.113549.1.9.5
pub const OID_PKCS9_SIGNING_TIME: Oid<'static> = oid!(1.2.840.113549.1.9.5);
/// 1.2.840.113549.1.9.7
pub const OID_PKCS9_CHALLENGE_PASSWORD: Oid<'static> = oid!(1.2.840.113549.1.9.7);
/// 1.2.840.113549.1.9.14
pub const OID_PKCS9_EXTENSION_REQUEST: Oid<'static> = oid!(1.2.840.113549.1.9.14);
/// 1.2.840.113549.1.9.15
pub const OID_PKCS9_SMIME_CAPABILITIES: Oid<'static> = oid!(1.2.840.113549.1.9.15);
/// 1.2.840.113549.1.9.20
pub const OID_PKCS9_FRIENDLY_NAME: Oid<'static> = oid!(1.2.840.113549.1.9.20);
/// 2.5
pub const OID_X500: Oid<'static> = oid!(2.5);
/// 0.9.2342.19200300.100.1.1
pub const OID_USERID: Oid<'static> = oid!(0.9.2342.19200300.100.1.1);
/// 0.9.2342.19200300.100.1.25
pub const OID_DOMAIN_COMPONENT: Oid<'static> = oid!(0.9.2342.19200300.100.1.25);
/// 1.2.643.2.2.3
pub const OID_SIG_GOST_R3411_94_WITH_R3410_2001: Oid<'static> = oid!(1.2.643.2.2.3);
/// 1.2.643.2.2.19
pub const OID_GOST_R3410_2001: Oid<'static> = oid!(1.2.643.2.2.19);
/// 1.2.643.*******.1
pub const OID_KEY_TYPE_GOST_R3410_2012_256: Oid<'static> = oid!(1.2.643.*******.1);
/// 1.2.643.*******.2
pub const OID_KEY_TYPE_GOST_R3410_2012_512: Oid<'static> = oid!(1.2.643.*******.2);
/// 1.2.643.*******.2
pub const OID_SIG_GOST_R3410_2012_256: Oid<'static> = oid!(1.2.643.*******.2);
/// 1.2.643.*******.3
pub const OID_SIG_GOST_R3410_2012_512: Oid<'static> = oid!(1.2.643.*******.3);
/// 1.2.840.10040.4.1
pub const OID_KEY_TYPE_DSA: Oid<'static> = oid!(1.2.840.10040.4.1);
/// 1.2.840.10040.4.3
pub const OID_SIG_DSA_WITH_SHA1: Oid<'static> = oid!(1.2.840.10040.4.3);
/// ********.3.1.2
pub const OID_SIG_RSA_RIPE_MD160: Oid<'static> = oid!(********.3.1.2);
/// ***********
pub const OID_SIG_ED25519: Oid<'static> = oid!(***********);
/// ***********
pub const OID_SIG_ED448: Oid<'static> = oid!(***********);
/// *******.4.1.311.********
pub const MS_JURISDICTION_LOCALITY: Oid<'static> = oid!(*******.4.1.311.********);
/// *******.4.1.311.********
pub const MS_JURISDICTION_STATE_OR_PROVINCE: Oid<'static> = oid!(*******.4.1.311.********);
/// *******.4.1.311.********
pub const MS_JURISDICTION_COUNTRY: Oid<'static> = oid!(*******.4.1.311.********);
/// *******.4.1.11129.2.4.2
pub const OID_CT_LIST_SCT: Oid<'static> = oid!(*******.4.1.11129.2.4.2);
/// *******.*******.1
pub const OID_PKIX_AUTHORITY_INFO_ACCESS: Oid<'static> = oid!(*******.*******.1);
/// *******.*******.11
pub const OID_PKIX_SUBJECT_INFO_ACCESS: Oid<'static> = oid!(*******.*******.11);
/// *******.********.1
pub const OID_PKIX_ACCESS_DESCRIPTOR_OCSP: Oid<'static> = oid!(*******.********.1);
/// *******.********.2
pub const OID_PKIX_ACCESS_DESCRIPTOR_CA_ISSUERS: Oid<'static> = oid!(*******.********.2);
/// *******.********.3
pub const OID_PKIX_ACCESS_DESCRIPTOR_TIMESTAMPING: Oid<'static> = oid!(*******.********.3);
/// *******.********.4
pub const OID_PKIX_ACCESS_DESCRIPTOR_DVCS: Oid<'static> = oid!(*******.********.4);
/// *******.********.5
pub const OID_PKIX_ACCESS_DESCRIPTOR_CA_REPOSITORY: Oid<'static> = oid!(*******.********.5);
/// *******.********.6
pub const OID_PKIX_ACCESS_DESCRIPTOR_HTTP_CERTS: Oid<'static> = oid!(*******.********.6);
/// *******.********.7
pub const OID_PKIX_ACCESS_DESCRIPTOR_HTTP_CRLS: Oid<'static> = oid!(*******.********.7);
/// *******.********.10
pub const OID_PKIX_ACCESS_DESCRIPTOR_RPKI_MANIFEST: Oid<'static> = oid!(*******.********.10);
/// *******.********.11
pub const OID_PKIX_ACCESS_DESCRIPTOR_SIGNED_OBJECT: Oid<'static> = oid!(*******.********.11);
/// *******.********.12
pub const OID_PKIX_ACCESS_DESCRIPTOR_CMC: Oid<'static> = oid!(*******.********.12);
/// *******.********.13
pub const OID_PKIX_ACCESS_DESCRIPTOR_RPKI_NOTIFY: Oid<'static> = oid!(*******.********.13);
/// *******.********.14
pub const OID_PKIX_ACCESS_DESCRIPTOR_STIRTNLIST: Oid<'static> = oid!(*******.********.14);
/// 2.5.4
pub const OID_X509: Oid<'static> = oid!(2.5.4);
/// *******
pub const OID_X509_OBJECT_CLASS: Oid<'static> = oid!(*******);
/// *******
pub const OID_X509_ALIASED_ENTRY_NAME: Oid<'static> = oid!(*******);
/// *******
pub const OID_X509_KNOWLEDGE_INFORMATION: Oid<'static> = oid!(*******);
/// *******
pub const OID_X509_COMMON_NAME: Oid<'static> = oid!(*******);
/// *******
pub const OID_X509_SURNAME: Oid<'static> = oid!(*******);
/// *******
pub const OID_X509_SERIALNUMBER: Oid<'static> = oid!(*******);
/// *******
pub const OID_X509_COUNTRY_NAME: Oid<'static> = oid!(*******);
/// *******
pub const OID_X509_LOCALITY_NAME: Oid<'static> = oid!(*******);
/// *******
pub const OID_X509_STATE_OR_PROVINCE_NAME: Oid<'static> = oid!(*******);
/// *******
pub const OID_X509_STREET_ADDRESS: Oid<'static> = oid!(*******);
/// *******0
pub const OID_X509_ORGANIZATION_NAME: Oid<'static> = oid!(*******0);
/// *******1
pub const OID_X509_ORGANIZATIONAL_UNIT: Oid<'static> = oid!(*******1);
/// *******2
pub const OID_X509_TITLE: Oid<'static> = oid!(*******2);
/// *******3
pub const OID_X509_DESCRIPTION: Oid<'static> = oid!(*******3);
/// *******4
pub const OID_X509_SEARCH_GUIDE: Oid<'static> = oid!(*******4);
/// *******5
pub const OID_X509_BUSINESS_CATEGORY: Oid<'static> = oid!(*******5);
/// *******6
pub const OID_X509_POSTAL_ADDRESS: Oid<'static> = oid!(*******6);
/// *******7
pub const OID_X509_POSTAL_CODE: Oid<'static> = oid!(*******7);
/// *******1
pub const OID_X509_NAME: Oid<'static> = oid!(*******1);
/// *******2
pub const OID_X509_GIVEN_NAME: Oid<'static> = oid!(*******2);
/// *******3
pub const OID_X509_INITIALS: Oid<'static> = oid!(*******3);
/// *******4
pub const OID_X509_GENERATION_QUALIFIER: Oid<'static> = oid!(*******4);
/// *******5
pub const OID_X509_UNIQUE_IDENTIFIER: Oid<'static> = oid!(*******5);
/// *******6
pub const OID_X509_DN_QUALIFIER: Oid<'static> = oid!(*******6);
/// ********
pub const OID_X509_OBSOLETE_AUTHORITY_KEY_IDENTIFIER: Oid<'static> = oid!(********);
/// ********
pub const OID_X509_OBSOLETE_KEY_ATTRIBUTES: Oid<'static> = oid!(********);
/// ********
pub const OID_X509_OBSOLETE_CERTIFICATE_POLICIES: Oid<'static> = oid!(********);
/// 2.5.29.4
pub const OID_X509_OBSOLETE_KEY_USAGE: Oid<'static> = oid!(2.5.29.4);
/// 2.5.29.5
pub const OID_X509_OBSOLETE_POLICY_MAPPING: Oid<'static> = oid!(2.5.29.5);
/// 2.5.29.6
pub const OID_X509_OBSOLETE_SUBTREES_CONSTRAINT: Oid<'static> = oid!(2.5.29.6);
/// 2.5.29.7
pub const OID_X509_OBSOLETE_SUBJECT_ALT_NAME: Oid<'static> = oid!(2.5.29.7);
/// 2.5.29.8
pub const OID_X509_OBSOLETE_ISSUER_ALT_NAME: Oid<'static> = oid!(2.5.29.8);
/// ********4
pub const OID_X509_EXT_SUBJECT_KEY_IDENTIFIER: Oid<'static> = oid!(********4);
/// ********5
pub const OID_X509_EXT_KEY_USAGE: Oid<'static> = oid!(********5);
/// ********6
pub const OID_X509_EXT_PRIVATE_KEY_USAGE_PERIOD: Oid<'static> = oid!(********6);
/// ********7
pub const OID_X509_EXT_SUBJECT_ALT_NAME: Oid<'static> = oid!(********7);
/// ********8
pub const OID_X509_EXT_ISSUER_ALT_NAME: Oid<'static> = oid!(********8);
/// ********9
pub const OID_X509_EXT_BASIC_CONSTRAINTS: Oid<'static> = oid!(********9);
/// ********0
pub const OID_X509_EXT_CRL_NUMBER: Oid<'static> = oid!(********0);
/// ********1
pub const OID_X509_EXT_REASON_CODE: Oid<'static> = oid!(********1);
/// ********3
pub const OID_X509_EXT_HOLD_INSTRUCTION_CODE: Oid<'static> = oid!(********3);
/// ********4
pub const OID_X509_EXT_INVALIDITY_DATE: Oid<'static> = oid!(********4);
/// ********7
pub const OID_X509_EXT_DELTA_CRL_INDICATOR: Oid<'static> = oid!(********7);
/// ********8
pub const OID_X509_EXT_ISSUER_DISTRIBUTION_POINT: Oid<'static> = oid!(********8);
/// ********9
pub const OID_X509_EXT_ISSUER: Oid<'static> = oid!(********9);
/// ********0
pub const OID_X509_EXT_NAME_CONSTRAINTS: Oid<'static> = oid!(********0);
/// ********1
pub const OID_X509_EXT_CRL_DISTRIBUTION_POINTS: Oid<'static> = oid!(********1);
/// ********2
pub const OID_X509_EXT_CERTIFICATE_POLICIES: Oid<'static> = oid!(********2);
/// ********3
pub const OID_X509_EXT_POLICY_MAPPINGS: Oid<'static> = oid!(********3);
/// ********5
pub const OID_X509_EXT_AUTHORITY_KEY_IDENTIFIER: Oid<'static> = oid!(********5);
/// ********6
pub const OID_X509_EXT_POLICY_CONSTRAINTS: Oid<'static> = oid!(********6);
/// ********7
pub const OID_X509_EXT_EXTENDED_KEY_USAGE: Oid<'static> = oid!(********7);
/// 2.5.29.46
pub const OID_X509_EXT_FRESHEST_CRL: Oid<'static> = oid!(2.5.29.46);
/// 2.5.29.54
pub const OID_X509_EXT_INHIBIT_ANY_POLICY: Oid<'static> = oid!(2.5.29.54);
/// 2.16.840.1.113730.1.1
pub const OID_X509_EXT_CERT_TYPE: Oid<'static> = oid!(2.16.840.1.113730.1.1);
/// 2.16.840.1.113730.1.2
pub const OID_X509_EXT_BASE_URL: Oid<'static> = oid!(2.16.840.1.113730.1.2);
/// 2.16.840.1.113730.1.3
pub const OID_X509_EXT_REVOCATION_URL: Oid<'static> = oid!(2.16.840.1.113730.1.3);
/// 2.16.840.1.113730.1.4
pub const OID_X509_EXT_CA_REVOCATION_URL: Oid<'static> = oid!(2.16.840.1.113730.1.4);
/// 2.16.840.1.113730.1.5
pub const OID_X509_EXT_CA_CRL_URL: Oid<'static> = oid!(2.16.840.1.113730.1.5);
/// 2.16.840.1.113730.1.6
pub const OID_X509_EXT_CA_CERT_URL: Oid<'static> = oid!(2.16.840.1.113730.1.6);
/// 2.16.840.1.113730.1.7
pub const OID_X509_EXT_RENEWAL_URL: Oid<'static> = oid!(2.16.840.1.113730.1.7);
/// 2.16.840.1.113730.1.8
pub const OID_X509_EXT_CA_POLICY_URL: Oid<'static> = oid!(2.16.840.1.113730.1.8);
/// 2.16.840.1.113730.1.9
pub const OID_X509_EXT_HOMEPAGE_URL: Oid<'static> = oid!(2.16.840.1.113730.1.9);
/// 2.16.840.1.113730.1.10
pub const OID_X509_EXT_ENTITY_LOGO: Oid<'static> = oid!(2.16.840.1.113730.1.10);
/// 2.16.840.1.113730.1.11
pub const OID_X509_EXT_USER_PICTURE: Oid<'static> = oid!(2.16.840.1.113730.1.11);
/// 2.16.840.1.113730.1.12
pub const OID_X509_EXT_SSL_SERVER_NAME: Oid<'static> = oid!(2.16.840.1.113730.1.12);
/// 2.16.840.1.113730.1.13
pub const OID_X509_EXT_CERT_COMMENT: Oid<'static> = oid!(2.16.840.1.113730.1.13);
/// 1.2.840.10045.2.1
pub const OID_KEY_TYPE_EC_PUBLIC_KEY: Oid<'static> = oid!(1.2.840.10045.2.1);
/// 1.2.840.10045.4.3.1
pub const OID_SIG_ECDSA_WITH_SHA224: Oid<'static> = oid!(1.2.840.10045.4.3.1);
/// 1.2.840.10045.4.3.2
pub const OID_SIG_ECDSA_WITH_SHA256: Oid<'static> = oid!(1.2.840.10045.4.3.2);
/// 1.2.840.10045.4.3.3
pub const OID_SIG_ECDSA_WITH_SHA384: Oid<'static> = oid!(1.2.840.10045.4.3.3);
/// 1.2.840.10045.4.3.4
pub const OID_SIG_ECDSA_WITH_SHA512: Oid<'static> = oid!(1.2.840.10045.4.3.4);
/// 1.2.840.10045.3.1.7
pub const OID_EC_P256: Oid<'static> = oid!(1.2.840.10045.3.1.7);

#[cfg(feature = "registry")]
#[cfg_attr(docsrs, doc(cfg(feature = "registry")))]
impl OidRegistry<'_> {
    #[cfg(feature = "kdf")]
    #[cfg_attr(docsrs, doc(cfg(feature = "kdf")))]
    #[doc = "Load all known OIDs for feature `kdf` in the registry."]
    pub fn with_kdf(mut self) -> Self {
        self.insert(oid!(1.3.133.16.840.63.0.2), OidEntry::new("dhSinglePass-stdDH-sha1kdf-scheme", "Single pass Secure Hash Algorithm 1 (SHA1) key derivation"));
        self
    }

    #[cfg(feature = "ms_spc")]
    #[cfg_attr(docsrs, doc(cfg(feature = "ms_spc")))]
    #[doc = "Load all known OIDs for feature `ms_spc` in the registry."]
    pub fn with_ms_spc(mut self) -> Self {
        self.insert(oid!(*******.4.1.311.2.1.4), OidEntry::new("spcIndirectData", "The SPC_INDIRECT_DATA_CONTENT structure is used in Authenticode signatures to store the digest and other attributes of the signed file"));
        self.insert(oid!(*******.4.1.311.2.1.11), OidEntry::new("spcStatementType", "spcStatementType"));
        self.insert(oid!(*******.4.1.311.2.1.12), OidEntry::new("spcSpOpusInfo", "SpcSpOpusInfo"));
        self.insert(oid!(*******.4.1.311.2.1.15), OidEntry::new("spcPEImageData", "spcPEImageData"));
        self.insert(oid!(*******.4.1.311.2.1.21), OidEntry::new("msCodeInd", "MsCodeInd (SPC_INDIVIDUAL_SP_KEY_PURPOSE_OBJID) is a ExtendedKeyUsage for Certificate Extensions which indicates Microsoft Individual Code Signing (authenticode)"));
        self.insert(oid!(*******.4.1.311.10.1), OidEntry::new("szOID_CTL", "MS_CTL"));
        self
    }

    #[cfg(feature = "nist_algs")]
    #[cfg_attr(docsrs, doc(cfg(feature = "nist_algs")))]
    #[doc = "Load all known OIDs for feature `nist_algs` in the registry."]
    pub fn with_nist_algs(mut self) -> Self {
        self.insert(oid!(*********.34), OidEntry::new("secp384r1", "P-384 elliptic curve parameter"));
        self.insert(oid!(*********.35), OidEntry::new("secp521r1", "P-521 elliptic curve parameter"));
        self.insert(oid!(********.2.25), OidEntry::new("md5WithRSASignature", "RSA algorithm coupled with the MD5 hashing algorithm (Oddball using ISO/IEC 9796-2 padding rules)"));
        self.insert(oid!(********.2.26), OidEntry::new("id-SHA1", "SHA-1 hash algorithm"));
        self.insert(oid!(********.2.29), OidEntry::new("sha1WithRSAEncryption", "RSA algorithm that uses the Secure Hash Algorithm 1 (SHA1) (obsolete)"));
        self.insert(oid!(2.16.840.*********.1.42), OidEntry::new("aes-256-cbc", "256-bit Advanced Encryption Standard (AES) algorithm with Cipher-Block Chaining (CBC) mode of operation"));
        self.insert(oid!(2.16.840.*********.2.1), OidEntry::new("sha256", "Secure Hash Algorithm that uses a 256 bit key (SHA256)"));
        self.insert(oid!(2.16.840.*********.2.2), OidEntry::new("sha384", "Secure Hash Algorithm that uses a 384 bit key (SHA384)"));
        self.insert(oid!(2.16.840.*********.2.3), OidEntry::new("sha512", "Secure Hash Algorithm that uses a 512 bit key (SHA512)"));
        self
    }

    #[cfg(feature = "pkcs1")]
    #[cfg_attr(docsrs, doc(cfg(feature = "pkcs1")))]
    #[doc = "Load all known OIDs for feature `pkcs1` in the registry."]
    pub fn with_pkcs1(mut self) -> Self {
        self.insert(oid!(1.2.840.113549.1.1.1), OidEntry::new("rsaEncryption", "RSAES-PKCS1-v1_5 encryption scheme"));
        self.insert(oid!(1.2.840.113549.1.1.2), OidEntry::new("md2WithRSAEncryption", "MD2 with RSA encryption"));
        self.insert(oid!(1.2.840.113549.1.1.3), OidEntry::new("md4WithRSAEncryption", "MD4 with RSA encryption"));
        self.insert(oid!(1.2.840.113549.1.1.4), OidEntry::new("md5WithRSAEncryption", "MD5 with RSA encryption"));
        self.insert(oid!(1.2.840.113549.1.1.5), OidEntry::new("sha1WithRSAEncryption", "SHA1 with RSA encryption"));
        self.insert(oid!(1.2.840.113549.1.1.10), OidEntry::new("rsassa-pss", "RSA Signature Scheme with Probabilistic Signature Scheme (RSASSA-PSS)"));
        self.insert(oid!(1.2.840.113549.1.1.11), OidEntry::new("sha256WithRSAEncryption", "SHA256 with RSA encryption"));
        self.insert(oid!(1.2.840.113549.1.1.12), OidEntry::new("sha384WithRSAEncryption", "SHA384 with RSA encryption"));
        self.insert(oid!(1.2.840.113549.1.1.13), OidEntry::new("sha512WithRSAEncryption", "SHA512 with RSA encryption"));
        self.insert(oid!(1.2.840.113549.1.1.14), OidEntry::new("sha224WithRSAEncryption", "SHA224 with RSA encryption"));
        self
    }

    #[cfg(feature = "pkcs12")]
    #[cfg_attr(docsrs, doc(cfg(feature = "pkcs12")))]
    #[doc = "Load all known OIDs for feature `pkcs12` in the registry."]
    pub fn with_pkcs12(mut self) -> Self {
        self.insert(oid!(1.2.840.113549.1.12), OidEntry::new("pkcs-12", "Public-Key Cryptography Standard (PKCS) #12"));
        self.insert(oid!(1.2.840.113549.1.12.1), OidEntry::new("pkcs-12PbeIds", "PKCS #12 Password Based Encryption IDs"));
        self.insert(oid!(1.2.840.113549.********), OidEntry::new("pbeWithSHAAnd128BitRC4", "PKCS #12 Password Based Encryption With SHA-1 and 128-bit RC4"));
        self.insert(oid!(1.2.840.113549.********), OidEntry::new("pbeWithSHAAnd40BitRC4", "PKCS #12 Password Based Encryption With SHA-1 and 40-bit RC4"));
        self.insert(oid!(1.2.840.113549.********), OidEntry::new("pbeWithSHAAnd3-KeyTripleDES-CBC", "PKCS #12 Password Based Encryption With SHA-1 and 3-key Triple DES in CBC mode"));
        self.insert(oid!(1.2.840.113549.********), OidEntry::new("pbeWithSHAAnd2-KeyTripleDES-CBC", "PKCS #12 Password Based Encryption With SHA-1 and 2-key Triple DES in CBC mode"));
        self.insert(oid!(1.2.840.113549.********), OidEntry::new("pbeWithSHAAnd128BitRC2-CBC", "PKCS #12 Password Based Encryption With SHA-1 and 128-bit RC2-CBC"));
        self.insert(oid!(1.2.840.113549.********), OidEntry::new("pbeWithSHAAnd40BitRC2-CBC", "PKCS #12 Password Based Encryption With SHA-1 and 40-bit RC2-CBC"));
        self
    }

    #[cfg(feature = "pkcs7")]
    #[cfg_attr(docsrs, doc(cfg(feature = "pkcs7")))]
    #[doc = "Load all known OIDs for feature `pkcs7` in the registry."]
    pub fn with_pkcs7(mut self) -> Self {
        self.insert(oid!(1.2.840.113549.1.7.1), OidEntry::new("pkcs7-data", "pkcs7-data"));
        self.insert(oid!(1.2.840.113549.1.7.2), OidEntry::new("pkcs7-signedData", "PKCS#7 Signed Data"));
        self.insert(oid!(1.2.840.113549.1.7.3), OidEntry::new("pkcs7-envelopedData", "PKCS#7 Enveloped Data"));
        self.insert(oid!(1.2.840.113549.1.7.4), OidEntry::new("pkcs7-signedAndEnvelopedData", "PKCS#7 Signed and Enveloped Data"));
        self.insert(oid!(1.2.840.113549.1.7.5), OidEntry::new("pkcs7-digestedData", "PKCS#7 Digested Data"));
        self.insert(oid!(1.2.840.113549.1.7.6), OidEntry::new("pkcs7-encryptedData", "PKCS#7 Encrypted Data"));
        self
    }

    #[cfg(feature = "pkcs9")]
    #[cfg_attr(docsrs, doc(cfg(feature = "pkcs9")))]
    #[doc = "Load all known OIDs for feature `pkcs9` in the registry."]
    pub fn with_pkcs9(mut self) -> Self {
        self.insert(oid!(1.2.840.113549.1.9.1), OidEntry::new("emailAddress", "Email Address attribute for use in signatures"));
        self.insert(oid!(1.2.840.113549.1.9.2), OidEntry::new("unstructuredName", "PKCS#9 unstructuredName"));
        self.insert(oid!(1.2.840.113549.1.9.3), OidEntry::new("contentType", "id-contentType"));
        self.insert(oid!(1.2.840.113549.1.9.4), OidEntry::new("id-messageDigest", "id-messageDigest"));
        self.insert(oid!(1.2.840.113549.1.9.5), OidEntry::new("signing-time", "id-signingTime"));
        self.insert(oid!(1.2.840.113549.1.9.7), OidEntry::new("challengePassword", "PKCS #9 challenge password (as specified for PKSC#10 in RFC2986)"));
        self.insert(oid!(1.2.840.113549.1.9.14), OidEntry::new("extensionRequest", "Extension list for Certification Requests"));
        self.insert(oid!(1.2.840.113549.1.9.15), OidEntry::new("smimeCapabilities", "aa-smimeCapabilities"));
        self.insert(oid!(1.2.840.113549.1.9.20), OidEntry::new("friendlyName", "PKCS #9 attribute friendlyName (for PKCS #12)"));
        self
    }

    #[cfg(feature = "x500")]
    #[cfg_attr(docsrs, doc(cfg(feature = "x500")))]
    #[doc = "Load all known OIDs for feature `x500` in the registry."]
    pub fn with_x500(mut self) -> Self {
        self.insert(oid!(2.5), OidEntry::new("x500", "X.500"));
        self
    }

    #[cfg(feature = "x509")]
    #[cfg_attr(docsrs, doc(cfg(feature = "x509")))]
    #[doc = "Load all known OIDs for feature `x509` in the registry."]
    pub fn with_x509(mut self) -> Self {
        self.insert(oid!(0.9.2342.19200300.100.1.1), OidEntry::new("uid", "User ID"));
        self.insert(oid!(0.9.2342.19200300.100.1.25), OidEntry::new("domainComponent", "Domain component"));
        self.insert(oid!(1.2.643.2.2.3), OidEntry::new("id-GostR3411-94-with-GostR3410-2001", "GOST R 3411-94 with GOST R 3410-2001"));
        self.insert(oid!(1.2.643.2.2.19), OidEntry::new("gostR3410-2001", "GOST R 34.10-2001"));
        self.insert(oid!(1.2.643.*******.1), OidEntry::new("gost3410-2012-256", "GOST R 34.10-2012 public keys with 256 bits private key length"));
        self.insert(oid!(1.2.643.*******.2), OidEntry::new("gost3410-2012-512", "GOST R 34.10-2012 public keys with 512 bits private key length"));
        self.insert(oid!(1.2.643.*******.2), OidEntry::new("id-tc26-signwithdigest-gost3410-12-256", "GOST R 34.10-2012 signature algorithm with 256-bit key length and GOST R 34.11-2012 hash function with 256-bit hash code"));
        self.insert(oid!(1.2.643.*******.3), OidEntry::new("id-tc26-signwithdigest-gost3410-12-512", "GOST R 34.10-2012 signature algorithm with 512-bit key length and GOST R 34.11-2012 hash function with 512-bit hash code"));
        self.insert(oid!(1.2.840.10040.4.1), OidEntry::new("id-dsa", "DSA subject public key"));
        self.insert(oid!(1.2.840.10040.4.3), OidEntry::new("dsa-with-sha1", "DSA signature generated with SHA-1 algorithm"));
        self.insert(oid!(********.3.1.2), OidEntry::new("rsaSignatureWithripemd160", "RSA signature in combination with hash algorithm RIPEMD-160"));
        self.insert(oid!(***********), OidEntry::new("ed25519", "Edwards-curve Digital Signature Algorithm (EdDSA) Ed25519"));
        self.insert(oid!(***********), OidEntry::new("ed448", "Edwards-curve Digital Signature Algorithm (EdDSA) Ed448"));
        self.insert(oid!(*******.4.1.311.********), OidEntry::new("msJurisdictionLocality", "X520LocalityName as specified in RFC 3280"));
        self.insert(oid!(*******.4.1.311.********), OidEntry::new("msJurisdictionStateOrProvince", "X520StateOrProvinceName as specified in RFC 3280"));
        self.insert(oid!(*******.4.1.311.********), OidEntry::new("msJurisdictionCountry", "X520countryName as specified in RFC 3280"));
        self.insert(oid!(*******.4.1.11129.2.4.2), OidEntry::new("ctSCTList", "Certificate Transparency Signed Certificate Timestamp List"));
        self.insert(oid!(*******.*******.1), OidEntry::new("authorityInfoAccess", "Certificate Authority Information Access"));
        self.insert(oid!(*******.*******.11), OidEntry::new("subjectInfoAccess", "Certificate Subject Information Access"));
        self.insert(oid!(*******.********.1), OidEntry::new("id-ad-ocsp", "PKIX Access Descriptor OCSP"));
        self.insert(oid!(*******.********.2), OidEntry::new("id-ad-caIssuers", "PKIX Access Descriptor CA Issuers"));
        self.insert(oid!(*******.********.3), OidEntry::new("id-ad-timestamping", "PKIX Access Descriptor Timestamping"));
        self.insert(oid!(*******.********.4), OidEntry::new("id-ad-dvcs", "PKIX Access Descriptor DVCS"));
        self.insert(oid!(*******.********.5), OidEntry::new("id-ad-caRepository", "PKIX Access Descriptor CA Repository"));
        self.insert(oid!(*******.********.6), OidEntry::new("id-ad-http-certs", "PKIX Access Descriptor HTTP Certificates"));
        self.insert(oid!(*******.********.7), OidEntry::new("id-ad-http-crls", "PKIX Access Descriptor HTTP Certificate Revocation Lists"));
        self.insert(oid!(*******.********.10), OidEntry::new("id-ad-rpki-manifest", "PKIX Access Descriptor RPKI Manifest"));
        self.insert(oid!(*******.********.11), OidEntry::new("id-ad-signed-object", "PKIX Access Descriptor Signed Object"));
        self.insert(oid!(*******.********.12), OidEntry::new("id-ad-cmc", "PKIX Access Descriptor CMC"));
        self.insert(oid!(*******.********.13), OidEntry::new("id-ad-rpki-notify", "PKIX Access Descriptor RPKI Notify"));
        self.insert(oid!(*******.********.14), OidEntry::new("id-ad-stirTNList", "PKIX Access Descriptor STIRTNLIST"));
        self.insert(oid!(2.5.4), OidEntry::new("x509", "X.509"));
        self.insert(oid!(*******), OidEntry::new("objectClass", "Object classes"));
        self.insert(oid!(*******), OidEntry::new("aliasedEntryName", "Aliased entry/object name"));
        self.insert(oid!(*******), OidEntry::new("knowledgeInformation", "'knowledgeInformation' attribute type"));
        self.insert(oid!(*******), OidEntry::new("commonName", "Common Name"));
        self.insert(oid!(*******), OidEntry::new("surname", "Surname"));
        self.insert(oid!(*******), OidEntry::new("serialNumber", "Serial Number"));
        self.insert(oid!(*******), OidEntry::new("countryName", "Country Name"));
        self.insert(oid!(*******), OidEntry::new("localityName", "Locality Name"));
        self.insert(oid!(*******), OidEntry::new("stateOrProvinceName", "State or Province name"));
        self.insert(oid!(*******), OidEntry::new("streetAddress", "Street Address"));
        self.insert(oid!(*******0), OidEntry::new("organizationName", "Organization Name"));
        self.insert(oid!(*******1), OidEntry::new("organizationalUnit", "Organizational Unit"));
        self.insert(oid!(*******2), OidEntry::new("title", "Title"));
        self.insert(oid!(*******3), OidEntry::new("description", "Description"));
        self.insert(oid!(*******4), OidEntry::new("searchGuide", "Search Guide"));
        self.insert(oid!(*******5), OidEntry::new("businessCategory", "Business Category"));
        self.insert(oid!(*******6), OidEntry::new("postalAddress", "Postal Address"));
        self.insert(oid!(*******7), OidEntry::new("postalCode", "Postal Code"));
        self.insert(oid!(*******1), OidEntry::new("name", "Name"));
        self.insert(oid!(*******2), OidEntry::new("givenName", "Given Name"));
        self.insert(oid!(*******3), OidEntry::new("initials", "Initials of an individual's name"));
        self.insert(oid!(*******4), OidEntry::new("generationQualifier", "Generation information to qualify an individual's name"));
        self.insert(oid!(*******5), OidEntry::new("uniqueIdentifier", "Unique Identifier"));
        self.insert(oid!(*******6), OidEntry::new("dnQualifier", "DN Qualifier"));
        self.insert(oid!(********), OidEntry::new("oldAuthorityKeyIdentifier", "X509v3 Authority Key Identifier (obsolete)"));
        self.insert(oid!(********), OidEntry::new("oldKeyAttributes", "X509v3 Key Attributes (obsolete)"));
        self.insert(oid!(********), OidEntry::new("oldCertificatePolicies", "X509v3 Certificate Policies (obsolete)"));
        self.insert(oid!(2.5.29.4), OidEntry::new("oldKeyUsage", "X509v3 Key Usage Restriction (obsolete)"));
        self.insert(oid!(2.5.29.5), OidEntry::new("oldPolicyMapping", "X509v3 Policy Mapping (obsolete)"));
        self.insert(oid!(2.5.29.6), OidEntry::new("oldSubtreesConstraint", "X509v3 Subtrees Constraint (obsolete)"));
        self.insert(oid!(2.5.29.7), OidEntry::new("oldSubjectAltNAme", "X509v3 Subject Alternative Name (obsolete)"));
        self.insert(oid!(2.5.29.8), OidEntry::new("oldIssuerAltNAme", "X509v3 Issuer Alternative Name (obsolete)"));
        self.insert(oid!(********4), OidEntry::new("subjectKeyIdentifier", "X509v3 Subject Key Identifier"));
        self.insert(oid!(********5), OidEntry::new("keyUsage", "X509v3 Key Usage"));
        self.insert(oid!(********6), OidEntry::new("privateKeyUsagePeriod", "X509v3 Private Key Usage Period"));
        self.insert(oid!(********7), OidEntry::new("subjectAltName", "X509v3 Subject Alternative Name"));
        self.insert(oid!(********8), OidEntry::new("issuerAltName", "X509v3 Issuer Alternative Name"));
        self.insert(oid!(********9), OidEntry::new("basicConstraints", "X509v3 Basic Constraints"));
        self.insert(oid!(********0), OidEntry::new("crlNumber", "X509v3 CRL Number"));
        self.insert(oid!(********1), OidEntry::new("reasonCode", "X509v3 Reason Code"));
        self.insert(oid!(********3), OidEntry::new("holdInstructionCode", "X509v3 Hold Instruction Code"));
        self.insert(oid!(********4), OidEntry::new("invalidityDate", "X509v3 Invalidity Date"));
        self.insert(oid!(********7), OidEntry::new("deltaCRLIndicator", "X509v3 Delta CRL Indicator"));
        self.insert(oid!(********8), OidEntry::new("issuerDistributionPoint", "X509v3 Issuer Distribution Point"));
        self.insert(oid!(********9), OidEntry::new("issuer", "X509v3 Issuer"));
        self.insert(oid!(********0), OidEntry::new("nameConstraints", "X509v3 Name Constraints"));
        self.insert(oid!(********1), OidEntry::new("crlDistributionPoints", "X509v3 CRL Distribution Points"));
        self.insert(oid!(********2), OidEntry::new("certificatePolicies", "X509v3 Certificate Policies"));
        self.insert(oid!(********3), OidEntry::new("policyMappings", "X509v3 Policy Mappings"));
        self.insert(oid!(********5), OidEntry::new("authorityKeyIdentifier", "X509v3 Authority Key Identifier"));
        self.insert(oid!(********6), OidEntry::new("policyConstraints", "X509v3 Policy Constraints"));
        self.insert(oid!(********7), OidEntry::new("extendedKeyUsage", "X509v3 Extended Key Usage"));
        self.insert(oid!(2.5.29.46), OidEntry::new("freshestCRL", "X509v3 Freshest CRL"));
        self.insert(oid!(2.5.29.54), OidEntry::new("inhibitAnyPolicy", "X509v3 Inhibit Any-policy"));
        self.insert(oid!(2.16.840.1.113730.1.1), OidEntry::new("nsCertType", "X.509 v3 Certificate Type"));
        self.insert(oid!(2.16.840.1.113730.1.2), OidEntry::new("nsBaseURL", "Base URL"));
        self.insert(oid!(2.16.840.1.113730.1.3), OidEntry::new("nsRevocationURL", "Revocation URL"));
        self.insert(oid!(2.16.840.1.113730.1.4), OidEntry::new("nsCARevocationURL", "CA Revocation URL"));
        self.insert(oid!(2.16.840.1.113730.1.5), OidEntry::new("nsCACRLURL", "CA CRL URL"));
        self.insert(oid!(2.16.840.1.113730.1.6), OidEntry::new("nsCACertURL", "CA Certificate URL"));
        self.insert(oid!(2.16.840.1.113730.1.7), OidEntry::new("nsRenewalURL", "Renewal URL"));
        self.insert(oid!(2.16.840.1.113730.1.8), OidEntry::new("nsCAPolicyURL", "CA Policy URL"));
        self.insert(oid!(2.16.840.1.113730.1.9), OidEntry::new("nsHomepageURL", "Certificate Homepage URL"));
        self.insert(oid!(2.16.840.1.113730.1.10), OidEntry::new("nsEntityLogo", "Certificate Entity Logo"));
        self.insert(oid!(2.16.840.1.113730.1.11), OidEntry::new("nsUserPicture", "Certificate User Picture"));
        self.insert(oid!(2.16.840.1.113730.1.12), OidEntry::new("nsSSLServerName", "SSL Server Name"));
        self.insert(oid!(2.16.840.1.113730.1.13), OidEntry::new("nsComment", "Certificate Comment"));
        self
    }

    #[cfg(feature = "x962")]
    #[cfg_attr(docsrs, doc(cfg(feature = "x962")))]
    #[doc = "Load all known OIDs for feature `x962` in the registry."]
    pub fn with_x962(mut self) -> Self {
        self.insert(oid!(1.2.840.10045.2.1), OidEntry::new("id-ecPublicKey", "Elliptic curve public key cryptography"));
        self.insert(oid!(1.2.840.10045.4.3.1), OidEntry::new("ecdsa-with-SHA224", "Elliptic curve Digital Signature Algorithm (DSA) coupled with the Secure Hash Algorithm 224 (SHA224) algorithm"));
        self.insert(oid!(1.2.840.10045.4.3.2), OidEntry::new("ecdsa-with-SHA256", "Elliptic curve Digital Signature Algorithm (DSA) coupled with the Secure Hash Algorithm 256 (SHA256) algorithm"));
        self.insert(oid!(1.2.840.10045.4.3.3), OidEntry::new("ecdsa-with-SHA384", "Elliptic curve Digital Signature Algorithm (DSA) coupled with the Secure Hash Algorithm 384 (SHA384) algorithm"));
        self.insert(oid!(1.2.840.10045.4.3.4), OidEntry::new("ecdsa-with-SHA512", "Elliptic curve Digital Signature Algorithm (DSA) coupled with the Secure Hash Algorithm 512 (SHA512) algorithm"));
        self.insert(oid!(1.2.840.10045.3.1.7), OidEntry::new("prime256v1", "P-256 elliptic curve parameter"));
        self
    }

}
