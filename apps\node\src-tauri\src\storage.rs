use crate::error::{AppError, Result};
use sqlx::SqlitePool;
use serde::{Deserialize, Serialize};
use std::path::Path;
use tracing::{info, error};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TaskRecord {
    pub id: i64,
    pub task_id: String,
    pub task_type: i32,
    pub target_url: String,
    pub priority: i32,
    pub status: i32,
    pub retry_count: i32,
    pub max_retries: i32,
    pub metadata: Option<String>,
    pub assigned_at: Option<chrono::DateTime<chrono::Utc>>,
    pub started_at: Option<chrono::DateTime<chrono::Utc>>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProxyRecord {
    pub id: i64,
    pub proxy_id: String,
    pub host: String,
    pub port: i32,
    pub protocol: String,
    pub username: Option<String>,
    pub password: Option<String>,
    pub country: Option<String>,
    pub region: Option<String>,
    pub provider: Option<String>,
    pub status: i32,
    pub success_count: i64,
    pub failure_count: i64,
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
    pub last_checked: Option<chrono::DateTime<chrono::Utc>>,
    pub response_time: Option<i32>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountRecord {
    pub id: i64,
    pub account_id: String,
    pub username: String,
    pub password: String,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub status: i32,
    pub login_count: i64,
    pub last_login: Option<chrono::DateTime<chrono::Utc>>,
    pub last_activity: Option<chrono::DateTime<chrono::Utc>>,
    pub risk_score: f64,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

// 新增数据结构

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskResult {
    pub id: i64,
    pub task_id: String,
    pub result_type: i32,
    pub result_data: Option<String>,
    pub error_message: Option<String>,
    pub error_code: Option<String>,
    pub execution_time: Option<i32>,
    pub data_count: i32,
    pub proxy_used: Option<String>,
    pub account_used: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskLog {
    pub id: i64,
    pub task_id: String,
    pub log_level: i32,
    pub log_message: String,
    pub log_context: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyUsage {
    pub id: i64,
    pub proxy_id: String,
    pub task_id: Option<String>,
    pub usage_type: i32,
    pub success: bool,
    pub response_time: Option<i32>,
    pub bytes_transferred: i64,
    pub error_message: Option<String>,
    pub used_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountLog {
    pub id: i64,
    pub account_id: String,
    pub operation_type: i32,
    pub operation_result: i32,
    pub operation_details: Option<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub proxy_used: Option<String>,
    pub error_message: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemMetrics {
    pub id: i64,
    pub node_id: String,
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub memory_total: i64,
    pub memory_used: i64,
    pub disk_usage: f64,
    pub disk_total: i64,
    pub disk_used: i64,
    pub network_in: i64,
    pub network_out: i64,
    pub active_connections: i32,
    pub recorded_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorLog {
    pub id: i64,
    pub error_type: i32,
    pub error_level: i32,
    pub error_code: Option<String>,
    pub error_message: String,
    pub error_context: Option<String>,
    pub stack_trace: Option<String>,
    pub component: Option<String>,
    pub task_id: Option<String>,
    pub account_id: Option<String>,
    pub proxy_id: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

// 统计和视图数据结构

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStatistics {
    pub total_tasks: i64,
    pub pending_tasks: i64,
    pub running_tasks: i64,
    pub completed_tasks: i64,
    pub failed_tasks: i64,
    pub success_rate: f64,
    pub avg_execution_time: Option<f64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyPoolStatus {
    pub total_proxies: i64,
    pub active_proxies: i64,
    pub unavailable_proxies: i64,
    pub maintenance_proxies: i64,
    pub testing_proxies: i64,
    pub avg_response_time: Option<f64>,
    pub health_rate: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccountPoolStatus {
    pub total_accounts: i64,
    pub normal_accounts: i64,
    pub banned_accounts: i64,
    pub abnormal_accounts: i64,
    pub maintenance_accounts: i64,
    pub recently_active: i64,
    pub avg_risk_score: Option<f64>,
    pub high_risk_accounts: i64,
}

pub struct StorageManager {
    pool: SqlitePool,
}

impl StorageManager {
    pub async fn new(database_url: &str) -> Result<Self> {
        info!("正在初始化数据库: {}", database_url);

        // 确保数据目录存在
        let db_path = database_url.trim_start_matches("sqlite:");
        if let Some(parent) = Path::new(db_path).parent() {
            info!("创建数据库目录: {:?}", parent);
            tokio::fs::create_dir_all(parent).await
                .map_err(|e| {
                    error!("无法创建数据库目录 {:?}: {}", parent, e);
                    AppError::Database(sqlx::Error::Io(e))
                })?;
        }

        // 检查数据库文件是否存在，如果不存在则创建
        if !Path::new(db_path).exists() {
            info!("数据库文件不存在，正在创建: {}", db_path);
            tokio::fs::File::create(db_path).await
                .map_err(|e| {
                    error!("无法创建数据库文件 {}: {}", db_path, e);
                    AppError::Database(sqlx::Error::Io(e))
                })?;
        }

        info!("正在连接数据库...");
        let pool = SqlitePool::connect(database_url).await
            .map_err(|e| {
                error!("数据库连接失败: {}", e);
                AppError::Database(e)
            })?;

        info!("正在运行数据库迁移...");
        // 使用SQLx迁移系统
        sqlx::migrate!("./migrations").run(&pool).await
            .map_err(|e| {
                error!("数据库迁移失败: {}", e);
                AppError::Migration(format!("Migration failed: {}", e))
            })?;

        info!("数据库初始化完成: {}", database_url);

        Ok(Self { pool })
    }

    // 任务结果相关方法 (暂时注释，等待迁移完成)
    /*
    pub async fn insert_task_result(&self, result: &TaskResult) -> Result<i64> {
        let id = sqlx::query!(
            r#"
            INSERT INTO task_results (
                task_id, result_type, result_data, error_message, error_code,
                execution_time, data_count, proxy_used, account_used
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            result.task_id,
            result.result_type,
            result.result_data,
            result.error_message,
            result.error_code,
            result.execution_time,
            result.data_count,
            result.proxy_used,
            result.account_used
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?
        .last_insert_rowid();

        Ok(id)
    }
    */

    // 新增数据库访问方法 (暂时注释，等待类型匹配问题解决)
    /*
    pub async fn get_task_results_by_task_id(&self, task_id: &str) -> Result<Vec<TaskResult>> {
        let results = sqlx::query_as!(
            TaskResult,
            r#"
            SELECT id, task_id, result_type, result_data, error_message,
                   CAST(NULL AS TEXT) as error_code,
                   execution_time, data_count, proxy_used, account_used, created_at
            FROM task_results
            WHERE task_id = ?
            ORDER BY created_at DESC
            "#,
            task_id
        )
        .fetch_all(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(results)
    }

    // 任务日志相关方法
    pub async fn insert_task_log(&self, log: &TaskLog) -> Result<i64> {
        let id = sqlx::query!(
            r#"
            INSERT INTO task_logs (task_id, log_level, log_message, log_context)
            VALUES (?, ?, ?, ?)
            "#,
            log.task_id,
            log.log_level,
            log.log_message,
            log.log_context
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?
        .last_insert_rowid();

        Ok(id)
    }

    pub async fn get_task_logs(&self, task_id: &str, limit: Option<i32>) -> Result<Vec<TaskLog>> {
        let limit = limit.unwrap_or(100);
        let logs = sqlx::query_as!(
            TaskLog,
            r#"
            SELECT id, task_id, log_level, log_message, log_context, created_at
            FROM task_logs
            WHERE task_id = ?
            ORDER BY created_at DESC
            LIMIT ?
            "#,
            task_id,
            limit
        )
        .fetch_all(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(logs)
    }

    // 代理使用统计相关方法
    pub async fn insert_proxy_usage(&self, usage: &ProxyUsage) -> Result<i64> {
        let id = sqlx::query!(
            r#"
            INSERT INTO proxy_usage (
                proxy_id, task_id, usage_type, success, response_time,
                bytes_transferred, error_message
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
            "#,
            usage.proxy_id,
            usage.task_id,
            usage.usage_type,
            usage.success,
            usage.response_time,
            usage.bytes_transferred,
            usage.error_message
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?
        .last_insert_rowid();

        Ok(id)
    }

    // 账号日志相关方法
    pub async fn insert_account_log(&self, log: &AccountLog) -> Result<i64> {
        let id = sqlx::query!(
            r#"
            INSERT INTO account_logs (
                account_id, operation_type, operation_result, operation_details,
                ip_address, user_agent, proxy_used, error_message
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            log.account_id,
            log.operation_type,
            log.operation_result,
            log.operation_details,
            log.ip_address,
            log.user_agent,
            log.proxy_used,
            log.error_message
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?
        .last_insert_rowid();

        Ok(id)
    }

    // 系统监控相关方法
    pub async fn insert_system_metrics(&self, metrics: &SystemMetrics) -> Result<i64> {
        let id = sqlx::query!(
            r#"
            INSERT INTO system_metrics (
                node_id, cpu_usage, memory_usage, memory_total, memory_used,
                disk_usage, disk_total, disk_used, network_in, network_out, active_connections
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            metrics.node_id,
            metrics.cpu_usage,
            metrics.memory_usage,
            metrics.memory_total,
            metrics.memory_used,
            metrics.disk_usage,
            metrics.disk_total,
            metrics.disk_used,
            metrics.network_in,
            metrics.network_out,
            metrics.active_connections
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?
        .last_insert_rowid();

        Ok(id)
    }

    pub async fn get_latest_system_metrics(&self, node_id: &str) -> Result<Option<SystemMetrics>> {
        let metrics = sqlx::query_as!(
            SystemMetrics,
            r#"
            SELECT id, node_id, cpu_usage, memory_usage, memory_total, memory_used,
                   disk_usage, disk_total, disk_used, network_in, network_out,
                   active_connections, recorded_at
            FROM system_metrics
            WHERE node_id = ?
            ORDER BY recorded_at DESC
            LIMIT 1
            "#,
            node_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(metrics)
    }

    // 错误日志相关方法
    pub async fn insert_error_log(&self, log: &ErrorLog) -> Result<i64> {
        let id = sqlx::query!(
            r#"
            INSERT INTO error_logs (
                error_type, error_level, error_code, error_message, error_context,
                stack_trace, component, task_id, account_id, proxy_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            log.error_type,
            log.error_level,
            log.error_code,
            log.error_message,
            log.error_context,
            log.stack_trace,
            log.component,
            log.task_id,
            log.account_id,
            log.proxy_id
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?
        .last_insert_rowid();

        Ok(id)
    }
    */

    // 统计查询方法 (暂时注释，等待迁移完成)
    /*
    pub async fn get_task_statistics(&self) -> Result<TaskStatistics> {
        let row = sqlx::query!(
            r#"
            SELECT
                COUNT(*) as total_tasks,
                COUNT(CASE WHEN status = 0 THEN 1 END) as pending_tasks,
                COUNT(CASE WHEN status = 1 THEN 1 END) as running_tasks,
                COUNT(CASE WHEN status = 2 THEN 1 END) as completed_tasks,
                COUNT(CASE WHEN status = 3 THEN 1 END) as failed_tasks,
                CAST(COUNT(CASE WHEN status = 2 THEN 1 END) AS REAL) /
                NULLIF(COUNT(CASE WHEN status IN (2, 3) THEN 1 END), 0) * 100 as success_rate
            FROM crawl_tasks
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(AppError::Database)?;

        // 获取平均执行时间
        let avg_time_row = sqlx::query!(
            r#"
            SELECT AVG(execution_time) as avg_execution_time
            FROM task_results
            WHERE execution_time IS NOT NULL
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(TaskStatistics {
            total_tasks: row.total_tasks,
            pending_tasks: row.pending_tasks.unwrap_or(0),
            running_tasks: row.running_tasks.unwrap_or(0),
            completed_tasks: row.completed_tasks.unwrap_or(0),
            failed_tasks: row.failed_tasks.unwrap_or(0),
            success_rate: row.success_rate.unwrap_or(0.0),
            avg_execution_time: avg_time_row.avg_execution_time,
        })
    }

    pub async fn get_proxy_pool_status(&self) -> Result<ProxyPoolStatus> {
        let row = sqlx::query!(
            r#"
            SELECT
                COUNT(*) as total_proxies,
                COUNT(CASE WHEN status = 1 THEN 1 END) as active_proxies,
                COUNT(CASE WHEN status = 2 THEN 1 END) as unavailable_proxies,
                COUNT(CASE WHEN status = 3 THEN 1 END) as maintenance_proxies,
                COUNT(CASE WHEN status = 4 THEN 1 END) as testing_proxies,
                AVG(response_time) as avg_response_time,
                CAST(COUNT(CASE WHEN status = 1 THEN 1 END) AS REAL) /
                NULLIF(COUNT(*), 0) * 100 as health_rate
            FROM proxies
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(ProxyPoolStatus {
            total_proxies: row.total_proxies,
            active_proxies: row.active_proxies.unwrap_or(0),
            unavailable_proxies: row.unavailable_proxies.unwrap_or(0),
            maintenance_proxies: row.maintenance_proxies.unwrap_or(0),
            testing_proxies: row.testing_proxies.unwrap_or(0),
            avg_response_time: row.avg_response_time,
            health_rate: row.health_rate.unwrap_or(0.0),
        })
    }

    pub async fn get_account_pool_status(&self) -> Result<AccountPoolStatus> {
        let row = sqlx::query!(
            r#"
            SELECT
                COUNT(*) as total_accounts,
                COUNT(CASE WHEN status = 1 THEN 1 END) as normal_accounts,
                COUNT(CASE WHEN status = 2 THEN 1 END) as banned_accounts,
                COUNT(CASE WHEN status = 3 THEN 1 END) as abnormal_accounts,
                COUNT(CASE WHEN status = 4 THEN 1 END) as maintenance_accounts,
                COUNT(CASE WHEN last_login > datetime('now', '-24 hours') THEN 1 END) as recently_active,
                AVG(risk_score) as avg_risk_score,
                COUNT(CASE WHEN risk_score > 0.7 THEN 1 END) as high_risk_accounts
            FROM accounts
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(AccountPoolStatus {
            total_accounts: row.total_accounts,
            normal_accounts: row.normal_accounts.unwrap_or(0),
            banned_accounts: row.banned_accounts.unwrap_or(0),
            abnormal_accounts: row.abnormal_accounts.unwrap_or(0),
            maintenance_accounts: row.maintenance_accounts.unwrap_or(0),
            recently_active: row.recently_active.unwrap_or(0),
            avg_risk_score: row.avg_risk_score,
            high_risk_accounts: row.high_risk_accounts.unwrap_or(0),
        })
    }
    */

    // 任务相关操作
    pub async fn save_task(&self, task: &crate::crawler::CrawlTask) -> Result<i64> {
        let task_type = match task.task_type {
            crate::crawler::TaskType::User => 1,
            crate::crawler::TaskType::Post => 2,
            crate::crawler::TaskType::Comment => 3,
            crate::crawler::TaskType::Topic => 4,
        };

        let metadata = serde_json::to_string(&task.metadata)
            .map_err(|e| AppError::Serialization(e))?;

        let result = sqlx::query!(
            r#"
            INSERT INTO crawl_tasks (task_id, task_type, target_url, priority, status, retry_count, max_retries, metadata)
            VALUES (?1, ?2, ?3, ?4, 0, ?5, ?6, ?7)
            "#,
            task.id,
            task_type,
            task.target_url,
            task.priority,
            task.retry_count,
            task.max_retries,
            metadata
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(result.last_insert_rowid())
    }

    pub async fn update_task_status(&self, task_id: &str, status: i32) -> Result<()> {
        sqlx::query!(
            "UPDATE crawl_tasks SET status = ?1, updated_at = CURRENT_TIMESTAMP WHERE task_id = ?2",
            status,
            task_id
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(())
    }

    pub async fn get_task_by_id(&self, task_id: &str) -> Result<Option<TaskRecord>> {
        let row = sqlx::query!(
            "SELECT * FROM crawl_tasks WHERE task_id = ?1",
            task_id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(AppError::Database)?;

        if let Some(row) = row {
            Ok(Some(TaskRecord {
                id: row.id.unwrap_or(0),
                task_id: row.task_id,
                task_type: row.task_type as i32,
                target_url: row.target_url,
                priority: row.priority as i32,
                status: row.status as i32,
                retry_count: row.retry_count as i32,
                max_retries: row.max_retries as i32,
                metadata: row.metadata,
                assigned_at: row.assigned_at.map(|dt| chrono::DateTime::from_naive_utc_and_offset(dt, chrono::Utc)),
                started_at: row.started_at.map(|dt| chrono::DateTime::from_naive_utc_and_offset(dt, chrono::Utc)),
                completed_at: row.completed_at.map(|dt| chrono::DateTime::from_naive_utc_and_offset(dt, chrono::Utc)),
                created_at: chrono::DateTime::from_naive_utc_and_offset(row.created_at, chrono::Utc),
                updated_at: chrono::DateTime::from_naive_utc_and_offset(row.updated_at, chrono::Utc),
            }))
        } else {
            Ok(None)
        }
    }

    // 代理相关操作
    pub async fn save_proxy(&self, proxy: &crate::proxy::Proxy) -> Result<i64> {
        let protocol = match proxy.protocol {
            crate::proxy::ProxyProtocol::Http => "http",
            crate::proxy::ProxyProtocol::Https => "https",
            crate::proxy::ProxyProtocol::Socks5 => "socks5",
        };

        let status = match proxy.status {
            crate::proxy::ProxyStatus::Available => 1,
            crate::proxy::ProxyStatus::Unavailable => 2,
            crate::proxy::ProxyStatus::Maintenance => 3,
            crate::proxy::ProxyStatus::Testing => 4,
        };

        let port = proxy.port as i32;
        let success_count = proxy.success_count as i64;
        let failure_count = proxy.failure_count as i64;
        let response_time = proxy.response_time.map(|t| t as i32);

        let result = sqlx::query!(
            r#"
            INSERT INTO proxies (proxy_id, host, port, protocol, username, password, country, region, provider, status, success_count, failure_count, response_time)
            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13)
            "#,
            proxy.id,
            proxy.host,
            port,
            protocol,
            proxy.username,
            proxy.password,
            proxy.country,
            proxy.region,
            proxy.provider,
            status,
            success_count,
            failure_count,
            response_time
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(result.last_insert_rowid())
    }

    pub async fn update_proxy_status(&self, proxy_id: &str, status: i32, response_time: Option<u64>) -> Result<()> {
        let response_time_i32 = response_time.map(|t| t as i32);

        sqlx::query!(
            "UPDATE proxies SET status = ?1, response_time = ?2, last_checked = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE proxy_id = ?3",
            status,
            response_time_i32,
            proxy_id
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(())
    }

    // 账号相关操作
    pub async fn save_account(&self, account: &crate::account::Account) -> Result<i64> {
        let status = match account.status {
            crate::account::AccountStatus::Normal => 1,
            crate::account::AccountStatus::Banned => 2,
            crate::account::AccountStatus::Abnormal => 3,
            crate::account::AccountStatus::Maintenance => 4,
        };

        let login_count_i64 = account.login_count as i64;

        let result = sqlx::query!(
            r#"
            INSERT INTO accounts (account_id, username, password, phone, email, status, login_count, risk_score)
            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)
            "#,
            account.id,
            account.username,
            account.password,
            account.phone,
            account.email,
            status,
            login_count_i64,
            account.risk_score
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(result.last_insert_rowid())
    }

    pub async fn update_account_login(&self, account_id: &str, login_count: u64, risk_score: f64) -> Result<()> {
        let login_count_i64 = login_count as i64;

        sqlx::query!(
            "UPDATE accounts SET login_count = ?1, risk_score = ?2, last_login = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE account_id = ?3",
            login_count_i64,
            risk_score,
            account_id
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(())
    }

    // 缓存相关操作
    pub async fn save_cache(&self, key: &str, value: &str, expires_at: chrono::DateTime<chrono::Utc>) -> Result<()> {
        sqlx::query!(
            "INSERT OR REPLACE INTO request_cache (cache_key, cache_value, expires_at) VALUES (?1, ?2, ?3)",
            key,
            value,
            expires_at
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(())
    }

    pub async fn get_cache(&self, key: &str) -> Result<Option<String>> {
        let row = sqlx::query!(
            "SELECT cache_value FROM request_cache WHERE cache_key = ?1 AND expires_at > CURRENT_TIMESTAMP",
            key
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(row.map(|r| r.cache_value))
    }

    pub async fn cleanup_expired_cache(&self) -> Result<u64> {
        let result = sqlx::query!(
            "DELETE FROM request_cache WHERE expires_at <= CURRENT_TIMESTAMP"
        )
        .execute(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok(result.rows_affected())
    }

    // 统计相关操作
    pub async fn get_task_statistics(&self) -> Result<(u64, u64, u64)> {
        let row = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as failed
            FROM crawl_tasks
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok((row.total as u64, row.completed.unwrap_or(0) as u64, row.failed.unwrap_or(0) as u64))
    }

    pub async fn get_proxy_statistics(&self) -> Result<(u64, u64)> {
        let row = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as available
            FROM proxies
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok((row.total as u64, row.available.unwrap_or(0) as u64))
    }

    pub async fn get_account_statistics(&self) -> Result<(u64, u64)> {
        let row = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as normal
            FROM accounts
            "#
        )
        .fetch_one(&self.pool)
        .await
        .map_err(AppError::Database)?;

        Ok((row.total as u64, row.normal.unwrap_or(0) as u64))
    }

    pub async fn close(&self) {
        self.pool.close().await;
        info!("数据库连接已关闭");
    }
}
