<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="可配置的服务特征"><title>ConfigurableService in weibo_crawler_node::service - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc trait"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Configurable<wbr>Service</a></h2><h3><a href="#required-associated-types">Required Associated Types</a></h3><ul class="block"><li><a href="#associatedtype.Config" title="Config">Config</a></li></ul><h3><a href="#required-methods">Required Methods</a></h3><ul class="block"><li><a href="#tymethod.get_config" title="get_config">get_config</a></li><li><a href="#tymethod.update_config" title="update_config">update_config</a></li></ul><h3><a href="#implementors">Implementors</a></h3></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>service</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">service</a></div><h1>Trait <span class="trait">ConfigurableService</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/service/mod.rs.html#56-61">Source</a> </span></div><pre class="rust item-decl"><code>pub trait ConfigurableService: <a class="trait" href="trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a> {
    type <a href="#associatedtype.Config" class="associatedtype">Config</a>;

    // Required methods
    fn <a href="#tymethod.get_config" class="fn">get_config</a>(&amp;self) -&gt; &amp;Self::<a class="associatedtype" href="trait.ConfigurableService.html#associatedtype.Config" title="type weibo_crawler_node::service::ConfigurableService::Config">Config</a>;
<span class="item-spacer"></span>    fn <a href="#tymethod.update_config" class="fn">update_config</a>(&amp;mut self, config: Self::<a class="associatedtype" href="trait.ConfigurableService.html#associatedtype.Config" title="type weibo_crawler_node::service::ConfigurableService::Config">Config</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;;
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>可配置的服务特征</p>
</div></details><h2 id="required-associated-types" class="section-header">Required Associated Types<a href="#required-associated-types" class="anchor">§</a></h2><div class="methods"><section id="associatedtype.Config" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#57">Source</a><h4 class="code-header">type <a href="#associatedtype.Config" class="associatedtype">Config</a></h4></section></div><h2 id="required-methods" class="section-header">Required Methods<a href="#required-methods" class="anchor">§</a></h2><div class="methods"><section id="tymethod.get_config" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#59">Source</a><h4 class="code-header">fn <a href="#tymethod.get_config" class="fn">get_config</a>(&amp;self) -&gt; &amp;Self::<a class="associatedtype" href="trait.ConfigurableService.html#associatedtype.Config" title="type weibo_crawler_node::service::ConfigurableService::Config">Config</a></h4></section><section id="tymethod.update_config" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#60">Source</a><h4 class="code-header">fn <a href="#tymethod.update_config" class="fn">update_config</a>(&amp;mut self, config: Self::<a class="associatedtype" href="trait.ConfigurableService.html#associatedtype.Config" title="type weibo_crawler_node::service::ConfigurableService::Config">Config</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></div><h2 id="implementors" class="section-header">Implementors<a href="#implementors" class="anchor">§</a></h2><div id="implementors-list"></div><script src="../../trait.impl/weibo_crawler_node/service/trait.ConfigurableService.js" async></script></section></div></main></body></html>