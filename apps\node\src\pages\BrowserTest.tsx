import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';

interface LoginCredentials {
  username: string;
  password: string;
}

interface LoginResult {
  success: boolean;
  message: string;
  user_info?: {
    uid: string;
    nickname: string;
    avatar_url?: string;
  };
  cookies?: string;
}

interface BrowserConfig {
  headless: boolean;
  viewport_width: number;
  viewport_height: number;
  timeout: number;
  user_agent?: string;
}

interface QrCodeData {
  qr_code_url: string;
  qr_code_image: string;
  session_id: string;
}

interface QrLoginStatus {
  status: string; // "waiting", "scanned", "success", "expired", "error"
  message: string;
  login_result?: LoginResult;
}

const BrowserTest: React.FC = () => {
  const [testResult, setTestResult] = useState<string>('');
  const [loginResult, setLoginResult] = useState<LoginResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [credentials, setCredentials] = useState<LoginCredentials>({
    username: '',
    password: ''
  });
  const [config, setConfig] = useState<BrowserConfig>({
    headless: true,
    viewport_width: 1920,
    viewport_height: 1080,
    timeout: 30000,
    user_agent: undefined
  });

  // 二维码登录相关状态
  const [qrCodeData, setQrCodeData] = useState<QrCodeData | null>(null);
  const [qrLoginStatus, setQrLoginStatus] = useState<QrLoginStatus | null>(null);
  const [qrLoading, setQrLoading] = useState(false);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  const testBrowser = async () => {
    setLoading(true);
    try {
      const result = await invoke<string>('test_browser');
      setTestResult(`浏览器测试成功！页面标题: ${result}`);
    } catch (error) {
      setTestResult(`浏览器测试失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const loginWeibo = async () => {
    if (!credentials.username || !credentials.password) {
      alert('请输入用户名和密码');
      return;
    }

    setLoading(true);
    try {
      const result = await invoke<LoginResult>('login_weibo', {
        credentials
      });
      setLoginResult(result);
    } catch (error) {
      setLoginResult({
        success: false,
        message: `登录失败: ${error}`
      });
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = async () => {
    setLoading(true);
    try {
      await invoke('update_browser_config', { config });
      alert('配置更新成功');
    } catch (error) {
      alert(`配置更新失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const getConfig = async () => {
    try {
      const result = await invoke<BrowserConfig>('get_browser_config');
      setConfig(result);
      alert('配置获取成功');
    } catch (error) {
      alert(`配置获取失败: ${error}`);
    }
  };

  // 二维码登录相关函数
  const getQrCode = async () => {
    setQrLoading(true);
    setQrCodeData(null);
    setQrLoginStatus(null);

    // 清除之前的轮询
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }

    try {
      const result = await invoke<QrCodeData>('get_weibo_qr_code');
      setQrCodeData(result);
      setQrLoginStatus({
        status: 'waiting',
        message: '请使用微博APP扫描二维码'
      });

      // 开始轮询登录状态
      startPolling(result.session_id);
    } catch (error) {
      setQrLoginStatus({
        status: 'error',
        message: `获取二维码失败: ${error}`
      });
    } finally {
      setQrLoading(false);
    }
  };

  const startPolling = (sessionId: string) => {
    const interval = setInterval(async () => {
      try {
        const status = await invoke<QrLoginStatus>('check_qr_login_status', { sessionId });
        setQrLoginStatus(status);

        if (status.status === 'success') {
          // 登录成功，停止轮询
          clearInterval(interval);
          setPollingInterval(null);

          if (status.login_result) {
            setLoginResult(status.login_result);

            // 保存账号到用户池
            if (status.login_result.user_info && status.login_result.cookies) {
              try {
                await invoke('save_account_to_pool', {
                  accountId: status.login_result.user_info.uid,
                  nickname: status.login_result.user_info.nickname,
                  cookies: status.login_result.cookies,
                  avatarUrl: status.login_result.user_info.avatar_url
                });
                alert('账号已保存到用户池');
              } catch (error) {
                console.error('保存账号失败:', error);
              }
            }
          }
        } else if (status.status === 'expired' || status.status === 'error') {
          // 二维码过期或出错，停止轮询
          clearInterval(interval);
          setPollingInterval(null);
        }
      } catch (error) {
        console.error('轮询状态失败:', error);
        setQrLoginStatus({
          status: 'error',
          message: `检查登录状态失败: ${error}`
        });
        clearInterval(interval);
        setPollingInterval(null);
      }
    }, 3000); // 每3秒轮询一次

    setPollingInterval(interval);
  };

  const stopPolling = () => {
    if (pollingInterval) {
      clearInterval(pollingInterval);
      setPollingInterval(null);
    }
    setQrCodeData(null);
    setQrLoginStatus(null);
  };

  // 组件卸载时清理轮询
  React.useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">浏览器功能测试</h1>
      
      {/* 浏览器测试 */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">浏览器基础测试</h2>
        <button
          onClick={testBrowser}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? '测试中...' : '测试浏览器'}
        </button>
        {testResult && (
          <div className="mt-4 p-3 bg-gray-100 rounded">
            <p>{testResult}</p>
          </div>
        )}
      </div>

      {/* 浏览器配置 */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">浏览器配置</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">无头模式</label>
            <input
              type="checkbox"
              checked={config.headless}
              onChange={(e) => setConfig({...config, headless: e.target.checked})}
              className="w-4 h-4"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">超时时间 (ms)</label>
            <input
              type="number"
              value={config.timeout}
              onChange={(e) => setConfig({...config, timeout: parseInt(e.target.value)})}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">视口宽度</label>
            <input
              type="number"
              value={config.viewport_width}
              onChange={(e) => setConfig({...config, viewport_width: parseInt(e.target.value)})}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">视口高度</label>
            <input
              type="number"
              value={config.viewport_height}
              onChange={(e) => setConfig({...config, viewport_height: parseInt(e.target.value)})}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <button
            onClick={updateConfig}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            更新配置
          </button>
          <button
            onClick={getConfig}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            获取配置
          </button>
        </div>
      </div>

      {/* 微博登录测试 */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">微博登录测试</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">用户名</label>
            <input
              type="text"
              value={credentials.username}
              onChange={(e) => setCredentials({...credentials, username: e.target.value})}
              className="w-full px-3 py-2 border rounded"
              placeholder="请输入微博用户名"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">密码</label>
            <input
              type="password"
              value={credentials.password}
              onChange={(e) => setCredentials({...credentials, password: e.target.value})}
              className="w-full px-3 py-2 border rounded"
              placeholder="请输入密码"
            />
          </div>
        </div>
        <button
          onClick={loginWeibo}
          disabled={loading}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
        >
          {loading ? '登录中...' : '测试微博登录'}
        </button>
        {loginResult && (
          <div className="mt-4 p-3 bg-gray-100 rounded">
            <h3 className="font-semibold mb-2">登录结果:</h3>
            <p><strong>成功:</strong> {loginResult.success ? '是' : '否'}</p>
            <p><strong>消息:</strong> {loginResult.message}</p>
            {loginResult.user_info && (
              <div className="mt-2">
                <p><strong>用户ID:</strong> {loginResult.user_info.uid}</p>
                <p><strong>昵称:</strong> {loginResult.user_info.nickname}</p>
                {loginResult.user_info.avatar_url && (
                  <p><strong>头像:</strong> {loginResult.user_info.avatar_url}</p>
                )}
              </div>
            )}
            {loginResult.cookies && (
              <div className="mt-2">
                <p><strong>Cookies:</strong></p>
                <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-32">
                  {loginResult.cookies}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 微博二维码登录 */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">微博二维码登录</h2>
        <div className="flex gap-4 mb-4">
          <button
            onClick={getQrCode}
            disabled={qrLoading}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
          >
            {qrLoading ? '获取中...' : '获取二维码'}
          </button>
          {pollingInterval && (
            <button
              onClick={stopPolling}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              停止扫码
            </button>
          )}
        </div>

        {qrCodeData && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 二维码显示 */}
            <div className="text-center">
              <h3 className="font-semibold mb-2">扫描二维码登录</h3>
              <div className="inline-block p-4 bg-white border rounded-lg shadow">
                <img
                  src={`data:image/png;base64,${qrCodeData.qr_code_image}`}
                  alt="微博登录二维码"
                  className="w-48 h-48 mx-auto"
                />
              </div>
              <p className="text-sm text-gray-600 mt-2">
                请使用微博APP扫描二维码
              </p>
            </div>

            {/* 登录状态 */}
            <div>
              <h3 className="font-semibold mb-2">登录状态</h3>
              {qrLoginStatus && (
                <div className={`p-3 rounded ${
                  qrLoginStatus.status === 'success' ? 'bg-green-100 text-green-800' :
                  qrLoginStatus.status === 'error' || qrLoginStatus.status === 'expired' ? 'bg-red-100 text-red-800' :
                  qrLoginStatus.status === 'scanned' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-blue-100 text-blue-800'
                }`}>
                  <p><strong>状态:</strong> {
                    qrLoginStatus.status === 'waiting' ? '等待扫码' :
                    qrLoginStatus.status === 'scanned' ? '已扫码，等待确认' :
                    qrLoginStatus.status === 'success' ? '登录成功' :
                    qrLoginStatus.status === 'expired' ? '二维码已过期' :
                    qrLoginStatus.status === 'error' ? '登录失败' :
                    qrLoginStatus.status
                  }</p>
                  <p><strong>消息:</strong> {qrLoginStatus.message}</p>

                  {qrLoginStatus.login_result && (
                    <div className="mt-3 p-3 bg-white rounded border">
                      <h4 className="font-semibold mb-2">登录信息:</h4>
                      <p><strong>成功:</strong> {qrLoginStatus.login_result.success ? '是' : '否'}</p>
                      {qrLoginStatus.login_result.user_info && (
                        <div className="mt-2">
                          <p><strong>用户ID:</strong> {qrLoginStatus.login_result.user_info.uid}</p>
                          <p><strong>昵称:</strong> {qrLoginStatus.login_result.user_info.nickname}</p>
                          {qrLoginStatus.login_result.user_info.avatar_url && (
                            <p><strong>头像:</strong> {qrLoginStatus.login_result.user_info.avatar_url}</p>
                          )}
                        </div>
                      )}
                      {qrLoginStatus.login_result.cookies && (
                        <div className="mt-2">
                          <p><strong>Cookies:</strong></p>
                          <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-24">
                            {qrLoginStatus.login_result.cookies}
                          </pre>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {pollingInterval && (
                <div className="mt-3 flex items-center text-sm text-gray-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
                  正在轮询登录状态...
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BrowserTest;
