import React, { useState } from 'react';
import { invoke } from '@tauri-apps/api/tauri';

interface LoginCredentials {
  username: string;
  password: string;
}

interface LoginResult {
  success: boolean;
  message: string;
  user_info?: {
    uid: string;
    nickname: string;
    avatar_url?: string;
  };
  cookies?: string;
}

interface BrowserConfig {
  headless: boolean;
  viewport_width: number;
  viewport_height: number;
  timeout: number;
  user_agent?: string;
}

const BrowserTest: React.FC = () => {
  const [testResult, setTestResult] = useState<string>('');
  const [loginResult, setLoginResult] = useState<LoginResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [credentials, setCredentials] = useState<LoginCredentials>({
    username: '',
    password: ''
  });
  const [config, setConfig] = useState<BrowserConfig>({
    headless: true,
    viewport_width: 1920,
    viewport_height: 1080,
    timeout: 30000,
    user_agent: undefined
  });

  const testBrowser = async () => {
    setLoading(true);
    try {
      const result = await invoke<string>('test_browser');
      setTestResult(`浏览器测试成功！页面标题: ${result}`);
    } catch (error) {
      setTestResult(`浏览器测试失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const loginWeibo = async () => {
    if (!credentials.username || !credentials.password) {
      alert('请输入用户名和密码');
      return;
    }

    setLoading(true);
    try {
      const result = await invoke<LoginResult>('login_weibo', {
        credentials
      });
      setLoginResult(result);
    } catch (error) {
      setLoginResult({
        success: false,
        message: `登录失败: ${error}`
      });
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = async () => {
    setLoading(true);
    try {
      await invoke('update_browser_config', { config });
      alert('配置更新成功');
    } catch (error) {
      alert(`配置更新失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const getConfig = async () => {
    try {
      const result = await invoke<BrowserConfig>('get_browser_config');
      setConfig(result);
      alert('配置获取成功');
    } catch (error) {
      alert(`配置获取失败: ${error}`);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">浏览器功能测试</h1>
      
      {/* 浏览器测试 */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">浏览器基础测试</h2>
        <button
          onClick={testBrowser}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? '测试中...' : '测试浏览器'}
        </button>
        {testResult && (
          <div className="mt-4 p-3 bg-gray-100 rounded">
            <p>{testResult}</p>
          </div>
        )}
      </div>

      {/* 浏览器配置 */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">浏览器配置</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">无头模式</label>
            <input
              type="checkbox"
              checked={config.headless}
              onChange={(e) => setConfig({...config, headless: e.target.checked})}
              className="w-4 h-4"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">超时时间 (ms)</label>
            <input
              type="number"
              value={config.timeout}
              onChange={(e) => setConfig({...config, timeout: parseInt(e.target.value)})}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">视口宽度</label>
            <input
              type="number"
              value={config.viewport_width}
              onChange={(e) => setConfig({...config, viewport_width: parseInt(e.target.value)})}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">视口高度</label>
            <input
              type="number"
              value={config.viewport_height}
              onChange={(e) => setConfig({...config, viewport_height: parseInt(e.target.value)})}
              className="w-full px-3 py-2 border rounded"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <button
            onClick={updateConfig}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            更新配置
          </button>
          <button
            onClick={getConfig}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            获取配置
          </button>
        </div>
      </div>

      {/* 微博登录测试 */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">微博登录测试</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">用户名</label>
            <input
              type="text"
              value={credentials.username}
              onChange={(e) => setCredentials({...credentials, username: e.target.value})}
              className="w-full px-3 py-2 border rounded"
              placeholder="请输入微博用户名"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">密码</label>
            <input
              type="password"
              value={credentials.password}
              onChange={(e) => setCredentials({...credentials, password: e.target.value})}
              className="w-full px-3 py-2 border rounded"
              placeholder="请输入密码"
            />
          </div>
        </div>
        <button
          onClick={loginWeibo}
          disabled={loading}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
        >
          {loading ? '登录中...' : '测试微博登录'}
        </button>
        {loginResult && (
          <div className="mt-4 p-3 bg-gray-100 rounded">
            <h3 className="font-semibold mb-2">登录结果:</h3>
            <p><strong>成功:</strong> {loginResult.success ? '是' : '否'}</p>
            <p><strong>消息:</strong> {loginResult.message}</p>
            {loginResult.user_info && (
              <div className="mt-2">
                <p><strong>用户ID:</strong> {loginResult.user_info.uid}</p>
                <p><strong>昵称:</strong> {loginResult.user_info.nickname}</p>
                {loginResult.user_info.avatar_url && (
                  <p><strong>头像:</strong> {loginResult.user_info.avatar_url}</p>
                )}
              </div>
            )}
            {loginResult.cookies && (
              <div className="mt-2">
                <p><strong>Cookies:</strong></p>
                <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-32">
                  {loginResult.cookies}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default BrowserTest;
