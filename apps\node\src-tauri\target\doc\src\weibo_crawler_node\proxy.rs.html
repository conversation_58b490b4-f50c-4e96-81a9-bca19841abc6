<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\proxy.rs`."><title>proxy.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>proxy.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{AppError, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>reqwest::Client;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::collections::HashMap;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>std::sync::Arc;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>std::time::{Duration, Instant};
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span>tokio::sync::RwLock;
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">use </span>tracing::{info, warn};
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">use </span>uuid::Uuid;
<a href=#10 id=10 data-nosnippet>10</a>
<a href=#11 id=11 data-nosnippet>11</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#12 id=12 data-nosnippet>12</a></span><span class="kw">pub struct </span>Proxy {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>id: String,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>host: String,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>port: u16,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>protocol: ProxyProtocol,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>username: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">pub </span>password: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#19 id=19 data-nosnippet>19</a>    <span class="kw">pub </span>country: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">pub </span>region: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#21 id=21 data-nosnippet>21</a>    <span class="kw">pub </span>provider: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">pub </span>status: ProxyStatus,
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">pub </span>success_count: u64,
<a href=#24 id=24 data-nosnippet>24</a>    <span class="kw">pub </span>failure_count: u64,
<a href=#25 id=25 data-nosnippet>25</a>    <span class="kw">pub </span>last_used: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#26 id=26 data-nosnippet>26</a>    <span class="kw">pub </span>last_checked: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#27 id=27 data-nosnippet>27</a>    <span class="kw">pub </span>response_time: <span class="prelude-ty">Option</span>&lt;u64&gt;, <span class="comment">// 毫秒
<a href=#28 id=28 data-nosnippet>28</a>    </span><span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#29 id=29 data-nosnippet>29</a>}
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#32 id=32 data-nosnippet>32</a></span><span class="kw">pub enum </span>ProxyProtocol {
<a href=#33 id=33 data-nosnippet>33</a>    Http,
<a href=#34 id=34 data-nosnippet>34</a>    Https,
<a href=#35 id=35 data-nosnippet>35</a>    Socks5,
<a href=#36 id=36 data-nosnippet>36</a>}
<a href=#37 id=37 data-nosnippet>37</a>
<a href=#38 id=38 data-nosnippet>38</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#39 id=39 data-nosnippet>39</a></span><span class="kw">pub enum </span>ProxyStatus {
<a href=#40 id=40 data-nosnippet>40</a>    Available,
<a href=#41 id=41 data-nosnippet>41</a>    Unavailable,
<a href=#42 id=42 data-nosnippet>42</a>    Maintenance,
<a href=#43 id=43 data-nosnippet>43</a>    Testing,
<a href=#44 id=44 data-nosnippet>44</a>}
<a href=#45 id=45 data-nosnippet>45</a>
<a href=#46 id=46 data-nosnippet>46</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#47 id=47 data-nosnippet>47</a></span><span class="kw">pub struct </span>ProxyHealthCheck {
<a href=#48 id=48 data-nosnippet>48</a>    <span class="kw">pub </span>proxy_id: String,
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">pub </span>is_healthy: bool,
<a href=#50 id=50 data-nosnippet>50</a>    <span class="kw">pub </span>response_time: <span class="prelude-ty">Option</span>&lt;u64&gt;,
<a href=#51 id=51 data-nosnippet>51</a>    <span class="kw">pub </span>error_message: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#52 id=52 data-nosnippet>52</a>    <span class="kw">pub </span>checked_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#53 id=53 data-nosnippet>53</a>}
<a href=#54 id=54 data-nosnippet>54</a>
<a href=#55 id=55 data-nosnippet>55</a><span class="kw">pub struct </span>ProxyManager {
<a href=#56 id=56 data-nosnippet>56</a>    proxies: Arc&lt;RwLock&lt;HashMap&lt;String, Proxy&gt;&gt;&gt;,
<a href=#57 id=57 data-nosnippet>57</a>    health_check_client: Client,
<a href=#58 id=58 data-nosnippet>58</a>    max_pool_size: usize,
<a href=#59 id=59 data-nosnippet>59</a>    health_check_interval: Duration,
<a href=#60 id=60 data-nosnippet>60</a>    test_url: String,
<a href=#61 id=61 data-nosnippet>61</a>}
<a href=#62 id=62 data-nosnippet>62</a>
<a href=#63 id=63 data-nosnippet>63</a><span class="kw">impl </span>ProxyManager {
<a href=#64 id=64 data-nosnippet>64</a>    <span class="kw">pub async fn </span>new(max_pool_size: usize) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#65 id=65 data-nosnippet>65</a>        <span class="kw">let </span>health_check_client = Client::builder()
<a href=#66 id=66 data-nosnippet>66</a>            .timeout(Duration::from_secs(<span class="number">10</span>))
<a href=#67 id=67 data-nosnippet>67</a>            .build()
<a href=#68 id=68 data-nosnippet>68</a>            .map_err(|e| AppError::Proxy(<span class="macro">format!</span>(<span class="string">"创建健康检查客户端失败: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#69 id=69 data-nosnippet>69</a>
<a href=#70 id=70 data-nosnippet>70</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#71 id=71 data-nosnippet>71</a>            proxies: Arc::new(RwLock::new(HashMap::new())),
<a href=#72 id=72 data-nosnippet>72</a>            health_check_client,
<a href=#73 id=73 data-nosnippet>73</a>            max_pool_size,
<a href=#74 id=74 data-nosnippet>74</a>            health_check_interval: Duration::from_secs(<span class="number">300</span>), <span class="comment">// 5分钟
<a href=#75 id=75 data-nosnippet>75</a>            </span>test_url: <span class="string">"http://httpbin.org/ip"</span>.to_string(),
<a href=#76 id=76 data-nosnippet>76</a>        })
<a href=#77 id=77 data-nosnippet>77</a>    }
<a href=#78 id=78 data-nosnippet>78</a>
<a href=#79 id=79 data-nosnippet>79</a>    <span class="kw">pub async fn </span>add_proxy(<span class="kw-2">&amp;</span><span class="self">self</span>, proxy_config: <span class="kw">crate</span>::commands::ProxyConfig) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#80 id=80 data-nosnippet>80</a>        <span class="kw">let </span>proxy_id = Uuid::new_v4().to_string();
<a href=#81 id=81 data-nosnippet>81</a>        
<a href=#82 id=82 data-nosnippet>82</a>        <span class="kw">let </span>protocol = <span class="kw">match </span>proxy_config.protocol.as_str() {
<a href=#83 id=83 data-nosnippet>83</a>            <span class="string">"http" </span>=&gt; ProxyProtocol::Http,
<a href=#84 id=84 data-nosnippet>84</a>            <span class="string">"https" </span>=&gt; ProxyProtocol::Https,
<a href=#85 id=85 data-nosnippet>85</a>            <span class="string">"socks5" </span>=&gt; ProxyProtocol::Socks5,
<a href=#86 id=86 data-nosnippet>86</a>            <span class="kw">_ </span>=&gt; <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Proxy(<span class="string">"不支持的代理协议"</span>.to_string())),
<a href=#87 id=87 data-nosnippet>87</a>        };
<a href=#88 id=88 data-nosnippet>88</a>
<a href=#89 id=89 data-nosnippet>89</a>        <span class="kw">let </span>proxy = Proxy {
<a href=#90 id=90 data-nosnippet>90</a>            id: proxy_id.clone(),
<a href=#91 id=91 data-nosnippet>91</a>            host: proxy_config.host,
<a href=#92 id=92 data-nosnippet>92</a>            port: proxy_config.port,
<a href=#93 id=93 data-nosnippet>93</a>            protocol,
<a href=#94 id=94 data-nosnippet>94</a>            username: proxy_config.username,
<a href=#95 id=95 data-nosnippet>95</a>            password: proxy_config.password,
<a href=#96 id=96 data-nosnippet>96</a>            country: proxy_config.country,
<a href=#97 id=97 data-nosnippet>97</a>            region: <span class="prelude-val">None</span>,
<a href=#98 id=98 data-nosnippet>98</a>            provider: proxy_config.provider,
<a href=#99 id=99 data-nosnippet>99</a>            status: ProxyStatus::Available,
<a href=#100 id=100 data-nosnippet>100</a>            success_count: <span class="number">0</span>,
<a href=#101 id=101 data-nosnippet>101</a>            failure_count: <span class="number">0</span>,
<a href=#102 id=102 data-nosnippet>102</a>            last_used: <span class="prelude-val">None</span>,
<a href=#103 id=103 data-nosnippet>103</a>            last_checked: <span class="prelude-val">None</span>,
<a href=#104 id=104 data-nosnippet>104</a>            response_time: <span class="prelude-val">None</span>,
<a href=#105 id=105 data-nosnippet>105</a>            created_at: chrono::Utc::now(),
<a href=#106 id=106 data-nosnippet>106</a>        };
<a href=#107 id=107 data-nosnippet>107</a>
<a href=#108 id=108 data-nosnippet>108</a>        <span class="comment">// 检查代理池大小
<a href=#109 id=109 data-nosnippet>109</a>        </span>{
<a href=#110 id=110 data-nosnippet>110</a>            <span class="kw">let </span>proxies = <span class="self">self</span>.proxies.read().<span class="kw">await</span>;
<a href=#111 id=111 data-nosnippet>111</a>            <span class="kw">if </span>proxies.len() &gt;= <span class="self">self</span>.max_pool_size {
<a href=#112 id=112 data-nosnippet>112</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Proxy(<span class="string">"代理池已满"</span>.to_string()));
<a href=#113 id=113 data-nosnippet>113</a>            }
<a href=#114 id=114 data-nosnippet>114</a>        }
<a href=#115 id=115 data-nosnippet>115</a>
<a href=#116 id=116 data-nosnippet>116</a>        <span class="comment">// 测试代理可用性
<a href=#117 id=117 data-nosnippet>117</a>        </span><span class="kw">let </span>health_check = <span class="self">self</span>.test_proxy_health(<span class="kw-2">&amp;</span>proxy).<span class="kw">await</span>;
<a href=#118 id=118 data-nosnippet>118</a>        
<a href=#119 id=119 data-nosnippet>119</a>        <span class="kw">let </span><span class="kw-2">mut </span>proxies = <span class="self">self</span>.proxies.write().<span class="kw">await</span>;
<a href=#120 id=120 data-nosnippet>120</a>        <span class="kw">let </span><span class="kw-2">mut </span>proxy = proxy;
<a href=#121 id=121 data-nosnippet>121</a>        
<a href=#122 id=122 data-nosnippet>122</a>        <span class="kw">if </span>health_check.is_healthy {
<a href=#123 id=123 data-nosnippet>123</a>            proxy.status = ProxyStatus::Available;
<a href=#124 id=124 data-nosnippet>124</a>            proxy.response_time = health_check.response_time;
<a href=#125 id=125 data-nosnippet>125</a>            <span class="macro">info!</span>(<span class="string">"代理 {} 添加成功并通过健康检查"</span>, proxy_id);
<a href=#126 id=126 data-nosnippet>126</a>        } <span class="kw">else </span>{
<a href=#127 id=127 data-nosnippet>127</a>            proxy.status = ProxyStatus::Unavailable;
<a href=#128 id=128 data-nosnippet>128</a>            <span class="macro">warn!</span>(<span class="string">"代理 {} 添加成功但健康检查失败: {:?}"</span>, proxy_id, health_check.error_message);
<a href=#129 id=129 data-nosnippet>129</a>        }
<a href=#130 id=130 data-nosnippet>130</a>        
<a href=#131 id=131 data-nosnippet>131</a>        proxy.last_checked = <span class="prelude-val">Some</span>(health_check.checked_at);
<a href=#132 id=132 data-nosnippet>132</a>        proxies.insert(proxy_id.clone(), proxy);
<a href=#133 id=133 data-nosnippet>133</a>
<a href=#134 id=134 data-nosnippet>134</a>        <span class="prelude-val">Ok</span>(proxy_id)
<a href=#135 id=135 data-nosnippet>135</a>    }
<a href=#136 id=136 data-nosnippet>136</a>
<a href=#137 id=137 data-nosnippet>137</a>    <span class="kw">pub async fn </span>remove_proxy(<span class="kw-2">&amp;</span><span class="self">self</span>, proxy_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#138 id=138 data-nosnippet>138</a>        <span class="kw">let </span><span class="kw-2">mut </span>proxies = <span class="self">self</span>.proxies.write().<span class="kw">await</span>;
<a href=#139 id=139 data-nosnippet>139</a>        
<a href=#140 id=140 data-nosnippet>140</a>        <span class="kw">if </span>proxies.remove(proxy_id).is_some() {
<a href=#141 id=141 data-nosnippet>141</a>            <span class="macro">info!</span>(<span class="string">"代理 {} 移除成功"</span>, proxy_id);
<a href=#142 id=142 data-nosnippet>142</a>            <span class="prelude-val">Ok</span>(())
<a href=#143 id=143 data-nosnippet>143</a>        } <span class="kw">else </span>{
<a href=#144 id=144 data-nosnippet>144</a>            <span class="prelude-val">Err</span>(AppError::Proxy(<span class="macro">format!</span>(<span class="string">"代理 {} 不存在"</span>, proxy_id)))
<a href=#145 id=145 data-nosnippet>145</a>        }
<a href=#146 id=146 data-nosnippet>146</a>    }
<a href=#147 id=147 data-nosnippet>147</a>
<a href=#148 id=148 data-nosnippet>148</a>    <span class="kw">pub async fn </span>get_proxy(<span class="kw-2">&amp;</span><span class="self">self</span>, proxy_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;Proxy&gt;&gt; {
<a href=#149 id=149 data-nosnippet>149</a>        <span class="kw">let </span>proxies = <span class="self">self</span>.proxies.read().<span class="kw">await</span>;
<a href=#150 id=150 data-nosnippet>150</a>        <span class="prelude-val">Ok</span>(proxies.get(proxy_id).cloned())
<a href=#151 id=151 data-nosnippet>151</a>    }
<a href=#152 id=152 data-nosnippet>152</a>
<a href=#153 id=153 data-nosnippet>153</a>    <span class="kw">pub async fn </span>get_available_proxy(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;Proxy&gt;&gt; {
<a href=#154 id=154 data-nosnippet>154</a>        <span class="kw">let </span>proxies = <span class="self">self</span>.proxies.read().<span class="kw">await</span>;
<a href=#155 id=155 data-nosnippet>155</a>        
<a href=#156 id=156 data-nosnippet>156</a>        <span class="comment">// 找到可用的代理，优先选择响应时间短的
<a href=#157 id=157 data-nosnippet>157</a>        </span><span class="kw">let </span><span class="kw-2">mut </span>available_proxies: Vec&lt;<span class="kw-2">&amp;</span>Proxy&gt; = proxies
<a href=#158 id=158 data-nosnippet>158</a>            .values()
<a href=#159 id=159 data-nosnippet>159</a>            .filter(|p| <span class="macro">matches!</span>(p.status, ProxyStatus::Available))
<a href=#160 id=160 data-nosnippet>160</a>            .collect();
<a href=#161 id=161 data-nosnippet>161</a>
<a href=#162 id=162 data-nosnippet>162</a>        <span class="kw">if </span>available_proxies.is_empty() {
<a href=#163 id=163 data-nosnippet>163</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>);
<a href=#164 id=164 data-nosnippet>164</a>        }
<a href=#165 id=165 data-nosnippet>165</a>
<a href=#166 id=166 data-nosnippet>166</a>        <span class="comment">// 按响应时间排序
<a href=#167 id=167 data-nosnippet>167</a>        </span>available_proxies.sort_by(|a, b| {
<a href=#168 id=168 data-nosnippet>168</a>            <span class="kw">match </span>(a.response_time, b.response_time) {
<a href=#169 id=169 data-nosnippet>169</a>                (<span class="prelude-val">Some</span>(a_time), <span class="prelude-val">Some</span>(b_time)) =&gt; a_time.cmp(<span class="kw-2">&amp;</span>b_time),
<a href=#170 id=170 data-nosnippet>170</a>                (<span class="prelude-val">Some</span>(<span class="kw">_</span>), <span class="prelude-val">None</span>) =&gt; std::cmp::Ordering::Less,
<a href=#171 id=171 data-nosnippet>171</a>                (<span class="prelude-val">None</span>, <span class="prelude-val">Some</span>(<span class="kw">_</span>)) =&gt; std::cmp::Ordering::Greater,
<a href=#172 id=172 data-nosnippet>172</a>                (<span class="prelude-val">None</span>, <span class="prelude-val">None</span>) =&gt; std::cmp::Ordering::Equal,
<a href=#173 id=173 data-nosnippet>173</a>            }
<a href=#174 id=174 data-nosnippet>174</a>        });
<a href=#175 id=175 data-nosnippet>175</a>
<a href=#176 id=176 data-nosnippet>176</a>        <span class="prelude-val">Ok</span>(available_proxies.first().map(|p| (<span class="kw-2">*</span>p).clone()))
<a href=#177 id=177 data-nosnippet>177</a>    }
<a href=#178 id=178 data-nosnippet>178</a>
<a href=#179 id=179 data-nosnippet>179</a>    <span class="kw">pub async fn </span>test_proxy(<span class="kw-2">&amp;</span><span class="self">self</span>, proxy_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;ProxyHealthCheck&gt; {
<a href=#180 id=180 data-nosnippet>180</a>        <span class="kw">let </span>proxy = {
<a href=#181 id=181 data-nosnippet>181</a>            <span class="kw">let </span>proxies = <span class="self">self</span>.proxies.read().<span class="kw">await</span>;
<a href=#182 id=182 data-nosnippet>182</a>            proxies.get(proxy_id).cloned()
<a href=#183 id=183 data-nosnippet>183</a>                .ok_or_else(|| AppError::Proxy(<span class="macro">format!</span>(<span class="string">"代理 {} 不存在"</span>, proxy_id)))<span class="question-mark">?
<a href=#184 id=184 data-nosnippet>184</a>        </span>};
<a href=#185 id=185 data-nosnippet>185</a>
<a href=#186 id=186 data-nosnippet>186</a>        <span class="prelude-val">Ok</span>(<span class="self">self</span>.test_proxy_health(<span class="kw-2">&amp;</span>proxy).<span class="kw">await</span>)
<a href=#187 id=187 data-nosnippet>187</a>    }
<a href=#188 id=188 data-nosnippet>188</a>
<a href=#189 id=189 data-nosnippet>189</a>    <span class="kw">async fn </span>test_proxy_health(<span class="kw-2">&amp;</span><span class="self">self</span>, proxy: <span class="kw-2">&amp;</span>Proxy) -&gt; ProxyHealthCheck {
<a href=#190 id=190 data-nosnippet>190</a>        <span class="kw">let </span>start_time = Instant::now();
<a href=#191 id=191 data-nosnippet>191</a>        
<a href=#192 id=192 data-nosnippet>192</a>        <span class="comment">// 构建代理URL
<a href=#193 id=193 data-nosnippet>193</a>        </span><span class="kw">let </span>proxy_url = <span class="kw">match </span>proxy.protocol {
<a href=#194 id=194 data-nosnippet>194</a>            ProxyProtocol::Http =&gt; <span class="macro">format!</span>(<span class="string">"http://{}:{}"</span>, proxy.host, proxy.port),
<a href=#195 id=195 data-nosnippet>195</a>            ProxyProtocol::Https =&gt; <span class="macro">format!</span>(<span class="string">"https://{}:{}"</span>, proxy.host, proxy.port),
<a href=#196 id=196 data-nosnippet>196</a>            ProxyProtocol::Socks5 =&gt; <span class="macro">format!</span>(<span class="string">"socks5://{}:{}"</span>, proxy.host, proxy.port),
<a href=#197 id=197 data-nosnippet>197</a>        };
<a href=#198 id=198 data-nosnippet>198</a>
<a href=#199 id=199 data-nosnippet>199</a>        <span class="comment">// 如果有用户名密码，添加认证信息
<a href=#200 id=200 data-nosnippet>200</a>        </span><span class="kw">let </span>proxy_url = <span class="kw">if let </span>(<span class="prelude-val">Some</span>(username), <span class="prelude-val">Some</span>(password)) = (<span class="kw-2">&amp;</span>proxy.username, <span class="kw-2">&amp;</span>proxy.password) {
<a href=#201 id=201 data-nosnippet>201</a>            proxy_url.replace(<span class="string">"://"</span>, <span class="kw-2">&amp;</span><span class="macro">format!</span>(<span class="string">"://{}:{}@"</span>, username, password))
<a href=#202 id=202 data-nosnippet>202</a>        } <span class="kw">else </span>{
<a href=#203 id=203 data-nosnippet>203</a>            proxy_url
<a href=#204 id=204 data-nosnippet>204</a>        };
<a href=#205 id=205 data-nosnippet>205</a>
<a href=#206 id=206 data-nosnippet>206</a>        <span class="comment">// 创建使用代理的客户端
<a href=#207 id=207 data-nosnippet>207</a>        </span><span class="kw">let </span>client_result = Client::builder()
<a href=#208 id=208 data-nosnippet>208</a>            .proxy(reqwest::Proxy::all(<span class="kw-2">&amp;</span>proxy_url).unwrap_or_else(|<span class="kw">_</span>| reqwest::Proxy::http(<span class="kw-2">&amp;</span>proxy_url).unwrap()))
<a href=#209 id=209 data-nosnippet>209</a>            .timeout(Duration::from_secs(<span class="number">10</span>))
<a href=#210 id=210 data-nosnippet>210</a>            .build();
<a href=#211 id=211 data-nosnippet>211</a>
<a href=#212 id=212 data-nosnippet>212</a>        <span class="kw">let </span>client = <span class="kw">match </span>client_result {
<a href=#213 id=213 data-nosnippet>213</a>            <span class="prelude-val">Ok</span>(client) =&gt; client,
<a href=#214 id=214 data-nosnippet>214</a>            <span class="prelude-val">Err</span>(e) =&gt; {
<a href=#215 id=215 data-nosnippet>215</a>                <span class="kw">return </span>ProxyHealthCheck {
<a href=#216 id=216 data-nosnippet>216</a>                    proxy_id: proxy.id.clone(),
<a href=#217 id=217 data-nosnippet>217</a>                    is_healthy: <span class="bool-val">false</span>,
<a href=#218 id=218 data-nosnippet>218</a>                    response_time: <span class="prelude-val">None</span>,
<a href=#219 id=219 data-nosnippet>219</a>                    error_message: <span class="prelude-val">Some</span>(<span class="macro">format!</span>(<span class="string">"创建代理客户端失败: {}"</span>, e)),
<a href=#220 id=220 data-nosnippet>220</a>                    checked_at: chrono::Utc::now(),
<a href=#221 id=221 data-nosnippet>221</a>                };
<a href=#222 id=222 data-nosnippet>222</a>            }
<a href=#223 id=223 data-nosnippet>223</a>        };
<a href=#224 id=224 data-nosnippet>224</a>
<a href=#225 id=225 data-nosnippet>225</a>        <span class="comment">// 测试代理连接
<a href=#226 id=226 data-nosnippet>226</a>        </span><span class="kw">match </span>client.get(<span class="kw-2">&amp;</span><span class="self">self</span>.test_url).send().<span class="kw">await </span>{
<a href=#227 id=227 data-nosnippet>227</a>            <span class="prelude-val">Ok</span>(response) =&gt; {
<a href=#228 id=228 data-nosnippet>228</a>                <span class="kw">let </span>response_time = start_time.elapsed().as_millis() <span class="kw">as </span>u64;
<a href=#229 id=229 data-nosnippet>229</a>                
<a href=#230 id=230 data-nosnippet>230</a>                <span class="kw">if </span>response.status().is_success() {
<a href=#231 id=231 data-nosnippet>231</a>                    ProxyHealthCheck {
<a href=#232 id=232 data-nosnippet>232</a>                        proxy_id: proxy.id.clone(),
<a href=#233 id=233 data-nosnippet>233</a>                        is_healthy: <span class="bool-val">true</span>,
<a href=#234 id=234 data-nosnippet>234</a>                        response_time: <span class="prelude-val">Some</span>(response_time),
<a href=#235 id=235 data-nosnippet>235</a>                        error_message: <span class="prelude-val">None</span>,
<a href=#236 id=236 data-nosnippet>236</a>                        checked_at: chrono::Utc::now(),
<a href=#237 id=237 data-nosnippet>237</a>                    }
<a href=#238 id=238 data-nosnippet>238</a>                } <span class="kw">else </span>{
<a href=#239 id=239 data-nosnippet>239</a>                    ProxyHealthCheck {
<a href=#240 id=240 data-nosnippet>240</a>                        proxy_id: proxy.id.clone(),
<a href=#241 id=241 data-nosnippet>241</a>                        is_healthy: <span class="bool-val">false</span>,
<a href=#242 id=242 data-nosnippet>242</a>                        response_time: <span class="prelude-val">Some</span>(response_time),
<a href=#243 id=243 data-nosnippet>243</a>                        error_message: <span class="prelude-val">Some</span>(<span class="macro">format!</span>(<span class="string">"HTTP状态码: {}"</span>, response.status())),
<a href=#244 id=244 data-nosnippet>244</a>                        checked_at: chrono::Utc::now(),
<a href=#245 id=245 data-nosnippet>245</a>                    }
<a href=#246 id=246 data-nosnippet>246</a>                }
<a href=#247 id=247 data-nosnippet>247</a>            }
<a href=#248 id=248 data-nosnippet>248</a>            <span class="prelude-val">Err</span>(e) =&gt; {
<a href=#249 id=249 data-nosnippet>249</a>                ProxyHealthCheck {
<a href=#250 id=250 data-nosnippet>250</a>                    proxy_id: proxy.id.clone(),
<a href=#251 id=251 data-nosnippet>251</a>                    is_healthy: <span class="bool-val">false</span>,
<a href=#252 id=252 data-nosnippet>252</a>                    response_time: <span class="prelude-val">None</span>,
<a href=#253 id=253 data-nosnippet>253</a>                    error_message: <span class="prelude-val">Some</span>(<span class="macro">format!</span>(<span class="string">"请求失败: {}"</span>, e)),
<a href=#254 id=254 data-nosnippet>254</a>                    checked_at: chrono::Utc::now(),
<a href=#255 id=255 data-nosnippet>255</a>                }
<a href=#256 id=256 data-nosnippet>256</a>            }
<a href=#257 id=257 data-nosnippet>257</a>        }
<a href=#258 id=258 data-nosnippet>258</a>    }
<a href=#259 id=259 data-nosnippet>259</a>
<a href=#260 id=260 data-nosnippet>260</a>    <span class="kw">pub async fn </span>start_health_check_loop(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#261 id=261 data-nosnippet>261</a>        <span class="kw">let </span>proxies = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.proxies);
<a href=#262 id=262 data-nosnippet>262</a>        <span class="kw">let </span>_health_check_client = <span class="self">self</span>.health_check_client.clone();
<a href=#263 id=263 data-nosnippet>263</a>        <span class="kw">let </span>_test_url = <span class="self">self</span>.test_url.clone();
<a href=#264 id=264 data-nosnippet>264</a>        <span class="kw">let </span>interval = <span class="self">self</span>.health_check_interval;
<a href=#265 id=265 data-nosnippet>265</a>
<a href=#266 id=266 data-nosnippet>266</a>        tokio::spawn(<span class="kw">async move </span>{
<a href=#267 id=267 data-nosnippet>267</a>            <span class="kw">let </span><span class="kw-2">mut </span>interval_timer = tokio::time::interval(interval);
<a href=#268 id=268 data-nosnippet>268</a>            
<a href=#269 id=269 data-nosnippet>269</a>            <span class="kw">loop </span>{
<a href=#270 id=270 data-nosnippet>270</a>                interval_timer.tick().<span class="kw">await</span>;
<a href=#271 id=271 data-nosnippet>271</a>                
<a href=#272 id=272 data-nosnippet>272</a>                <span class="kw">let </span>proxy_ids: Vec&lt;String&gt; = {
<a href=#273 id=273 data-nosnippet>273</a>                    <span class="kw">let </span>proxies_guard = proxies.read().<span class="kw">await</span>;
<a href=#274 id=274 data-nosnippet>274</a>                    proxies_guard.keys().cloned().collect()
<a href=#275 id=275 data-nosnippet>275</a>                };
<a href=#276 id=276 data-nosnippet>276</a>
<a href=#277 id=277 data-nosnippet>277</a>                <span class="kw">for </span>proxy_id <span class="kw">in </span>proxy_ids {
<a href=#278 id=278 data-nosnippet>278</a>                    <span class="kw">let </span>proxy = {
<a href=#279 id=279 data-nosnippet>279</a>                        <span class="kw">let </span>proxies_guard = proxies.read().<span class="kw">await</span>;
<a href=#280 id=280 data-nosnippet>280</a>                        proxies_guard.get(<span class="kw-2">&amp;</span>proxy_id).cloned()
<a href=#281 id=281 data-nosnippet>281</a>                    };
<a href=#282 id=282 data-nosnippet>282</a>
<a href=#283 id=283 data-nosnippet>283</a>                    <span class="kw">if let </span><span class="prelude-val">Some</span>(_proxy) = proxy {
<a href=#284 id=284 data-nosnippet>284</a>                        <span class="comment">// 这里应该调用健康检查逻辑
<a href=#285 id=285 data-nosnippet>285</a>                        // 目前先跳过实际检查
<a href=#286 id=286 data-nosnippet>286</a>                        </span><span class="macro">info!</span>(<span class="string">"对代理 {} 执行健康检查"</span>, proxy_id);
<a href=#287 id=287 data-nosnippet>287</a>                    }
<a href=#288 id=288 data-nosnippet>288</a>                }
<a href=#289 id=289 data-nosnippet>289</a>            }
<a href=#290 id=290 data-nosnippet>290</a>        });
<a href=#291 id=291 data-nosnippet>291</a>    }
<a href=#292 id=292 data-nosnippet>292</a>
<a href=#293 id=293 data-nosnippet>293</a>    <span class="kw">pub async fn </span>get_pool_status(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; (usize, usize, usize) {
<a href=#294 id=294 data-nosnippet>294</a>        <span class="kw">let </span>proxies = <span class="self">self</span>.proxies.read().<span class="kw">await</span>;
<a href=#295 id=295 data-nosnippet>295</a>        <span class="kw">let </span>total = proxies.len();
<a href=#296 id=296 data-nosnippet>296</a>        <span class="kw">let </span>available = proxies.values()
<a href=#297 id=297 data-nosnippet>297</a>            .filter(|p| <span class="macro">matches!</span>(p.status, ProxyStatus::Available))
<a href=#298 id=298 data-nosnippet>298</a>            .count();
<a href=#299 id=299 data-nosnippet>299</a>        <span class="kw">let </span>healthy = proxies.values()
<a href=#300 id=300 data-nosnippet>300</a>            .filter(|p| <span class="macro">matches!</span>(p.status, ProxyStatus::Available) &amp;&amp; p.response_time.is_some())
<a href=#301 id=301 data-nosnippet>301</a>            .count();
<a href=#302 id=302 data-nosnippet>302</a>
<a href=#303 id=303 data-nosnippet>303</a>        (total, available, healthy)
<a href=#304 id=304 data-nosnippet>304</a>    }
<a href=#305 id=305 data-nosnippet>305</a>
<a href=#306 id=306 data-nosnippet>306</a>    <span class="kw">pub async fn </span>get_all_proxies(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;Proxy&gt; {
<a href=#307 id=307 data-nosnippet>307</a>        <span class="kw">let </span>proxies = <span class="self">self</span>.proxies.read().<span class="kw">await</span>;
<a href=#308 id=308 data-nosnippet>308</a>        proxies.values().cloned().collect()
<a href=#309 id=309 data-nosnippet>309</a>    }
<a href=#310 id=310 data-nosnippet>310</a>}</code></pre></div></section></main></body></html>