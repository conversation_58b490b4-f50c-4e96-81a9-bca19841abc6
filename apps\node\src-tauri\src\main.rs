// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod commands;
mod crawler;
mod proxy;
mod account;
mod scheduler;
mod storage;
mod monitor;
mod error;
mod config;
mod repository;
mod service;
mod updater;
mod notification;
mod window_manager;
mod browser;

use commands::*;
use config::AppConfig;
use error::Result;
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{info, error};
use tracing_subscriber;
use tauri::{
    Manager,
    AppHandle
};

#[derive(Default)]
pub struct AppState {
    pub config: Arc<Mutex<AppConfig>>,
    pub crawler_engine: Arc<Mutex<Option<crawler::CrawlerEngine>>>,
    pub proxy_manager: Arc<Mutex<Option<proxy::ProxyManager>>>,
    pub account_manager: Arc<Mutex<Option<account::AccountManager>>>,
    pub task_scheduler: Arc<Mutex<Option<scheduler::TaskScheduler>>>,
    pub storage_manager: Arc<Mutex<Option<storage::StorageManager>>>,
    pub system_monitor: Arc<Mutex<Option<monitor::SystemMonitor>>>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env().add_directive("weibo_crawler_node=debug".parse().unwrap()))
        .init();

    info!("启动微博爬虫节点...");

    // 加载配置
    let config = AppConfig::load().await?;
    info!("配置加载完成: {}", config.node_name);

    // 初始化应用状态
    let app_state = AppState {
        config: Arc::new(Mutex::new(config)),
        ..Default::default()
    };

    // 启动Tauri应用
    tauri::Builder::default()
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![
            // 系统管理命令
            get_node_status,
            start_crawler,
            stop_crawler,
            restart_crawler,
            
            // 任务管理命令
            get_task_queue_status,
            start_task_processing,
            stop_task_processing,
            get_task_statistics,
            
            // 代理池管理命令
            get_proxy_pool_status,
            add_proxy,
            remove_proxy,
            test_proxy,
            
            // 账号管理命令
            get_account_pool_status,
            add_account,
            login_account,
            refresh_account_cookies,
            
            // 监控命令
            get_system_metrics,
            get_performance_stats,
            
            // 配置管理命令
            get_config,
            update_config,

            // 更新相关命令
            updater::check_for_updates,
            updater::download_update,
            updater::get_update_status,

            // 通知相关命令
            notification::get_notifications,
            notification::get_unread_notifications,
            notification::mark_notification_read,
            notification::remove_notification,
            notification::clear_all_notifications,
            notification::handle_notification_action,

            // 窗口管理命令
            window_manager::create_window,
            window_manager::close_window,
            window_manager::show_window,
            window_manager::hide_window,
            window_manager::get_window_info,
            window_manager::get_all_windows,

            // 浏览器相关命令
            test_browser,
            login_weibo,
            update_browser_config,
            get_browser_config,
            get_weibo_qr_code,
            check_qr_login_status,
            save_account_to_pool
        ])
        .setup(|app| {
            let app_handle = app.handle();
            
            // 异步初始化系统组件
            tauri::async_runtime::spawn(async move {
                if let Err(e) = initialize_system_components(&app_handle).await {
                    error!("系统组件初始化失败: {}", e);
                }
            });
            
            Ok(())
        })
        .setup(|app| {
            // 应用启动时的初始化
            if let Err(e) = setup_app_initialization(&app.handle()) {
                error!("应用初始化失败: {}", e);
            }
            Ok(())
        })
        .on_window_event(|event| {
            // 全局窗口事件处理
            if let tauri::WindowEvent::CloseRequested { api, .. } = event.event() {
                // 阻止默认关闭行为，隐藏到系统托盘
                api.prevent_close();
                if let Some(window) = event.window().get_window("main") {
                    let _ = window.hide();
                }
            }
        })
        .build(tauri::generate_context!())
        .expect("启动Tauri应用失败")
        .run(|app_handle, event| {
            match event {
                tauri::RunEvent::ExitRequested { api, .. } => {
                    // 应用退出时的清理
                    api.prevent_exit();
                    let app_handle_clone = app_handle.clone();
                    tauri::async_runtime::spawn(async move {
                        if let Err(e) = cleanup_on_exit(&app_handle_clone).await {
                            error!("应用清理失败: {}", e);
                        }
                        app_handle_clone.exit(0);
                    });
                }
                _ => {}
            }
        });

    Ok(())
}

async fn initialize_system_components(app_handle: &tauri::AppHandle) -> Result<()> {
    use tauri::Manager;
    let state = app_handle.state::<AppState>();
    let config = state.config.lock().await.clone();

    info!("初始化系统组件...");

    // 初始化存储管理器
    info!("正在初始化存储管理器，数据库URL: {}", config.database_url);
    let storage_manager = storage::StorageManager::new(&config.database_url).await
        .map_err(|e| {
            error!("存储管理器初始化失败: {}", e);
            e
        })?;
    *state.storage_manager.lock().await = Some(storage_manager);
    info!("存储管理器初始化完成");

    // 初始化代理管理器
    let proxy_manager = proxy::ProxyManager::new(config.proxy_pool_size).await?;
    *state.proxy_manager.lock().await = Some(proxy_manager);
    info!("代理管理器初始化完成");

    // 初始化账号管理器
    let account_manager = account::AccountManager::new(config.account_pool_size).await?;
    *state.account_manager.lock().await = Some(account_manager);
    info!("账号管理器初始化完成");

    // 初始化任务调度器
    let task_scheduler = scheduler::TaskScheduler::new(config.max_concurrent_tasks).await?;
    *state.task_scheduler.lock().await = Some(task_scheduler);
    info!("任务调度器初始化完成");

    // 初始化爬虫引擎
    let crawler_engine = crawler::CrawlerEngine::new().await?;
    *state.crawler_engine.lock().await = Some(crawler_engine);
    info!("爬虫引擎初始化完成");

    // 初始化系统监控
    let system_monitor = monitor::SystemMonitor::new().await?;
    *state.system_monitor.lock().await = Some(system_monitor);
    info!("系统监控初始化完成");

    // 初始化浏览器管理器
    if let Err(e) = browser::initialize_browser().await {
        error!("浏览器管理器初始化失败: {}", e);
        // 浏览器初始化失败不应该阻止应用启动
    } else {
        info!("浏览器管理器初始化完成");
    }

    info!("所有系统组件初始化完成");
    Ok(())
}

/*
// 系统托盘功能暂时禁用，等待后续完善
// 创建系统托盘
fn create_system_tray() -> SystemTray {
    // ... 系统托盘代码
}

// 处理系统托盘事件
fn handle_system_tray_event(app: &AppHandle, event: SystemTrayEvent) {
    // ... 托盘事件处理代码
}

// 处理托盘菜单点击
fn handle_tray_menu_click(app: &AppHandle, menu_id: &str) {
    // ... 菜单点击处理代码
}
*/

// 显示关于对话框
fn show_about_dialog(app: &AppHandle) {
    let version = app.package_info().version.to_string();
    let message = format!(
        "微博爬虫节点 v{}\n\n一个基于 Rust + React 的高性能分布式微博舆情分析系统\n\n© 2024 微博舆情分析团队",
        version
    );

    // 使用JavaScript显示对话框
    if let Some(window) = app.get_window("main") {
        let script = format!(
            r#"alert("{}");"#,
            message.replace('\n', "\\n").replace('"', "\\\"")
        );
        let _ = window.eval(&script);
    }
}

// 设置全局快捷键 (已禁用)
/*
fn setup_global_shortcuts(app: &AppHandle) -> Result<()> {
    let mut shortcut_manager = app.global_shortcut_manager();

    // 尝试注册快捷键，如果失败则尝试其他组合
    let shortcuts_to_try = vec![
        ("CmdOrCtrl+Alt+W", "显示/隐藏主窗口"),
        ("CmdOrCtrl+Alt+Shift+W", "显示/隐藏主窗口"),
        ("F12", "显示/隐藏主窗口"),
    ];

    let mut registered_toggle = false;
    for (shortcut, description) in shortcuts_to_try {
        let app_handle = app.clone();
        match shortcut_manager.register(shortcut, move || {
            if let Some(window) = app_handle.get_window("main") {
                if window.is_visible().unwrap_or(false) {
                    let _ = window.hide();
                } else {
                    let _ = window.show();
                    let _ = window.set_focus();
                }
            }
        }) {
            Ok(_) => {
                info!("成功注册全局快捷键: {} - {}", shortcut, description);
                registered_toggle = true;
                break;
            }
            Err(e) => {
                warn!("注册快捷键 {} 失败: {}", shortcut, e);
            }
        }
    }

    if !registered_toggle {
        warn!("无法注册显示/隐藏窗口的全局快捷键，将跳过此功能");
    }

    // 尝试注册退出快捷键
    let exit_shortcuts = vec![
        ("CmdOrCtrl+Alt+Q", "快速退出"),
        ("CmdOrCtrl+Alt+Shift+Q", "快速退出"),
    ];

    let mut registered_exit = false;
    for (shortcut, description) in exit_shortcuts {
        let app_handle = app.clone();
        match shortcut_manager.register(shortcut, move || {
            app_handle.exit(0);
        }) {
            Ok(_) => {
                info!("成功注册全局快捷键: {} - {}", shortcut, description);
                registered_exit = true;
                break;
            }
            Err(e) => {
                warn!("注册快捷键 {} 失败: {}", shortcut, e);
            }
        }
    }

    if !registered_exit {
        warn!("无法注册退出的全局快捷键，将跳过此功能");
    }

    if registered_toggle || registered_exit {
        info!("全局快捷键设置完成");
    } else {
        warn!("所有全局快捷键注册失败，可能是权限问题或快捷键冲突");
    }

    Ok(())
}
*/

// 窗口事件处理
fn handle_window_event(event: &tauri::WindowEvent) {
    match event {
        tauri::WindowEvent::CloseRequested { api, .. } => {
            // 阻止窗口关闭，改为隐藏到系统托盘
            api.prevent_close();
            // 注意：这里无法直接从事件获取窗口引用，需要其他方式处理
        }
        tauri::WindowEvent::Focused(focused) => {
            if *focused {
                info!("窗口获得焦点");
            } else {
                info!("窗口失去焦点");
            }
        }
        _ => {}
    }
}

// 应用启动时的初始化
fn setup_app_initialization(app: &AppHandle) -> Result<()> {
    // 移除全局快捷键功能
    // if let Err(e) = setup_global_shortcuts(app) {
    //     warn!("设置全局快捷键失败: {}", e);
    // }

    // 设置窗口事件监听
    if let Some(window) = app.get_window("main") {
        window.on_window_event(move |event| {
            handle_window_event(&event);
        });

        // 设置窗口初始状态
        let _ = window.set_title("Weibo Crawler Node - Distributed Data Collection System");

        // 如果是首次启动，显示欢迎信息
        let _ = window.eval(r#"
            console.log('Weibo Crawler Node started');
            if (window.location.hash === '') {
                window.location.hash = '#/';
            }
        "#);
    }

    info!("应用初始化完成");
    Ok(())
}

// 应用退出时的清理
async fn cleanup_on_exit(app_handle: &AppHandle) -> Result<()> {
    info!("开始应用清理...");

    // 移除全局快捷键清理
    // let mut shortcut_manager = app_handle.global_shortcut_manager();
    // shortcut_manager.unregister_all().map_err(|e| {
    //     error::AppError::Other(format!("清理全局快捷键失败: {}", e))
    // })?;

    // 停止所有服务
    let _state = app_handle.state::<AppState>();

    // 清理浏览器资源
    if let Err(e) = browser::shutdown_browser().await {
        error!("浏览器清理失败: {}", e);
    } else {
        info!("浏览器资源清理完成");
    }

    // 这里可以添加更多清理逻辑
    // 比如保存当前状态、关闭数据库连接等

    info!("应用清理完成");
    Ok(())
}
