use crate::error::{Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>};
use tokio::sync::RwLock;
use tracing::{info};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum NotificationType {
    Info,
    Success,
    Warning,
    Error,
    System,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Notification {
    pub id: String,
    pub title: String,
    pub message: String,
    pub notification_type: NotificationType,
    pub timestamp: DateTime<Utc>,
    pub read: bool,
    pub persistent: bool,
    pub actions: Vec<NotificationAction>,
    pub metadata: HashMap<String, String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct NotificationAction {
    pub id: String,
    pub label: String,
    pub action_type: ActionType,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum ActionType {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>,
}

impl Notification {
    pub fn new(title: &str, message: &str, notification_type: NotificationType) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            title: title.to_string(),
            message: message.to_string(),
            notification_type,
            timestamp: Utc::now(),
            read: false,
            persistent: false,
            actions: Vec::new(),
            metadata: HashMap::new(),
        }
    }

    pub fn with_action(mut self, action: NotificationAction) -> Self {
        self.actions.push(action);
        self
    }

    pub fn with_metadata(mut self, key: &str, value: &str) -> Self {
        self.metadata.insert(key.to_string(), value.to_string());
        self
    }

    pub fn persistent(mut self) -> Self {
        self.persistent = true;
        self
    }
}

pub struct NotificationManager {
    app_handle: AppHandle,
    notifications: Arc<RwLock<Vec<Notification>>>,
    max_notifications: usize,
}

impl NotificationManager {
    pub fn new(app_handle: AppHandle) -> Self {
        Self {
            app_handle,
            notifications: Arc::new(RwLock::new(Vec::new())),
            max_notifications: 100,
        }
    }

    /// 发送通知
    pub async fn send(&self, notification: Notification) -> Result<()> {
        let mut notifications = self.notifications.write().await;
        
        // 如果超过最大数量，移除最旧的非持久化通知
        if notifications.len() >= self.max_notifications {
            notifications.retain(|n| n.persistent);
            if notifications.len() >= self.max_notifications {
                notifications.remove(0);
            }
        }
        
        notifications.push(notification.clone());
        drop(notifications);

        // 发送到前端
        self.emit_notification(&notification).await?;
        
        // 显示系统通知
        self.show_system_notification(&notification).await?;
        
        info!("发送通知: {}", notification.title);
        Ok(())
    }

    /// 发送简单通知
    pub async fn send_simple(&self, title: &str, message: &str, notification_type: NotificationType) -> Result<()> {
        let notification = Notification::new(title, message, notification_type);
        self.send(notification).await
    }

    /// 发送成功通知
    pub async fn success(&self, title: &str, message: &str) -> Result<()> {
        self.send_simple(title, message, NotificationType::Success).await
    }

    /// 发送错误通知
    pub async fn error(&self, title: &str, message: &str) -> Result<()> {
        self.send_simple(title, message, NotificationType::Error).await
    }

    /// 发送警告通知
    pub async fn warning(&self, title: &str, message: &str) -> Result<()> {
        self.send_simple(title, message, NotificationType::Warning).await
    }

    /// 发送信息通知
    pub async fn info(&self, title: &str, message: &str) -> Result<()> {
        self.send_simple(title, message, NotificationType::Info).await
    }

    /// 获取所有通知
    pub async fn get_all(&self) -> Vec<Notification> {
        self.notifications.read().await.clone()
    }

    /// 获取未读通知
    pub async fn get_unread(&self) -> Vec<Notification> {
        self.notifications.read().await
            .iter()
            .filter(|n| !n.read)
            .cloned()
            .collect()
    }

    /// 标记通知为已读
    pub async fn mark_as_read(&self, notification_id: &str) -> Result<()> {
        let mut notifications = self.notifications.write().await;
        if let Some(notification) = notifications.iter_mut().find(|n| n.id == notification_id) {
            notification.read = true;
            
            // 发送更新事件
            let _ = self.app_handle.emit_all("notification-updated", notification.clone());
        }
        Ok(())
    }

    /// 删除通知
    pub async fn remove(&self, notification_id: &str) -> Result<()> {
        let mut notifications = self.notifications.write().await;
        notifications.retain(|n| n.id != notification_id);
        
        // 发送删除事件
        let _ = self.app_handle.emit_all("notification-removed", notification_id);
        Ok(())
    }

    /// 清除所有通知
    pub async fn clear_all(&self) -> Result<()> {
        let mut notifications = self.notifications.write().await;
        notifications.retain(|n| n.persistent);
        
        // 发送清除事件
        let _ = self.app_handle.emit_all("notifications-cleared", ());
        Ok(())
    }

    /// 处理通知操作
    pub async fn handle_action(&self, notification_id: &str, action_id: &str) -> Result<()> {
        let notifications = self.notifications.read().await;
        if let Some(notification) = notifications.iter().find(|n| n.id == notification_id) {
            if let Some(action) = notification.actions.iter().find(|a| a.id == action_id) {
                match action.action_type {
                    ActionType::Button => {
                        // 处理按钮点击
                        info!("处理通知按钮点击: {} -> {}", notification.title, action.label);
                    }
                    ActionType::Link => {
                        // 打开链接
                        if let Some(url) = notification.metadata.get("url") {
                            let _ = open::that(url);
                        }
                    }
                    ActionType::Command => {
                        // 执行命令
                        if let Some(command) = notification.metadata.get("command") {
                            let _ = self.app_handle.emit_all("execute-command", command);
                        }
                    }
                }
            }
        }
        Ok(())
    }

    /// 发送通知事件到前端
    async fn emit_notification(&self, notification: &Notification) -> Result<()> {
        let _ = self.app_handle.emit_all("new-notification", notification);
        Ok(())
    }

    /// 显示系统通知
    async fn show_system_notification(&self, notification: &Notification) -> Result<()> {
        if let Some(window) = self.app_handle.get_window("main") {
            let icon = match notification.notification_type {
                NotificationType::Success => "✅",
                NotificationType::Error => "❌",
                NotificationType::Warning => "⚠️",
                NotificationType::Info => "ℹ️",
                NotificationType::System => "🔧",
            };

            let script = format!(
                r#"
                if ('Notification' in window) {{
                    if (Notification.permission === 'granted') {{
                        new Notification('{} {}', {{
                            body: '{}',
                            icon: '/icon.png',
                            tag: '{}'
                        }});
                    }} else if (Notification.permission !== 'denied') {{
                        Notification.requestPermission().then(function (permission) {{
                            if (permission === 'granted') {{
                                new Notification('{} {}', {{
                                    body: '{}',
                                    icon: '/icon.png',
                                    tag: '{}'
                                }});
                            }}
                        }});
                    }}
                }}
                "#,
                icon,
                notification.title.replace('\'', "\\'"),
                notification.message.replace('\'', "\\'"),
                notification.id,
                icon,
                notification.title.replace('\'', "\\'"),
                notification.message.replace('\'', "\\'"),
                notification.id
            );
            
            let _ = window.eval(&script);
        }
        Ok(())
    }

    /// 设置最大通知数量
    pub fn set_max_notifications(&mut self, max: usize) {
        self.max_notifications = max;
    }
}

// Tauri命令
#[tauri::command]
pub async fn get_notifications(
    app_handle: AppHandle,
) -> std::result::Result<Vec<Notification>, String> {
    let manager = NotificationManager::new(app_handle);
    Ok(manager.get_all().await)
}

#[tauri::command]
pub async fn get_unread_notifications(
    app_handle: AppHandle,
) -> std::result::Result<Vec<Notification>, String> {
    let manager = NotificationManager::new(app_handle);
    Ok(manager.get_unread().await)
}

#[tauri::command]
pub async fn mark_notification_read(
    app_handle: AppHandle,
    notification_id: String,
) -> std::result::Result<(), String> {
    let manager = NotificationManager::new(app_handle);
    manager.mark_as_read(&notification_id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn remove_notification(
    app_handle: AppHandle,
    notification_id: String,
) -> std::result::Result<(), String> {
    let manager = NotificationManager::new(app_handle);
    manager.remove(&notification_id).await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn clear_all_notifications(
    app_handle: AppHandle,
) -> std::result::Result<(), String> {
    let manager = NotificationManager::new(app_handle);
    manager.clear_all().await.map_err(|e| e.to_string())
}

#[tauri::command]
pub async fn handle_notification_action(
    app_handle: AppHandle,
    notification_id: String,
    action_id: String,
) -> std::result::Result<(), String> {
    let manager = NotificationManager::new(app_handle);
    manager.handle_action(&notification_id, &action_id).await.map_err(|e| e.to_string())
}
