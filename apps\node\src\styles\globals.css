@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 现代化色彩系统 - 浅色主题 */
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;

  /* 主色调 - 现代蓝色系 */
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --primary-hover: 221.2 83.2% 48%;
  --primary-light: 221.2 83.2% 95%;

  /* 次要色彩 */
  --secondary: 210 40% 98%;
  --secondary-foreground: 222.2 47.4% 11.2%;
  --secondary-hover: 210 40% 96%;

  /* 中性色彩 */
  --muted: 210 40% 98%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 47.4% 11.2%;

  /* 状态色彩 */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --success: 142.1 76.2% 36.3%;
  --success-foreground: 355.7 100% 97.3%;
  --warning: 32.5 94.6% 43.7%;
  --warning-foreground: 210 40% 98%;
  --info: 199.89 89.47% 49.41%;
  --info-foreground: 210 40% 98%;

  /* 边框和输入 */
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 圆角系统 */
  --radius-sm: 0.25rem;
  --radius: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;

  /* 间距系统 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
}

.dark {
  /* 现代化色彩系统 - 暗色主题 */
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;

  /* 主色调 - 暗色模式优化 */
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --primary-hover: 217.2 91.2% 65%;
  --primary-light: 217.2 91.2% 15%;

  /* 次要色彩 */
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --secondary-hover: 240 5.9% 10%;

  /* 中性色彩 */
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;

  /* 状态色彩 - 暗色优化 */
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 85.7% 97.3%;
  --success: 142.1 70.6% 45.3%;
  --success-foreground: 144.9 80.4% 10%;
  --warning: 32.5 94.6% 43.7%;
  --warning-foreground: 20.5 90.2% 4.3%;
  --info: 199.89 89.47% 49.41%;
  --info-foreground: 198.4 88.1% 4.3%;

  /* 边框和输入 */
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 217.2 91.2% 59.8%;

  /* 阴影系统 - 暗色优化 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4), 0 8px 10px -6px rgb(0 0 0 / 0.4);
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* 现代化动画系统 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 0%,
    hsl(var(--accent)) 50%,
    hsl(var(--muted)) 100%
  );
  background-size: 200px 100%;
}

/* 过渡效果 */
.transition-all {
  transition: all 0.2s ease-in-out;
}

.transition-colors {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.transition-transform {
  transition: transform 0.2s ease-in-out;
}

.transition-opacity {
  transition: opacity 0.2s ease-in-out;
}

/* 状态指示器 */
.status-online {
  @apply bg-green-500;
}

.status-offline {
  @apply bg-red-500;
}

.status-warning {
  @apply bg-yellow-500;
}

.status-maintenance {
  @apply bg-blue-500;
}

/* 现代化卡片样式系统 */
.card {
  @apply bg-card text-card-foreground rounded-xl border shadow-sm transition-all duration-200;
  box-shadow: var(--shadow);
}

.card:hover {
  @apply shadow-md;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.card-elevated {
  @apply shadow-lg;
  box-shadow: var(--shadow-lg);
}

.card-interactive {
  @apply cursor-pointer hover:shadow-lg;
  transition: all 0.2s ease-in-out;
}

.card-interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  @apply flex flex-col space-y-1.5 p-6 pb-4;
}

.card-title {
  @apply text-xl font-semibold leading-none tracking-tight;
  color: hsl(var(--foreground));
}

.card-description {
  @apply text-sm text-muted-foreground leading-relaxed;
}

.card-content {
  @apply p-6 pt-0;
}

.card-footer {
  @apply flex items-center p-6 pt-0;
}

/* 卡片变体 */
.card-compact {
  @apply p-4;
}

.card-compact .card-header {
  @apply p-4 pb-2;
}

.card-compact .card-content {
  @apply p-4 pt-0;
}

.card-compact .card-footer {
  @apply p-4 pt-0;
}

/* 现代化按钮样式系统 */
.btn {
  @apply inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  @apply bg-primary text-primary-foreground h-10 py-2 px-4;
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-hover)));
}

.btn-primary:hover {
  background: linear-gradient(135deg, hsl(var(--primary-hover)), hsl(var(--primary)));
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  @apply bg-secondary text-secondary-foreground h-10 py-2 px-4;
}

.btn-secondary:hover {
  @apply bg-secondary-hover;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-destructive {
  @apply bg-destructive text-destructive-foreground h-10 py-2 px-4;
}

.btn-destructive:hover {
  @apply bg-destructive/90;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  @apply border border-input hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4;
  background: transparent;
}

.btn-outline:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-ghost {
  @apply hover:bg-accent hover:text-accent-foreground h-10 py-2 px-4;
  background: transparent;
}

.btn-ghost:hover {
  transform: translateY(-1px);
}

/* 按钮尺寸 */
.btn-sm {
  @apply h-9 px-3 rounded-md text-xs;
}

.btn-lg {
  @apply h-12 px-8 rounded-lg text-base;
}

.btn-xl {
  @apply h-14 px-10 rounded-xl text-lg;
}

/* 按钮状态 */
.btn-loading {
  @apply opacity-70 cursor-not-allowed;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 实用样式类 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-bg {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-hover)));
}

.text-gradient {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-hover)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 状态指示器增强 */
.status-indicator {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-online {
  @apply bg-green-100 text-green-800;
  color: hsl(var(--success-foreground));
  background-color: hsl(var(--success));
}

.status-offline {
  @apply bg-red-100 text-red-800;
  color: hsl(var(--destructive-foreground));
  background-color: hsl(var(--destructive));
}

.status-warning {
  @apply bg-yellow-100 text-yellow-800;
  color: hsl(var(--warning-foreground));
  background-color: hsl(var(--warning));
}

.status-info {
  @apply bg-blue-100 text-blue-800;
  color: hsl(var(--info-foreground));
  background-color: hsl(var(--info));
}

/* 响应式工具类 */
.container-responsive {
  @apply w-full mx-auto px-4 sm:px-6 lg:px-8;
  max-width: 1400px;
}

.grid-responsive {
  @apply grid gap-4 sm:gap-6;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-responsive-sm {
  @apply grid gap-4 sm:gap-6;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.grid-responsive-lg {
  @apply grid gap-4 sm:gap-6;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* 文本样式增强 */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* 滚动条美化 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground)) hsl(var(--muted));
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* 输入框样式 */
.input {
  @apply flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
}

/* 表格样式 */
.table {
  @apply w-full caption-bottom text-sm;
}

.table-header {
  @apply border-b;
}

.table-body {
  @apply [&_tr:last-child]:border-0;
}

.table-row {
  @apply border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted;
}

.table-head {
  @apply h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0;
}

.table-cell {
  @apply p-4 align-middle [&:has([role=checkbox])]:pr-0;
}

/* 徽章样式 */
.badge {
  @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
}

.badge-default {
  @apply border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
}

.badge-secondary {
  @apply border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
}

.badge-destructive {
  @apply border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80;
}

.badge-outline {
  @apply text-foreground;
}
