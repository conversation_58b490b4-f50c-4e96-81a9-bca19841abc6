{"rustc": 1842507548689473721, "features": "[\"global-shortcut\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 5144538945173598434, "path": 18318639559245105225, "deps": [[3150220818285335163, "url", false, 1746054398145793718], [4381063397040571828, "webview2_com", false, 2334546348545801766], [4405182208873388884, "http", false, 5891631273443115995], [7653476968652377684, "windows", false, 4435937259384173537], [8008191657135824715, "thiserror", false, 5374037758448816721], [8292277814562636972, "tauri_utils", false, 11396416046195520354], [8319709847752024821, "uuid", false, 9442600809944700742], [8569119365930580996, "serde_json", false, 3777298738341353814], [8866577183823226611, "http_range", false, 10715649658474383766], [9689903380558560274, "serde", false, 4389908846715814937], [11693073011723388840, "raw_window_handle", false, 2576869162297233201], [13208667028893622512, "rand", false, 5662173108048055673], [14162324460024849578, "build_script_build", false, 5409264713050439994]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-3938ba7e962308a5\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}