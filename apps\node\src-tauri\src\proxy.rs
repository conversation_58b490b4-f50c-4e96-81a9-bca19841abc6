use crate::error::{AppError, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{info, warn};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Proxy {
    pub id: String,
    pub host: String,
    pub port: u16,
    pub protocol: ProxyProtocol,
    pub username: Option<String>,
    pub password: Option<String>,
    pub country: Option<String>,
    pub region: Option<String>,
    pub provider: Option<String>,
    pub status: ProxyStatus,
    pub success_count: u64,
    pub failure_count: u64,
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
    pub last_checked: Option<chrono::DateTime<chrono::Utc>>,
    pub response_time: Option<u64>, // 毫秒
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum ProxyProtocol {
    Http,
    Https,
    Socks5,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProxyStatus {
    Available,
    Unavailable,
    Maintenance,
    Testing,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProxyHealthCheck {
    pub proxy_id: String,
    pub is_healthy: bool,
    pub response_time: Option<u64>,
    pub error_message: Option<String>,
    pub checked_at: chrono::DateTime<chrono::Utc>,
}

pub struct ProxyManager {
    proxies: Arc<RwLock<HashMap<String, Proxy>>>,
    health_check_client: Client,
    max_pool_size: usize,
    health_check_interval: Duration,
    test_url: String,
}

impl ProxyManager {
    pub async fn new(max_pool_size: usize) -> Result<Self> {
        let health_check_client = Client::builder()
            .timeout(Duration::from_secs(10))
            .build()
            .map_err(|e| AppError::Proxy(format!("创建健康检查客户端失败: {}", e)))?;

        Ok(Self {
            proxies: Arc::new(RwLock::new(HashMap::new())),
            health_check_client,
            max_pool_size,
            health_check_interval: Duration::from_secs(300), // 5分钟
            test_url: "http://httpbin.org/ip".to_string(),
        })
    }

    pub async fn add_proxy(&self, proxy_config: crate::commands::ProxyConfig) -> Result<String> {
        let proxy_id = Uuid::new_v4().to_string();
        
        let protocol = match proxy_config.protocol.as_str() {
            "http" => ProxyProtocol::Http,
            "https" => ProxyProtocol::Https,
            "socks5" => ProxyProtocol::Socks5,
            _ => return Err(AppError::Proxy("不支持的代理协议".to_string())),
        };

        let proxy = Proxy {
            id: proxy_id.clone(),
            host: proxy_config.host,
            port: proxy_config.port,
            protocol,
            username: proxy_config.username,
            password: proxy_config.password,
            country: proxy_config.country,
            region: None,
            provider: proxy_config.provider,
            status: ProxyStatus::Available,
            success_count: 0,
            failure_count: 0,
            last_used: None,
            last_checked: None,
            response_time: None,
            created_at: chrono::Utc::now(),
        };

        // 检查代理池大小
        {
            let proxies = self.proxies.read().await;
            if proxies.len() >= self.max_pool_size {
                return Err(AppError::Proxy("代理池已满".to_string()));
            }
        }

        // 测试代理可用性
        let health_check = self.test_proxy_health(&proxy).await;
        
        let mut proxies = self.proxies.write().await;
        let mut proxy = proxy;
        
        if health_check.is_healthy {
            proxy.status = ProxyStatus::Available;
            proxy.response_time = health_check.response_time;
            info!("代理 {} 添加成功并通过健康检查", proxy_id);
        } else {
            proxy.status = ProxyStatus::Unavailable;
            warn!("代理 {} 添加成功但健康检查失败: {:?}", proxy_id, health_check.error_message);
        }
        
        proxy.last_checked = Some(health_check.checked_at);
        proxies.insert(proxy_id.clone(), proxy);

        Ok(proxy_id)
    }

    pub async fn remove_proxy(&self, proxy_id: &str) -> Result<()> {
        let mut proxies = self.proxies.write().await;
        
        if proxies.remove(proxy_id).is_some() {
            info!("代理 {} 移除成功", proxy_id);
            Ok(())
        } else {
            Err(AppError::Proxy(format!("代理 {} 不存在", proxy_id)))
        }
    }

    pub async fn get_proxy(&self, proxy_id: &str) -> Result<Option<Proxy>> {
        let proxies = self.proxies.read().await;
        Ok(proxies.get(proxy_id).cloned())
    }

    pub async fn get_available_proxy(&self) -> Result<Option<Proxy>> {
        let proxies = self.proxies.read().await;
        
        // 找到可用的代理，优先选择响应时间短的
        let mut available_proxies: Vec<&Proxy> = proxies
            .values()
            .filter(|p| matches!(p.status, ProxyStatus::Available))
            .collect();

        if available_proxies.is_empty() {
            return Ok(None);
        }

        // 按响应时间排序
        available_proxies.sort_by(|a, b| {
            match (a.response_time, b.response_time) {
                (Some(a_time), Some(b_time)) => a_time.cmp(&b_time),
                (Some(_), None) => std::cmp::Ordering::Less,
                (None, Some(_)) => std::cmp::Ordering::Greater,
                (None, None) => std::cmp::Ordering::Equal,
            }
        });

        Ok(available_proxies.first().map(|p| (*p).clone()))
    }

    pub async fn test_proxy(&self, proxy_id: &str) -> Result<ProxyHealthCheck> {
        let proxy = {
            let proxies = self.proxies.read().await;
            proxies.get(proxy_id).cloned()
                .ok_or_else(|| AppError::Proxy(format!("代理 {} 不存在", proxy_id)))?
        };

        Ok(self.test_proxy_health(&proxy).await)
    }

    async fn test_proxy_health(&self, proxy: &Proxy) -> ProxyHealthCheck {
        let start_time = Instant::now();
        
        // 构建代理URL
        let proxy_url = match proxy.protocol {
            ProxyProtocol::Http => format!("http://{}:{}", proxy.host, proxy.port),
            ProxyProtocol::Https => format!("https://{}:{}", proxy.host, proxy.port),
            ProxyProtocol::Socks5 => format!("socks5://{}:{}", proxy.host, proxy.port),
        };

        // 如果有用户名密码，添加认证信息
        let proxy_url = if let (Some(username), Some(password)) = (&proxy.username, &proxy.password) {
            proxy_url.replace("://", &format!("://{}:{}@", username, password))
        } else {
            proxy_url
        };

        // 创建使用代理的客户端
        let client_result = Client::builder()
            .proxy(reqwest::Proxy::all(&proxy_url).unwrap_or_else(|_| reqwest::Proxy::http(&proxy_url).unwrap()))
            .timeout(Duration::from_secs(10))
            .build();

        let client = match client_result {
            Ok(client) => client,
            Err(e) => {
                return ProxyHealthCheck {
                    proxy_id: proxy.id.clone(),
                    is_healthy: false,
                    response_time: None,
                    error_message: Some(format!("创建代理客户端失败: {}", e)),
                    checked_at: chrono::Utc::now(),
                };
            }
        };

        // 测试代理连接
        match client.get(&self.test_url).send().await {
            Ok(response) => {
                let response_time = start_time.elapsed().as_millis() as u64;
                
                if response.status().is_success() {
                    ProxyHealthCheck {
                        proxy_id: proxy.id.clone(),
                        is_healthy: true,
                        response_time: Some(response_time),
                        error_message: None,
                        checked_at: chrono::Utc::now(),
                    }
                } else {
                    ProxyHealthCheck {
                        proxy_id: proxy.id.clone(),
                        is_healthy: false,
                        response_time: Some(response_time),
                        error_message: Some(format!("HTTP状态码: {}", response.status())),
                        checked_at: chrono::Utc::now(),
                    }
                }
            }
            Err(e) => {
                ProxyHealthCheck {
                    proxy_id: proxy.id.clone(),
                    is_healthy: false,
                    response_time: None,
                    error_message: Some(format!("请求失败: {}", e)),
                    checked_at: chrono::Utc::now(),
                }
            }
        }
    }

    pub async fn start_health_check_loop(&self) {
        let proxies = Arc::clone(&self.proxies);
        let _health_check_client = self.health_check_client.clone();
        let _test_url = self.test_url.clone();
        let interval = self.health_check_interval;

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            
            loop {
                interval_timer.tick().await;
                
                let proxy_ids: Vec<String> = {
                    let proxies_guard = proxies.read().await;
                    proxies_guard.keys().cloned().collect()
                };

                for proxy_id in proxy_ids {
                    let proxy = {
                        let proxies_guard = proxies.read().await;
                        proxies_guard.get(&proxy_id).cloned()
                    };

                    if let Some(_proxy) = proxy {
                        // 这里应该调用健康检查逻辑
                        // 目前先跳过实际检查
                        info!("对代理 {} 执行健康检查", proxy_id);
                    }
                }
            }
        });
    }

    pub async fn get_pool_status(&self) -> (usize, usize, usize) {
        let proxies = self.proxies.read().await;
        let total = proxies.len();
        let available = proxies.values()
            .filter(|p| matches!(p.status, ProxyStatus::Available))
            .count();
        let healthy = proxies.values()
            .filter(|p| matches!(p.status, ProxyStatus::Available) && p.response_time.is_some())
            .count();

        (total, available, healthy)
    }

    pub async fn get_all_proxies(&self) -> Vec<Proxy> {
        let proxies = self.proxies.read().await;
        proxies.values().cloned().collect()
    }
}
