(function() {
    var implementors = Object.fromEntries([["weibo_crawler_node",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>&gt; for <a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html\" title=\"struct alloc::string::String\">String</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"struct\" href=\"https://doc.rust-lang.org/1.88.0/std/io/error/struct.Error.html\" title=\"struct std::io::error::Error\">Error</a>&gt; for <a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"struct\" href=\"https://docs.rs/anyhow/1.0.98/anyhow/struct.Error.html\" title=\"struct anyhow::Error\">Error</a>&gt; for <a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;<a class=\"struct\" href=\"https://docs.rs/serde_json/1.0.141/serde_json/error/struct.Error.html\" title=\"struct serde_json::error::Error\">Error</a>&gt; for <a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;Error&gt; for <a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;Error&gt; for <a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html\" title=\"trait core::convert::From\">From</a>&lt;RedisError&gt; for <a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[2639]}