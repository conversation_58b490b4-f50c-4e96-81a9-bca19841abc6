{"rustc": 1842507548689473721, "features": "[\"rustls-connector\", \"rustls-native-certs\"]", "declared_features": "[\"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-connector\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\"]", "target": 11924301528678068555, "profile": 15657897354478470176, "path": 3463737712322726721, "deps": [[8606274917505247608, "tracing", false, 17646992783032211729], [11096876330329401515, "amq_protocol_uri", false, 2937034675637483620], [17059544261156971941, "tcp_stream", false, 8363704550271422409]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\amq-protocol-tcp-4e277d0229c4d12a\\dep-lib-amq_protocol_tcp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}