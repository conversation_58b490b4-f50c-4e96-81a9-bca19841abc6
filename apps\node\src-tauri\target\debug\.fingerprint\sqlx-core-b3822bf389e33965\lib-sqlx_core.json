{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 2225463790103693989, "path": 1859583268020246255, "deps": [[5103565458935487, "futures_io", false, 7436328532129351512], [40386456601120721, "percent_encoding", false, 12247433751253000581], [530211389790465181, "hex", false, 13185128541112989339], [788558663644978524, "crossbeam_queue", false, 7296623841652223786], [966925859616469517, "ahash", false, 9435346758607138196], [1162433738665300155, "crc", false, 16217298483365646130], [1464803193346256239, "event_listener", false, 7345649113269027287], [1811549171721445101, "futures_channel", false, 4629572407803545947], [3150220818285335163, "url", false, 12968655790473244891], [3405817021026194662, "hashlink", false, 3561913574780890197], [3646857438214563691, "futures_intrusive", false, 11684405027220980027], [3666196340704888985, "smallvec", false, 7382791388656124989], [3712811570531045576, "byteorder", false, 7251821513490054451], [3722963349756955755, "once_cell", false, 2565366582676818794], [5986029879202738730, "log", false, 11093138838220169164], [6493259146304816786, "indexmap", false, 8411789867366952717], [7620660491849607393, "futures_core", false, 14442838981038780947], [8008191657135824715, "thiserror", false, 5187766954170195458], [8319709847752024821, "uuid", false, 2829323984500747669], [8569119365930580996, "serde_json", false, 15813735211382407096], [8606274917505247608, "tracing", false, 5833543133503305489], [9689903380558560274, "serde", false, 7307778708627749338], [9857275760291862238, "sha2", false, 1393316979248719060], [9897246384292347999, "chrono", false, 16033156832765928802], [10629569228670356391, "futures_util", false, 3242258540150301387], [10862088793507253106, "sqlformat", false, 369579549241225971], [11295624341523567602, "rustls", false, 15998758801881071301], [12170264697963848012, "either", false, 1829476624730405159], [12944427623413450645, "tokio", false, 3798794974350704788], [15932120279885307830, "memchr", false, 15571097349483053606], [16066129441945555748, "bytes", false, 1952666714230133387], [16311359161338405624, "rustls_pemfile", false, 4055964487576107773], [16973251432615581304, "tokio_stream", false, 14834729326107211550], [17106256174509013259, "atoi", false, 16512988888853089780], [17605717126308396068, "paste", false, 265303868298634720], [17652733826348741533, "webpki_roots", false, 16275286507393306160]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-b3822bf389e33965\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}