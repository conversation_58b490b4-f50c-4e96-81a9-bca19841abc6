{"rustc": 1842507548689473721, "features": "[\"compression\", \"default\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"path-all\", \"regex\", \"shell-open\", \"shell-open-api\", \"sys-locale\", \"tauri-runtime-wry\", \"window-close\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 7668817577240348489, "path": 2505088397572599310, "deps": [[40386456601120721, "percent_encoding", false, 4385803709286603540], [1260461579271933187, "serialize_to_javascript", false, 11263243190610223910], [1441306149310335789, "tempfile", false, 8202610963164116638], [3150220818285335163, "url", false, 8909501939063229777], [3722963349756955755, "once_cell", false, 6428016074931338643], [3988549704697787137, "open", false, 6656786418674100696], [4381063397040571828, "webview2_com", false, 1159489376968944555], [4405182208873388884, "http", false, 5525313316207021045], [4450062412064442726, "dirs_next", false, 14596735839537201324], [4899080583175475170, "semver", false, 2766431719234362145], [5024769281214949041, "os_info", false, 1992764769358074539], [5180608563399064494, "tauri_macros", false, 6719003015343851757], [5610773616282026064, "build_script_build", false, 1553452907996798157], [5986029879202738730, "log", false, 3004741351825838512], [7653476968652377684, "windows", false, 5238405700565607631], [8008191657135824715, "thiserror", false, 4083214613751541842], [8292277814562636972, "tauri_utils", false, 17150142075726711639], [8319709847752024821, "uuid", false, 1140400842430028499], [8569119365930580996, "serde_json", false, 2740896963817221287], [9451456094439810778, "regex", false, 1597721282727493944], [9623796893764309825, "ignore", false, 6935667644480431584], [9689903380558560274, "serde", false, 4202820352788480372], [9920160576179037441, "getrandom", false, 9496806685848881522], [10629569228670356391, "futures_util", false, 2925717846322628741], [11601763207901161556, "tar", false, 15705909628012540988], [11693073011723388840, "raw_window_handle", false, 7780770964976528412], [11989259058781683633, "dunce", false, 16187145314844288619], [12944427623413450645, "tokio", false, 5789438060576990513], [12986574360607194341, "serde_repr", false, 880615272535003271], [13208667028893622512, "rand", false, 6363632852697834881], [13625485746686963219, "anyhow", false, 6029505153493273202], [14162324460024849578, "tauri_runtime", false, 13527492245305971298], [14564311161534545801, "encoding_rs", false, 7578403266852120990], [14618885535728128396, "sys_locale", false, 10516461152572413856], [16228250612241359704, "tauri_runtime_wry", false, 8208006908008331759], [17155886227862585100, "glob", false, 1058744767332970092], [17278893514130263345, "state", false, 12697109374277102099], [17772299992546037086, "flate2", false, 562275668731591289]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-3a0d2d0ed77d34c2\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}