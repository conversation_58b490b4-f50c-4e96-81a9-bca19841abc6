# 爬虫节点 (Crawler Node)

微博舆情分析系统的分布式爬虫节点，基于 Rust + Tauri 构建的高性能数据采集应用，负责执行具体的数据爬取任务，支持反反爬虫机制、智能代理池管理和多账号轮换。

## 🎯 核心功能

### 数据采集
- **多源数据爬取**：微博内容、用户信息、评论数据、话题信息
- **智能任务调度**：基于优先级的任务队列管理
- **并发处理**：支持1000+并发任务执行
- **数据去重**：智能去重算法避免重复采集

### 反反爬虫机制
- **IP代理池管理**：动态代理轮换、健康检测、负载均衡
- **请求伪装技术**：User-Agent轮换、请求头伪装、行为模拟
- **验证码处理**：OCR自动识别、人工打码服务集成
- **风控规避**：智能延时、访问频率控制、异常检测

### 账号管理
- **多账号轮换**：账号池管理、登录态维护、权限控制
- **Cookie管理**：登录态持久化、自动刷新、失效检测
- **风险控制**：账号健康监控、异常行为检测、自动切换

### 系统监控
- **节点状态监控**：CPU/内存/磁盘使用率实时监控
- **任务执行统计**：成功率、平均耗时、错误分析
- **性能指标上报**：实时状态同步到管理节点

## 🏗️ 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户界面层                                │
│              Tauri + React + TypeScript                   │
├─────────────────────────────────────────────────────────────┤
│                    任务调度层                                │
│            任务接收 + 优先级队列 + 负载均衡                   │
├─────────────────────────────────────────────────────────────┤
│                    爬虫引擎层                                │
│        HTTP客户端 + 反反爬虫 + 数据解析 + 结果处理            │
├─────────────────────────────────────────────────────────────┤
│                    资源管理层                                │
│          代理池 + 账号池 + Cookie管理 + 缓存系统             │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                                │
│              SQLite + Redis + 文件存储                     │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 后端技术
- **语言**：Rust 1.70+
- **异步运行时**：Tokio 1.0
- **HTTP客户端**：Reqwest 0.11
- **数据库ORM**：SQLx 0.7
- **序列化**：Serde 1.0
- **日志追踪**：Tracing 0.1

### 前端技术
- **框架**：React 18 + TypeScript 5+
- **样式**：TailwindCSS 3+ + Shadcn/ui
- **状态管理**：Zustand
- **图表库**：Recharts
- **路由**：React Router v6
- **构建工具**：Vite

### 桌面应用
- **框架**：Tauri 1.5+
- **打包目标**：Windows (MSI) / Linux (DEB, AppImage) / macOS (DMG)
- **权限控制**：最小权限原则，仅开放必要API

### 数据存储
- **本地数据库**：SQLite 3.40+ (任务队列、缓存数据)
- **缓存**：Redis 7+ (代理状态、账号信息)
- **消息队列**：RabbitMQ 3.12+ (任务接收、结果上报)

## 📁 项目结构

```
apps/node/
├── src/                          # 前端源码
│   ├── components/               # React组件
│   │   ├── Dashboard/           # 节点仪表板
│   │   ├── TaskManager/         # 任务管理
│   │   ├── ProxyPool/           # 代理池管理
│   │   ├── AccountManager/      # 账号管理
│   │   ├── Settings/            # 配置管理
│   │   └── ui/                  # 基础UI组件
│   ├── stores/                  # 状态管理
│   ├── services/                # API服务
│   ├── types/                   # TypeScript类型定义
│   ├── utils/                   # 工具函数
│   └── App.tsx                  # 主应用组件
├── src-tauri/                   # Tauri后端源码
│   ├── src/
│   │   ├── commands/            # Tauri命令处理
│   │   ├── crawler/             # 爬虫引擎
│   │   ├── proxy/               # 代理池管理
│   │   ├── account/             # 账号管理
│   │   ├── scheduler/           # 任务调度
│   │   ├── storage/             # 数据存储
│   │   ├── monitor/             # 系统监控
│   │   └── main.rs              # 主程序入口
│   ├── Cargo.toml               # Rust依赖配置
│   └── tauri.conf.json          # Tauri配置
├── migrations/                  # SQLite迁移文件
├── public/                      # 静态资源
├── package.json                 # Node.js依赖配置
├── tailwind.config.js           # TailwindCSS配置
├── vite.config.ts               # Vite构建配置
└── README.md                    # 项目文档
```

## 🚀 快速开始

### 环境要求

- **Rust**: 1.70+ (包含 Cargo)
- **Node.js**: 18+ (包含 npm/yarn)
- **SQLite**: 3.40+
- **Redis**: 7+ (可选，用于分布式缓存)
- **RabbitMQ**: 3.12+ (用于任务队列)

### 安装依赖

1. **安装前端依赖**
```bash
cd apps/node
npm install
```

2. **安装Rust依赖**
```bash
cd apps/node/src-tauri
cargo build
```

### 配置环境

1. **创建配置文件**
```bash
cp .env.example .env
```

2. **编辑配置文件**
```bash
# 节点配置
NODE_ID=crawler_node_001
NODE_NAME=爬虫节点001
NODE_TYPE=crawler

# 数据库配置
SQLITE_DATABASE_URL=sqlite:./data/crawler_node.db
REDIS_URL=redis://localhost:6379
RABBITMQ_URL=amqp://admin:password@localhost:5672

# 管理节点配置
MASTER_NODE_URL=http://localhost:8080
HEARTBEAT_INTERVAL=30

# 爬虫配置
MAX_CONCURRENT_TASKS=100
REQUEST_TIMEOUT=30
RETRY_MAX_ATTEMPTS=3

# 代理配置
PROXY_POOL_SIZE=50
PROXY_HEALTH_CHECK_INTERVAL=300

# 账号配置
ACCOUNT_POOL_SIZE=20
ACCOUNT_ROTATION_INTERVAL=3600

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/crawler_node.log
```

### 数据库初始化

#### 1. 安装SQLx CLI工具

```bash
# 安装SQLx CLI
cargo install sqlx-cli --no-default-features --features sqlite

# 验证安装
sqlx --version
```

#### 2. 运行数据库迁移

```bash
cd apps/node/src-tauri

# 创建数据库文件
mkdir -p data
touch data/crawler_node.db

# 运行迁移
sqlx migrate run --database-url sqlite:./data/crawler_node.db

# 或使用环境变量
export SQLITE_DATABASE_URL=sqlite:./data/crawler_node.db
sqlx migrate run
```

### 启动应用

1. **开发模式**
```bash
cd apps/node
npm run tauri dev
```

2. **生产构建**
```bash
cd apps/node
npm run tauri build
```

构建完成后，可执行文件位于 `src-tauri/target/release/bundle/` 目录下。

## 📊 核心功能模块

### 1. 任务调度系统

**功能特性**：
- 基于优先级的任务队列管理
- 智能负载均衡算法
- 失败重试机制
- 任务去重系统

**技术实现**：
- 二叉堆优先级队列
- 异步任务处理
- Redis分布式锁
- 指数退避重试策略

### 2. 反反爬虫系统

**功能特性**：
- IP代理池动态管理
- 请求头伪装和轮换
- 验证码自动识别
- 访问频率智能控制

**技术实现**：
- 代理健康检测算法
- User-Agent池轮换
- OCR验证码识别
- 基于规则的风控系统

### 3. 账号管理系统

**功能特性**：
- 多账号池管理
- 登录态自动维护
- Cookie持久化存储
- 账号风险评估

**技术实现**：
- 账号状态机管理
- 加密Cookie存储
- 登录态自动刷新
- 异常行为检测

### 4. 数据采集引擎

**功能特性**：
- 多源数据爬取
- 智能数据解析
- 实时数据清洗
- 结构化数据输出

**技术实现**：
- 高性能HTTP客户端
- 自适应解析器
- 数据验证管道
- JSON/XML数据处理

## 🔧 开发指南

### Tauri命令接口

爬虫节点通过Tauri命令与前端交互：

**任务管理命令**：
```rust
#[tauri::command]
async fn get_task_queue_status() -> Result<TaskQueueStatus, String>

#[tauri::command]
async fn start_task_processing() -> Result<(), String>

#[tauri::command]
async fn stop_task_processing() -> Result<(), String>

#[tauri::command]
async fn get_task_statistics() -> Result<TaskStatistics, String>
```

**代理池管理命令**：
```rust
#[tauri::command]
async fn get_proxy_pool_status() -> Result<ProxyPoolStatus, String>

#[tauri::command]
async fn add_proxy(proxy: ProxyConfig) -> Result<(), String>

#[tauri::command]
async fn remove_proxy(proxy_id: String) -> Result<(), String>

#[tauri::command]
async fn test_proxy(proxy_id: String) -> Result<ProxyTestResult, String>
```

**账号管理命令**：
```rust
#[tauri::command]
async fn get_account_pool_status() -> Result<AccountPoolStatus, String>

#[tauri::command]
async fn add_account(account: AccountConfig) -> Result<(), String>

#[tauri::command]
async fn login_account(account_id: String) -> Result<LoginResult, String>

#[tauri::command]
async fn refresh_account_cookies(account_id: String) -> Result<(), String>
```

### 状态管理

使用Zustand进行全局状态管理：

```typescript
// 节点状态
interface NodeState {
  nodeId: string;
  nodeStatus: 'online' | 'offline' | 'maintenance';
  systemMetrics: SystemMetrics;
  taskQueue: TaskQueueInfo;
  isProcessing: boolean;
}

// 任务状态
interface TaskState {
  activeTasks: Task[];
  completedTasks: Task[];
  failedTasks: Task[];
  taskStatistics: TaskStatistics;
}

// 代理池状态
interface ProxyState {
  proxies: Proxy[];
  activeProxies: number;
  healthyProxies: number;
  proxyStatistics: ProxyStatistics;
}

// 账号池状态
interface AccountState {
  accounts: Account[];
  activeAccounts: number;
  loggedInAccounts: number;
  accountStatistics: AccountStatistics;
}
```

### 组件开发规范

1. **组件命名**：使用PascalCase，文件名与组件名一致
2. **Props接口**：定义清晰的TypeScript接口
3. **样式规范**：使用TailwindCSS类名，避免内联样式
4. **错误处理**：使用ErrorBoundary包装组件
5. **性能优化**：合理使用React.memo和useMemo

## 🗄️ 数据库设计

### SQLite本地数据库

#### 核心表结构

**爬取任务表**：
- `crawl_tasks`: 任务队列和状态管理
- `task_results`: 任务执行结果
- `task_logs`: 任务执行日志

**代理管理表**：
- `proxies`: 代理服务器信息
- `proxy_health`: 代理健康状态记录
- `proxy_usage`: 代理使用统计

**账号管理表**：
- `accounts`: 账号基本信息
- `account_cookies`: Cookie存储
- `account_logs`: 账号操作日志

**缓存表**：
- `request_cache`: 请求结果缓存
- `user_cache`: 用户信息缓存
- `content_cache`: 内容数据缓存

### 迁移文件结构

```
migrations/
├── 20240101000001_create_crawl_tasks_table.sql
├── 20240101000002_create_task_results_table.sql
├── 20240101000003_create_proxies_table.sql
├── 20240101000004_create_proxy_health_table.sql
├── 20240101000005_create_accounts_table.sql
├── 20240101000006_create_account_cookies_table.sql
├── 20240101000007_create_cache_tables.sql
├── 20240101000008_create_indexes.sql
└── 20240101000009_create_triggers.sql
```

### 核心迁移文件

#### 1. 爬取任务表 (20240101000001_create_crawl_tasks_table.sql)

```sql
-- 爬取任务表
CREATE TABLE crawl_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT UNIQUE NOT NULL,
    task_type INTEGER NOT NULL, -- 1:用户 2:帖子 3:评论 4:话题
    target_url TEXT NOT NULL,
    priority INTEGER DEFAULT 5, -- 1-10优先级
    status INTEGER DEFAULT 0, -- 0:待处理 1:处理中 2:完成 3:失败 4:跳过
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    metadata TEXT, -- JSON格式的元数据
    assigned_at DATETIME,
    started_at DATETIME,
    completed_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_crawl_tasks_status ON crawl_tasks(status);
CREATE INDEX idx_crawl_tasks_priority ON crawl_tasks(priority DESC);
CREATE INDEX idx_crawl_tasks_task_type ON crawl_tasks(task_type);
CREATE INDEX idx_crawl_tasks_created_at ON crawl_tasks(created_at);

-- 创建更新时间触发器
CREATE TRIGGER update_crawl_tasks_updated_at
    AFTER UPDATE ON crawl_tasks
    FOR EACH ROW
BEGIN
    UPDATE crawl_tasks SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
```

#### 2. 代理管理表 (20240101000003_create_proxies_table.sql)

```sql
-- 代理服务器表
CREATE TABLE proxies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proxy_id TEXT UNIQUE NOT NULL,
    host TEXT NOT NULL,
    port INTEGER NOT NULL,
    protocol TEXT DEFAULT 'http', -- http, https, socks5
    username TEXT,
    password TEXT,
    country TEXT,
    region TEXT,
    provider TEXT,
    status INTEGER DEFAULT 1, -- 1:可用 2:不可用 3:维护中
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    last_used DATETIME,
    last_checked DATETIME,
    response_time INTEGER DEFAULT 0, -- 毫秒
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 代理健康检查记录表
CREATE TABLE proxy_health (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proxy_id TEXT NOT NULL,
    check_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_healthy BOOLEAN NOT NULL,
    response_time INTEGER,
    error_message TEXT,
    FOREIGN KEY (proxy_id) REFERENCES proxies(proxy_id)
);

-- 创建索引
CREATE INDEX idx_proxies_status ON proxies(status);
CREATE INDEX idx_proxies_last_used ON proxies(last_used);
CREATE INDEX idx_proxies_response_time ON proxies(response_time);
CREATE INDEX idx_proxy_health_proxy_id ON proxy_health(proxy_id);
CREATE INDEX idx_proxy_health_check_time ON proxy_health(check_time);
```

#### 3. 账号管理表 (20240101000005_create_accounts_table.sql)

```sql
-- 账号信息表
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id TEXT UNIQUE NOT NULL,
    username TEXT NOT NULL,
    password TEXT NOT NULL, -- 加密存储
    phone TEXT,
    email TEXT,
    status INTEGER DEFAULT 1, -- 1:正常 2:被封 3:异常 4:维护中
    login_count INTEGER DEFAULT 0,
    last_login DATETIME,
    last_activity DATETIME,
    risk_score REAL DEFAULT 0.0, -- 0.0-1.0风险评分
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Cookie存储表
CREATE TABLE account_cookies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id TEXT NOT NULL,
    cookie_name TEXT NOT NULL,
    cookie_value TEXT NOT NULL,
    domain TEXT,
    path TEXT DEFAULT '/',
    expires DATETIME,
    secure BOOLEAN DEFAULT FALSE,
    http_only BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id),
    UNIQUE(account_id, cookie_name, domain)
);

-- 创建索引
CREATE INDEX idx_accounts_status ON accounts(status);
CREATE INDEX idx_accounts_last_login ON accounts(last_login);
CREATE INDEX idx_accounts_risk_score ON accounts(risk_score);
CREATE INDEX idx_account_cookies_account_id ON account_cookies(account_id);
CREATE INDEX idx_account_cookies_expires ON account_cookies(expires);
```

### 迁移管理命令

#### 常用SQLx命令

```bash
# 创建新的迁移文件
sqlx migrate add create_new_table

# 运行所有待执行的迁移
sqlx migrate run

# 回滚最后一次迁移
sqlx migrate revert

# 查看迁移状态
sqlx migrate info

# 验证迁移文件
sqlx migrate validate
```

#### 自定义迁移脚本

```bash
#!/bin/bash
# scripts/migrate.sh

set -e

SQLITE_DATABASE_URL=${SQLITE_DATABASE_URL:-"sqlite:./data/crawler_node.db"}

echo "开始数据库迁移..."

# 检查数据库文件
DB_FILE=$(echo $SQLITE_DATABASE_URL | sed 's/sqlite://')
DB_DIR=$(dirname "$DB_FILE")

# 创建数据目录
mkdir -p "$DB_DIR"

# 运行迁移
sqlx migrate run --database-url "$SQLITE_DATABASE_URL"

echo "数据库迁移完成!"
```

## 🔍 监控与运维

### 系统监控

**监控指标**：
- 节点状态：在线状态、系统资源使用率
- 任务执行：队列长度、处理速度、成功率
- 代理池状态：可用代理数、健康率、响应时间
- 账号池状态：可用账号数、登录成功率、风险评分
- 网络性能：请求延迟、带宽使用、错误率

**告警规则**：
- 节点离线超过2分钟
- 任务队列积压超过1000个
- 代理池健康率低于50%
- 账号池可用率低于30%
- 系统资源使用率超过90%

### 日志管理

使用结构化日志记录：

```rust
// 任务执行日志
tracing::info!(
    task_id = %task.id,
    task_type = %task.task_type,
    target_url = %task.target_url,
    duration_ms = %duration.as_millis(),
    "Task completed successfully"
);

// 代理使用日志
tracing::warn!(
    proxy_id = %proxy.id,
    host = %proxy.host,
    error = %e,
    "Proxy request failed"
);

// 账号状态日志
tracing::error!(
    account_id = %account.id,
    username = %account.username,
    error = %e,
    "Account login failed"
);
```

### 性能优化

**爬虫引擎优化**：
- 连接池复用
- 请求批处理
- 智能重试策略
- 缓存机制

**数据库优化**：
- 索引优化
- 批量操作
- 连接池管理
- 定期清理

**内存管理**：
- 对象池复用
- 及时释放资源
- 内存使用监控
- 垃圾回收优化

## 🧪 测试

### 单元测试

```bash
# 前端测试
npm run test

# 后端测试
cd src-tauri
cargo test
```

### 集成测试

```bash
# 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 运行集成测试
npm run test:integration
```

### 性能测试

```bash
# 爬虫性能测试
cargo test --release performance_tests

# 并发测试
cargo test --release concurrent_tests

# 压力测试
cargo test --release stress_tests
```

## 📦 部署

### 开发环境部署

```bash
# 启动依赖服务
docker-compose up -d redis rabbitmq

# 初始化数据库
./scripts/migrate.sh

# 启动开发服务器
npm run tauri dev
```

### 生产环境部署

```bash
# 构建应用
npm run tauri build

# 生成安装包
# Windows: target/release/bundle/msi/
# Linux: target/release/bundle/deb/ 和 target/release/bundle/appimage/
# macOS: target/release/bundle/dmg/
```

### Docker部署

```dockerfile
# Dockerfile
FROM rust:1.70 as builder

WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

COPY --from=builder /app/target/release/crawler-node /usr/local/bin/
COPY --from=builder /app/migrations /app/migrations

EXPOSE 8081
CMD ["crawler-node"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  crawler-node:
    build: .
    environment:
      - NODE_ID=crawler_node_001
      - REDIS_URL=redis://redis:6379
      - RABBITMQ_URL=amqp://admin:password@rabbitmq:5672
    depends_on:
      - redis
      - rabbitmq
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  rabbitmq:
    image: rabbitmq:3.12-management
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=password
    ports:
      - "5672:5672"
      - "15672:15672"
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范

- **Rust**: 使用 `cargo fmt` 和 `cargo clippy`
- **TypeScript**: 使用 ESLint 和 Prettier
- **提交信息**: 遵循 Conventional Commits 规范

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](../../LICENSE) 文件了解详情。

## 🆘 故障排除

### 常见问题

**Q: 节点启动失败，提示数据库连接错误**
A: 检查SQLite数据库文件路径是否正确，确保目录存在且有写权限。

**Q: 任务队列无法接收任务**
A: 检查RabbitMQ连接配置，确保消息队列服务正常运行。

**Q: 代理池中的代理无法使用**
A: 检查代理服务器配置，运行代理健康检查，移除失效代理。

**Q: 账号登录失败**
A: 检查账号状态，确认用户名密码正确，查看是否触发风控机制。

**Q: 爬取任务执行缓慢**
A: 检查网络连接，调整并发数量，优化代理池配置。

### 获取帮助

- 📖 查看[项目文档](../../docs/)
- 🐛 提交[Issue](https://github.com/your-org/weibo-sentiment-analysis/issues)
- 💬 加入[讨论区](https://github.com/your-org/weibo-sentiment-analysis/discussions)
- 📧 联系维护者：<EMAIL>

## 📱 应用界面

### 主要功能页面

1. **仪表板** - 节点状态总览和实时监控
2. **任务管理** - 爬虫任务队列管理和统计
3. **代理池管理** - 代理服务器池配置和健康检查
4. **账号池管理** - 微博账号管理和登录状态维护
5. **系统监控** - 系统资源使用和性能监控
6. **设置** - 节点配置和参数调整

### 界面特性

- **响应式设计**：支持桌面和移动端访问
- **实时更新**：数据自动刷新，实时显示状态变化
- **暗色主题**：支持明暗主题切换
- **多语言支持**：中文界面，易于使用

## 🔧 核心功能详解

### 1. 智能任务调度

**优先级队列管理**：
- 基于优先级的任务排序
- 支持1000+并发任务处理
- 智能负载均衡和资源分配
- 任务失败自动重试机制

**任务类型支持**：
- 用户信息爬取
- 微博内容抓取
- 评论数据收集
- 话题信息分析

### 2. 反反爬虫机制

**IP代理池管理**：
- 动态代理轮换策略
- 实时健康检查和故障转移
- 支持HTTP/HTTPS/SOCKS5协议
- 地理位置分布和负载均衡

**请求伪装技术**：
- User-Agent随机轮换
- 请求头智能伪装
- 访问频率控制
- 行为模式模拟

### 3. 账号管理系统

**多账号轮换**：
- 账号池自动管理
- 登录态持久化存储
- Cookie自动刷新机制
- 账号健康状态监控

**风险控制**：
- 智能风险评分算法
- 异常行为检测
- 自动账号切换
- 封号风险预警

### 4. 系统监控

**实时性能监控**：
- CPU/内存/磁盘使用率
- 网络IO统计
- 任务处理性能指标
- 错误率和成功率统计

**健康检查**：
- 节点状态自动上报
- 心跳机制保持连接
- 异常自动恢复
- 告警通知机制

## 🏗️ 项目架构说明

### 技术栈选择

**前端技术**：
- **React 18**：现代化的用户界面框架
- **TypeScript**：类型安全的JavaScript超集
- **Tailwind CSS**：实用优先的CSS框架
- **Vite**：快速的前端构建工具

**后端技术**：
- **Rust**：高性能系统编程语言
- **Tauri**：跨平台桌面应用框架
- **SQLx**：异步SQL工具包
- **Tokio**：异步运行时

**数据存储**：
- **SQLite**：轻量级关系数据库
- **Redis**：内存数据结构存储
- **文件系统**：日志和缓存文件

### 模块设计

```
src-tauri/src/
├── main.rs          # 应用入口点
├── commands.rs      # Tauri命令定义
├── config.rs        # 配置管理
├── error.rs         # 错误处理
├── crawler.rs       # 爬虫引擎
├── proxy.rs         # 代理管理
├── account.rs       # 账号管理
├── scheduler.rs     # 任务调度
├── storage.rs       # 数据存储
└── monitor.rs       # 系统监控
```

### 数据流设计

```
任务接收 → 优先级队列 → 任务调度器 → 爬虫引擎 → 数据存储
    ↓           ↓           ↓           ↓           ↓
代理池管理 → 账号池管理 → 反爬机制 → 结果处理 → 状态上报
```

## 🚀 快速开始（完整版）

### 环境准备

1. **安装Rust**
```bash
# 安装Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

2. **安装Node.js**
```bash
# 使用nvm安装Node.js 18
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# 验证安装
node --version
npm --version
```

3. **安装Tauri CLI**
```bash
cargo install tauri-cli
```

### 项目设置

1. **克隆项目**
```bash
git clone https://github.com/your-org/weibo-sentiment-analysis.git
cd weibo-sentiment-analysis/apps/node
```

2. **自动设置（推荐）**
```bash
# Linux/macOS
./scripts/setup.sh

# Windows
scripts\setup.bat
```

3. **手动设置**
```bash
# 安装前端依赖
npm install

# 创建目录
mkdir -p data logs

# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env

# 初始化数据库
cd src-tauri
cargo run --bin migrate
cargo run --bin seed
cd ..
```

### 开发模式

```bash
# 启动开发服务器
npm run tauri:dev

# 或分别启动前后端
npm run dev          # 前端开发服务器
cargo tauri dev      # Tauri开发模式
```

### 生产构建

```bash
# 构建应用
npm run tauri:build

# 构建产物位置
# - 可执行文件: src-tauri/target/release/
# - 安装包: src-tauri/target/release/bundle/
```

### Docker部署

```bash
# 构建镜像
docker build -t weibo-crawler-node .

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f crawler-node
```