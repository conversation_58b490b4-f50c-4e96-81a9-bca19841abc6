import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { setupTauriMocks, mockTauriCommands } from '../utils'

describe('API Integration Tests', () => {
  let mockInvoke: ReturnType<typeof setupTauriMocks>

  beforeEach(() => {
    mockInvoke = setupTauriMocks()
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('Task Management API', () => {
    it('should create task successfully', async () => {
      const taskData = {
        task_type: 1,
        target_url: 'https://example.com',
        priority: 1,
        max_retries: 3,
        metadata: null,
      }

      const expectedTask = {
        id: '1',
        task_id: 'task-1',
        ...taskData,
        status: 0,
        retry_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      mockTauriCommands.create_task.mockResolvedValueOnce(expectedTask)

      const result = await window.__TAURI__.invoke('create_task', taskData)

      expect(result).toEqual(expectedTask)
      expect(mockTauriCommands.create_task).toHaveBeenCalledWith(taskData)
    })

    it('should get task list with pagination', async () => {
      const mockTasks = Array.from({ length: 5 }, (_, i) => ({
        id: String(i + 1),
        task_id: `task-${i + 1}`,
        task_type: 1,
        target_url: `https://example${i + 1}.com`,
        priority: 1,
        status: 0,
        retry_count: 0,
        max_retries: 3,
        metadata: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }))

      const paginatedResult = {
        items: mockTasks,
        total: 50,
        page: 1,
        limit: 10,
        total_pages: 5,
      }

      mockTauriCommands.get_tasks.mockResolvedValueOnce(paginatedResult)

      const result = await window.__TAURI__.invoke('get_tasks', { page: 1, limit: 10 })

      expect(result).toEqual(paginatedResult)
      expect(result.items).toHaveLength(5)
      expect(result.total).toBe(50)
    })

    it('should update task successfully', async () => {
      const taskId = 'task-1'
      const updateData = {
        priority: 2,
        metadata: JSON.stringify({ updated: true }),
      }

      const updatedTask = {
        id: '1',
        task_id: taskId,
        task_type: 1,
        target_url: 'https://example.com',
        priority: 2,
        status: 0,
        retry_count: 0,
        max_retries: 3,
        metadata: JSON.stringify({ updated: true }),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      mockTauriCommands.update_task.mockResolvedValueOnce(updatedTask)

      const result = await window.__TAURI__.invoke('update_task', { task_id: taskId, ...updateData })

      expect(result).toEqual(updatedTask)
      expect(result.priority).toBe(2)
      expect(result.metadata).toBe(JSON.stringify({ updated: true }))
    })

    it('should delete task successfully', async () => {
      const taskId = 'task-1'

      mockTauriCommands.delete_task.mockResolvedValueOnce(null)

      const result = await window.__TAURI__.invoke('delete_task', { task_id: taskId })

      expect(result).toBeNull()
      expect(mockTauriCommands.delete_task).toHaveBeenCalledWith({ task_id: taskId })
    })

    it('should get task statistics', async () => {
      const mockStats = {
        total_tasks: 100,
        pending_tasks: 20,
        running_tasks: 5,
        completed_tasks: 70,
        failed_tasks: 5,
        success_rate: 93.3,
        average_processing_time: 2.5,
        tasks_per_hour: 24.0,
        last_updated: new Date().toISOString(),
      }

      mockTauriCommands.get_task_statistics.mockResolvedValueOnce(mockStats)

      const result = await window.__TAURI__.invoke('get_task_statistics')

      expect(result).toEqual(mockStats)
      expect(result.success_rate).toBeCloseTo(93.3)
      expect(result.total_tasks).toBe(100)
    })
  })

  describe('Proxy Management API', () => {
    it('should get proxy list', async () => {
      const mockProxies = Array.from({ length: 3 }, (_, i) => ({
        id: String(i + 1),
        proxy_id: `proxy-${i + 1}`,
        host: '127.0.0.1',
        port: 8080 + i,
        username: 'user',
        password: 'pass',
        proxy_type: 'http',
        status: 1,
        last_check: new Date().toISOString(),
        response_time: 100,
        success_rate: 95.5,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }))

      mockTauriCommands.get_proxies.mockResolvedValueOnce(mockProxies)

      const result = await window.__TAURI__.invoke('get_proxies')

      expect(result).toEqual(mockProxies)
      expect(result).toHaveLength(3)
    })

    it('should test proxy connectivity', async () => {
      const proxyId = 'proxy-1'

      mockTauriCommands.test_proxy.mockResolvedValueOnce(true)

      const result = await window.__TAURI__.invoke('test_proxy', { proxy_id: proxyId })

      expect(result).toBe(true)
      expect(mockTauriCommands.test_proxy).toHaveBeenCalledWith({ proxy_id: proxyId })
    })

    it('should add new proxy', async () => {
      const proxyData = {
        host: '*************',
        port: 8080,
        username: 'testuser',
        password: 'testpass',
        proxy_type: 'http',
      }

      const expectedProxy = {
        id: '1',
        proxy_id: 'proxy-1',
        ...proxyData,
        status: 1,
        last_check: new Date().toISOString(),
        response_time: 0,
        success_rate: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      mockTauriCommands.add_proxy.mockResolvedValueOnce(expectedProxy)

      const result = await window.__TAURI__.invoke('add_proxy', proxyData)

      expect(result).toEqual(expectedProxy)
      expect(result.host).toBe('*************')
      expect(result.port).toBe(8080)
    })
  })

  describe('Account Management API', () => {
    it('should get account list', async () => {
      const mockAccounts = Array.from({ length: 3 }, (_, i) => ({
        id: String(i + 1),
        account_id: `account-${i + 1}`,
        username: `testuser${i + 1}`,
        password: 'password',
        email: `test${i + 1}@example.com`,
        phone: '***********',
        status: 1,
        risk_level: 0,
        last_login: new Date().toISOString(),
        cookies: null,
        user_agent: 'Mozilla/5.0...',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }))

      mockTauriCommands.get_accounts.mockResolvedValueOnce(mockAccounts)

      const result = await window.__TAURI__.invoke('get_accounts')

      expect(result).toEqual(mockAccounts)
      expect(result).toHaveLength(3)
    })

    it('should login account successfully', async () => {
      const accountId = 'account-1'

      mockTauriCommands.login_account.mockResolvedValueOnce({ success: true })

      const result = await window.__TAURI__.invoke('login_account', { account_id: accountId })

      expect(result).toEqual({ success: true })
      expect(mockTauriCommands.login_account).toHaveBeenCalledWith({ account_id: accountId })
    })

    it('should add new account', async () => {
      const accountData = {
        username: 'newuser',
        password: 'newpass',
        email: '<EMAIL>',
        phone: '***********',
      }

      const expectedAccount = {
        id: '1',
        account_id: 'account-1',
        ...accountData,
        status: 1,
        risk_level: 0,
        last_login: null,
        cookies: null,
        user_agent: 'Mozilla/5.0...',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      mockTauriCommands.add_account.mockResolvedValueOnce(expectedAccount)

      const result = await window.__TAURI__.invoke('add_account', accountData)

      expect(result).toEqual(expectedAccount)
      expect(result.username).toBe('newuser')
      expect(result.email).toBe('<EMAIL>')
    })
  })

  describe('System Monitoring API', () => {
    it('should get system metrics', async () => {
      const mockMetrics = {
        cpu_usage: 45.2,
        memory_usage: 68.5,
        disk_usage: 32.1,
        network_in: 1024,
        network_out: 2048,
        uptime: 86400,
      }

      mockTauriCommands.get_system_metrics.mockResolvedValueOnce(mockMetrics)

      const result = await window.__TAURI__.invoke('get_system_metrics')

      expect(result).toEqual(mockMetrics)
      expect(result.cpu_usage).toBeCloseTo(45.2)
      expect(result.memory_usage).toBeCloseTo(68.5)
    })

    it('should get configuration', async () => {
      const mockConfig = {
        crawler: {
          max_concurrent_tasks: 10,
          request_delay: 1000,
          retry_attempts: 3,
        },
        proxy: {
          enabled: true,
          rotation_interval: 300,
        },
        account: {
          max_accounts: 50,
          login_interval: 3600,
        },
      }

      mockTauriCommands.get_config.mockResolvedValueOnce(mockConfig)

      const result = await window.__TAURI__.invoke('get_config')

      expect(result).toEqual(mockConfig)
      expect(result.crawler.max_concurrent_tasks).toBe(10)
      expect(result.proxy.enabled).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle command not found error', async () => {
      const unknownCommand = 'unknown_command'

      await expect(
        window.__TAURI__.invoke(unknownCommand)
      ).rejects.toThrow(`Unknown command: ${unknownCommand}`)
    })

    it('should handle task creation error', async () => {
      const invalidTaskData = {
        task_type: -1, // 无效的任务类型
        target_url: 'invalid-url',
        priority: 1,
        max_retries: 3,
      }

      mockTauriCommands.create_task.mockRejectedValueOnce(
        new Error('Invalid task data')
      )

      await expect(
        window.__TAURI__.invoke('create_task', invalidTaskData)
      ).rejects.toThrow('Invalid task data')
    })

    it('should handle network timeout error', async () => {
      mockTauriCommands.test_proxy.mockRejectedValueOnce(
        new Error('Connection timeout')
      )

      await expect(
        window.__TAURI__.invoke('test_proxy', { proxy_id: 'proxy-1' })
      ).rejects.toThrow('Connection timeout')
    })
  })
})
