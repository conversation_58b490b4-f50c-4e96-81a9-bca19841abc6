import { invoke } from "@tauri-apps/api/tauri";

// 系统管理相关接口
export interface NodeStatus {
  node_id: string;
  node_name: string;
  status: string;
  uptime: number;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  active_tasks: number;
  total_tasks: number;
  success_rate: number;
}

export interface TaskQueueStatus {
  pending_tasks: number;
  running_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  queue_length: number;
  processing_speed: number;
}

export interface TaskStatistics {
  total_processed: number;
  success_count: number;
  failure_count: number;
  average_duration: number;
  success_rate: number;
  tasks_per_hour: number;
}

export interface ProxyPoolStatus {
  total_proxies: number;
  active_proxies: number;
  healthy_proxies: number;
  average_response_time: number;
  success_rate: number;
}

export interface ProxyConfig {
  host: string;
  port: number;
  protocol: string;
  username?: string;
  password?: string;
  country?: string;
  provider?: string;
}

export interface ProxyTestResult {
  proxy_id: string;
  is_working: boolean;
  response_time?: number;
  error_message?: string;
}

export interface AccountPoolStatus {
  total_accounts: number;
  active_accounts: number;
  logged_in_accounts: number;
  healthy_accounts: number;
  average_risk_score: number;
}

export interface AccountConfig {
  username: string;
  password: string;
  phone?: string;
  email?: string;
}

export interface LoginResult {
  account_id: string;
  success: boolean;
  error_message?: string;
  cookies?: string;
}

export interface SystemMetrics {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_in: number;
  network_out: number;
  timestamp: number;
}

export interface PerformanceStats {
  requests_per_second: number;
  average_response_time: number;
  error_rate: number;
  concurrent_connections: number;
  queue_depth: number;
}

export interface AppConfig {
  node_id: string;
  node_name: string;
  node_type: string;
  database_url: string;
  redis_url: string;
  rabbitmq_url: string;
  master_node_url: string;
  heartbeat_interval: number;
  max_concurrent_tasks: number;
  request_timeout: number;
  retry_max_attempts: number;
  proxy_pool_size: number;
  proxy_health_check_interval: number;
  account_pool_size: number;
  account_rotation_interval: number;
  log_level: string;
  log_file: string;
}

// Tauri命令调用封装
export class TauriAPI {
  // 系统管理命令
  static async getNodeStatus(): Promise<NodeStatus> {
    return invoke("get_node_status");
  }

  static async startCrawler(): Promise<string> {
    return invoke("start_crawler");
  }

  static async stopCrawler(): Promise<string> {
    return invoke("stop_crawler");
  }

  static async restartCrawler(): Promise<string> {
    return invoke("restart_crawler");
  }

  // 任务管理命令
  static async getTaskQueueStatus(): Promise<TaskQueueStatus> {
    return invoke("get_task_queue_status");
  }

  static async startTaskProcessing(): Promise<string> {
    return invoke("start_task_processing");
  }

  static async stopTaskProcessing(): Promise<string> {
    return invoke("stop_task_processing");
  }

  static async getTaskStatistics(): Promise<TaskStatistics> {
    return invoke("get_task_statistics");
  }

  // 代理池管理命令
  static async getProxyPoolStatus(): Promise<ProxyPoolStatus> {
    return invoke("get_proxy_pool_status");
  }

  static async addProxy(proxy: ProxyConfig): Promise<string> {
    return invoke("add_proxy", { proxy });
  }

  static async removeProxy(proxyId: string): Promise<string> {
    return invoke("remove_proxy", { proxyId });
  }

  static async testProxy(proxyId: string): Promise<ProxyTestResult> {
    return invoke("test_proxy", { proxyId });
  }

  // 账号管理命令
  static async getAccountPoolStatus(): Promise<AccountPoolStatus> {
    return invoke("get_account_pool_status");
  }

  static async addAccount(account: AccountConfig): Promise<string> {
    return invoke("add_account", { account });
  }

  static async loginAccount(accountId: string): Promise<LoginResult> {
    return invoke("login_account", { accountId });
  }

  static async refreshAccountCookies(accountId: string): Promise<string> {
    return invoke("refresh_account_cookies", { accountId });
  }

  // 监控命令
  static async getSystemMetrics(): Promise<SystemMetrics> {
    return invoke("get_system_metrics");
  }

  static async getPerformanceStats(): Promise<PerformanceStats> {
    return invoke("get_performance_stats");
  }

  // 配置管理命令
  static async getConfig(): Promise<AppConfig> {
    return invoke("get_config");
  }

  static async updateConfig(newConfig: AppConfig): Promise<string> {
    return invoke("update_config", { newConfig });
  }
}

// 状态管理相关类型定义
export interface BaseState {
  loading: boolean;
  error: string | null;
  lastUpdated: number;
}

// 节点状态相关类型扩展
export interface NodeInfo {
  nodeId: string;
  nodeName: string;
  nodeType: string;
  status: 'online' | 'offline' | 'maintenance';
  uptime: number;
  version: string;
}



export interface NodeState extends BaseState {
  nodeInfo: NodeInfo | null;
  systemMetrics: SystemMetrics | null;
  isProcessing: boolean;
  activeTasks: number;
  totalTasks: number;
  successRate: number;
}

// 任务状态相关类型
export interface Task {
  id: string;
  taskId: string;
  taskType: number;
  targetUrl: string;
  priority: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  retryCount: number;
  maxRetries: number;
  createdAt: string;
  startedAt?: string;
  completedAt?: string;
  metadata?: Record<string, any>;
}

export interface TaskState extends BaseState {
  tasks: Task[];
  statistics: TaskStatistics | null;
  queueInfo: TaskQueueStatus | null;
  isProcessing: boolean;
}

// 代理池状态相关类型
export interface Proxy {
  id: string;
  host: string;
  port: number;
  protocol: 'http' | 'https' | 'socks5';
  username?: string;
  password?: string;
  country?: string;
  provider?: string;
  status: 'active' | 'inactive' | 'banned' | 'testing';
  responseTime?: number;
  successRate: number;
  lastUsed?: string;
  createdAt: string;
}

export interface ProxyState extends BaseState {
  proxies: Proxy[];
  statistics: ProxyPoolStatus | null;
  testingProxies: Set<string>;
}

// 账号池状态相关类型
export interface Account {
  id: string;
  username: string;
  phone?: string;
  email?: string;
  status: 'normal' | 'banned' | 'abnormal' | 'maintenance';
  isLoggedIn: boolean;
  loginCount: number;
  riskScore: number;
  lastUsed?: string;
  cookies?: string;
  createdAt: string;
}

export interface AccountState extends BaseState {
  accounts: Account[];
  statistics: AccountPoolStatus | null;
  loggingInAccounts: Set<string>;
}

// Zustand Store 实现
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

// 节点状态Store
interface NodeActions {
  fetchNodeStatus: () => Promise<void>;
  startCrawler: () => Promise<void>;
  stopCrawler: () => Promise<void>;
  restartCrawler: () => Promise<void>;
  updateSystemMetrics: (metrics: SystemMetrics) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useNodeStore = create<NodeState & NodeActions>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        loading: false,
        error: null,
        lastUpdated: 0,
        nodeInfo: null,
        systemMetrics: null,
        isProcessing: false,
        activeTasks: 0,
        totalTasks: 0,
        successRate: 0,

        // Actions
        fetchNodeStatus: async () => {
          set({ loading: true, error: null });

          try {
            const nodeStatus = await TauriAPI.getNodeStatus();
            set({
              nodeInfo: {
                nodeId: nodeStatus.node_id,
                nodeName: nodeStatus.node_name,
                nodeType: 'crawler',
                status: nodeStatus.status as 'online' | 'offline' | 'maintenance',
                uptime: nodeStatus.uptime,
                version: '0.1.0'
              },
              systemMetrics: {
                cpu_usage: nodeStatus.cpu_usage,
                memory_usage: nodeStatus.memory_usage,
                disk_usage: nodeStatus.disk_usage,
                network_in: 0,
                network_out: 0,
                timestamp: Date.now()
              },
              activeTasks: nodeStatus.active_tasks,
              totalTasks: nodeStatus.total_tasks,
              successRate: nodeStatus.success_rate,
              loading: false,
              lastUpdated: Date.now()
            });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Unknown error',
              loading: false
            });
          }
        },

        startCrawler: async () => {
          try {
            await TauriAPI.startCrawler();
            set({ isProcessing: true });
            // 重新获取状态
            get().fetchNodeStatus();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to start crawler' });
          }
        },

        stopCrawler: async () => {
          try {
            await TauriAPI.stopCrawler();
            set({ isProcessing: false });
            // 重新获取状态
            get().fetchNodeStatus();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to stop crawler' });
          }
        },

        restartCrawler: async () => {
          try {
            await TauriAPI.restartCrawler();
            // 重新获取状态
            get().fetchNodeStatus();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to restart crawler' });
          }
        },

        updateSystemMetrics: (metrics: SystemMetrics) => {
          set({ systemMetrics: metrics, lastUpdated: Date.now() });
        },

        setError: (error: string | null) => {
          set({ error });
        },

        clearError: () => {
          set({ error: null });
        }
      }),
      {
        name: 'node-store',
        partialize: (state) => ({
          nodeInfo: state.nodeInfo,
          isProcessing: state.isProcessing
        })
      }
    ),
    { name: 'node-store' }
  )
);

// 任务状态Store
interface TaskActions {
  fetchTasks: () => Promise<void>;
  fetchTaskStatistics: () => Promise<void>;
  fetchQueueStatus: () => Promise<void>;
  startTaskProcessing: () => Promise<void>;
  stopTaskProcessing: () => Promise<void>;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useTaskStore = create<TaskState & TaskActions>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        loading: false,
        error: null,
        lastUpdated: 0,
        tasks: [],
        statistics: null,
        queueInfo: null,
        isProcessing: false,

        // Actions
        fetchTasks: async () => {
          set({ loading: true, error: null });
          try {
            // 暂时使用空数组，因为getTasks API不存在
            const tasks: any[] = [];
            set({
              tasks: tasks,
              loading: false,
              lastUpdated: Date.now()
            });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch tasks',
              loading: false
            });
          }
        },

        fetchTaskStatistics: async () => {
          try {
            // 暂时使用模拟数据
            const stats = null;
            set({ statistics: stats, lastUpdated: Date.now() });
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to fetch task statistics' });
          }
        },

        fetchQueueStatus: async () => {
          try {
            const queueStatus = await TauriAPI.getTaskQueueStatus();
            set({ queueInfo: queueStatus, lastUpdated: Date.now() });
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to fetch queue status' });
          }
        },

        startTaskProcessing: async () => {
          try {
            await TauriAPI.startTaskProcessing();
            set({ isProcessing: true });
            // 重新获取状态
            get().fetchQueueStatus();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to start task processing' });
          }
        },

        stopTaskProcessing: async () => {
          try {
            await TauriAPI.stopTaskProcessing();
            set({ isProcessing: false });
            // 重新获取状态
            get().fetchQueueStatus();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to stop task processing' });
          }
        },

        setError: (error: string | null) => {
          set({ error });
        },

        clearError: () => {
          set({ error: null });
        }
      }),
      {
        name: 'task-store',
        partialize: (state) => ({
          isProcessing: state.isProcessing
        })
      }
    ),
    { name: 'task-store' }
  )
);

// 代理池状态Store
interface ProxyActions {
  fetchProxies: () => Promise<void>;
  fetchProxyStatistics: () => Promise<void>;
  addProxy: (proxy: Omit<Proxy, 'id' | 'createdAt'>) => Promise<void>;
  removeProxy: (proxyId: string) => Promise<void>;
  testProxy: (proxyId: string) => Promise<void>;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useProxyStore = create<ProxyState & ProxyActions>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        loading: false,
        error: null,
        lastUpdated: 0,
        proxies: [],
        statistics: null,
        testingProxies: new Set<string>(),

        // Actions
        fetchProxies: async () => {
          set({ loading: true, error: null });

          try {
            // 暂时使用空数组，因为getProxies API不存在
            const proxies: any[] = [];
            set({
              proxies: proxies.map(proxy => ({
                id: proxy.id,
                host: proxy.host,
                port: proxy.port,
                protocol: proxy.protocol as 'http' | 'https' | 'socks5',
                username: proxy.username,
                password: proxy.password,
                country: proxy.country,
                provider: proxy.provider,
                status: proxy.status as 'active' | 'inactive' | 'banned' | 'testing',
                responseTime: proxy.response_time,
                successRate: proxy.success_rate,
                lastUsed: proxy.last_used,
                createdAt: proxy.created_at
              })),
              loading: false,
              lastUpdated: Date.now()
            });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch proxies',
              loading: false
            });
          }
        },

        fetchProxyStatistics: async () => {
          try {
            const stats = await TauriAPI.getProxyPoolStatus();
            set({ statistics: stats, lastUpdated: Date.now() });
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to fetch proxy statistics' });
          }
        },

        addProxy: async (proxy) => {
          try {
            await TauriAPI.addProxy(proxy);
            // 重新获取代理列表
            get().fetchProxies();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to add proxy' });
          }
        },

        removeProxy: async (proxyId: string) => {
          try {
            await TauriAPI.removeProxy(proxyId);
            // 重新获取代理列表
            get().fetchProxies();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to remove proxy' });
          }
        },

        testProxy: async (proxyId: string) => {
          const currentState = get();
          const newTestingProxies = new Set(currentState.testingProxies);
          newTestingProxies.add(proxyId);
          set({ testingProxies: newTestingProxies });

          try {
            await TauriAPI.testProxy(proxyId);
            // 重新获取代理列表以更新状态
            get().fetchProxies();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to test proxy' });
          } finally {
            const finalState = get();
            const finalTestingProxies = new Set(finalState.testingProxies);
            finalTestingProxies.delete(proxyId);
            set({ testingProxies: finalTestingProxies });
          }
        },

        setError: (error: string | null) => {
          set({ error });
        },

        clearError: () => {
          set({ error: null });
        }
      }),
      {
        name: 'proxy-store',
        partialize: (state) => ({
          proxies: state.proxies
        })
      }
    ),
    { name: 'proxy-store' }
  )
);

// 账号池状态Store
interface AccountActions {
  fetchAccounts: () => Promise<void>;
  fetchAccountStatistics: () => Promise<void>;
  addAccount: (account: AccountConfig) => Promise<void>;
  loginAccount: (accountId: string) => Promise<void>;
  refreshAccountCookies: (accountId: string) => Promise<void>;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useAccountStore = create<AccountState & AccountActions>()(
  devtools(
    persist(
      (set, get) => ({
        // State
        loading: false,
        error: null,
        lastUpdated: 0,
        accounts: [],
        statistics: null,
        loggingInAccounts: new Set<string>(),

        // Actions
        fetchAccounts: async () => {
          set({ loading: true, error: null });

          try {
            // 暂时使用空数组，因为getAccounts API不存在
            const accounts: any[] = [];
            set({
              accounts: accounts.map(account => ({
                id: account.id,
                username: account.username,
                phone: account.phone,
                email: account.email,
                status: account.status as 'normal' | 'banned' | 'abnormal' | 'maintenance',
                isLoggedIn: account.is_logged_in,
                loginCount: account.login_count,
                riskScore: account.risk_score,
                lastUsed: account.last_used,
                cookies: account.cookies,
                createdAt: account.created_at
              })),
              loading: false,
              lastUpdated: Date.now()
            });
          } catch (error) {
            set({
              error: error instanceof Error ? error.message : 'Failed to fetch accounts',
              loading: false
            });
          }
        },

        fetchAccountStatistics: async () => {
          try {
            const stats = await TauriAPI.getAccountPoolStatus();
            set({ statistics: stats, lastUpdated: Date.now() });
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to fetch account statistics' });
          }
        },

        addAccount: async (account) => {
          try {
            await TauriAPI.addAccount(account);
            // 重新获取账号列表
            get().fetchAccounts();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to add account' });
          }
        },

        loginAccount: async (accountId: string) => {
          const currentState = get();
          const newLoggingInAccounts = new Set(currentState.loggingInAccounts);
          newLoggingInAccounts.add(accountId);
          set({ loggingInAccounts: newLoggingInAccounts });

          try {
            await TauriAPI.loginAccount(accountId);
            // 重新获取账号列表以更新状态
            get().fetchAccounts();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to login account' });
          } finally {
            const finalState = get();
            const finalLoggingInAccounts = new Set(finalState.loggingInAccounts);
            finalLoggingInAccounts.delete(accountId);
            set({ loggingInAccounts: finalLoggingInAccounts });
          }
        },

        refreshAccountCookies: async (accountId: string) => {
          try {
            await TauriAPI.refreshAccountCookies(accountId);
            // 重新获取账号列表以更新状态
            get().fetchAccounts();
          } catch (error) {
            set({ error: error instanceof Error ? error.message : 'Failed to refresh account cookies' });
          }
        },

        setError: (error: string | null) => {
          set({ error });
        },

        clearError: () => {
          set({ error: null });
        }
      }),
      {
        name: 'account-store',
        partialize: (state) => ({
          accounts: state.accounts
        })
      }
    ),
    { name: 'account-store' }
  )
);

// 统一导出类型（stores已经单独导出）

// Store 选择器 hooks (可选的便利函数)
export const useNodeInfo = () => useNodeStore(state => state.nodeInfo);
export const useSystemMetrics = () => useNodeStore(state => state.systemMetrics);
export const useNodeLoading = () => useNodeStore(state => state.loading);
export const useNodeError = () => useNodeStore(state => state.error);

export const useTasks = () => useTaskStore(state => state.tasks);
export const useTaskStatistics = () => useTaskStore(state => state.statistics);
export const useTaskQueueInfo = () => useTaskStore(state => state.queueInfo);
export const useTaskLoading = () => useTaskStore(state => state.loading);

export const useProxies = () => useProxyStore(state => state.proxies);
export const useProxyStatistics = () => useProxyStore(state => state.statistics);
export const useProxyLoading = () => useProxyStore(state => state.loading);

export const useAccounts = () => useAccountStore(state => state.accounts);
export const useAccountStatistics = () => useAccountStore(state => state.statistics);
export const useAccountLoading = () => useAccountStore(state => state.loading);
