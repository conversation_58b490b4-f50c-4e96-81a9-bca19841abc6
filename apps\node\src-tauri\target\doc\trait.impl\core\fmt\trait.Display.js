(function() {
    var implementors = Object.fromEntries([["weibo_crawler_node",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Display.html\" title=\"trait core::fmt::Display\">Display</a> for <a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Display.html\" title=\"trait core::fmt::Display\">Display</a> for <a class=\"enum\" href=\"weibo_crawler_node/repository/enum.FilterOperator.html\" title=\"enum weibo_crawler_node::repository::FilterOperator\">FilterOperator</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/fmt/trait.Display.html\" title=\"trait core::fmt::Display\">Display</a> for <a class=\"enum\" href=\"weibo_crawler_node/repository/enum.OrderDirection.html\" title=\"enum weibo_crawler_node::repository::OrderDirection\">OrderDirection</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[932]}