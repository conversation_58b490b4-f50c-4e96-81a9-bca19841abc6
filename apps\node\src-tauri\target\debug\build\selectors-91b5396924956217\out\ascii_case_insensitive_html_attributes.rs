{ static SET: ::phf::Set<&'static str> = ::phf::Set { map: ::phf::Map {
    key: 10121458955350035957,
    disps: &[
        (0, 2),
        (8, 17),
        (1, 5),
        (0, 0),
        (0, 20),
        (0, 3),
        (15, 2),
        (17, 19),
        (8, 9),
        (0, 15),
    ],
    entries: &[
        ("dir", ()),
        ("http-equiv", ()),
        ("rel", ()),
        ("enctype", ()),
        ("align", ()),
        ("accept", ()),
        ("nohref", ()),
        ("lang", ()),
        ("bgcolor", ()),
        ("direction", ()),
        ("valign", ()),
        ("checked", ()),
        ("frame", ()),
        ("link", ()),
        ("accept-charset", ()),
        ("hreflang", ()),
        ("text", ()),
        ("valuetype", ()),
        ("language", ()),
        ("nowrap", ()),
        ("vlink", ()),
        ("disabled", ()),
        ("noshade", ()),
        ("codetype", ()),
        ("defer", ()),
        ("noresize", ()),
        ("target", ()),
        ("scrolling", ()),
        ("rules", ()),
        ("scope", ()),
        ("rev", ()),
        ("media", ()),
        ("method", ()),
        ("charset", ()),
        ("alink", ()),
        ("selected", ()),
        ("multiple", ()),
        ("color", ()),
        ("shape", ()),
        ("type", ()),
        ("clear", ()),
        ("compact", ()),
        ("face", ()),
        ("declare", ()),
        ("axis", ()),
        ("readonly", ()),
    ],
} }; &SET }