<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `repository` mod in crate `weibo_crawler_node`."><title>weibo_crawler_node::repository - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc mod"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Module repository</a></h2><h3><a href="#reexports">Module Items</a></h3><ul class="block"><li><a href="#reexports" title="Re-exports">Re-exports</a></li><li><a href="#modules" title="Modules">Modules</a></li><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li><li><a href="#traits" title="Traits">Traits</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="../index.html">In crate weibo_<wbr>crawler_<wbr>node</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a></div><h1>Module <span>repository</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/repository/mod.rs.html#1-245">Source</a> </span></div><h2 id="reexports" class="section-header">Re-exports<a href="#reexports" class="anchor">§</a></h2><dl class="item-table reexports"><dt id="reexport.TaskRepository"><code>pub use task_repository::<a class="struct" href="task_repository/struct.TaskRepository.html" title="struct weibo_crawler_node::repository::task_repository::TaskRepository">TaskRepository</a>;</code></dt><dt id="reexport.ProxyRepository"><code>pub use proxy_repository::<a class="struct" href="proxy_repository/struct.ProxyRepository.html" title="struct weibo_crawler_node::repository::proxy_repository::ProxyRepository">ProxyRepository</a>;</code></dt><dt id="reexport.AccountRepository"><code>pub use account_repository::<a class="struct" href="account_repository/struct.AccountRepository.html" title="struct weibo_crawler_node::repository::account_repository::AccountRepository">AccountRepository</a>;</code></dt></dl><h2 id="modules" class="section-header">Modules<a href="#modules" class="anchor">§</a></h2><dl class="item-table"><dt><a class="mod" href="account_repository/index.html" title="mod weibo_crawler_node::repository::account_repository">account_<wbr>repository</a></dt><dt><a class="mod" href="proxy_repository/index.html" title="mod weibo_crawler_node::repository::proxy_repository">proxy_<wbr>repository</a></dt><dt><a class="mod" href="task_repository/index.html" title="mod weibo_crawler_node::repository::task_repository">task_<wbr>repository</a></dt></dl><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.Filter.html" title="struct weibo_crawler_node::repository::Filter">Filter</a></dt><dd>过滤条件</dd><dt><a class="struct" href="struct.PaginatedResult.html" title="struct weibo_crawler_node::repository::PaginatedResult">Paginated<wbr>Result</a></dt><dd>分页结果</dd><dt><a class="struct" href="struct.QueryParams.html" title="struct weibo_crawler_node::repository::QueryParams">Query<wbr>Params</a></dt><dd>通用查询参数</dd><dt><a class="struct" href="struct.RepositoryManager.html" title="struct weibo_crawler_node::repository::RepositoryManager">Repository<wbr>Manager</a></dt><dd>仓库管理器 - 统一管理所有仓库</dd></dl><h2 id="enums" class="section-header">Enums<a href="#enums" class="anchor">§</a></h2><dl class="item-table"><dt><a class="enum" href="enum.FilterOperator.html" title="enum weibo_crawler_node::repository::FilterOperator">Filter<wbr>Operator</a></dt><dt><a class="enum" href="enum.FilterValue.html" title="enum weibo_crawler_node::repository::FilterValue">Filter<wbr>Value</a></dt><dt><a class="enum" href="enum.OrderDirection.html" title="enum weibo_crawler_node::repository::OrderDirection">Order<wbr>Direction</a></dt><dd>排序方向</dd></dl><h2 id="traits" class="section-header">Traits<a href="#traits" class="anchor">§</a></h2><dl class="item-table"><dt><a class="trait" href="trait.Auditable.html" title="trait weibo_crawler_node::repository::Auditable">Auditable</a></dt><dd>审计日志特征</dd><dt><a class="trait" href="trait.Cacheable.html" title="trait weibo_crawler_node::repository::Cacheable">Cacheable</a></dt><dd>缓存特征</dd><dt><a class="trait" href="trait.PaginatedRepository.html" title="trait weibo_crawler_node::repository::PaginatedRepository">Paginated<wbr>Repository</a></dt><dd>分页查询特征</dd><dt><a class="trait" href="trait.QueryBuilder.html" title="trait weibo_crawler_node::repository::QueryBuilder">Query<wbr>Builder</a></dt><dd>查询构建器特征</dd><dt><a class="trait" href="trait.Repository.html" title="trait weibo_crawler_node::repository::Repository">Repository</a></dt><dd>基础仓库特征</dd><dt><a class="trait" href="trait.SoftDeletable.html" title="trait weibo_crawler_node::repository::SoftDeletable">Soft<wbr>Deletable</a></dt><dd>软删除特征</dd><dt><a class="trait" href="trait.TransactionManager.html" title="trait weibo_crawler_node::repository::TransactionManager">Transaction<wbr>Manager</a></dt><dd>事务管理特征</dd></dl></section></div></main></body></html>