import { useEffect, useRef, useCallback, useState } from 'react'

/**
 * 定时器 Hook - 安全的 setInterval 封装
 */
export function useInterval(callback: () => void, delay: number | null) {
  const savedCallback = useRef<() => void>()

  // 记住最新的回调函数
  useEffect(() => {
    savedCallback.current = callback
  }, [callback])

  // 设置定时器
  useEffect(() => {
    function tick() {
      savedCallback.current?.()
    }

    if (delay !== null) {
      const id = setInterval(tick, delay)
      return () => clearInterval(id)
    }
  }, [delay])
}

/**
 * 可控制的定时器 Hook
 */
export function useControllableInterval(
  callback: () => void,
  delay: number,
  immediate: boolean = false
) {
  const savedCallback = useRef<() => void>()
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    savedCallback.current = callback
  }, [callback])

  const start = useCallback(() => {
    if (intervalRef.current) return

    if (immediate) {
      savedCallback.current?.()
    }

    intervalRef.current = setInterval(() => {
      savedCallback.current?.()
    }, delay)
  }, [delay, immediate])

  const stop = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }, [])

  const restart = useCallback(() => {
    stop()
    start()
  }, [start, stop])

  useEffect(() => {
    return () => {
      stop()
    }
  }, [stop])

  return {
    start,
    stop,
    restart,
    isRunning: intervalRef.current !== null
  }
}

/**
 * 倒计时 Hook
 */
export function useCountdown(
  initialTime: number,
  onComplete?: () => void
): {
  timeLeft: number
  start: () => void
  stop: () => void
  reset: () => void
  isRunning: boolean
} {
  const [timeLeft, setTimeLeft] = useState(initialTime)
  const [isRunning, setIsRunning] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const start = useCallback(() => {
    if (intervalRef.current || timeLeft <= 0) return

    setIsRunning(true)
    intervalRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          setIsRunning(false)
          if (intervalRef.current) {
            clearInterval(intervalRef.current)
            intervalRef.current = null
          }
          onComplete?.()
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }, [timeLeft, onComplete])

  const stop = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    setIsRunning(false)
  }, [])

  const reset = useCallback(() => {
    stop()
    setTimeLeft(initialTime)
  }, [initialTime, stop])

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  return {
    timeLeft,
    start,
    stop,
    reset,
    isRunning
  }
}

/**
 * 轮询 Hook - 带有错误处理和重试机制
 */
export function usePolling<T>(
  fetchFunction: () => Promise<T>,
  interval: number,
  options: {
    immediate?: boolean
    maxRetries?: number
    onSuccess?: (data: T) => void
    onError?: (error: Error) => void
  } = {}
) {
  const {
    immediate = true,
    maxRetries = 3,
    onSuccess,
    onError
  } = options

  const [data, setData] = useState<T | null>(null)
  const [error, setError] = useState<Error | null>(null)
  const [isPolling, setIsPolling] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const fetchRef = useRef(fetchFunction)

  useEffect(() => {
    fetchRef.current = fetchFunction
  }, [fetchFunction])

  const executeFetch = useCallback(async () => {
    try {
      const result = await fetchRef.current()
      setData(result)
      setError(null)
      setRetryCount(0)
      onSuccess?.(result)
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err))
      setError(error)
      setRetryCount(prev => prev + 1)
      onError?.(error)

      if (retryCount >= maxRetries) {
        stop()
      }
    }
  }, [retryCount, maxRetries, onSuccess, onError])

  const start = useCallback(() => {
    if (intervalRef.current) return

    setIsPolling(true)
    setRetryCount(0)

    if (immediate) {
      executeFetch()
    }

    intervalRef.current = setInterval(executeFetch, interval)
  }, [interval, immediate, executeFetch])

  const stop = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    setIsPolling(false)
  }, [])

  const restart = useCallback(() => {
    stop()
    start()
  }, [start, stop])

  useEffect(() => {
    return () => {
      stop()
    }
  }, [stop])

  return {
    data,
    error,
    isPolling,
    retryCount,
    start,
    stop,
    restart
  }
}
