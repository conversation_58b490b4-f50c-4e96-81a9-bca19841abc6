{"rustc": 1842507548689473721, "features": "[\"rustls-connector\", \"rustls-native-certs\"]", "declared_features": "[\"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-connector\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\"]", "target": 11924301528678068555, "profile": 2241668132362809309, "path": 3463737712322726721, "deps": [[8606274917505247608, "tracing", false, 5492527985859474487], [11096876330329401515, "amq_protocol_uri", false, 8671601887169378153], [17059544261156971941, "tcp_stream", false, 15918053340259454829]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\amq-protocol-tcp-d6eff599e5fb553f\\dep-lib-amq_protocol_tcp", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}