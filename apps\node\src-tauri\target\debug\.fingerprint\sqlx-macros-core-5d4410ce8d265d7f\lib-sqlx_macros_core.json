{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2225463790103693989, "path": 1772539668647958475, "deps": [[530211389790465181, "hex", false, 13185128541112989339], [996810380461694889, "sqlx_core", false, 1949963474824180015], [1441306149310335789, "tempfile", false, 887047401160413171], [2713742371683562785, "syn", false, 9891413255480890497], [3060637413840920116, "proc_macro2", false, 10993881616374897205], [3150220818285335163, "url", false, 12968655790473244891], [3405707034081185165, "dotenvy", false, 6305364608311466902], [3722963349756955755, "once_cell", false, 2565366582676818794], [8045585743974080694, "heck", false, 3367532544243419950], [8569119365930580996, "serde_json", false, 15813735211382407096], [9689903380558560274, "serde", false, 7307778708627749338], [9857275760291862238, "sha2", false, 1393316979248719060], [11838249260056359578, "sqlx_sqlite", false, 17996176676098527383], [12170264697963848012, "either", false, 1829476624730405159], [12944427623413450645, "tokio", false, 3798794974350704788], [17990358020177143287, "quote", false, 8157755205024365785]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-5d4410ce8d265d7f\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}