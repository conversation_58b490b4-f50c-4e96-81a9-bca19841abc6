{"rustc": 1842507548689473721, "features": "[\"client\", \"h2\", \"http1\", \"http2\", \"runtime\", \"socket2\", \"tcp\"]", "declared_features": "[\"__internal_happy_eyeballs_tests\", \"backports\", \"client\", \"default\", \"deprecated\", \"ffi\", \"full\", \"h2\", \"http1\", \"http2\", \"libc\", \"nightly\", \"runtime\", \"server\", \"socket2\", \"stream\", \"tcp\"]", "target": 5299595107718448861, "profile": 2225463790103693989, "path": 883317369159468112, "deps": [[784494742817713399, "tower_service", false, 17354112385031192803], [1569313478171189446, "want", false, 1044775748960835585], [1811549171721445101, "futures_channel", false, 4629572407803545947], [1906322745568073236, "pin_project_lite", false, 1635349456338117859], [4405182208873388884, "http", false, 1887975841554659199], [6163892036024256188, "httparse", false, 1427200709472518731], [6304235478050270880, "httpdate", false, 4558510367724500147], [7620660491849607393, "futures_core", false, 14442838981038780947], [7695812897323945497, "itoa", false, 2811843832342590281], [8606274917505247608, "tracing", false, 5833543133503305489], [8915503303801890683, "http_body", false, 293437560388310251], [10629569228670356391, "futures_util", false, 3242258540150301387], [12614995553916589825, "socket2", false, 17942765444819251113], [12944427623413450645, "tokio", false, 3798794974350704788], [13763625454224483636, "h2", false, 6444612320479226662], [16066129441945555748, "bytes", false, 1952666714230133387]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-6951f1030af97cd2\\dep-lib-hyper", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}