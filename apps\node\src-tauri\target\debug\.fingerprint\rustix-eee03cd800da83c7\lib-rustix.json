{"rustc": 1842507548689473721, "features": "[\"alloc\", \"fs\", \"net\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 12103734316894039792, "path": 7700357978671954893, "deps": [[7263319592666514104, "windows_sys", false, 1652526641323906310], [7896293946984509699, "bitflags", false, 13671712061467703597], [8253628577145923712, "libc_errno", false, 4819671684851950196], [10004434995811528692, "build_script_build", false, 7152800947359110069]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustix-eee03cd800da83c7\\dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}