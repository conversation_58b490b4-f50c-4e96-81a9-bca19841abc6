# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Tauri
src-tauri/target
src-tauri/Cargo.lock

# Database
data/
*.db
*.db-shm
*.db-wal

# Environment
.env
.env.local
.env.production

# Logs
logs/
*.log
