<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\bin\seed.rs`."><title>seed.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="seed" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">seed/</div>seed.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>sqlx::SqlitePool;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>std::env;
<a href=#3 id=3 data-nosnippet>3</a>
<a href=#4 id=4 data-nosnippet>4</a><span class="attr">#[tokio::main]
<a href=#5 id=5 data-nosnippet>5</a></span><span class="kw">async fn </span>main() -&gt; <span class="prelude-ty">Result</span>&lt;(), Box&lt;<span class="kw">dyn </span>std::error::Error&gt;&gt; {
<a href=#6 id=6 data-nosnippet>6</a>    <span class="comment">// 从环境变量或默认值获取数据库URL
<a href=#7 id=7 data-nosnippet>7</a>    </span><span class="kw">let </span>database_url = env::var(<span class="string">"SQLITE_DATABASE_URL"</span>)
<a href=#8 id=8 data-nosnippet>8</a>        .unwrap_or_else(|<span class="kw">_</span>| <span class="string">"sqlite:./data/crawler_node.db"</span>.to_string());
<a href=#9 id=9 data-nosnippet>9</a>
<a href=#10 id=10 data-nosnippet>10</a>    <span class="macro">println!</span>(<span class="string">"连接数据库: {}"</span>, database_url);
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a>    <span class="comment">// 连接数据库
<a href=#13 id=13 data-nosnippet>13</a>    </span><span class="kw">let </span>pool = SqlitePool::connect(<span class="kw-2">&amp;</span>database_url).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#14 id=14 data-nosnippet>14</a>
<a href=#15 id=15 data-nosnippet>15</a>    <span class="macro">println!</span>(<span class="string">"插入种子数据..."</span>);
<a href=#16 id=16 data-nosnippet>16</a>
<a href=#17 id=17 data-nosnippet>17</a>    <span class="comment">// 插入示例代理数据
<a href=#18 id=18 data-nosnippet>18</a>    </span><span class="macro">sqlx::query!</span>(
<a href=#19 id=19 data-nosnippet>19</a>        <span class="string">r#"
<a href=#20 id=20 data-nosnippet>20</a>        INSERT OR IGNORE INTO proxies (proxy_id, host, port, protocol, status, success_count, failure_count)
<a href=#21 id=21 data-nosnippet>21</a>        VALUES 
<a href=#22 id=22 data-nosnippet>22</a>            ('proxy_001', '*************', 8080, 'http', 1, 100, 5),
<a href=#23 id=23 data-nosnippet>23</a>            ('proxy_002', '*************', 8080, 'http', 1, 95, 8),
<a href=#24 id=24 data-nosnippet>24</a>            ('proxy_003', '*************', 3128, 'http', 2, 50, 20),
<a href=#25 id=25 data-nosnippet>25</a>            ('proxy_004', '*************', 1080, 'socks5', 1, 120, 3),
<a href=#26 id=26 data-nosnippet>26</a>            ('proxy_005', '*************', 8080, 'https', 1, 80, 10)
<a href=#27 id=27 data-nosnippet>27</a>        "#
<a href=#28 id=28 data-nosnippet>28</a>    </span>)
<a href=#29 id=29 data-nosnippet>29</a>    .execute(<span class="kw-2">&amp;</span>pool)
<a href=#30 id=30 data-nosnippet>30</a>    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#31 id=31 data-nosnippet>31</a>
<a href=#32 id=32 data-nosnippet>32</a>    <span class="comment">// 插入示例账号数据
<a href=#33 id=33 data-nosnippet>33</a>    </span><span class="macro">sqlx::query!</span>(
<a href=#34 id=34 data-nosnippet>34</a>        <span class="string">r#"
<a href=#35 id=35 data-nosnippet>35</a>        INSERT OR IGNORE INTO accounts (account_id, username, password, status, login_count, risk_score)
<a href=#36 id=36 data-nosnippet>36</a>        VALUES 
<a href=#37 id=37 data-nosnippet>37</a>            ('account_001', 'test_user_001', 'password123', 1, 10, 0.1),
<a href=#38 id=38 data-nosnippet>38</a>            ('account_002', 'test_user_002', 'password456', 1, 15, 0.05),
<a href=#39 id=39 data-nosnippet>39</a>            ('account_003', 'test_user_003', 'password789', 3, 5, 0.8),
<a href=#40 id=40 data-nosnippet>40</a>            ('account_004', 'test_user_004', 'passwordabc', 1, 20, 0.2),
<a href=#41 id=41 data-nosnippet>41</a>            ('account_005', 'test_user_005', 'passworddef', 2, 0, 1.0)
<a href=#42 id=42 data-nosnippet>42</a>        "#
<a href=#43 id=43 data-nosnippet>43</a>    </span>)
<a href=#44 id=44 data-nosnippet>44</a>    .execute(<span class="kw-2">&amp;</span>pool)
<a href=#45 id=45 data-nosnippet>45</a>    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#46 id=46 data-nosnippet>46</a>
<a href=#47 id=47 data-nosnippet>47</a>    <span class="comment">// 插入示例任务数据
<a href=#48 id=48 data-nosnippet>48</a>    </span><span class="macro">sqlx::query!</span>(
<a href=#49 id=49 data-nosnippet>49</a>        <span class="string">r#"
<a href=#50 id=50 data-nosnippet>50</a>        INSERT OR IGNORE INTO crawl_tasks (task_id, task_type, target_url, priority, status, retry_count, max_retries)
<a href=#51 id=51 data-nosnippet>51</a>        VALUES 
<a href=#52 id=52 data-nosnippet>52</a>            ('task_001', 1, 'https://weibo.com/u/**********', 5, 2, 0, 3),
<a href=#53 id=53 data-nosnippet>53</a>            ('task_002', 2, 'https://weibo.com/**********/ABCDEFG', 3, 2, 0, 3),
<a href=#54 id=54 data-nosnippet>54</a>            ('task_003', 3, 'https://weibo.com/ajax/statuses/buildComments', 4, 1, 1, 3),
<a href=#55 id=55 data-nosnippet>55</a>            ('task_004', 4, 'https://weibo.com/p/100808topic', 2, 0, 0, 3),
<a href=#56 id=56 data-nosnippet>56</a>            ('task_005', 1, 'https://weibo.com/u/0987654321', 5, 3, 3, 3)
<a href=#57 id=57 data-nosnippet>57</a>        "#
<a href=#58 id=58 data-nosnippet>58</a>    </span>)
<a href=#59 id=59 data-nosnippet>59</a>    .execute(<span class="kw-2">&amp;</span>pool)
<a href=#60 id=60 data-nosnippet>60</a>    .<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#61 id=61 data-nosnippet>61</a>
<a href=#62 id=62 data-nosnippet>62</a>    <span class="macro">println!</span>(<span class="string">"种子数据插入完成！"</span>);
<a href=#63 id=63 data-nosnippet>63</a>
<a href=#64 id=64 data-nosnippet>64</a>    pool.close().<span class="kw">await</span>;
<a href=#65 id=65 data-nosnippet>65</a>    <span class="prelude-val">Ok</span>(())
<a href=#66 id=66 data-nosnippet>66</a>}</code></pre></div></section></main></body></html>