<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="List of all items in this crate"><title>List of all items in this crate</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../static.files/storage-4e99c027.js"></script><script defer src="../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc mod sys"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h3><a href="#structs">Crate Items</a></h3><ul class="block"><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li><li><a href="#constants" title="Constants">Constants</a></li><li><a href="#statics" title="Statics">Statics</a></li><li><a href="#traits" title="Traits">Traits</a></li><li><a href="#functions" title="Functions">Functions</a></li><li><a href="#types" title="Type Aliases">Type Aliases</a></li></ul></section><div id="rustdoc-modnav"></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><h1>List of all items</h1><h3 id="structs">Structs</h3><ul class="all-items"><li><a href="struct.AppState.html">AppState</a></li><li><a href="account/struct.Account.html">account::Account</a></li><li><a href="account/struct.AccountManager.html">account::AccountManager</a></li><li><a href="account/struct.LoginSession.html">account::LoginSession</a></li><li><a href="browser/struct.BrowserConfig.html">browser::BrowserConfig</a></li><li><a href="browser/struct.BrowserManager.html">browser::BrowserManager</a></li><li><a href="browser/struct.LoginCredentials.html">browser::LoginCredentials</a></li><li><a href="browser/struct.LoginResult.html">browser::LoginResult</a></li><li><a href="browser/struct.UserInfo.html">browser::UserInfo</a></li><li><a href="commands/struct.AccountConfig.html">commands::AccountConfig</a></li><li><a href="commands/struct.AccountPoolStatus.html">commands::AccountPoolStatus</a></li><li><a href="commands/struct.LoginResult.html">commands::LoginResult</a></li><li><a href="commands/struct.NodeStatus.html">commands::NodeStatus</a></li><li><a href="commands/struct.PerformanceStats.html">commands::PerformanceStats</a></li><li><a href="commands/struct.ProxyConfig.html">commands::ProxyConfig</a></li><li><a href="commands/struct.ProxyPoolStatus.html">commands::ProxyPoolStatus</a></li><li><a href="commands/struct.ProxyTestResult.html">commands::ProxyTestResult</a></li><li><a href="commands/struct.SystemMetrics.html">commands::SystemMetrics</a></li><li><a href="commands/struct.TaskQueueStatus.html">commands::TaskQueueStatus</a></li><li><a href="commands/struct.TaskStatistics.html">commands::TaskStatistics</a></li><li><a href="config/struct.AppConfig.html">config::AppConfig</a></li><li><a href="crawler/struct.CrawlResult.html">crawler::CrawlResult</a></li><li><a href="crawler/struct.CrawlTask.html">crawler::CrawlTask</a></li><li><a href="crawler/struct.CrawlerEngine.html">crawler::CrawlerEngine</a></li><li><a href="monitor/struct.NodeHealth.html">monitor::NodeHealth</a></li><li><a href="monitor/struct.PerformanceMetrics.html">monitor::PerformanceMetrics</a></li><li><a href="monitor/struct.SystemMetrics.html">monitor::SystemMetrics</a></li><li><a href="monitor/struct.SystemMonitor.html">monitor::SystemMonitor</a></li><li><a href="notification/struct.Notification.html">notification::Notification</a></li><li><a href="notification/struct.NotificationAction.html">notification::NotificationAction</a></li><li><a href="notification/struct.NotificationManager.html">notification::NotificationManager</a></li><li><a href="proxy/struct.Proxy.html">proxy::Proxy</a></li><li><a href="proxy/struct.ProxyHealthCheck.html">proxy::ProxyHealthCheck</a></li><li><a href="proxy/struct.ProxyManager.html">proxy::ProxyManager</a></li><li><a href="repository/struct.Filter.html">repository::Filter</a></li><li><a href="repository/struct.PaginatedResult.html">repository::PaginatedResult</a></li><li><a href="repository/struct.QueryParams.html">repository::QueryParams</a></li><li><a href="repository/struct.RepositoryManager.html">repository::RepositoryManager</a></li><li><a href="repository/account_repository/struct.AccountRepository.html">repository::account_repository::AccountRepository</a></li><li><a href="repository/proxy_repository/struct.ProxyRepository.html">repository::proxy_repository::ProxyRepository</a></li><li><a href="repository/task_repository/struct.TaskRepository.html">repository::task_repository::TaskRepository</a></li><li><a href="repository/task_repository/struct.TaskStatistics.html">repository::task_repository::TaskStatistics</a></li><li><a href="scheduler/struct.PriorityTask.html">scheduler::PriorityTask</a></li><li><a href="scheduler/struct.TaskScheduler.html">scheduler::TaskScheduler</a></li><li><a href="scheduler/struct.TaskStatistics.html">scheduler::TaskStatistics</a></li><li><a href="service/struct.ServiceConfig.html">service::ServiceConfig</a></li><li><a href="service/struct.ServiceContainer.html">service::ServiceContainer</a></li><li><a href="service/struct.ServiceLifecycleManager.html">service::ServiceLifecycleManager</a></li><li><a href="service/struct.ServiceManager.html">service::ServiceManager</a></li><li><a href="service/struct.ServiceRegistry.html">service::ServiceRegistry</a></li><li><a href="service/account_service/struct.AccountMetrics.html">service::account_service::AccountMetrics</a></li><li><a href="service/account_service/struct.AccountService.html">service::account_service::AccountService</a></li><li><a href="service/crawler_service/struct.CrawlerMetrics.html">service::crawler_service::CrawlerMetrics</a></li><li><a href="service/crawler_service/struct.CrawlerService.html">service::crawler_service::CrawlerService</a></li><li><a href="service/monitor_service/struct.MonitorService.html">service::monitor_service::MonitorService</a></li><li><a href="service/monitor_service/struct.SystemMetrics.html">service::monitor_service::SystemMetrics</a></li><li><a href="service/proxy_service/struct.ProxyMetrics.html">service::proxy_service::ProxyMetrics</a></li><li><a href="service/proxy_service/struct.ProxyService.html">service::proxy_service::ProxyService</a></li><li><a href="service/task_service/struct.CreateTaskRequest.html">service::task_service::CreateTaskRequest</a></li><li><a href="service/task_service/struct.TaskMetrics.html">service::task_service::TaskMetrics</a></li><li><a href="service/task_service/struct.TaskQuery.html">service::task_service::TaskQuery</a></li><li><a href="service/task_service/struct.TaskService.html">service::task_service::TaskService</a></li><li><a href="service/task_service/struct.UpdateTaskRequest.html">service::task_service::UpdateTaskRequest</a></li><li><a href="storage/struct.AccountLog.html">storage::AccountLog</a></li><li><a href="storage/struct.AccountPoolStatus.html">storage::AccountPoolStatus</a></li><li><a href="storage/struct.AccountRecord.html">storage::AccountRecord</a></li><li><a href="storage/struct.ErrorLog.html">storage::ErrorLog</a></li><li><a href="storage/struct.ProxyPoolStatus.html">storage::ProxyPoolStatus</a></li><li><a href="storage/struct.ProxyRecord.html">storage::ProxyRecord</a></li><li><a href="storage/struct.ProxyUsage.html">storage::ProxyUsage</a></li><li><a href="storage/struct.StorageManager.html">storage::StorageManager</a></li><li><a href="storage/struct.SystemMetrics.html">storage::SystemMetrics</a></li><li><a href="storage/struct.TaskLog.html">storage::TaskLog</a></li><li><a href="storage/struct.TaskRecord.html">storage::TaskRecord</a></li><li><a href="storage/struct.TaskResult.html">storage::TaskResult</a></li><li><a href="storage/struct.TaskStatistics.html">storage::TaskStatistics</a></li><li><a href="updater/struct.UpdateInfo.html">updater::UpdateInfo</a></li><li><a href="updater/struct.UpdateManager.html">updater::UpdateManager</a></li><li><a href="updater/struct.UpdateStatus.html">updater::UpdateStatus</a></li><li><a href="window_manager/struct.WindowConfig.html">window_manager::WindowConfig</a></li><li><a href="window_manager/struct.WindowInfo.html">window_manager::WindowInfo</a></li><li><a href="window_manager/struct.WindowManager.html">window_manager::WindowManager</a></li></ul><h3 id="enums">Enums</h3><ul class="all-items"><li><a href="account/enum.AccountStatus.html">account::AccountStatus</a></li><li><a href="crawler/enum.TaskType.html">crawler::TaskType</a></li><li><a href="error/enum.AppError.html">error::AppError</a></li><li><a href="notification/enum.ActionType.html">notification::ActionType</a></li><li><a href="notification/enum.NotificationType.html">notification::NotificationType</a></li><li><a href="proxy/enum.ProxyProtocol.html">proxy::ProxyProtocol</a></li><li><a href="proxy/enum.ProxyStatus.html">proxy::ProxyStatus</a></li><li><a href="repository/enum.FilterOperator.html">repository::FilterOperator</a></li><li><a href="repository/enum.FilterValue.html">repository::FilterValue</a></li><li><a href="repository/enum.OrderDirection.html">repository::OrderDirection</a></li><li><a href="service/enum.HealthStatus.html">service::HealthStatus</a></li><li><a href="service/enum.ServiceEvent.html">service::ServiceEvent</a></li></ul><h3 id="traits">Traits</h3><ul class="all-items"><li><a href="repository/trait.Auditable.html">repository::Auditable</a></li><li><a href="repository/trait.Cacheable.html">repository::Cacheable</a></li><li><a href="repository/trait.PaginatedRepository.html">repository::PaginatedRepository</a></li><li><a href="repository/trait.QueryBuilder.html">repository::QueryBuilder</a></li><li><a href="repository/trait.Repository.html">repository::Repository</a></li><li><a href="repository/trait.SoftDeletable.html">repository::SoftDeletable</a></li><li><a href="repository/trait.TransactionManager.html">repository::TransactionManager</a></li><li><a href="service/trait.ConfigurableService.html">service::ConfigurableService</a></li><li><a href="service/trait.EventListener.html">service::EventListener</a></li><li><a href="service/trait.MonitorableService.html">service::MonitorableService</a></li><li><a href="service/trait.Service.html">service::Service</a></li><li><a href="service/trait.StartableService.html">service::StartableService</a></li></ul><h3 id="functions">Functions</h3><ul class="all-items"><li><a href="browser/fn.get_browser_manager.html">browser::get_browser_manager</a></li><li><a href="browser/fn.initialize_browser.html">browser::initialize_browser</a></li><li><a href="browser/fn.shutdown_browser.html">browser::shutdown_browser</a></li><li><a href="browser/weibo_login/fn.extract_login_info.html">browser::weibo_login::extract_login_info</a></li><li><a href="browser/weibo_login/fn.extract_user_info.html">browser::weibo_login::extract_user_info</a></li><li><a href="browser/weibo_login/fn.get_cookies_string.html">browser::weibo_login::get_cookies_string</a></li><li><a href="browser/weibo_login/fn.get_login_error_message.html">browser::weibo_login::get_login_error_message</a></li><li><a href="browser/weibo_login/fn.is_already_logged_in.html">browser::weibo_login::is_already_logged_in</a></li><li><a href="browser/weibo_login/fn.is_captcha_required.html">browser::weibo_login::is_captcha_required</a></li><li><a href="browser/weibo_login/fn.is_login_successful.html">browser::weibo_login::is_login_successful</a></li><li><a href="browser/weibo_login/fn.login_with_playwright.html">browser::weibo_login::login_with_playwright</a></li><li><a href="browser/weibo_login/fn.perform_login.html">browser::weibo_login::perform_login</a></li><li><a href="fn.cleanup_on_exit.html">cleanup_on_exit</a></li><li><a href="commands/fn.add_account.html">commands::add_account</a></li><li><a href="commands/fn.add_proxy.html">commands::add_proxy</a></li><li><a href="commands/fn.get_account_pool_status.html">commands::get_account_pool_status</a></li><li><a href="commands/fn.get_browser_config.html">commands::get_browser_config</a></li><li><a href="commands/fn.get_config.html">commands::get_config</a></li><li><a href="commands/fn.get_node_status.html">commands::get_node_status</a></li><li><a href="commands/fn.get_performance_stats.html">commands::get_performance_stats</a></li><li><a href="commands/fn.get_proxy_pool_status.html">commands::get_proxy_pool_status</a></li><li><a href="commands/fn.get_system_metrics.html">commands::get_system_metrics</a></li><li><a href="commands/fn.get_task_queue_status.html">commands::get_task_queue_status</a></li><li><a href="commands/fn.get_task_statistics.html">commands::get_task_statistics</a></li><li><a href="commands/fn.login_account.html">commands::login_account</a></li><li><a href="commands/fn.login_weibo.html">commands::login_weibo</a></li><li><a href="commands/fn.refresh_account_cookies.html">commands::refresh_account_cookies</a></li><li><a href="commands/fn.remove_proxy.html">commands::remove_proxy</a></li><li><a href="commands/fn.restart_crawler.html">commands::restart_crawler</a></li><li><a href="commands/fn.start_crawler.html">commands::start_crawler</a></li><li><a href="commands/fn.start_task_processing.html">commands::start_task_processing</a></li><li><a href="commands/fn.stop_crawler.html">commands::stop_crawler</a></li><li><a href="commands/fn.stop_task_processing.html">commands::stop_task_processing</a></li><li><a href="commands/fn.test_browser.html">commands::test_browser</a></li><li><a href="commands/fn.test_proxy.html">commands::test_proxy</a></li><li><a href="commands/fn.update_browser_config.html">commands::update_browser_config</a></li><li><a href="commands/fn.update_config.html">commands::update_config</a></li><li><a href="fn.handle_window_event.html">handle_window_event</a></li><li><a href="fn.initialize_system_components.html">initialize_system_components</a></li><li><a href="fn.main.html">main</a></li><li><a href="notification/fn.clear_all_notifications.html">notification::clear_all_notifications</a></li><li><a href="notification/fn.get_notifications.html">notification::get_notifications</a></li><li><a href="notification/fn.get_unread_notifications.html">notification::get_unread_notifications</a></li><li><a href="notification/fn.handle_notification_action.html">notification::handle_notification_action</a></li><li><a href="notification/fn.mark_notification_read.html">notification::mark_notification_read</a></li><li><a href="notification/fn.remove_notification.html">notification::remove_notification</a></li><li><a href="fn.setup_app_initialization.html">setup_app_initialization</a></li><li><a href="fn.show_about_dialog.html">show_about_dialog</a></li><li><a href="updater/fn.check_for_updates.html">updater::check_for_updates</a></li><li><a href="updater/fn.download_update.html">updater::download_update</a></li><li><a href="updater/fn.get_update_status.html">updater::get_update_status</a></li><li><a href="window_manager/fn.close_window.html">window_manager::close_window</a></li><li><a href="window_manager/fn.create_window.html">window_manager::create_window</a></li><li><a href="window_manager/fn.get_all_windows.html">window_manager::get_all_windows</a></li><li><a href="window_manager/fn.get_window_info.html">window_manager::get_window_info</a></li><li><a href="window_manager/fn.hide_window.html">window_manager::hide_window</a></li><li><a href="window_manager/fn.show_window.html">window_manager::show_window</a></li></ul><h3 id="types">Type Aliases</h3><ul class="all-items"><li><a href="error/type.Result.html">error::Result</a></li></ul><h3 id="statics">Statics</h3><ul class="all-items"><li><a href="browser/static.BROWSER_MANAGER.html">browser::BROWSER_MANAGER</a></li></ul><h3 id="constants">Constants</h3><ul class="all-items"><li><a href="browser/weibo_login/constant.LOGIN_TIMEOUT.html">browser::weibo_login::LOGIN_TIMEOUT</a></li><li><a href="browser/weibo_login/constant.WEIBO_LOGIN_URL.html">browser::weibo_login::WEIBO_LOGIN_URL</a></li></ul></section></div></main></body></html>