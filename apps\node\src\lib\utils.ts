import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatBytes(bytes: number, decimals = 2) {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}

export function formatDuration(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`;
  } else {
    return `${secs}秒`;
  }
}

export function formatNumber(num: number) {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + "M";
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + "K";
  }
  return num.toString();
}

export function getStatusColor(status: string) {
  switch (status.toLowerCase()) {
    case "online":
    case "active":
    case "healthy":
    case "success":
      return "bg-green-500";
    case "offline":
    case "inactive":
    case "failed":
    case "error":
      return "bg-red-500";
    case "warning":
    case "maintenance":
    case "pending":
      return "bg-yellow-500";
    case "testing":
    case "processing":
      return "bg-blue-500";
    default:
      return "bg-gray-500";
  }
}

export function getStatusText(status: string) {
  switch (status.toLowerCase()) {
    case "online":
      return "在线";
    case "offline":
      return "离线";
    case "active":
      return "活跃";
    case "inactive":
      return "非活跃";
    case "healthy":
      return "健康";
    case "unhealthy":
      return "不健康";
    case "success":
      return "成功";
    case "failed":
      return "失败";
    case "error":
      return "错误";
    case "warning":
      return "警告";
    case "maintenance":
      return "维护中";
    case "pending":
      return "等待中";
    case "testing":
      return "测试中";
    case "processing":
      return "处理中";
    default:
      return status;
  }
}

// 测试工具函数
export const testUtils = {
  // Mock数据
  mockNodeStatus: {
    node_id: 'test-node-001',
    node_name: 'Test Node',
    status: 'online',
    uptime: 3600,
    cpu_usage: 45.2,
    memory_usage: 67.8,
    disk_usage: 23.1,
    active_tasks: 5,
    total_tasks: 100,
    success_rate: 95.5,
  },

  mockTaskQueueStatus: {
    pending_tasks: 10,
    running_tasks: 3,
    completed_tasks: 87,
    failed_tasks: 5,
    queue_length: 13,
    processing_speed: 2.5,
  },

  mockProxyPoolStatus: {
    total_proxies: 50,
    active_proxies: 45,
    healthy_proxies: 42,
    average_response_time: 250,
    success_rate: 84.0,
  },

  mockAccountPoolStatus: {
    total_accounts: 20,
    active_accounts: 18,
    logged_in_accounts: 15,
    healthy_accounts: 17,
    average_risk_score: 2.3,
  },

  // 等待异步操作完成的辅助函数
  waitFor: (condition: () => boolean, timeout = 1000): Promise<void> => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const check = () => {
        if (condition()) {
          resolve();
        } else if (Date.now() - startTime > timeout) {
          reject(new Error('Timeout waiting for condition'));
        } else {
          setTimeout(check, 10);
        }
      };
      check();
    });
  },

  // 模拟延迟的辅助函数
  delay: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  // 创建Mock Store的辅助函数
  createMockStore: <T>(initialState: T) => {
    let state = initialState;
    const listeners = new Set<() => void>();

    return {
      getState: () => state,
      setState: (newState: Partial<T>) => {
        state = { ...state, ...newState };
        listeners.forEach(listener => listener());
      },
      subscribe: (listener: () => void) => {
        listeners.add(listener);
        return () => listeners.delete(listener);
      },
      destroy: () => listeners.clear(),
    };
  },

  // 测试示例 (可以复制到 .test.ts 文件中)
  storeTestExample: `
// Store单元测试示例 - 复制到 src/lib/stores.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useNodeStore, TauriAPI } from './tauri';
import { testUtils } from './utils';

// Mock Tauri API
vi.mock('@tauri-apps/api/tauri', () => ({
  invoke: vi.fn(),
}));

describe('NodeStore', () => {
  beforeEach(() => {
    useNodeStore.getState().clearError();
    vi.clearAllMocks();
  });

  it('should initialize with default state', () => {
    const state = useNodeStore.getState();
    expect(state.loading).toBe(false);
    expect(state.error).toBe(null);
    expect(state.nodeInfo).toBe(null);
  });

  it('should handle fetchNodeStatus success', async () => {
    const mockResponse = testUtils.mockNodeStatus;
    vi.spyOn(TauriAPI, 'getNodeStatus').mockResolvedValue(mockResponse);

    await useNodeStore.getState().fetchNodeStatus();

    const newState = useNodeStore.getState();
    expect(newState.loading).toBe(false);
    expect(newState.error).toBe(null);
    expect(newState.nodeInfo?.nodeId).toBe(mockResponse.node_id);
  });
});
  `,

  componentTestExample: `
// 组件测试示例 - 复制到 src/components/Dashboard.test.tsx
import { describe, it, expect, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { Dashboard } from '../pages/Dashboard';
import { testUtils } from '../lib/utils';

// Mock stores
vi.mock('../lib/tauri', () => ({
  useNodeStore: () => ({
    nodeInfo: testUtils.mockNodeStatus,
    systemMetrics: { cpuUsage: 45.2, memoryUsage: 67.8 },
    loading: false,
    fetchNodeStatus: vi.fn(),
  }),
  useTaskStore: () => ({
    queueInfo: testUtils.mockTaskQueueStatus,
    loading: false,
    fetchQueueStatus: vi.fn(),
  }),
}));

describe('Dashboard', () => {
  it('should render dashboard with node status', async () => {
    render(<Dashboard />);

    await waitFor(() => {
      expect(screen.getByText('活跃任务')).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument();
    });
  });
});
  `,
};
