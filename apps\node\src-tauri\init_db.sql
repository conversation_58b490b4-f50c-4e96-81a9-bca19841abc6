-- 初始化数据库脚本
-- 创建爬取任务表
CREATE TABLE IF NOT EXISTS crawl_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT UNIQUE NOT NULL,
    task_type INTEGER NOT NULL,
    target_url TEXT NOT NULL,
    priority INTEGER NOT NULL DEFAULT 5,
    status INTEGER NOT NULL DEFAULT 0,
    retry_count INTEGER NOT NULL DEFAULT 0,
    max_retries INTEGER NOT NULL DEFAULT 3,
    metadata TEXT,
    assigned_at DATETIME,
    started_at DATETIME,
    completed_at DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建代理表
CREATE TABLE IF NOT EXISTS proxies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proxy_id TEXT UNIQUE NOT NULL,
    host TEXT NOT NULL,
    port INTEGER NOT NULL,
    protocol TEXT NOT NULL,
    username TEXT,
    password TEXT,
    country TEXT,
    provider TEXT,
    status INTEGER NOT NULL DEFAULT 1,
    success_count INTEGER NOT NULL DEFAULT 0,
    failure_count INTEGER NOT NULL DEFAULT 0,
    response_time INTEGER,
    last_used DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建账号表
CREATE TABLE IF NOT EXISTS accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    status INTEGER NOT NULL DEFAULT 1,
    login_count INTEGER NOT NULL DEFAULT 0,
    last_login DATETIME,
    last_activity DATETIME,
    risk_score REAL NOT NULL DEFAULT 0.0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_crawl_tasks_task_id ON crawl_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_crawl_tasks_status ON crawl_tasks(status);
CREATE INDEX IF NOT EXISTS idx_crawl_tasks_priority ON crawl_tasks(priority);
CREATE INDEX IF NOT EXISTS idx_crawl_tasks_created_at ON crawl_tasks(created_at);

CREATE INDEX IF NOT EXISTS idx_proxies_proxy_id ON proxies(proxy_id);
CREATE INDEX IF NOT EXISTS idx_proxies_status ON proxies(status);
CREATE INDEX IF NOT EXISTS idx_proxies_last_used ON proxies(last_used);

CREATE INDEX IF NOT EXISTS idx_accounts_account_id ON accounts(account_id);
CREATE INDEX IF NOT EXISTS idx_accounts_username ON accounts(username);
CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status);
CREATE INDEX IF NOT EXISTS idx_accounts_last_login ON accounts(last_login);
CREATE INDEX IF NOT EXISTS idx_accounts_risk_score ON accounts(risk_score);

-- 插入一些测试数据
INSERT OR IGNORE INTO crawl_tasks (task_id, task_type, target_url, priority, status) VALUES
('task_001', 1, 'https://weibo.com/u/**********', 5, 0),
('task_002', 2, 'https://weibo.com/**********/ABCDEFG', 3, 0);

INSERT OR IGNORE INTO proxies (proxy_id, host, port, protocol, status) VALUES
('proxy_001', '127.0.0.1', 8080, 'http', 1),
('proxy_002', '127.0.0.1', 8081, 'http', 1);

INSERT OR IGNORE INTO accounts (account_id, username, password, status) VALUES
('account_001', 'test_user_001', 'password123', 1),
('account_002', 'test_user_002', 'password456', 1);
