{"rustc": 1842507548689473721, "features": "[\"acl\", \"aio\", \"async-trait\", \"bytes\", \"default\", \"futures-util\", \"geospatial\", \"keep-alive\", \"pin-project-lite\", \"script\", \"sha1_smol\", \"socket2\", \"streams\", \"tokio\", \"tokio-comp\", \"tokio-util\"]", "declared_features": "[\"acl\", \"ahash\", \"aio\", \"arc-swap\", \"async-native-tls\", \"async-std\", \"async-std-comp\", \"async-std-native-tls-comp\", \"async-std-rustls-comp\", \"async-std-tls-comp\", \"async-trait\", \"bytes\", \"cluster\", \"cluster-async\", \"connection-manager\", \"crc16\", \"default\", \"futures\", \"futures-rustls\", \"futures-util\", \"geospatial\", \"json\", \"keep-alive\", \"log\", \"native-tls\", \"pin-project-lite\", \"r2d2\", \"rand\", \"rustls\", \"rustls-native-certs\", \"rustls-pemfile\", \"rustls-webpki\", \"script\", \"sentinel\", \"serde\", \"serde_json\", \"sha1_smol\", \"socket2\", \"streams\", \"tcp_nodelay\", \"tls\", \"tls-native-tls\", \"tls-rustls\", \"tls-rustls-insecure\", \"tls-rustls-webpki-roots\", \"tokio\", \"tokio-comp\", \"tokio-native-tls\", \"tokio-native-tls-comp\", \"tokio-retry\", \"tokio-rustls\", \"tokio-rustls-comp\", \"tokio-util\", \"webpki-roots\"]", "target": 5936222214539778515, "profile": 2241668132362809309, "path": 819742389124406106, "deps": [[40386456601120721, "percent_encoding", false, 4385803709286603540], [917570942013697716, "sha1_smol", false, 10811327255006160114], [1211321333142909612, "socket2", false, 30090749911751216], [1216309103264968120, "ryu", false, 7759414026013604877], [1288403060204016458, "tokio_util", false, 5339149626986303070], [1906322745568073236, "pin_project_lite", false, 8108972857832786586], [3150220818285335163, "url", false, 8909501939063229777], [7695812897323945497, "itoa", false, 5344268009995081169], [10629569228670356391, "futures_util", false, 2925717846322628741], [11946729385090170470, "async_trait", false, 1405635847080043314], [12944427623413450645, "tokio", false, 5789438060576990513], [16066129441945555748, "bytes", false, 2398977389649674023], [17915660048393766120, "combine", false, 17653301120464881142]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\redis-118cec8439a386ed\\dep-lib-redis", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}