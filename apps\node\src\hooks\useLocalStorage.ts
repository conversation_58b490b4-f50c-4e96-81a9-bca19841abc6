import { useState, useEffect, useCallback } from 'react'

/**
 * localStorage Hook - 同步状态与 localStorage
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  // 获取初始值
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  // 设置值的函数
  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        // 允许传入函数来更新状态
        const valueToStore = value instanceof Function ? value(storedValue) : value
        setStoredValue(valueToStore)
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      } catch (error) {
        console.warn(`Error setting localStorage key "${key}":`, error)
      }
    },
    [key, storedValue]
  )

  // 删除值的函数
  const removeValue = useCallback(() => {
    try {
      window.localStorage.removeItem(key)
      setStoredValue(initialValue)
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error)
    }
  }, [key, initialValue])

  return [storedValue, setValue, removeValue]
}

/**
 * sessionStorage Hook - 同步状态与 sessionStorage
 */
export function useSessionStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.sessionStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn(`Error reading sessionStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue = useCallback(
    (value: T | ((val: T) => T)) => {
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value
        setStoredValue(valueToStore)
        window.sessionStorage.setItem(key, JSON.stringify(valueToStore))
      } catch (error) {
        console.warn(`Error setting sessionStorage key "${key}":`, error)
      }
    },
    [key, storedValue]
  )

  const removeValue = useCallback(() => {
    try {
      window.sessionStorage.removeItem(key)
      setStoredValue(initialValue)
    } catch (error) {
      console.warn(`Error removing sessionStorage key "${key}":`, error)
    }
  }, [key, initialValue])

  return [storedValue, setValue, removeValue]
}

/**
 * 持久化状态 Hook - 自动选择存储方式
 */
export function usePersistentState<T>(
  key: string,
  initialValue: T,
  storage: 'local' | 'session' = 'local'
): [T, (value: T | ((val: T) => T)) => void, () => void] {
  if (storage === 'session') {
    return useSessionStorage(key, initialValue)
  }
  return useLocalStorage(key, initialValue)
}

/**
 * 存储监听 Hook - 监听存储变化
 */
export function useStorageListener<T>(
  key: string,
  storage: 'local' | 'session' = 'local'
): T | null {
  const [value, setValue] = useState<T | null>(null)

  useEffect(() => {
    // 初始化值
    const storageObj = storage === 'local' ? localStorage : sessionStorage
    try {
      const item = storageObj.getItem(key)
      setValue(item ? JSON.parse(item) : null)
    } catch (error) {
      console.warn(`Error reading ${storage}Storage key "${key}":`, error)
      setValue(null)
    }

    // 监听存储变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.storageArea === storageObj) {
        try {
          setValue(e.newValue ? JSON.parse(e.newValue) : null)
        } catch (error) {
          console.warn(`Error parsing ${storage}Storage value for key "${key}":`, error)
          setValue(null)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [key, storage])

  return value
}

/**
 * 存储容量检查 Hook
 */
export function useStorageCapacity(storage: 'local' | 'session' = 'local') {
  const [capacity, setCapacity] = useState<{
    used: number
    total: number
    available: number
    percentage: number
  } | null>(null)

  const checkCapacity = useCallback(() => {
    try {
      const storageObj = storage === 'local' ? localStorage : sessionStorage
      let used = 0
      
      // 计算已使用空间
      for (let key in storageObj) {
        if (storageObj.hasOwnProperty(key)) {
          used += storageObj[key].length + key.length
        }
      }

      // 估算总容量（通常为 5-10MB）
      const total = 5 * 1024 * 1024 // 5MB
      const available = total - used
      const percentage = (used / total) * 100

      setCapacity({
        used,
        total,
        available,
        percentage
      })
    } catch (error) {
      console.warn(`Error checking ${storage}Storage capacity:`, error)
      setCapacity(null)
    }
  }, [storage])

  useEffect(() => {
    checkCapacity()
  }, [checkCapacity])

  return {
    capacity,
    checkCapacity
  }
}
