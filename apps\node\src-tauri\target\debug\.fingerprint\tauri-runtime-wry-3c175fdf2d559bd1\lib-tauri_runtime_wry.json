{"rustc": 1842507548689473721, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 676420316161376534, "deps": [[4381063397040571828, "webview2_com", false, 2334546348545801766], [7653476968652377684, "windows", false, 4435937259384173537], [8292277814562636972, "tauri_utils", false, 11396416046195520354], [8319709847752024821, "uuid", false, 9442600809944700742], [8391357152270261188, "wry", false, 16400862234606691890], [11693073011723388840, "raw_window_handle", false, 2576869162297233201], [13208667028893622512, "rand", false, 5662173108048055673], [14162324460024849578, "tauri_runtime", false, 1129283759608522050], [16228250612241359704, "build_script_build", false, 13734380951236136779]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-3c175fdf2d559bd1\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}