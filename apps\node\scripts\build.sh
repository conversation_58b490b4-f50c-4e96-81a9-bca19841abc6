#!/bin/bash

# 微博爬虫节点构建脚本

set -e

echo "🔨 开始构建微博爬虫节点..."

# 清理之前的构建
echo "🧹 清理构建目录..."
rm -rf dist
rm -rf src-tauri/target/release

# 安装依赖
echo "📦 安装依赖..."
npm ci

# 构建前端
echo "🎨 构建前端应用..."
npm run build

# 构建Tauri应用
echo "⚙️ 构建Tauri应用..."
npm run tauri:build

echo "✅ 构建完成！"
echo ""
echo "📦 构建产物位置："
echo "- 前端: ./dist/"
echo "- 桌面应用: ./src-tauri/target/release/"
echo "- 安装包: ./src-tauri/target/release/bundle/"
