{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 15657897354478470176, "path": 1859583268020246255, "deps": [[5103565458935487, "futures_io", false, 487272345117200944], [40386456601120721, "percent_encoding", false, 14003801311530373528], [530211389790465181, "hex", false, 12229943825153590659], [788558663644978524, "crossbeam_queue", false, 4129960920246683090], [966925859616469517, "ahash", false, 14655466138859647267], [1162433738665300155, "crc", false, 14482875853779330492], [1464803193346256239, "event_listener", false, 11443230150984791371], [1811549171721445101, "futures_channel", false, 3188382322166671117], [3150220818285335163, "url", false, 1746054398145793718], [3405817021026194662, "hashlink", false, 6818393205007016669], [3646857438214563691, "futures_intrusive", false, 2384819550657298957], [3666196340704888985, "smallvec", false, 9984793362098171202], [3712811570531045576, "byteorder", false, 7284596742539608705], [3722963349756955755, "once_cell", false, 3092077161891181902], [5986029879202738730, "log", false, 13216658706447473378], [6493259146304816786, "indexmap", false, 1324243151160575700], [7620660491849607393, "futures_core", false, 6897113057850168815], [8008191657135824715, "thiserror", false, 5374037758448816721], [8319709847752024821, "uuid", false, 9442600809944700742], [8569119365930580996, "serde_json", false, 3777298738341353814], [8606274917505247608, "tracing", false, 17646992783032211729], [9689903380558560274, "serde", false, 4389908846715814937], [9857275760291862238, "sha2", false, 13753977988471142452], [9897246384292347999, "chrono", false, 8524163041540453310], [10629569228670356391, "futures_util", false, 15644822958053016495], [10862088793507253106, "sqlformat", false, 14447607220010169462], [11295624341523567602, "rustls", false, 11828993749138274798], [12170264697963848012, "either", false, 1516669039385124179], [12944427623413450645, "tokio", false, 13941386680418085679], [15932120279885307830, "memchr", false, 13280053228737166842], [16066129441945555748, "bytes", false, 256859762049119040], [16311359161338405624, "rustls_pemfile", false, 17295842003052255707], [16973251432615581304, "tokio_stream", false, 2949582084737634361], [17106256174509013259, "atoi", false, 16650541712205897126], [17605717126308396068, "paste", false, 265303868298634720], [17652733826348741533, "webpki_roots", false, 17944470366620595954]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-cd2b1b90a428a000\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}