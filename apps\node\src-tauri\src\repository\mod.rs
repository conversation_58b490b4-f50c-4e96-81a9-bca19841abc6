use crate::error::Result;
use async_trait::async_trait;
use sqlx::SqlitePool;
use std::sync::Arc;

pub mod task_repository;
pub mod proxy_repository;
pub mod account_repository;

pub use task_repository::TaskRepository;
pub use proxy_repository::ProxyRepository;
pub use account_repository::AccountRepository;

/// 基础仓库特征
#[async_trait]
pub trait Repository<T, ID> {
    async fn find_by_id(&self, id: ID) -> Result<Option<T>>;
    async fn find_all(&self) -> Result<Vec<T>>;
    async fn create(&self, entity: T) -> Result<T>;
    async fn update(&self, id: ID, entity: T) -> Result<T>;
    async fn delete(&self, id: ID) -> Result<()>;
    async fn count(&self) -> Result<i64>;
}

/// 分页查询特征
#[async_trait]
pub trait PaginatedRepository<T> {
    async fn find_paginated(&self, page: u32, limit: u32) -> Result<PaginatedResult<T>>;
}

/// 分页结果
#[derive(Debug, Clone)]
pub struct PaginatedResult<T> {
    pub items: Vec<T>,
    pub total: i64,
    pub page: u32,
    pub limit: u32,
    pub total_pages: u32,
}

impl<T> PaginatedResult<T> {
    pub fn new(items: Vec<T>, total: i64, page: u32, limit: u32) -> Self {
        let total_pages = if limit > 0 {
            ((total as f64) / (limit as f64)).ceil() as u32
        } else {
            0
        };

        Self {
            items,
            total,
            page,
            limit,
            total_pages,
        }
    }
}

/// 仓库管理器 - 统一管理所有仓库
pub struct RepositoryManager {
    pub task: TaskRepository,
    pub proxy: ProxyRepository,
    pub account: AccountRepository,
}

impl RepositoryManager {
    pub fn new(pool: Arc<SqlitePool>) -> Self {
        Self {
            task: TaskRepository::new(pool.clone()),
            proxy: ProxyRepository::new(pool.clone()),
            account: AccountRepository::new(pool),
        }
    }
}

/// 查询构建器特征
pub trait QueryBuilder {
    type Query;
    
    fn new() -> Self;
    fn filter(self, field: &str, value: &str) -> Self;
    fn order_by(self, field: &str, direction: OrderDirection) -> Self;
    fn limit(self, limit: u32) -> Self;
    fn offset(self, offset: u32) -> Self;
    fn build(self) -> Self::Query;
}

/// 排序方向
#[derive(Debug, Clone, Copy)]
pub enum OrderDirection {
    Asc,
    Desc,
}

impl std::fmt::Display for OrderDirection {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            OrderDirection::Asc => write!(f, "ASC"),
            OrderDirection::Desc => write!(f, "DESC"),
        }
    }
}

/// 过滤条件
#[derive(Debug, Clone)]
pub struct Filter {
    pub field: String,
    pub operator: FilterOperator,
    pub value: FilterValue,
}

#[derive(Debug, Clone)]
pub enum FilterOperator {
    Equal,
    NotEqual,
    GreaterThan,
    GreaterThanOrEqual,
    LessThan,
    LessThanOrEqual,
    Like,
    In,
    NotIn,
    IsNull,
    IsNotNull,
}

#[derive(Debug, Clone)]
pub enum FilterValue {
    String(String),
    Integer(i64),
    Float(f64),
    Boolean(bool),
    Array(Vec<String>),
    Null,
}

impl std::fmt::Display for FilterOperator {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            FilterOperator::Equal => write!(f, "="),
            FilterOperator::NotEqual => write!(f, "!="),
            FilterOperator::GreaterThan => write!(f, ">"),
            FilterOperator::GreaterThanOrEqual => write!(f, ">="),
            FilterOperator::LessThan => write!(f, "<"),
            FilterOperator::LessThanOrEqual => write!(f, "<="),
            FilterOperator::Like => write!(f, "LIKE"),
            FilterOperator::In => write!(f, "IN"),
            FilterOperator::NotIn => write!(f, "NOT IN"),
            FilterOperator::IsNull => write!(f, "IS NULL"),
            FilterOperator::IsNotNull => write!(f, "IS NOT NULL"),
        }
    }
}

/// 通用查询参数
#[derive(Debug, Clone)]
pub struct QueryParams {
    pub filters: Vec<Filter>,
    pub order_by: Option<(String, OrderDirection)>,
    pub limit: Option<u32>,
    pub offset: Option<u32>,
}

impl Default for QueryParams {
    fn default() -> Self {
        Self {
            filters: Vec::new(),
            order_by: None,
            limit: None,
            offset: None,
        }
    }
}

impl QueryParams {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn filter(mut self, field: &str, operator: FilterOperator, value: FilterValue) -> Self {
        self.filters.push(Filter {
            field: field.to_string(),
            operator,
            value,
        });
        self
    }

    pub fn order_by(mut self, field: &str, direction: OrderDirection) -> Self {
        self.order_by = Some((field.to_string(), direction));
        self
    }

    pub fn limit(mut self, limit: u32) -> Self {
        self.limit = Some(limit);
        self
    }

    pub fn offset(mut self, offset: u32) -> Self {
        self.offset = Some(offset);
        self
    }

    pub fn page(mut self, page: u32, limit: u32) -> Self {
        self.limit = Some(limit);
        self.offset = Some((page.saturating_sub(1)) * limit);
        self
    }
}

/// 事务管理特征
#[async_trait]
pub trait TransactionManager {
    type Transaction;
    
    async fn begin_transaction(&self) -> Result<Self::Transaction>;
    async fn commit_transaction(&self, tx: Self::Transaction) -> Result<()>;
    async fn rollback_transaction(&self, tx: Self::Transaction) -> Result<()>;
}

/// 缓存特征
#[async_trait]
pub trait Cacheable<T> {
    async fn get_from_cache(&self, key: &str) -> Result<Option<T>>;
    async fn set_cache(&self, key: &str, value: &T, ttl: Option<u64>) -> Result<()>;
    async fn invalidate_cache(&self, key: &str) -> Result<()>;
    async fn invalidate_cache_pattern(&self, pattern: &str) -> Result<()>;
}

/// 审计日志特征
#[async_trait]
pub trait Auditable {
    async fn log_create(&self, entity_type: &str, entity_id: &str, data: &str) -> Result<()>;
    async fn log_update(&self, entity_type: &str, entity_id: &str, old_data: &str, new_data: &str) -> Result<()>;
    async fn log_delete(&self, entity_type: &str, entity_id: &str, data: &str) -> Result<()>;
}

/// 软删除特征
#[async_trait]
pub trait SoftDeletable<T, ID> {
    async fn soft_delete(&self, id: ID) -> Result<()>;
    async fn restore(&self, id: ID) -> Result<()>;
    async fn find_deleted(&self) -> Result<Vec<T>>;
    async fn permanently_delete(&self, id: ID) -> Result<()>;
}
