#!/bin/bash

# 微博爬虫节点部署脚本

set -e

echo "🚀 开始部署微博爬虫节点..."

# 检查Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查docker-compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose 未安装，请先安装 docker-compose"
    exit 1
fi

# 构建Docker镜像
echo "🔨 构建Docker镜像..."
docker build -t weibo-crawler-node:latest .

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

echo "✅ 部署完成！"
echo ""
echo "🌐 服务访问地址："
echo "- 爬虫节点: http://localhost:8084"
echo "- Redis: localhost:6379"
echo "- RabbitMQ管理界面: http://localhost:15672 (admin/password)"
echo ""
echo "📋 常用命令："
echo "- 查看日志: docker-compose logs -f crawler-node"
echo "- 停止服务: docker-compose down"
echo "- 重启服务: docker-compose restart"
