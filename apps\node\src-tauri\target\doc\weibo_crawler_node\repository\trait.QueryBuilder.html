<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="查询构建器特征"><title>QueryBuilder in weibo_crawler_node::repository - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc trait"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Query<wbr>Builder</a></h2><h3><a href="#required-associated-types">Required Associated Types</a></h3><ul class="block"><li><a href="#associatedtype.Query" title="Query">Query</a></li></ul><h3><a href="#required-methods">Required Methods</a></h3><ul class="block"><li><a href="#tymethod.build" title="build">build</a></li><li><a href="#tymethod.filter" title="filter">filter</a></li><li><a href="#tymethod.limit" title="limit">limit</a></li><li><a href="#tymethod.new" title="new">new</a></li><li><a href="#tymethod.offset" title="offset">offset</a></li><li><a href="#tymethod.order_by" title="order_by">order_by</a></li></ul><h3><a href="#dyn-compatibility">Dyn Compatibility</a></h3><h3><a href="#implementors">Implementors</a></h3></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>repository</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">repository</a></div><h1>Trait <span class="trait">QueryBuilder</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/repository/mod.rs.html#77-86">Source</a> </span></div><pre class="rust item-decl"><code>pub trait QueryBuilder {
    type <a href="#associatedtype.Query" class="associatedtype">Query</a>;

    // Required methods
    fn <a href="#tymethod.new" class="fn">new</a>() -&gt; Self;
<span class="item-spacer"></span>    fn <a href="#tymethod.filter" class="fn">filter</a>(self, field: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>, value: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; Self;
<span class="item-spacer"></span>    fn <a href="#tymethod.order_by" class="fn">order_by</a>(self, field: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>, direction: <a class="enum" href="enum.OrderDirection.html" title="enum weibo_crawler_node::repository::OrderDirection">OrderDirection</a>) -&gt; Self;
<span class="item-spacer"></span>    fn <a href="#tymethod.limit" class="fn">limit</a>(self, limit: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>) -&gt; Self;
<span class="item-spacer"></span>    fn <a href="#tymethod.offset" class="fn">offset</a>(self, offset: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>) -&gt; Self;
<span class="item-spacer"></span>    fn <a href="#tymethod.build" class="fn">build</a>(self) -&gt; Self::<a class="associatedtype" href="trait.QueryBuilder.html#associatedtype.Query" title="type weibo_crawler_node::repository::QueryBuilder::Query">Query</a>;
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>查询构建器特征</p>
</div></details><h2 id="required-associated-types" class="section-header">Required Associated Types<a href="#required-associated-types" class="anchor">§</a></h2><div class="methods"><section id="associatedtype.Query" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#78">Source</a><h4 class="code-header">type <a href="#associatedtype.Query" class="associatedtype">Query</a></h4></section></div><h2 id="required-methods" class="section-header">Required Methods<a href="#required-methods" class="anchor">§</a></h2><div class="methods"><section id="tymethod.new" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#80">Source</a><h4 class="code-header">fn <a href="#tymethod.new" class="fn">new</a>() -&gt; Self</h4></section><section id="tymethod.filter" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#81">Source</a><h4 class="code-header">fn <a href="#tymethod.filter" class="fn">filter</a>(self, field: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>, value: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; Self</h4></section><section id="tymethod.order_by" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#82">Source</a><h4 class="code-header">fn <a href="#tymethod.order_by" class="fn">order_by</a>(self, field: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>, direction: <a class="enum" href="enum.OrderDirection.html" title="enum weibo_crawler_node::repository::OrderDirection">OrderDirection</a>) -&gt; Self</h4></section><section id="tymethod.limit" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#83">Source</a><h4 class="code-header">fn <a href="#tymethod.limit" class="fn">limit</a>(self, limit: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>) -&gt; Self</h4></section><section id="tymethod.offset" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#84">Source</a><h4 class="code-header">fn <a href="#tymethod.offset" class="fn">offset</a>(self, offset: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>) -&gt; Self</h4></section><section id="tymethod.build" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#85">Source</a><h4 class="code-header">fn <a href="#tymethod.build" class="fn">build</a>(self) -&gt; Self::<a class="associatedtype" href="trait.QueryBuilder.html#associatedtype.Query" title="type weibo_crawler_node::repository::QueryBuilder::Query">Query</a></h4></section></div><h2 id="dyn-compatibility" class="section-header">Dyn Compatibility<a href="#dyn-compatibility" class="anchor">§</a></h2><div class="dyn-compatibility-info"><p>This trait is <b>not</b> <a href="https://doc.rust-lang.org/1.88.0/reference/items/traits.html#dyn-compatibility">dyn compatible</a>.</p><p><i>In older versions of Rust, dyn compatibility was called "object safety", so this trait is not object safe.</i></p></div><h2 id="implementors" class="section-header">Implementors<a href="#implementors" class="anchor">§</a></h2><div id="implementors-list"></div><script src="../../trait.impl/weibo_crawler_node/repository/trait.QueryBuilder.js" async></script></section></div></main></body></html>