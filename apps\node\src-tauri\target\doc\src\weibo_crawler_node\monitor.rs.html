<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\monitor.rs`."><title>monitor.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>monitor.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{AppError, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::sync::Arc;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::time::{Duration, SystemTime, UNIX_EPOCH};
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>tokio::sync::RwLock;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>tracing::{info, warn, error};
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#9 id=9 data-nosnippet>9</a></span><span class="kw">pub struct </span>SystemMetrics {
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub </span>cpu_usage: f64,
<a href=#11 id=11 data-nosnippet>11</a>    <span class="kw">pub </span>memory_usage: f64,
<a href=#12 id=12 data-nosnippet>12</a>    <span class="kw">pub </span>disk_usage: f64,
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>network_in: u64,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>network_out: u64,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>timestamp: u64,
<a href=#16 id=16 data-nosnippet>16</a>}
<a href=#17 id=17 data-nosnippet>17</a>
<a href=#18 id=18 data-nosnippet>18</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#19 id=19 data-nosnippet>19</a></span><span class="kw">pub struct </span>NodeHealth {
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">pub </span>node_id: String,
<a href=#21 id=21 data-nosnippet>21</a>    <span class="kw">pub </span>status: String,
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">pub </span>uptime: u64,
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">pub </span>last_heartbeat: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#24 id=24 data-nosnippet>24</a>    <span class="kw">pub </span>active_connections: u32,
<a href=#25 id=25 data-nosnippet>25</a>    <span class="kw">pub </span>error_rate: f64,
<a href=#26 id=26 data-nosnippet>26</a>}
<a href=#27 id=27 data-nosnippet>27</a>
<a href=#28 id=28 data-nosnippet>28</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#29 id=29 data-nosnippet>29</a></span><span class="kw">pub struct </span>PerformanceMetrics {
<a href=#30 id=30 data-nosnippet>30</a>    <span class="kw">pub </span>requests_per_second: f64,
<a href=#31 id=31 data-nosnippet>31</a>    <span class="kw">pub </span>average_response_time: f64,
<a href=#32 id=32 data-nosnippet>32</a>    <span class="kw">pub </span>error_rate: f64,
<a href=#33 id=33 data-nosnippet>33</a>    <span class="kw">pub </span>concurrent_connections: u32,
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">pub </span>queue_depth: u32,
<a href=#35 id=35 data-nosnippet>35</a>    <span class="kw">pub </span>memory_usage_mb: f64,
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>cpu_usage_percent: f64,
<a href=#37 id=37 data-nosnippet>37</a>}
<a href=#38 id=38 data-nosnippet>38</a>
<a href=#39 id=39 data-nosnippet>39</a><span class="kw">pub struct </span>SystemMonitor {
<a href=#40 id=40 data-nosnippet>40</a>    metrics_history: Arc&lt;RwLock&lt;Vec&lt;SystemMetrics&gt;&gt;&gt;,
<a href=#41 id=41 data-nosnippet>41</a>    performance_history: Arc&lt;RwLock&lt;Vec&lt;PerformanceMetrics&gt;&gt;&gt;,
<a href=#42 id=42 data-nosnippet>42</a>    start_time: SystemTime,
<a href=#43 id=43 data-nosnippet>43</a>    max_history_size: usize,
<a href=#44 id=44 data-nosnippet>44</a>}
<a href=#45 id=45 data-nosnippet>45</a>
<a href=#46 id=46 data-nosnippet>46</a><span class="kw">impl </span>SystemMonitor {
<a href=#47 id=47 data-nosnippet>47</a>    <span class="kw">pub async fn </span>new() -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#48 id=48 data-nosnippet>48</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#49 id=49 data-nosnippet>49</a>            metrics_history: Arc::new(RwLock::new(Vec::new())),
<a href=#50 id=50 data-nosnippet>50</a>            performance_history: Arc::new(RwLock::new(Vec::new())),
<a href=#51 id=51 data-nosnippet>51</a>            start_time: SystemTime::now(),
<a href=#52 id=52 data-nosnippet>52</a>            max_history_size: <span class="number">1000</span>, <span class="comment">// 保留最近1000个数据点
<a href=#53 id=53 data-nosnippet>53</a>        </span>})
<a href=#54 id=54 data-nosnippet>54</a>    }
<a href=#55 id=55 data-nosnippet>55</a>
<a href=#56 id=56 data-nosnippet>56</a>    <span class="kw">pub async fn </span>start_monitoring(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#57 id=57 data-nosnippet>57</a>        <span class="macro">info!</span>(<span class="string">"启动系统监控"</span>);
<a href=#58 id=58 data-nosnippet>58</a>        
<a href=#59 id=59 data-nosnippet>59</a>        <span class="comment">// 启动指标收集循环
<a href=#60 id=60 data-nosnippet>60</a>        </span><span class="self">self</span>.start_metrics_collection().<span class="kw">await</span>;
<a href=#61 id=61 data-nosnippet>61</a>        
<a href=#62 id=62 data-nosnippet>62</a>        <span class="comment">// 启动性能监控循环
<a href=#63 id=63 data-nosnippet>63</a>        </span><span class="self">self</span>.start_performance_monitoring().<span class="kw">await</span>;
<a href=#64 id=64 data-nosnippet>64</a>        
<a href=#65 id=65 data-nosnippet>65</a>        <span class="comment">// 启动心跳发送循环
<a href=#66 id=66 data-nosnippet>66</a>        </span><span class="self">self</span>.start_heartbeat_loop().<span class="kw">await</span>;
<a href=#67 id=67 data-nosnippet>67</a>        
<a href=#68 id=68 data-nosnippet>68</a>        <span class="prelude-val">Ok</span>(())
<a href=#69 id=69 data-nosnippet>69</a>    }
<a href=#70 id=70 data-nosnippet>70</a>
<a href=#71 id=71 data-nosnippet>71</a>    <span class="kw">async fn </span>start_metrics_collection(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#72 id=72 data-nosnippet>72</a>        <span class="kw">let </span>metrics_history = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.metrics_history);
<a href=#73 id=73 data-nosnippet>73</a>        <span class="kw">let </span>max_history_size = <span class="self">self</span>.max_history_size;
<a href=#74 id=74 data-nosnippet>74</a>
<a href=#75 id=75 data-nosnippet>75</a>        tokio::spawn(<span class="kw">async move </span>{
<a href=#76 id=76 data-nosnippet>76</a>            <span class="kw">let </span><span class="kw-2">mut </span>interval = tokio::time::interval(Duration::from_secs(<span class="number">10</span>)); <span class="comment">// 每10秒收集一次
<a href=#77 id=77 data-nosnippet>77</a>            
<a href=#78 id=78 data-nosnippet>78</a>            </span><span class="kw">loop </span>{
<a href=#79 id=79 data-nosnippet>79</a>                interval.tick().<span class="kw">await</span>;
<a href=#80 id=80 data-nosnippet>80</a>                
<a href=#81 id=81 data-nosnippet>81</a>                <span class="kw">let </span>metrics = <span class="self">Self</span>::collect_system_metrics().<span class="kw">await</span>;
<a href=#82 id=82 data-nosnippet>82</a>                
<a href=#83 id=83 data-nosnippet>83</a>                <span class="kw">let </span><span class="kw-2">mut </span>history = metrics_history.write().<span class="kw">await</span>;
<a href=#84 id=84 data-nosnippet>84</a>                history.push(metrics);
<a href=#85 id=85 data-nosnippet>85</a>                
<a href=#86 id=86 data-nosnippet>86</a>                <span class="comment">// 限制历史记录大小
<a href=#87 id=87 data-nosnippet>87</a>                </span><span class="kw">if </span>history.len() &gt; max_history_size {
<a href=#88 id=88 data-nosnippet>88</a>                    <span class="kw">let </span>excess = history.len() - max_history_size;
<a href=#89 id=89 data-nosnippet>89</a>                    history.drain(<span class="number">0</span>..excess);
<a href=#90 id=90 data-nosnippet>90</a>                }
<a href=#91 id=91 data-nosnippet>91</a>            }
<a href=#92 id=92 data-nosnippet>92</a>        });
<a href=#93 id=93 data-nosnippet>93</a>    }
<a href=#94 id=94 data-nosnippet>94</a>
<a href=#95 id=95 data-nosnippet>95</a>    <span class="kw">async fn </span>start_performance_monitoring(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#96 id=96 data-nosnippet>96</a>        <span class="kw">let </span>performance_history = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.performance_history);
<a href=#97 id=97 data-nosnippet>97</a>        <span class="kw">let </span>max_history_size = <span class="self">self</span>.max_history_size;
<a href=#98 id=98 data-nosnippet>98</a>
<a href=#99 id=99 data-nosnippet>99</a>        tokio::spawn(<span class="kw">async move </span>{
<a href=#100 id=100 data-nosnippet>100</a>            <span class="kw">let </span><span class="kw-2">mut </span>interval = tokio::time::interval(Duration::from_secs(<span class="number">30</span>)); <span class="comment">// 每30秒收集一次
<a href=#101 id=101 data-nosnippet>101</a>            
<a href=#102 id=102 data-nosnippet>102</a>            </span><span class="kw">loop </span>{
<a href=#103 id=103 data-nosnippet>103</a>                interval.tick().<span class="kw">await</span>;
<a href=#104 id=104 data-nosnippet>104</a>                
<a href=#105 id=105 data-nosnippet>105</a>                <span class="kw">let </span>performance = <span class="self">Self</span>::collect_performance_metrics().<span class="kw">await</span>;
<a href=#106 id=106 data-nosnippet>106</a>                
<a href=#107 id=107 data-nosnippet>107</a>                <span class="kw">let </span><span class="kw-2">mut </span>history = performance_history.write().<span class="kw">await</span>;
<a href=#108 id=108 data-nosnippet>108</a>                history.push(performance);
<a href=#109 id=109 data-nosnippet>109</a>                
<a href=#110 id=110 data-nosnippet>110</a>                <span class="comment">// 限制历史记录大小
<a href=#111 id=111 data-nosnippet>111</a>                </span><span class="kw">if </span>history.len() &gt; max_history_size {
<a href=#112 id=112 data-nosnippet>112</a>                    <span class="kw">let </span>excess = history.len() - max_history_size;
<a href=#113 id=113 data-nosnippet>113</a>                    history.drain(<span class="number">0</span>..excess);
<a href=#114 id=114 data-nosnippet>114</a>                }
<a href=#115 id=115 data-nosnippet>115</a>            }
<a href=#116 id=116 data-nosnippet>116</a>        });
<a href=#117 id=117 data-nosnippet>117</a>    }
<a href=#118 id=118 data-nosnippet>118</a>
<a href=#119 id=119 data-nosnippet>119</a>    <span class="kw">async fn </span>start_heartbeat_loop(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#120 id=120 data-nosnippet>120</a>        <span class="kw">let </span>start_time = <span class="self">self</span>.start_time;
<a href=#121 id=121 data-nosnippet>121</a>
<a href=#122 id=122 data-nosnippet>122</a>        tokio::spawn(<span class="kw">async move </span>{
<a href=#123 id=123 data-nosnippet>123</a>            <span class="kw">let </span><span class="kw-2">mut </span>interval = tokio::time::interval(Duration::from_secs(<span class="number">30</span>)); <span class="comment">// 每30秒发送心跳
<a href=#124 id=124 data-nosnippet>124</a>            
<a href=#125 id=125 data-nosnippet>125</a>            </span><span class="kw">loop </span>{
<a href=#126 id=126 data-nosnippet>126</a>                interval.tick().<span class="kw">await</span>;
<a href=#127 id=127 data-nosnippet>127</a>                
<a href=#128 id=128 data-nosnippet>128</a>                <span class="comment">// 发送心跳到管理节点
<a href=#129 id=129 data-nosnippet>129</a>                </span><span class="kw">if let </span><span class="prelude-val">Err</span>(e) = <span class="self">Self</span>::send_heartbeat(start_time).<span class="kw">await </span>{
<a href=#130 id=130 data-nosnippet>130</a>                    <span class="macro">error!</span>(<span class="string">"发送心跳失败: {}"</span>, e);
<a href=#131 id=131 data-nosnippet>131</a>                }
<a href=#132 id=132 data-nosnippet>132</a>            }
<a href=#133 id=133 data-nosnippet>133</a>        });
<a href=#134 id=134 data-nosnippet>134</a>    }
<a href=#135 id=135 data-nosnippet>135</a>
<a href=#136 id=136 data-nosnippet>136</a>    <span class="kw">async fn </span>collect_system_metrics() -&gt; SystemMetrics {
<a href=#137 id=137 data-nosnippet>137</a>        <span class="comment">// 这里应该实现真实的系统指标收集
<a href=#138 id=138 data-nosnippet>138</a>        // 目前返回模拟数据
<a href=#139 id=139 data-nosnippet>139</a>        
<a href=#140 id=140 data-nosnippet>140</a>        </span><span class="kw">let </span>timestamp = SystemTime::now()
<a href=#141 id=141 data-nosnippet>141</a>            .duration_since(UNIX_EPOCH)
<a href=#142 id=142 data-nosnippet>142</a>            .unwrap()
<a href=#143 id=143 data-nosnippet>143</a>            .as_secs();
<a href=#144 id=144 data-nosnippet>144</a>
<a href=#145 id=145 data-nosnippet>145</a>        SystemMetrics {
<a href=#146 id=146 data-nosnippet>146</a>            cpu_usage: <span class="self">Self</span>::get_cpu_usage().<span class="kw">await</span>,
<a href=#147 id=147 data-nosnippet>147</a>            memory_usage: <span class="self">Self</span>::get_memory_usage().<span class="kw">await</span>,
<a href=#148 id=148 data-nosnippet>148</a>            disk_usage: <span class="self">Self</span>::get_disk_usage().<span class="kw">await</span>,
<a href=#149 id=149 data-nosnippet>149</a>            network_in: <span class="self">Self</span>::get_network_in().<span class="kw">await</span>,
<a href=#150 id=150 data-nosnippet>150</a>            network_out: <span class="self">Self</span>::get_network_out().<span class="kw">await</span>,
<a href=#151 id=151 data-nosnippet>151</a>            timestamp,
<a href=#152 id=152 data-nosnippet>152</a>        }
<a href=#153 id=153 data-nosnippet>153</a>    }
<a href=#154 id=154 data-nosnippet>154</a>
<a href=#155 id=155 data-nosnippet>155</a>    <span class="kw">async fn </span>collect_performance_metrics() -&gt; PerformanceMetrics {
<a href=#156 id=156 data-nosnippet>156</a>        <span class="comment">// 这里应该实现真实的性能指标收集
<a href=#157 id=157 data-nosnippet>157</a>        // 目前返回模拟数据
<a href=#158 id=158 data-nosnippet>158</a>        
<a href=#159 id=159 data-nosnippet>159</a>        </span>PerformanceMetrics {
<a href=#160 id=160 data-nosnippet>160</a>            requests_per_second: <span class="number">12.5</span>,
<a href=#161 id=161 data-nosnippet>161</a>            average_response_time: <span class="number">2.3</span>,
<a href=#162 id=162 data-nosnippet>162</a>            error_rate: <span class="number">2.4</span>,
<a href=#163 id=163 data-nosnippet>163</a>            concurrent_connections: <span class="number">15</span>,
<a href=#164 id=164 data-nosnippet>164</a>            queue_depth: <span class="number">25</span>,
<a href=#165 id=165 data-nosnippet>165</a>            memory_usage_mb: <span class="number">256.8</span>,
<a href=#166 id=166 data-nosnippet>166</a>            cpu_usage_percent: <span class="number">45.2</span>,
<a href=#167 id=167 data-nosnippet>167</a>        }
<a href=#168 id=168 data-nosnippet>168</a>    }
<a href=#169 id=169 data-nosnippet>169</a>
<a href=#170 id=170 data-nosnippet>170</a>    <span class="kw">async fn </span>send_heartbeat(start_time: SystemTime) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#171 id=171 data-nosnippet>171</a>        <span class="kw">let </span>uptime = start_time.elapsed()
<a href=#172 id=172 data-nosnippet>172</a>            .map_err(|e| AppError::Monitor(<span class="macro">format!</span>(<span class="string">"获取运行时间失败: {}"</span>, e)))<span class="question-mark">?
<a href=#173 id=173 data-nosnippet>173</a>            </span>.as_secs();
<a href=#174 id=174 data-nosnippet>174</a>
<a href=#175 id=175 data-nosnippet>175</a>        <span class="kw">let </span>heartbeat = NodeHealth {
<a href=#176 id=176 data-nosnippet>176</a>            node_id: std::env::var(<span class="string">"NODE_ID"</span>).unwrap_or_else(|<span class="kw">_</span>| <span class="string">"crawler_node_001"</span>.to_string()),
<a href=#177 id=177 data-nosnippet>177</a>            status: <span class="string">"online"</span>.to_string(),
<a href=#178 id=178 data-nosnippet>178</a>            uptime,
<a href=#179 id=179 data-nosnippet>179</a>            last_heartbeat: chrono::Utc::now(),
<a href=#180 id=180 data-nosnippet>180</a>            active_connections: <span class="number">15</span>,
<a href=#181 id=181 data-nosnippet>181</a>            error_rate: <span class="number">2.4</span>,
<a href=#182 id=182 data-nosnippet>182</a>        };
<a href=#183 id=183 data-nosnippet>183</a>
<a href=#184 id=184 data-nosnippet>184</a>        <span class="comment">// 这里应该发送心跳到管理节点
<a href=#185 id=185 data-nosnippet>185</a>        // 目前只记录日志
<a href=#186 id=186 data-nosnippet>186</a>        </span><span class="macro">info!</span>(<span class="string">"发送心跳: 节点 {}, 运行时间: {}秒"</span>, heartbeat.node_id, heartbeat.uptime);
<a href=#187 id=187 data-nosnippet>187</a>        
<a href=#188 id=188 data-nosnippet>188</a>        <span class="prelude-val">Ok</span>(())
<a href=#189 id=189 data-nosnippet>189</a>    }
<a href=#190 id=190 data-nosnippet>190</a>
<a href=#191 id=191 data-nosnippet>191</a>    <span class="comment">// 系统指标收集方法（模拟实现）
<a href=#192 id=192 data-nosnippet>192</a>    </span><span class="kw">async fn </span>get_cpu_usage() -&gt; f64 {
<a href=#193 id=193 data-nosnippet>193</a>        <span class="comment">// 在实际实现中，这里应该调用系统API获取CPU使用率
<a href=#194 id=194 data-nosnippet>194</a>        // 可以使用 sysinfo crate
<a href=#195 id=195 data-nosnippet>195</a>        </span><span class="number">45.2 </span>+ (rand::random::&lt;f64&gt;() - <span class="number">0.5</span>) * <span class="number">10.0
<a href=#196 id=196 data-nosnippet>196</a>    </span>}
<a href=#197 id=197 data-nosnippet>197</a>
<a href=#198 id=198 data-nosnippet>198</a>    <span class="kw">async fn </span>get_memory_usage() -&gt; f64 {
<a href=#199 id=199 data-nosnippet>199</a>        <span class="comment">// 在实际实现中，这里应该调用系统API获取内存使用率
<a href=#200 id=200 data-nosnippet>200</a>        </span><span class="number">67.8 </span>+ (rand::random::&lt;f64&gt;() - <span class="number">0.5</span>) * <span class="number">20.0
<a href=#201 id=201 data-nosnippet>201</a>    </span>}
<a href=#202 id=202 data-nosnippet>202</a>
<a href=#203 id=203 data-nosnippet>203</a>    <span class="kw">async fn </span>get_disk_usage() -&gt; f64 {
<a href=#204 id=204 data-nosnippet>204</a>        <span class="comment">// 在实际实现中，这里应该调用系统API获取磁盘使用率
<a href=#205 id=205 data-nosnippet>205</a>        </span><span class="number">23.5 </span>+ (rand::random::&lt;f64&gt;() - <span class="number">0.5</span>) * <span class="number">5.0
<a href=#206 id=206 data-nosnippet>206</a>    </span>}
<a href=#207 id=207 data-nosnippet>207</a>
<a href=#208 id=208 data-nosnippet>208</a>    <span class="kw">async fn </span>get_network_in() -&gt; u64 {
<a href=#209 id=209 data-nosnippet>209</a>        <span class="comment">// 在实际实现中，这里应该获取网络入流量
<a href=#210 id=210 data-nosnippet>210</a>        </span><span class="number">1024000 </span>+ (rand::random::&lt;u64&gt;() % <span class="number">512000</span>)
<a href=#211 id=211 data-nosnippet>211</a>    }
<a href=#212 id=212 data-nosnippet>212</a>
<a href=#213 id=213 data-nosnippet>213</a>    <span class="kw">async fn </span>get_network_out() -&gt; u64 {
<a href=#214 id=214 data-nosnippet>214</a>        <span class="comment">// 在实际实现中，这里应该获取网络出流量
<a href=#215 id=215 data-nosnippet>215</a>        </span><span class="number">512000 </span>+ (rand::random::&lt;u64&gt;() % <span class="number">256000</span>)
<a href=#216 id=216 data-nosnippet>216</a>    }
<a href=#217 id=217 data-nosnippet>217</a>
<a href=#218 id=218 data-nosnippet>218</a>    <span class="comment">// 公共方法
<a href=#219 id=219 data-nosnippet>219</a>    </span><span class="kw">pub async fn </span>get_current_metrics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;SystemMetrics&gt; {
<a href=#220 id=220 data-nosnippet>220</a>        <span class="prelude-val">Ok</span>(<span class="self">Self</span>::collect_system_metrics().<span class="kw">await</span>)
<a href=#221 id=221 data-nosnippet>221</a>    }
<a href=#222 id=222 data-nosnippet>222</a>
<a href=#223 id=223 data-nosnippet>223</a>    <span class="kw">pub async fn </span>get_performance_stats(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;PerformanceMetrics&gt; {
<a href=#224 id=224 data-nosnippet>224</a>        <span class="prelude-val">Ok</span>(<span class="self">Self</span>::collect_performance_metrics().<span class="kw">await</span>)
<a href=#225 id=225 data-nosnippet>225</a>    }
<a href=#226 id=226 data-nosnippet>226</a>
<a href=#227 id=227 data-nosnippet>227</a>    <span class="kw">pub async fn </span>get_metrics_history(<span class="kw-2">&amp;</span><span class="self">self</span>, limit: <span class="prelude-ty">Option</span>&lt;usize&gt;) -&gt; Vec&lt;SystemMetrics&gt; {
<a href=#228 id=228 data-nosnippet>228</a>        <span class="kw">let </span>history = <span class="self">self</span>.metrics_history.read().<span class="kw">await</span>;
<a href=#229 id=229 data-nosnippet>229</a>        
<a href=#230 id=230 data-nosnippet>230</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(limit) = limit {
<a href=#231 id=231 data-nosnippet>231</a>            <span class="kw">if </span>history.len() &gt; limit {
<a href=#232 id=232 data-nosnippet>232</a>                history[history.len() - limit..].to_vec()
<a href=#233 id=233 data-nosnippet>233</a>            } <span class="kw">else </span>{
<a href=#234 id=234 data-nosnippet>234</a>                history.clone()
<a href=#235 id=235 data-nosnippet>235</a>            }
<a href=#236 id=236 data-nosnippet>236</a>        } <span class="kw">else </span>{
<a href=#237 id=237 data-nosnippet>237</a>            history.clone()
<a href=#238 id=238 data-nosnippet>238</a>        }
<a href=#239 id=239 data-nosnippet>239</a>    }
<a href=#240 id=240 data-nosnippet>240</a>
<a href=#241 id=241 data-nosnippet>241</a>    <span class="kw">pub async fn </span>get_performance_history(<span class="kw-2">&amp;</span><span class="self">self</span>, limit: <span class="prelude-ty">Option</span>&lt;usize&gt;) -&gt; Vec&lt;PerformanceMetrics&gt; {
<a href=#242 id=242 data-nosnippet>242</a>        <span class="kw">let </span>history = <span class="self">self</span>.performance_history.read().<span class="kw">await</span>;
<a href=#243 id=243 data-nosnippet>243</a>        
<a href=#244 id=244 data-nosnippet>244</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(limit) = limit {
<a href=#245 id=245 data-nosnippet>245</a>            <span class="kw">if </span>history.len() &gt; limit {
<a href=#246 id=246 data-nosnippet>246</a>                history[history.len() - limit..].to_vec()
<a href=#247 id=247 data-nosnippet>247</a>            } <span class="kw">else </span>{
<a href=#248 id=248 data-nosnippet>248</a>                history.clone()
<a href=#249 id=249 data-nosnippet>249</a>            }
<a href=#250 id=250 data-nosnippet>250</a>        } <span class="kw">else </span>{
<a href=#251 id=251 data-nosnippet>251</a>            history.clone()
<a href=#252 id=252 data-nosnippet>252</a>        }
<a href=#253 id=253 data-nosnippet>253</a>    }
<a href=#254 id=254 data-nosnippet>254</a>
<a href=#255 id=255 data-nosnippet>255</a>    <span class="kw">pub async fn </span>get_node_health(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;NodeHealth&gt; {
<a href=#256 id=256 data-nosnippet>256</a>        <span class="kw">let </span>uptime = <span class="self">self</span>.start_time.elapsed()
<a href=#257 id=257 data-nosnippet>257</a>            .map_err(|e| AppError::Monitor(<span class="macro">format!</span>(<span class="string">"获取运行时间失败: {}"</span>, e)))<span class="question-mark">?
<a href=#258 id=258 data-nosnippet>258</a>            </span>.as_secs();
<a href=#259 id=259 data-nosnippet>259</a>
<a href=#260 id=260 data-nosnippet>260</a>        <span class="prelude-val">Ok</span>(NodeHealth {
<a href=#261 id=261 data-nosnippet>261</a>            node_id: std::env::var(<span class="string">"NODE_ID"</span>).unwrap_or_else(|<span class="kw">_</span>| <span class="string">"crawler_node_001"</span>.to_string()),
<a href=#262 id=262 data-nosnippet>262</a>            status: <span class="string">"online"</span>.to_string(),
<a href=#263 id=263 data-nosnippet>263</a>            uptime,
<a href=#264 id=264 data-nosnippet>264</a>            last_heartbeat: chrono::Utc::now(),
<a href=#265 id=265 data-nosnippet>265</a>            active_connections: <span class="number">15</span>,
<a href=#266 id=266 data-nosnippet>266</a>            error_rate: <span class="number">2.4</span>,
<a href=#267 id=267 data-nosnippet>267</a>        })
<a href=#268 id=268 data-nosnippet>268</a>    }
<a href=#269 id=269 data-nosnippet>269</a>
<a href=#270 id=270 data-nosnippet>270</a>    <span class="kw">pub async fn </span>check_system_health(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;bool&gt; {
<a href=#271 id=271 data-nosnippet>271</a>        <span class="kw">let </span>metrics = <span class="self">self</span>.get_current_metrics().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#272 id=272 data-nosnippet>272</a>        
<a href=#273 id=273 data-nosnippet>273</a>        <span class="comment">// 检查系统健康状态
<a href=#274 id=274 data-nosnippet>274</a>        </span><span class="kw">let </span>is_healthy = metrics.cpu_usage &lt; <span class="number">90.0 
<a href=#275 id=275 data-nosnippet>275</a>            </span>&amp;&amp; metrics.memory_usage &lt; <span class="number">90.0 
<a href=#276 id=276 data-nosnippet>276</a>            </span>&amp;&amp; metrics.disk_usage &lt; <span class="number">95.0</span>;
<a href=#277 id=277 data-nosnippet>277</a>
<a href=#278 id=278 data-nosnippet>278</a>        <span class="kw">if </span>!is_healthy {
<a href=#279 id=279 data-nosnippet>279</a>            <span class="macro">warn!</span>(<span class="string">"系统健康检查失败: CPU: {:.1}%, 内存: {:.1}%, 磁盘: {:.1}%"</span>, 
<a href=#280 id=280 data-nosnippet>280</a>                  metrics.cpu_usage, metrics.memory_usage, metrics.disk_usage);
<a href=#281 id=281 data-nosnippet>281</a>        }
<a href=#282 id=282 data-nosnippet>282</a>
<a href=#283 id=283 data-nosnippet>283</a>        <span class="prelude-val">Ok</span>(is_healthy)
<a href=#284 id=284 data-nosnippet>284</a>    }
<a href=#285 id=285 data-nosnippet>285</a>
<a href=#286 id=286 data-nosnippet>286</a>    <span class="kw">pub async fn </span>get_uptime(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#287 id=287 data-nosnippet>287</a>        <span class="prelude-val">Ok</span>(<span class="self">self</span>.start_time.elapsed()
<a href=#288 id=288 data-nosnippet>288</a>            .map_err(|e| AppError::Monitor(<span class="macro">format!</span>(<span class="string">"获取运行时间失败: {}"</span>, e)))<span class="question-mark">?
<a href=#289 id=289 data-nosnippet>289</a>            </span>.as_secs())
<a href=#290 id=290 data-nosnippet>290</a>    }
<a href=#291 id=291 data-nosnippet>291</a>
<a href=#292 id=292 data-nosnippet>292</a>    <span class="kw">pub async fn </span>reset_metrics_history(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#293 id=293 data-nosnippet>293</a>        <span class="kw">let </span><span class="kw-2">mut </span>history = <span class="self">self</span>.metrics_history.write().<span class="kw">await</span>;
<a href=#294 id=294 data-nosnippet>294</a>        history.clear();
<a href=#295 id=295 data-nosnippet>295</a>        <span class="macro">info!</span>(<span class="string">"系统指标历史记录已清空"</span>);
<a href=#296 id=296 data-nosnippet>296</a>    }
<a href=#297 id=297 data-nosnippet>297</a>
<a href=#298 id=298 data-nosnippet>298</a>    <span class="kw">pub async fn </span>reset_performance_history(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#299 id=299 data-nosnippet>299</a>        <span class="kw">let </span><span class="kw-2">mut </span>history = <span class="self">self</span>.performance_history.write().<span class="kw">await</span>;
<a href=#300 id=300 data-nosnippet>300</a>        history.clear();
<a href=#301 id=301 data-nosnippet>301</a>        <span class="macro">info!</span>(<span class="string">"性能指标历史记录已清空"</span>);
<a href=#302 id=302 data-nosnippet>302</a>    }
<a href=#303 id=303 data-nosnippet>303</a>}</code></pre></div></section></main></body></html>