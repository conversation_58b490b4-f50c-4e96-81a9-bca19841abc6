{"rustc": 1842507548689473721, "features": "[\"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\", \"verbose-errors\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 13420477919323776582, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\amq-protocol-47fcf04cc5d02e8f\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}