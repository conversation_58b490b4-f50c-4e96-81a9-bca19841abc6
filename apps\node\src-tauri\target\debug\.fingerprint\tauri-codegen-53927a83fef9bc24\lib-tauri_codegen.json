{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 9830821840458823691, "deps": [[3060637413840920116, "proc_macro2", false, 10993881616374897205], [4899080583175475170, "semver", false, 2022204265236511886], [7392050791754369441, "ico", false, 4908634836235180829], [8008191657135824715, "thiserror", false, 5374037758448816721], [8292277814562636972, "tauri_utils", false, 10667560426277437857], [8319709847752024821, "uuid", false, 863863404221201939], [8569119365930580996, "serde_json", false, 6464436191210645041], [9451456094439810778, "regex", false, 2953680430888033729], [9689903380558560274, "serde", false, 4389908846715814937], [9857275760291862238, "sha2", false, 3599881831848126162], [10301936376833819828, "json_patch", false, 7788522536251391700], [12687914511023397207, "png", false, 12893556546655831963], [14132538657330703225, "brotli", false, 9994415381679156491], [15622660310229662834, "walkdir", false, 12179795921521016504], [17990358020177143287, "quote", false, 8157755205024365785], [18066890886671768183, "base64", false, 3541723674890208800]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-53927a83fef9bc24\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}