import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  StatCard,
  Input,
  SearchInput,
  Badge,
  StatusBadge,
  CountBadge,
  TrendBadge,
  Loading,
  SkeletonComponents,
  Modal,
  ConfirmModal,
  useModal,
  ToastProvider,
  useToast,
} from '../components/ui';
import {
  LineChart,
  AreaChart,
  BarChart,
  PieChart,
  RealTimeChart,
  chartColors,
} from '../components/charts';
import { Activity, Users, Globe, TrendingUp } from 'lucide-react';

// 示例数据
const chartData = [
  { name: '1月', value: 400 },
  { name: '2月', value: 300 },
  { name: '3月', value: 600 },
  { name: '4月', value: 800 },
  { name: '5月', value: 500 },
  { name: '6月', value: 700 },
];

const pieData = [
  { name: '成功', value: 400 },
  { name: '失败', value: 300 },
  { name: '等待', value: 200 },
  { name: '处理中', value: 100 },
];

const realTimeData = Array.from({ length: 20 }, (_, i) => ({
  timestamp: Date.now() - (20 - i) * 1000,
  value: Math.floor(Math.random() * 100),
}));

function ToastExample() {
  const { success, error, warning, info } = useToast();

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Toast 通知示例</h3>
      <div className="flex flex-wrap gap-2">
        <Button onClick={() => success('操作成功', '数据已保存')}>
          成功通知
        </Button>
        <Button 
          variant="destructive"
          onClick={() => error('操作失败', '请检查网络连接')}
        >
          错误通知
        </Button>
        <Button 
          variant="warning"
          onClick={() => warning('警告', '磁盘空间不足')}
        >
          警告通知
        </Button>
        <Button 
          variant="info"
          onClick={() => info('提示', '有新版本可用')}
        >
          信息通知
        </Button>
      </div>
    </div>
  );
}

function ModalExample() {
  const modal = useModal();
  const confirmModal = useModal();
  const [loading, setLoading] = useState(false);

  const handleConfirm = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
      confirmModal.closeModal();
    }, 2000);
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Modal 对话框示例</h3>
      <div className="flex flex-wrap gap-2">
        <Button onClick={modal.openModal}>
          打开普通对话框
        </Button>
        <Button variant="destructive" onClick={confirmModal.openModal}>
          打开确认对话框
        </Button>
      </div>

      <Modal
        open={modal.open}
        onClose={modal.closeModal}
        title="示例对话框"
        description="这是一个现代化的对话框组件"
      >
        <div className="space-y-4">
          <p>这里是对话框的内容区域。</p>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={modal.closeModal}>
              取消
            </Button>
            <Button onClick={modal.closeModal}>
              确认
            </Button>
          </div>
        </div>
      </Modal>

      <ConfirmModal
        open={confirmModal.open}
        onClose={confirmModal.closeModal}
        onConfirm={handleConfirm}
        title="确认删除"
        description="此操作不可撤销，确定要删除吗？"
        variant="destructive"
        loading={loading}
      />
    </div>
  );
}

export function ModernUIExample() {
  const [searchValue, setSearchValue] = useState('');
  const [loading, setLoading] = useState(false);

  return (
    <ToastProvider>
      <div className="container-responsive py-8 space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gradient mb-4">
            现代化 UI 组件示例
          </h1>
          <p className="text-muted-foreground">
            展示新的设计系统和组件库的使用效果
          </p>
        </div>

        {/* 按钮示例 */}
        <Card>
          <CardHeader>
            <CardTitle>按钮组件</CardTitle>
            <CardDescription>各种样式和状态的按钮</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button>默认按钮</Button>
              <Button variant="secondary">次要按钮</Button>
              <Button variant="outline">边框按钮</Button>
              <Button variant="ghost">幽灵按钮</Button>
              <Button variant="destructive">危险按钮</Button>
              <Button variant="success">成功按钮</Button>
              <Button loading={loading} onClick={() => setLoading(!loading)}>
                {loading ? '加载中...' : '切换加载'}
              </Button>
              <Button leftIcon={<Activity className="w-4 h-4" />}>
                带图标
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 输入框示例 */}
        <Card>
          <CardHeader>
            <CardTitle>输入框组件</CardTitle>
            <CardDescription>各种类型的输入框</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="用户名"
                placeholder="请输入用户名"
                leftIcon={<Users className="w-4 h-4" />}
              />
              <Input
                label="密码"
                type="password"
                placeholder="请输入密码"
                helperText="密码长度至少8位"
              />
              <SearchInput
                placeholder="搜索..."
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onSearch={(value) => console.log('搜索:', value)}
              />
              <Input
                label="邮箱"
                type="email"
                placeholder="请输入邮箱"
                error="邮箱格式不正确"
              />
            </div>
          </CardContent>
        </Card>

        {/* 徽章示例 */}
        <Card>
          <CardHeader>
            <CardTitle>徽章组件</CardTitle>
            <CardDescription>状态指示和标签</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge>默认</Badge>
                <Badge variant="secondary">次要</Badge>
                <Badge variant="success">成功</Badge>
                <Badge variant="warning">警告</Badge>
                <Badge variant="destructive">错误</Badge>
                <Badge variant="info">信息</Badge>
              </div>
              <div className="flex flex-wrap gap-2">
                <StatusBadge status="online" />
                <StatusBadge status="offline" />
                <StatusBadge status="warning" />
                <StatusBadge status="maintenance" />
              </div>
              <div className="flex flex-wrap gap-2">
                <TrendBadge trend="up" value="+12%" />
                <TrendBadge trend="down" value="-5%" />
                <TrendBadge trend="neutral" value="0%" />
                <CountBadge count={5} />
                <CountBadge count={99} />
                <CountBadge count={100} max={99} />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 统计卡片示例 */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="活跃用户"
            value="1,234"
            change="+12% 较上月"
            trend="up"
            icon={<Users className="w-6 h-6 text-primary" />}
          />
          <StatCard
            title="系统负载"
            value="45%"
            change="正常范围"
            trend="neutral"
            icon={<Activity className="w-6 h-6 text-success" />}
          />
          <StatCard
            title="网络连接"
            value="856"
            change="-3% 较昨日"
            trend="down"
            icon={<Globe className="w-6 h-6 text-info" />}
          />
          <StatCard
            title="增长率"
            value="23.5%"
            change="+8% 较上周"
            trend="up"
            icon={<TrendingUp className="w-6 h-6 text-warning" />}
          />
        </div>

        {/* 图表示例 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>线性图表</CardTitle>
              <CardDescription>月度数据趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <LineChart
                data={chartData}
                height={250}
                color={chartColors.primary}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>面积图表</CardTitle>
              <CardDescription>数据分布情况</CardDescription>
            </CardHeader>
            <CardContent>
              <AreaChart
                data={chartData}
                height={250}
                color={chartColors.success}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>柱状图表</CardTitle>
              <CardDescription>对比数据</CardDescription>
            </CardHeader>
            <CardContent>
              <BarChart
                data={chartData}
                height={250}
                color={chartColors.info}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>饼图</CardTitle>
              <CardDescription>数据占比</CardDescription>
            </CardHeader>
            <CardContent>
              <PieChart
                data={pieData}
                height={250}
                colors={chartColors.gradient}
              />
            </CardContent>
          </Card>
        </div>

        {/* 实时图表示例 */}
        <Card>
          <CardHeader>
            <CardTitle>实时数据图表</CardTitle>
            <CardDescription>模拟实时数据更新</CardDescription>
          </CardHeader>
          <CardContent>
            <RealTimeChart
              data={realTimeData}
              title="CPU 使用率"
              unit="%"
              color={chartColors.warning}
              type="area"
              height={200}
            />
          </CardContent>
        </Card>

        {/* 加载状态示例 */}
        <Card>
          <CardHeader>
            <CardTitle>加载状态</CardTitle>
            <CardDescription>各种加载动画效果</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
              <div>
                <Loading type="spinner" size="md" />
                <p className="text-sm mt-2">旋转</p>
              </div>
              <div>
                <Loading type="dots" size="md" />
                <p className="text-sm mt-2">点状</p>
              </div>
              <div>
                <Loading type="pulse" size="md" />
                <p className="text-sm mt-2">脉冲</p>
              </div>
              <div>
                <Loading type="wave" size="md" />
                <p className="text-sm mt-2">波浪</p>
              </div>
              <div>
                <SkeletonComponents.Avatar />
                <p className="text-sm mt-2">骨架屏</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Toast 示例 */}
        <Card>
          <CardHeader>
            <CardTitle>通知组件</CardTitle>
            <CardDescription>各种类型的通知消息</CardDescription>
          </CardHeader>
          <CardContent>
            <ToastExample />
          </CardContent>
        </Card>

        {/* Modal 示例 */}
        <Card>
          <CardHeader>
            <CardTitle>对话框组件</CardTitle>
            <CardDescription>模态对话框和确认框</CardDescription>
          </CardHeader>
          <CardContent>
            <ModalExample />
          </CardContent>
        </Card>
      </div>
    </ToastProvider>
  );
}
