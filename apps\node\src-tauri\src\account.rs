use crate::error::{AppError, Result};
use reqwest::{Client, cookie::Jar};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tracing::{info, error};
use uuid::Uuid;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Account {
    pub id: String,
    pub username: String,
    pub password: String, // 应该加密存储
    pub phone: Option<String>,
    pub email: Option<String>,
    pub status: AccountStatus,
    pub login_count: u64,
    pub last_login: Option<chrono::DateTime<chrono::Utc>>,
    pub last_activity: Option<chrono::DateTime<chrono::Utc>>,
    pub risk_score: f64, // 0.0-1.0
    pub cookies: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum AccountStatus {
    Normal,
    Banned,
    Abnormal,
    Maintenance,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginSession {
    pub account_id: String,
    pub session_id: String,
    pub cookies: String,
    pub user_agent: String,
    pub login_time: chrono::DateTime<chrono::Utc>,
    pub last_used: chrono::DateTime<chrono::Utc>,
    pub is_valid: bool,
}

pub struct AccountManager {
    accounts: Arc<RwLock<HashMap<String, Account>>>,
    sessions: Arc<RwLock<HashMap<String, LoginSession>>>,
    #[allow(dead_code)]
    login_client: Client,
    max_pool_size: usize,
    rotation_interval: Duration,
}

impl AccountManager {
    pub async fn new(max_pool_size: usize) -> Result<Self> {
        let cookie_jar = Arc::new(Jar::default());
        
        let login_client = Client::builder()
            .cookie_provider(cookie_jar)
            .timeout(Duration::from_secs(30))
            .user_agent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            .build()
            .map_err(|e| AppError::Account(format!("创建登录客户端失败: {}", e)))?;

        Ok(Self {
            accounts: Arc::new(RwLock::new(HashMap::new())),
            sessions: Arc::new(RwLock::new(HashMap::new())),
            login_client,
            max_pool_size,
            rotation_interval: Duration::from_secs(3600), // 1小时
        })
    }

    pub async fn add_account(&self, account_config: crate::commands::AccountConfig) -> Result<String> {
        let account_id = Uuid::new_v4().to_string();

        // 检查账号池大小
        {
            let accounts = self.accounts.read().await;
            if accounts.len() >= self.max_pool_size {
                return Err(AppError::Account("账号池已满".to_string()));
            }
        }

        // 检查用户名是否已存在
        {
            let accounts = self.accounts.read().await;
            for account in accounts.values() {
                if account.username == account_config.username {
                    return Err(AppError::Account("用户名已存在".to_string()));
                }
            }
        }

        let account = Account {
            id: account_id.clone(),
            username: account_config.username,
            password: account_config.password, // TODO: 应该加密存储
            phone: account_config.phone,
            email: account_config.email,
            status: AccountStatus::Normal,
            login_count: 0,
            last_login: None,
            last_activity: None,
            risk_score: 0.0,
            cookies: None,
            created_at: chrono::Utc::now(),
        };

        let mut accounts = self.accounts.write().await;
        accounts.insert(account_id.clone(), account);

        info!("账号 {} 添加成功", account_id);
        Ok(account_id)
    }

    pub async fn remove_account(&self, account_id: &str) -> Result<()> {
        let mut accounts = self.accounts.write().await;
        
        if accounts.remove(account_id).is_some() {
            // 同时移除相关的登录会话
            let mut sessions = self.sessions.write().await;
            sessions.retain(|_, session| session.account_id != account_id);
            
            info!("账号 {} 移除成功", account_id);
            Ok(())
        } else {
            Err(AppError::Account(format!("账号 {} 不存在", account_id)))
        }
    }

    pub async fn login_account(&self, account_id: &str) -> Result<crate::commands::LoginResult> {
        let account = {
            let accounts = self.accounts.read().await;
            accounts.get(account_id).cloned()
                .ok_or_else(|| AppError::Account(format!("账号 {} 不存在", account_id)))?
        };

        if !matches!(account.status, AccountStatus::Normal) {
            return Ok(crate::commands::LoginResult {
                account_id: account_id.to_string(),
                success: false,
                error_message: Some("账号状态异常".to_string()),
                cookies: None,
            });
        }

        // 执行登录逻辑
        match self.perform_login(&account).await {
            Ok(session) => {
                // 更新账号信息
                {
                    let mut accounts = self.accounts.write().await;
                    if let Some(acc) = accounts.get_mut(account_id) {
                        acc.login_count += 1;
                        acc.last_login = Some(chrono::Utc::now());
                        acc.last_activity = Some(chrono::Utc::now());
                        acc.cookies = Some(session.cookies.clone());
                    }
                }

                // 保存登录会话
                {
                    let mut sessions = self.sessions.write().await;
                    sessions.insert(session.session_id.clone(), session.clone());
                }

                info!("账号 {} 登录成功", account_id);
                Ok(crate::commands::LoginResult {
                    account_id: account_id.to_string(),
                    success: true,
                    error_message: None,
                    cookies: Some(session.cookies),
                })
            }
            Err(e) => {
                error!("账号 {} 登录失败: {}", account_id, e);
                
                // 增加风险评分
                {
                    let mut accounts = self.accounts.write().await;
                    if let Some(acc) = accounts.get_mut(account_id) {
                        acc.risk_score = (acc.risk_score + 0.1).min(1.0);
                        
                        // 如果风险评分过高，标记为异常
                        if acc.risk_score > 0.8 {
                            acc.status = AccountStatus::Abnormal;
                        }
                    }
                }

                Ok(crate::commands::LoginResult {
                    account_id: account_id.to_string(),
                    success: false,
                    error_message: Some(e.to_string()),
                    cookies: None,
                })
            }
        }
    }

    async fn perform_login(&self, account: &Account) -> Result<LoginSession> {
        // 这里应该实现实际的微博登录逻辑
        // 目前返回模拟的登录会话
        
        // 模拟登录延时
        tokio::time::sleep(Duration::from_millis(1000)).await;

        let session_id = Uuid::new_v4().to_string();
        let cookies = format!("session_id={}; user_id={}", session_id, account.id);

        Ok(LoginSession {
            account_id: account.id.clone(),
            session_id,
            cookies,
            user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36".to_string(),
            login_time: chrono::Utc::now(),
            last_used: chrono::Utc::now(),
            is_valid: true,
        })
    }

    pub async fn refresh_cookies(&self, account_id: &str) -> Result<()> {
        let _account = {
            let accounts = self.accounts.read().await;
            accounts.get(account_id).cloned()
                .ok_or_else(|| AppError::Account(format!("账号 {} 不存在", account_id)))?
        };

        // 重新登录以刷新Cookie
        let login_result = self.login_account(account_id).await?;
        
        if login_result.success {
            info!("账号 {} Cookie刷新成功", account_id);
            Ok(())
        } else {
            Err(AppError::Account(format!("Cookie刷新失败: {:?}", login_result.error_message)))
        }
    }

    pub async fn get_available_account(&self) -> Result<Option<Account>> {
        let accounts = self.accounts.read().await;
        
        // 找到可用的账号，优先选择风险评分低的
        let mut available_accounts: Vec<&Account> = accounts
            .values()
            .filter(|a| matches!(a.status, AccountStatus::Normal) && a.risk_score < 0.5)
            .collect();

        if available_accounts.is_empty() {
            return Ok(None);
        }

        // 按风险评分排序
        available_accounts.sort_by(|a, b| a.risk_score.partial_cmp(&b.risk_score).unwrap());

        Ok(available_accounts.first().map(|a| (*a).clone()))
    }

    pub async fn start_rotation_loop(&self) {
        let accounts = Arc::clone(&self.accounts);
        let sessions = Arc::clone(&self.sessions);
        let interval = self.rotation_interval;

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            
            loop {
                interval_timer.tick().await;
                
                // 清理过期会话
                {
                    let mut sessions_guard = sessions.write().await;
                    let now = chrono::Utc::now();
                    sessions_guard.retain(|_, session| {
                        let age = now.signed_duration_since(session.last_used);
                        age.num_hours() < 24 // 保留24小时内的会话
                    });
                }

                // 降低账号风险评分
                {
                    let mut accounts_guard = accounts.write().await;
                    for account in accounts_guard.values_mut() {
                        if account.risk_score > 0.0 {
                            account.risk_score = (account.risk_score - 0.05).max(0.0);
                            
                            // 如果风险评分降低，可能恢复正常状态
                            if matches!(account.status, AccountStatus::Abnormal) && account.risk_score < 0.3 {
                                account.status = AccountStatus::Normal;
                            }
                        }
                    }
                }

                info!("账号轮换和风险评分更新完成");
            }
        });
    }

    pub async fn get_pool_status(&self) -> (usize, usize, usize, usize) {
        let accounts = self.accounts.read().await;
        let sessions = self.sessions.read().await;
        
        let total = accounts.len();
        let active = accounts.values()
            .filter(|a| matches!(a.status, AccountStatus::Normal))
            .count();
        let logged_in = sessions.values()
            .filter(|s| s.is_valid)
            .count();
        let healthy = accounts.values()
            .filter(|a| matches!(a.status, AccountStatus::Normal) && a.risk_score < 0.3)
            .count();

        (total, active, logged_in, healthy)
    }

    pub async fn get_all_accounts(&self) -> Vec<Account> {
        let accounts = self.accounts.read().await;
        accounts.values().cloned().collect()
    }
}
