{"rustc": 1842507548689473721, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 8113656176662020586, "path": 14367665871832239459, "deps": [[5103565458935487, "futures_io", false, 487272345117200944], [1615478164327904835, "pin_utils", false, 4061926691854359275], [1906322745568073236, "pin_project_lite", false, 18131270670966278584], [5451793922601807560, "slab", false, 17589511508343128620], [7013762810557009322, "futures_sink", false, 13257182082487644860], [7620660491849607393, "futures_core", false, 6897113057850168815], [15932120279885307830, "memchr", false, 13280053228737166842], [16240732885093539806, "futures_task", false, 5618792812908536749]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-4047e8a48cb84a01\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}