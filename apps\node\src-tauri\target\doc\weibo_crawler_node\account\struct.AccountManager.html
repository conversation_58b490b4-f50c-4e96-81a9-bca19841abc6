<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `AccountManager` struct in crate `weibo_crawler_node`."><title>AccountManager in weibo_crawler_node::account - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc struct"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Account<wbr>Manager</a></h2><h3><a href="#fields">Fields</a></h3><ul class="block structfield"><li><a href="#structfield.accounts" title="accounts">accounts</a></li><li><a href="#structfield.login_client" title="login_client">login_client</a></li><li><a href="#structfield.max_pool_size" title="max_pool_size">max_pool_size</a></li><li><a href="#structfield.rotation_interval" title="rotation_interval">rotation_interval</a></li><li><a href="#structfield.sessions" title="sessions">sessions</a></li></ul><h3><a href="#implementations">Methods</a></h3><ul class="block method"><li><a href="#method.add_account" title="add_account">add_account</a></li><li><a href="#method.get_all_accounts" title="get_all_accounts">get_all_accounts</a></li><li><a href="#method.get_available_account" title="get_available_account">get_available_account</a></li><li><a href="#method.get_pool_status" title="get_pool_status">get_pool_status</a></li><li><a href="#method.login_account" title="login_account">login_account</a></li><li><a href="#method.new" title="new">new</a></li><li><a href="#method.perform_login" title="perform_login">perform_login</a></li><li><a href="#method.refresh_cookies" title="refresh_cookies">refresh_cookies</a></li><li><a href="#method.remove_account" title="remove_account">remove_account</a></li><li><a href="#method.start_rotation_loop" title="start_rotation_loop">start_rotation_loop</a></li></ul><h3><a href="#synthetic-implementations">Auto Trait Implementations</a></h3><ul class="block synthetic-implementation"><li><a href="#impl-RefUnwindSafe-for-AccountManager" title="!RefUnwindSafe">!RefUnwindSafe</a></li><li><a href="#impl-UnwindSafe-for-AccountManager" title="!UnwindSafe">!UnwindSafe</a></li><li><a href="#impl-Freeze-for-AccountManager" title="Freeze">Freeze</a></li><li><a href="#impl-Send-for-AccountManager" title="Send">Send</a></li><li><a href="#impl-Sync-for-AccountManager" title="Sync">Sync</a></li><li><a href="#impl-Unpin-for-AccountManager" title="Unpin">Unpin</a></li></ul><h3><a href="#blanket-implementations">Blanket Implementations</a></h3><ul class="block blanket-implementation"><li><a href="#impl-Any-for-T" title="Any">Any</a></li><li><a href="#impl-Borrow%3CT%3E-for-T" title="Borrow&#60;T&#62;">Borrow&#60;T&#62;</a></li><li><a href="#impl-BorrowMut%3CT%3E-for-T" title="BorrowMut&#60;T&#62;">BorrowMut&#60;T&#62;</a></li><li><a href="#impl-ErasedDestructor-for-T" title="ErasedDestructor">ErasedDestructor</a></li><li><a href="#impl-From%3CT%3E-for-T" title="From&#60;T&#62;">From&#60;T&#62;</a></li><li><a href="#impl-Instrument-for-T" title="Instrument">Instrument</a></li><li><a href="#impl-Into%3CU%3E-for-T" title="Into&#60;U&#62;">Into&#60;U&#62;</a></li><li><a href="#impl-IntoEither-for-T" title="IntoEither">IntoEither</a></li><li><a href="#impl-Pointable-for-T" title="Pointable">Pointable</a></li><li><a href="#impl-Same-for-T" title="Same">Same</a></li><li><a href="#impl-TryFrom%3CU%3E-for-T" title="TryFrom&#60;U&#62;">TryFrom&#60;U&#62;</a></li><li><a href="#impl-TryInto%3CU%3E-for-T" title="TryInto&#60;U&#62;">TryInto&#60;U&#62;</a></li><li><a href="#impl-VZip%3CV%3E-for-T" title="VZip&#60;V&#62;">VZip&#60;V&#62;</a></li><li><a href="#impl-WithSubscriber-for-T" title="WithSubscriber">WithSubscriber</a></li></ul></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>account</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">account</a></div><h1>Struct <span class="struct">AccountManager</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/account.rs.html#46-53">Source</a> </span></div><pre class="rust item-decl"><code>pub struct AccountManager {
    accounts: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/std/collections/hash/map/struct.HashMap.html" title="struct std::collections::hash::map::HashMap">HashMap</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>, <a class="struct" href="struct.Account.html" title="struct weibo_crawler_node::account::Account">Account</a>&gt;&gt;&gt;,
    sessions: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/std/collections/hash/map/struct.HashMap.html" title="struct std::collections::hash::map::HashMap">HashMap</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>, <a class="struct" href="struct.LoginSession.html" title="struct weibo_crawler_node::account::LoginSession">LoginSession</a>&gt;&gt;&gt;,
    login_client: Client,
    max_pool_size: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>,
    rotation_interval: <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/time/struct.Duration.html" title="struct core::time::Duration">Duration</a>,
}</code></pre><h2 id="fields" class="fields section-header">Fields<a href="#fields" class="anchor">§</a></h2><span id="structfield.accounts" class="structfield section-header"><a href="#structfield.accounts" class="anchor field">§</a><code>accounts: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/std/collections/hash/map/struct.HashMap.html" title="struct std::collections::hash::map::HashMap">HashMap</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>, <a class="struct" href="struct.Account.html" title="struct weibo_crawler_node::account::Account">Account</a>&gt;&gt;&gt;</code></span><span id="structfield.sessions" class="structfield section-header"><a href="#structfield.sessions" class="anchor field">§</a><code>sessions: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/std/collections/hash/map/struct.HashMap.html" title="struct std::collections::hash::map::HashMap">HashMap</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>, <a class="struct" href="struct.LoginSession.html" title="struct weibo_crawler_node::account::LoginSession">LoginSession</a>&gt;&gt;&gt;</code></span><span id="structfield.login_client" class="structfield section-header"><a href="#structfield.login_client" class="anchor field">§</a><code>login_client: Client</code></span><span id="structfield.max_pool_size" class="structfield section-header"><a href="#structfield.max_pool_size" class="anchor field">§</a><code>max_pool_size: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a></code></span><span id="structfield.rotation_interval" class="structfield section-header"><a href="#structfield.rotation_interval" class="anchor field">§</a><code>rotation_interval: <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/time/struct.Duration.html" title="struct core::time::Duration">Duration</a></code></span><h2 id="implementations" class="section-header">Implementations<a href="#implementations" class="anchor">§</a></h2><div id="implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-AccountManager" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#55-324">Source</a><a href="#impl-AccountManager" class="anchor">§</a><h3 class="code-header">impl <a class="struct" href="struct.AccountManager.html" title="struct weibo_crawler_node::account::AccountManager">AccountManager</a></h3></section></summary><div class="impl-items"><section id="method.new" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#56-73">Source</a><h4 class="code-header">pub async fn <a href="#method.new" class="fn">new</a>(max_pool_size: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.add_account" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#75-116">Source</a><h4 class="code-header">pub async fn <a href="#method.add_account" class="fn">add_account</a>(
    &amp;self,
    account_config: <a class="struct" href="../commands/struct.AccountConfig.html" title="struct weibo_crawler_node::commands::AccountConfig">AccountConfig</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.remove_account" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#118-131">Source</a><h4 class="code-header">pub async fn <a href="#method.remove_account" class="fn">remove_account</a>(&amp;self, account_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.login_account" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#133-201">Source</a><h4 class="code-header">pub async fn <a href="#method.login_account" class="fn">login_account</a>(
    &amp;self,
    account_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="../commands/struct.LoginResult.html" title="struct weibo_crawler_node::commands::LoginResult">LoginResult</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.perform_login" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#203-222">Source</a><h4 class="code-header">async fn <a href="#method.perform_login" class="fn">perform_login</a>(
    &amp;self,
    account: &amp;<a class="struct" href="struct.Account.html" title="struct weibo_crawler_node::account::Account">Account</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="struct.LoginSession.html" title="struct weibo_crawler_node::account::LoginSession">LoginSession</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.refresh_cookies" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#224-240">Source</a><h4 class="code-header">pub async fn <a href="#method.refresh_cookies" class="fn">refresh_cookies</a>(&amp;self, account_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.get_available_account" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#242-259">Source</a><h4 class="code-header">pub async fn <a href="#method.get_available_account" class="fn">get_available_account</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;<a class="struct" href="struct.Account.html" title="struct weibo_crawler_node::account::Account">Account</a>&gt;, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.start_rotation_loop" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#261-300">Source</a><h4 class="code-header">pub async fn <a href="#method.start_rotation_loop" class="fn">start_rotation_loop</a>(&amp;self)</h4></section><section id="method.get_pool_status" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#302-318">Source</a><h4 class="code-header">pub async fn <a href="#method.get_pool_status" class="fn">get_pool_status</a>(&amp;self) -&gt; (<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>, <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>, <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>, <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>)</h4></section><section id="method.get_all_accounts" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/account.rs.html#320-323">Source</a><h4 class="code-header">pub async fn <a href="#method.get_all_accounts" class="fn">get_all_accounts</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="struct.Account.html" title="struct weibo_crawler_node::account::Account">Account</a>&gt;</h4></section></div></details></div><h2 id="synthetic-implementations" class="section-header">Auto Trait Implementations<a href="#synthetic-implementations" class="anchor">§</a></h2><div id="synthetic-implementations-list"><section id="impl-Freeze-for-AccountManager" class="impl"><a href="#impl-Freeze-for-AccountManager" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Freeze.html" title="trait core::marker::Freeze">Freeze</a> for <a class="struct" href="struct.AccountManager.html" title="struct weibo_crawler_node::account::AccountManager">AccountManager</a></h3></section><section id="impl-RefUnwindSafe-for-AccountManager" class="impl"><a href="#impl-RefUnwindSafe-for-AccountManager" class="anchor">§</a><h3 class="code-header">impl !<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/panic/unwind_safe/trait.RefUnwindSafe.html" title="trait core::panic::unwind_safe::RefUnwindSafe">RefUnwindSafe</a> for <a class="struct" href="struct.AccountManager.html" title="struct weibo_crawler_node::account::AccountManager">AccountManager</a></h3></section><section id="impl-Send-for-AccountManager" class="impl"><a href="#impl-Send-for-AccountManager" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> for <a class="struct" href="struct.AccountManager.html" title="struct weibo_crawler_node::account::AccountManager">AccountManager</a></h3></section><section id="impl-Sync-for-AccountManager" class="impl"><a href="#impl-Sync-for-AccountManager" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> for <a class="struct" href="struct.AccountManager.html" title="struct weibo_crawler_node::account::AccountManager">AccountManager</a></h3></section><section id="impl-Unpin-for-AccountManager" class="impl"><a href="#impl-Unpin-for-AccountManager" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Unpin.html" title="trait core::marker::Unpin">Unpin</a> for <a class="struct" href="struct.AccountManager.html" title="struct weibo_crawler_node::account::AccountManager">AccountManager</a></h3></section><section id="impl-UnwindSafe-for-AccountManager" class="impl"><a href="#impl-UnwindSafe-for-AccountManager" class="anchor">§</a><h3 class="code-header">impl !<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/panic/unwind_safe/trait.UnwindSafe.html" title="trait core::panic::unwind_safe::UnwindSafe">UnwindSafe</a> for <a class="struct" href="struct.AccountManager.html" title="struct weibo_crawler_node::account::AccountManager">AccountManager</a></h3></section></div><h2 id="blanket-implementations" class="section-header">Blanket Implementations<a href="#blanket-implementations" class="anchor">§</a></h2><div id="blanket-implementations-list"><details class="toggle implementors-toggle"><summary><section id="impl-Any-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/any.rs.html#138">Source</a><a href="#impl-Any-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/any/trait.Any.html" title="trait core::any::Any">Any</a> for T<div class="where">where
    T: 'static + ?<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.type_id" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/any.rs.html#139">Source</a><a href="#method.type_id" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/any/trait.Any.html#tymethod.type_id" class="fn">type_id</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/any/struct.TypeId.html" title="struct core::any::TypeId">TypeId</a></h4></section></summary><div class='docblock'>Gets the <code>TypeId</code> of <code>self</code>. <a href="https://doc.rust-lang.org/1.88.0/core/any/trait.Any.html#tymethod.type_id">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Borrow%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#209">Source</a><a href="#impl-Borrow%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.Borrow.html" title="trait core::borrow::Borrow">Borrow</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#211">Source</a><a href="#method.borrow" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.Borrow.html#tymethod.borrow" class="fn">borrow</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;T</a></h4></section></summary><div class='docblock'>Immutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.Borrow.html#tymethod.borrow">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-BorrowMut%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#217">Source</a><a href="#impl-BorrowMut%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.BorrowMut.html" title="trait core::borrow::BorrowMut">BorrowMut</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow_mut" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#218">Source</a><a href="#method.borrow_mut" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut" class="fn">borrow_mut</a>(&amp;mut self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;mut T</a></h4></section></summary><div class='docblock'>Mutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-From%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#767">Source</a><a href="#impl-From%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#770">Source</a><a href="#method.from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html#tymethod.from" class="fn">from</a>(t: T) -&gt; T</h4></section></summary><div class="docblock"><p>Returns the argument unchanged.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Instrument-for-T" class="impl"><a href="#impl-Instrument-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; Instrument for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.instrument" class="method trait-impl"><a href="#method.instrument" class="anchor">§</a><h4 class="code-header">fn <a class="fn">instrument</a>(self, span: Span) -&gt; Instrumented&lt;Self&gt;</h4></section></summary><div class='docblock'>Instruments this type with the provided [<code>Span</code>], returning an
<code>Instrumented</code> wrapper. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.in_current_span" class="method trait-impl"><a href="#method.in_current_span" class="anchor">§</a><h4 class="code-header">fn <a class="fn">in_current_span</a>(self) -&gt; Instrumented&lt;Self&gt;</h4></section></summary><div class='docblock'>Instruments this type with the <a href="super::Span::current()">current</a> <a href="crate::Span"><code>Span</code></a>, returning an
<code>Instrumented</code> wrapper. <a>Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Into%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#750-752">Source</a><a href="#impl-Into%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#760">Source</a><a href="#method.into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html#tymethod.into" class="fn">into</a>(self) -&gt; U</h4></section></summary><div class="docblock"><p>Calls <code>U::from(self)</code>.</p>
<p>That is, this conversion is whatever the implementation of
<code><a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for U</code> chooses to do.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-IntoEither-for-T" class="impl"><a class="src rightside" href="https://docs.rs/either/1/src/either/into_either.rs.html#64">Source</a><a href="#impl-IntoEither-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html" title="trait either::into_either::IntoEither">IntoEither</a> for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into_either" class="method trait-impl"><a class="src rightside" href="https://docs.rs/either/1/src/either/into_either.rs.html#29">Source</a><a href="#method.into_either" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either" class="fn">into_either</a>(self, into_left: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a>) -&gt; <a class="enum" href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either">Either</a>&lt;Self, Self&gt;</h4></section></summary><div class='docblock'>Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Left" title="variant either::Either::Left"><code>Left</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
if <code>into_left</code> is <code>true</code>.
Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Right" title="variant either::Either::Right"><code>Right</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
otherwise. <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.into_either_with" class="method trait-impl"><a class="src rightside" href="https://docs.rs/either/1/src/either/into_either.rs.html#55-57">Source</a><a href="#method.into_either_with" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either_with" class="fn">into_either_with</a>&lt;F&gt;(self, into_left: F) -&gt; <a class="enum" href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either">Either</a>&lt;Self, Self&gt;<div class="where">where
    F: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html" title="trait core::ops::function::FnOnce">FnOnce</a>(&amp;Self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a>,</div></h4></section></summary><div class='docblock'>Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Left" title="variant either::Either::Left"><code>Left</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
if <code>into_left(&amp;self)</code> returns <code>true</code>.
Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Right" title="variant either::Either::Right"><code>Right</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
otherwise. <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either_with">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Pointable-for-T" class="impl"><a href="#impl-Pointable-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; Pointable for T</h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedconstant.ALIGN" class="associatedconstant trait-impl"><a href="#associatedconstant.ALIGN" class="anchor">§</a><h4 class="code-header">const <a class="constant">ALIGN</a>: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a></h4></section></summary><div class='docblock'>The alignment of pointer.</div></details><details class="toggle" open><summary><section id="associatedtype.Init" class="associatedtype trait-impl"><a href="#associatedtype.Init" class="anchor">§</a><h4 class="code-header">type <a class="associatedtype">Init</a> = T</h4></section></summary><div class='docblock'>The type for initializers.</div></details><details class="toggle method-toggle" open><summary><section id="method.init" class="method trait-impl"><a href="#method.init" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">init</a>(init: &lt;T as Pointable&gt;::Init) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a></h4></section></summary><div class='docblock'>Initializes a with the given initializer. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.deref" class="method trait-impl"><a href="#method.deref" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">deref</a>&lt;'a&gt;(ptr: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;'a T</a></h4></section></summary><div class='docblock'>Dereferences the given pointer. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.deref_mut" class="method trait-impl"><a href="#method.deref_mut" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">deref_mut</a>&lt;'a&gt;(ptr: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;'a mut T</a></h4></section></summary><div class='docblock'>Mutably dereferences the given pointer. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.drop" class="method trait-impl"><a href="#method.drop" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">drop</a>(ptr: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>)</h4></section></summary><div class='docblock'>Drops the object pointed to by the given pointer. <a>Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Same-for-T" class="impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#34">Source</a><a href="#impl-Same-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html" title="trait typenum::type_operators::Same">Same</a> for T</h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Output" class="associatedtype trait-impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#35">Source</a><a href="#associatedtype.Output" class="anchor">§</a><h4 class="code-header">type <a href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html#associatedtype.Output" class="associatedtype">Output</a> = T</h4></section></summary><div class='docblock'>Should always be <code>Self</code></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryFrom%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#806-808">Source</a><a href="#impl-TryFrom%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error-1" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#810">Source</a><a href="#associatedtype.Error-1" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" class="associatedtype">Error</a> = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/convert/enum.Infallible.html" title="enum core::convert::Infallible">Infallible</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#813">Source</a><a href="#method.try_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#tymethod.try_from" class="fn">try_from</a>(value: U) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, &lt;T as <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryInto%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#791-793">Source</a><a href="#impl-TryInto%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryInto.html" title="trait core::convert::TryInto">TryInto</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#795">Source</a><a href="#associatedtype.Error" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryInto.html#associatedtype.Error" class="associatedtype">Error</a> = &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#798">Source</a><a href="#method.try_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryInto.html#tymethod.try_into" class="fn">try_into</a>(self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;U, &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-VZip%3CV%3E-for-T" class="impl"><a href="#impl-VZip%3CV%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;V, T&gt; VZip&lt;V&gt; for T<div class="where">where
    V: MultiLane&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><section id="method.vzip" class="method trait-impl"><a href="#method.vzip" class="anchor">§</a><h4 class="code-header">fn <a class="fn">vzip</a>(self) -&gt; V</h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-WithSubscriber-for-T" class="impl"><a href="#impl-WithSubscriber-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; WithSubscriber for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.with_subscriber" class="method trait-impl"><a href="#method.with_subscriber" class="anchor">§</a><h4 class="code-header">fn <a class="fn">with_subscriber</a>&lt;S&gt;(self, subscriber: S) -&gt; WithDispatch&lt;Self&gt;<div class="where">where
    S: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;Dispatch&gt;,</div></h4></section></summary><div class='docblock'>Attaches the provided <a href="super::Subscriber"><code>Subscriber</code></a> to this type, returning a
[<code>WithDispatch</code>] wrapper. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.with_current_subscriber" class="method trait-impl"><a href="#method.with_current_subscriber" class="anchor">§</a><h4 class="code-header">fn <a class="fn">with_current_subscriber</a>(self) -&gt; WithDispatch&lt;Self&gt;</h4></section></summary><div class='docblock'>Attaches the current <a href="crate::dispatcher#setting-the-default-subscriber">default</a> <a href="super::Subscriber"><code>Subscriber</code></a> to this type, returning a
[<code>WithDispatch</code>] wrapper. <a>Read more</a></div></details></div></details><section id="impl-ErasedDestructor-for-T" class="impl"><a href="#impl-ErasedDestructor-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; ErasedDestructor for T<div class="where">where
    T: 'static,</div></h3></section></div></section></div></main></body></html>