{"rustc": 1842507548689473721, "features": "[\"chrono\", \"default\", \"rt-tokio\"]", "declared_features": "[\"actix-rt\", \"async-std\", \"chrono\", \"default\", \"only-for-docs-rs\", \"rt-actix\", \"rt-async-std\", \"rt-tokio\"]", "target": 17092474573756926029, "profile": 2241668132362809309, "path": 3942210427294595234, "deps": [[2283771217451780507, "serde_with", false, 4660710034729933916], [2644515958598432451, "dirs", false, 8266638516854349271], [2706460456408817945, "futures", false, 9697924446797448047], [5986029879202738730, "log", false, 3004741351825838512], [7626844547088148622, "build_script_build", false, 11893451957157802146], [8008191657135824715, "thiserror", false, 4083214613751541842], [8569119365930580996, "serde_json", false, 2740896963817221287], [9689903380558560274, "serde", false, 4202820352788480372], [9897246384292347999, "chrono", false, 15810629356749029949], [11903278875415370753, "itertools", false, 1830175283126089900], [12944427623413450645, "tokio", false, 5789438060576990513], [15822952346600863224, "strong", false, 6799772434997251028], [16973251432615581304, "tokio_stream", false, 17520908898395598628], [17205548227036139086, "zip", false, 16461238244087086181], [17282734725213053079, "base64", false, 6579854451732260008], [17605717126308396068, "paste", false, 265303868298634720]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\playwright-22ec11456f7d2347\\dep-lib-playwright", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}