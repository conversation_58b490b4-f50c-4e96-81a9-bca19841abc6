<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="基础仓库特征"><title>Repository in weibo_crawler_node::repository - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc trait"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Repository</a></h2><h3><a href="#required-methods">Required Methods</a></h3><ul class="block"><li><a href="#tymethod.count" title="count">count</a></li><li><a href="#tymethod.create" title="create">create</a></li><li><a href="#tymethod.delete" title="delete">delete</a></li><li><a href="#tymethod.find_all" title="find_all">find_all</a></li><li><a href="#tymethod.find_by_id" title="find_by_id">find_by_id</a></li><li><a href="#tymethod.update" title="update">update</a></li></ul><h3><a href="#implementors">Implementors</a></h3></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>repository</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">repository</a></div><h1>Trait <span class="trait">Repository</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/repository/mod.rs.html#16-23">Source</a> </span></div><pre class="rust item-decl"><code>pub trait Repository&lt;T, ID&gt; {
    // Required methods
    fn <a href="#tymethod.find_by_id" class="fn">find_by_id</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
        id: ID,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;T&gt;, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.find_all" class="fn">find_all</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;T&gt;, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.create" class="fn">create</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
        entity: T,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.update" class="fn">update</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
        id: ID,
        entity: T,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.delete" class="fn">delete</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
        id: ID,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.count" class="fn">count</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.i64.html">i64</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>基础仓库特征</p>
</div></details><h2 id="required-methods" class="section-header">Required Methods<a href="#required-methods" class="anchor">§</a></h2><div class="methods"><section id="tymethod.find_by_id" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#17">Source</a><h4 class="code-header">fn <a href="#tymethod.find_by_id" class="fn">find_by_id</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
    id: ID,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="enum" href="https://doc.rust-lang.org/1.88.0/core/option/enum.Option.html" title="enum core::option::Option">Option</a>&lt;T&gt;, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="tymethod.find_all" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#18">Source</a><h4 class="code-header">fn <a href="#tymethod.find_all" class="fn">find_all</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;T&gt;, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="tymethod.create" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#19">Source</a><h4 class="code-header">fn <a href="#tymethod.create" class="fn">create</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
    entity: T,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="tymethod.update" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#20">Source</a><h4 class="code-header">fn <a href="#tymethod.update" class="fn">update</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
    id: ID,
    entity: T,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="tymethod.delete" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#21">Source</a><h4 class="code-header">fn <a href="#tymethod.delete" class="fn">delete</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
    id: ID,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="tymethod.count" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#22">Source</a><h4 class="code-header">fn <a href="#tymethod.count" class="fn">count</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.i64.html">i64</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section></div><h2 id="implementors" class="section-header">Implementors<a href="#implementors" class="anchor">§</a></h2><div id="implementors-list"><section id="impl-Repository%3CAccountRecord,+i64%3E-for-AccountRepository" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/repository/account_repository.rs.html#49-75">Source</a><a href="#impl-Repository%3CAccountRecord,+i64%3E-for-AccountRepository" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.Repository.html" title="trait weibo_crawler_node::repository::Repository">Repository</a>&lt;<a class="struct" href="../storage/struct.AccountRecord.html" title="struct weibo_crawler_node::storage::AccountRecord">AccountRecord</a>, <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.i64.html">i64</a>&gt; for <a class="struct" href="account_repository/struct.AccountRepository.html" title="struct weibo_crawler_node::repository::account_repository::AccountRepository">AccountRepository</a></h3></section><section id="impl-Repository%3CProxyRecord,+i64%3E-for-ProxyRepository" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/repository/proxy_repository.rs.html#43-69">Source</a><a href="#impl-Repository%3CProxyRecord,+i64%3E-for-ProxyRepository" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.Repository.html" title="trait weibo_crawler_node::repository::Repository">Repository</a>&lt;<a class="struct" href="../storage/struct.ProxyRecord.html" title="struct weibo_crawler_node::storage::ProxyRecord">ProxyRecord</a>, <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.i64.html">i64</a>&gt; for <a class="struct" href="proxy_repository/struct.ProxyRepository.html" title="struct weibo_crawler_node::repository::proxy_repository::ProxyRepository">ProxyRepository</a></h3></section><section id="impl-Repository%3CTaskRecord,+i64%3E-for-TaskRepository" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/repository/task_repository.rs.html#82-106">Source</a><a href="#impl-Repository%3CTaskRecord,+i64%3E-for-TaskRepository" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.Repository.html" title="trait weibo_crawler_node::repository::Repository">Repository</a>&lt;<a class="struct" href="../storage/struct.TaskRecord.html" title="struct weibo_crawler_node::storage::TaskRecord">TaskRecord</a>, <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.i64.html">i64</a>&gt; for <a class="struct" href="task_repository/struct.TaskRepository.html" title="struct weibo_crawler_node::repository::task_repository::TaskRepository">TaskRepository</a></h3></section></div><script src="../../trait.impl/weibo_crawler_node/repository/trait.Repository.js" async></script></section></div></main></body></html>