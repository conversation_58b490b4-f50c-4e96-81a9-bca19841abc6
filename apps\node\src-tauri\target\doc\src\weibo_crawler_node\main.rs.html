<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\main.rs`."><title>main.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>main.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="comment">// Prevents additional console window on Windows in release, DO NOT REMOVE!!
<a href=#2 id=2 data-nosnippet>2</a></span><span class="attr">#![cfg_attr(not(debug_assertions), windows_subsystem = <span class="string">"windows"</span>)]
<a href=#3 id=3 data-nosnippet>3</a>
<a href=#4 id=4 data-nosnippet>4</a></span><span class="kw">mod </span>commands;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">mod </span>crawler;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">mod </span>proxy;
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">mod </span>account;
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">mod </span>scheduler;
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">mod </span>storage;
<a href=#10 id=10 data-nosnippet>10</a><span class="kw">mod </span>monitor;
<a href=#11 id=11 data-nosnippet>11</a><span class="kw">mod </span>error;
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">mod </span>config;
<a href=#13 id=13 data-nosnippet>13</a><span class="kw">mod </span>repository;
<a href=#14 id=14 data-nosnippet>14</a><span class="kw">mod </span>service;
<a href=#15 id=15 data-nosnippet>15</a><span class="kw">mod </span>updater;
<a href=#16 id=16 data-nosnippet>16</a><span class="kw">mod </span>notification;
<a href=#17 id=17 data-nosnippet>17</a><span class="kw">mod </span>window_manager;
<a href=#18 id=18 data-nosnippet>18</a><span class="kw">mod </span>browser;
<a href=#19 id=19 data-nosnippet>19</a>
<a href=#20 id=20 data-nosnippet>20</a><span class="kw">use </span>commands::<span class="kw-2">*</span>;
<a href=#21 id=21 data-nosnippet>21</a><span class="kw">use </span>config::AppConfig;
<a href=#22 id=22 data-nosnippet>22</a><span class="kw">use </span>error::Result;
<a href=#23 id=23 data-nosnippet>23</a><span class="kw">use </span>std::sync::Arc;
<a href=#24 id=24 data-nosnippet>24</a><span class="kw">use </span>tokio::sync::Mutex;
<a href=#25 id=25 data-nosnippet>25</a><span class="kw">use </span>tracing::{info, error};
<a href=#26 id=26 data-nosnippet>26</a><span class="kw">use </span>tracing_subscriber;
<a href=#27 id=27 data-nosnippet>27</a><span class="kw">use </span>tauri::{
<a href=#28 id=28 data-nosnippet>28</a>    Manager,
<a href=#29 id=29 data-nosnippet>29</a>    AppHandle
<a href=#30 id=30 data-nosnippet>30</a>};
<a href=#31 id=31 data-nosnippet>31</a>
<a href=#32 id=32 data-nosnippet>32</a><span class="attr">#[derive(Default)]
<a href=#33 id=33 data-nosnippet>33</a></span><span class="kw">pub struct </span>AppState {
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">pub </span>config: Arc&lt;Mutex&lt;AppConfig&gt;&gt;,
<a href=#35 id=35 data-nosnippet>35</a>    <span class="kw">pub </span>crawler_engine: Arc&lt;Mutex&lt;<span class="prelude-ty">Option</span>&lt;crawler::CrawlerEngine&gt;&gt;&gt;,
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>proxy_manager: Arc&lt;Mutex&lt;<span class="prelude-ty">Option</span>&lt;proxy::ProxyManager&gt;&gt;&gt;,
<a href=#37 id=37 data-nosnippet>37</a>    <span class="kw">pub </span>account_manager: Arc&lt;Mutex&lt;<span class="prelude-ty">Option</span>&lt;account::AccountManager&gt;&gt;&gt;,
<a href=#38 id=38 data-nosnippet>38</a>    <span class="kw">pub </span>task_scheduler: Arc&lt;Mutex&lt;<span class="prelude-ty">Option</span>&lt;scheduler::TaskScheduler&gt;&gt;&gt;,
<a href=#39 id=39 data-nosnippet>39</a>    <span class="kw">pub </span>storage_manager: Arc&lt;Mutex&lt;<span class="prelude-ty">Option</span>&lt;storage::StorageManager&gt;&gt;&gt;,
<a href=#40 id=40 data-nosnippet>40</a>    <span class="kw">pub </span>system_monitor: Arc&lt;Mutex&lt;<span class="prelude-ty">Option</span>&lt;monitor::SystemMonitor&gt;&gt;&gt;,
<a href=#41 id=41 data-nosnippet>41</a>}
<a href=#42 id=42 data-nosnippet>42</a>
<a href=#43 id=43 data-nosnippet>43</a><span class="attr">#[tokio::main]
<a href=#44 id=44 data-nosnippet>44</a></span><span class="kw">async fn </span>main() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#45 id=45 data-nosnippet>45</a>    <span class="comment">// 初始化日志
<a href=#46 id=46 data-nosnippet>46</a>    </span>tracing_subscriber::fmt()
<a href=#47 id=47 data-nosnippet>47</a>        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env().add_directive(<span class="string">"weibo_crawler_node=debug"</span>.parse().unwrap()))
<a href=#48 id=48 data-nosnippet>48</a>        .init();
<a href=#49 id=49 data-nosnippet>49</a>
<a href=#50 id=50 data-nosnippet>50</a>    <span class="macro">info!</span>(<span class="string">"启动微博爬虫节点..."</span>);
<a href=#51 id=51 data-nosnippet>51</a>
<a href=#52 id=52 data-nosnippet>52</a>    <span class="comment">// 加载配置
<a href=#53 id=53 data-nosnippet>53</a>    </span><span class="kw">let </span>config = AppConfig::load().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#54 id=54 data-nosnippet>54</a>    <span class="macro">info!</span>(<span class="string">"配置加载完成: {}"</span>, config.node_name);
<a href=#55 id=55 data-nosnippet>55</a>
<a href=#56 id=56 data-nosnippet>56</a>    <span class="comment">// 初始化应用状态
<a href=#57 id=57 data-nosnippet>57</a>    </span><span class="kw">let </span>app_state = AppState {
<a href=#58 id=58 data-nosnippet>58</a>        config: Arc::new(Mutex::new(config)),
<a href=#59 id=59 data-nosnippet>59</a>        ..Default::default()
<a href=#60 id=60 data-nosnippet>60</a>    };
<a href=#61 id=61 data-nosnippet>61</a>
<a href=#62 id=62 data-nosnippet>62</a>    <span class="comment">// 启动Tauri应用
<a href=#63 id=63 data-nosnippet>63</a>    </span>tauri::Builder::default()
<a href=#64 id=64 data-nosnippet>64</a>        .manage(app_state)
<a href=#65 id=65 data-nosnippet>65</a>        .invoke_handler(<span class="macro">tauri::generate_handler!</span>[
<a href=#66 id=66 data-nosnippet>66</a>            <span class="comment">// 系统管理命令
<a href=#67 id=67 data-nosnippet>67</a>            </span>get_node_status,
<a href=#68 id=68 data-nosnippet>68</a>            start_crawler,
<a href=#69 id=69 data-nosnippet>69</a>            stop_crawler,
<a href=#70 id=70 data-nosnippet>70</a>            restart_crawler,
<a href=#71 id=71 data-nosnippet>71</a>            
<a href=#72 id=72 data-nosnippet>72</a>            <span class="comment">// 任务管理命令
<a href=#73 id=73 data-nosnippet>73</a>            </span>get_task_queue_status,
<a href=#74 id=74 data-nosnippet>74</a>            start_task_processing,
<a href=#75 id=75 data-nosnippet>75</a>            stop_task_processing,
<a href=#76 id=76 data-nosnippet>76</a>            get_task_statistics,
<a href=#77 id=77 data-nosnippet>77</a>            
<a href=#78 id=78 data-nosnippet>78</a>            <span class="comment">// 代理池管理命令
<a href=#79 id=79 data-nosnippet>79</a>            </span>get_proxy_pool_status,
<a href=#80 id=80 data-nosnippet>80</a>            add_proxy,
<a href=#81 id=81 data-nosnippet>81</a>            remove_proxy,
<a href=#82 id=82 data-nosnippet>82</a>            test_proxy,
<a href=#83 id=83 data-nosnippet>83</a>            
<a href=#84 id=84 data-nosnippet>84</a>            <span class="comment">// 账号管理命令
<a href=#85 id=85 data-nosnippet>85</a>            </span>get_account_pool_status,
<a href=#86 id=86 data-nosnippet>86</a>            add_account,
<a href=#87 id=87 data-nosnippet>87</a>            login_account,
<a href=#88 id=88 data-nosnippet>88</a>            refresh_account_cookies,
<a href=#89 id=89 data-nosnippet>89</a>            
<a href=#90 id=90 data-nosnippet>90</a>            <span class="comment">// 监控命令
<a href=#91 id=91 data-nosnippet>91</a>            </span>get_system_metrics,
<a href=#92 id=92 data-nosnippet>92</a>            get_performance_stats,
<a href=#93 id=93 data-nosnippet>93</a>            
<a href=#94 id=94 data-nosnippet>94</a>            <span class="comment">// 配置管理命令
<a href=#95 id=95 data-nosnippet>95</a>            </span>get_config,
<a href=#96 id=96 data-nosnippet>96</a>            update_config,
<a href=#97 id=97 data-nosnippet>97</a>
<a href=#98 id=98 data-nosnippet>98</a>            <span class="comment">// 更新相关命令
<a href=#99 id=99 data-nosnippet>99</a>            </span>updater::check_for_updates,
<a href=#100 id=100 data-nosnippet>100</a>            updater::download_update,
<a href=#101 id=101 data-nosnippet>101</a>            updater::get_update_status,
<a href=#102 id=102 data-nosnippet>102</a>
<a href=#103 id=103 data-nosnippet>103</a>            <span class="comment">// 通知相关命令
<a href=#104 id=104 data-nosnippet>104</a>            </span>notification::get_notifications,
<a href=#105 id=105 data-nosnippet>105</a>            notification::get_unread_notifications,
<a href=#106 id=106 data-nosnippet>106</a>            notification::mark_notification_read,
<a href=#107 id=107 data-nosnippet>107</a>            notification::remove_notification,
<a href=#108 id=108 data-nosnippet>108</a>            notification::clear_all_notifications,
<a href=#109 id=109 data-nosnippet>109</a>            notification::handle_notification_action,
<a href=#110 id=110 data-nosnippet>110</a>
<a href=#111 id=111 data-nosnippet>111</a>            <span class="comment">// 窗口管理命令
<a href=#112 id=112 data-nosnippet>112</a>            </span>window_manager::create_window,
<a href=#113 id=113 data-nosnippet>113</a>            window_manager::close_window,
<a href=#114 id=114 data-nosnippet>114</a>            window_manager::show_window,
<a href=#115 id=115 data-nosnippet>115</a>            window_manager::hide_window,
<a href=#116 id=116 data-nosnippet>116</a>            window_manager::get_window_info,
<a href=#117 id=117 data-nosnippet>117</a>            window_manager::get_all_windows,
<a href=#118 id=118 data-nosnippet>118</a>
<a href=#119 id=119 data-nosnippet>119</a>            <span class="comment">// 浏览器相关命令
<a href=#120 id=120 data-nosnippet>120</a>            </span>test_browser,
<a href=#121 id=121 data-nosnippet>121</a>            login_weibo,
<a href=#122 id=122 data-nosnippet>122</a>            update_browser_config,
<a href=#123 id=123 data-nosnippet>123</a>            get_browser_config
<a href=#124 id=124 data-nosnippet>124</a>        ])
<a href=#125 id=125 data-nosnippet>125</a>        .setup(|app| {
<a href=#126 id=126 data-nosnippet>126</a>            <span class="kw">let </span>app_handle = app.handle();
<a href=#127 id=127 data-nosnippet>127</a>            
<a href=#128 id=128 data-nosnippet>128</a>            <span class="comment">// 异步初始化系统组件
<a href=#129 id=129 data-nosnippet>129</a>            </span>tauri::async_runtime::spawn(<span class="kw">async move </span>{
<a href=#130 id=130 data-nosnippet>130</a>                <span class="kw">if let </span><span class="prelude-val">Err</span>(e) = initialize_system_components(<span class="kw-2">&amp;</span>app_handle).<span class="kw">await </span>{
<a href=#131 id=131 data-nosnippet>131</a>                    <span class="macro">error!</span>(<span class="string">"系统组件初始化失败: {}"</span>, e);
<a href=#132 id=132 data-nosnippet>132</a>                }
<a href=#133 id=133 data-nosnippet>133</a>            });
<a href=#134 id=134 data-nosnippet>134</a>            
<a href=#135 id=135 data-nosnippet>135</a>            <span class="prelude-val">Ok</span>(())
<a href=#136 id=136 data-nosnippet>136</a>        })
<a href=#137 id=137 data-nosnippet>137</a>        .setup(|app| {
<a href=#138 id=138 data-nosnippet>138</a>            <span class="comment">// 应用启动时的初始化
<a href=#139 id=139 data-nosnippet>139</a>            </span><span class="kw">if let </span><span class="prelude-val">Err</span>(e) = setup_app_initialization(<span class="kw-2">&amp;</span>app.handle()) {
<a href=#140 id=140 data-nosnippet>140</a>                <span class="macro">error!</span>(<span class="string">"应用初始化失败: {}"</span>, e);
<a href=#141 id=141 data-nosnippet>141</a>            }
<a href=#142 id=142 data-nosnippet>142</a>            <span class="prelude-val">Ok</span>(())
<a href=#143 id=143 data-nosnippet>143</a>        })
<a href=#144 id=144 data-nosnippet>144</a>        .on_window_event(|event| {
<a href=#145 id=145 data-nosnippet>145</a>            <span class="comment">// 全局窗口事件处理
<a href=#146 id=146 data-nosnippet>146</a>            </span><span class="kw">if let </span>tauri::WindowEvent::CloseRequested { api, .. } = event.event() {
<a href=#147 id=147 data-nosnippet>147</a>                <span class="comment">// 阻止默认关闭行为，隐藏到系统托盘
<a href=#148 id=148 data-nosnippet>148</a>                </span>api.prevent_close();
<a href=#149 id=149 data-nosnippet>149</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(window) = event.window().get_window(<span class="string">"main"</span>) {
<a href=#150 id=150 data-nosnippet>150</a>                    <span class="kw">let _ </span>= window.hide();
<a href=#151 id=151 data-nosnippet>151</a>                }
<a href=#152 id=152 data-nosnippet>152</a>            }
<a href=#153 id=153 data-nosnippet>153</a>        })
<a href=#154 id=154 data-nosnippet>154</a>        .build(<span class="macro">tauri::generate_context!</span>())
<a href=#155 id=155 data-nosnippet>155</a>        .expect(<span class="string">"启动Tauri应用失败"</span>)
<a href=#156 id=156 data-nosnippet>156</a>        .run(|app_handle, event| {
<a href=#157 id=157 data-nosnippet>157</a>            <span class="kw">match </span>event {
<a href=#158 id=158 data-nosnippet>158</a>                tauri::RunEvent::ExitRequested { api, .. } =&gt; {
<a href=#159 id=159 data-nosnippet>159</a>                    <span class="comment">// 应用退出时的清理
<a href=#160 id=160 data-nosnippet>160</a>                    </span>api.prevent_exit();
<a href=#161 id=161 data-nosnippet>161</a>                    <span class="kw">let </span>app_handle_clone = app_handle.clone();
<a href=#162 id=162 data-nosnippet>162</a>                    tauri::async_runtime::spawn(<span class="kw">async move </span>{
<a href=#163 id=163 data-nosnippet>163</a>                        <span class="kw">if let </span><span class="prelude-val">Err</span>(e) = cleanup_on_exit(<span class="kw-2">&amp;</span>app_handle_clone).<span class="kw">await </span>{
<a href=#164 id=164 data-nosnippet>164</a>                            <span class="macro">error!</span>(<span class="string">"应用清理失败: {}"</span>, e);
<a href=#165 id=165 data-nosnippet>165</a>                        }
<a href=#166 id=166 data-nosnippet>166</a>                        app_handle_clone.exit(<span class="number">0</span>);
<a href=#167 id=167 data-nosnippet>167</a>                    });
<a href=#168 id=168 data-nosnippet>168</a>                }
<a href=#169 id=169 data-nosnippet>169</a>                <span class="kw">_ </span>=&gt; {}
<a href=#170 id=170 data-nosnippet>170</a>            }
<a href=#171 id=171 data-nosnippet>171</a>        });
<a href=#172 id=172 data-nosnippet>172</a>
<a href=#173 id=173 data-nosnippet>173</a>    <span class="prelude-val">Ok</span>(())
<a href=#174 id=174 data-nosnippet>174</a>}
<a href=#175 id=175 data-nosnippet>175</a>
<a href=#176 id=176 data-nosnippet>176</a><span class="kw">async fn </span>initialize_system_components(app_handle: <span class="kw-2">&amp;</span>tauri::AppHandle) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#177 id=177 data-nosnippet>177</a>    <span class="kw">use </span>tauri::Manager;
<a href=#178 id=178 data-nosnippet>178</a>    <span class="kw">let </span>state = app_handle.state::&lt;AppState&gt;();
<a href=#179 id=179 data-nosnippet>179</a>    <span class="kw">let </span>config = state.config.lock().<span class="kw">await</span>.clone();
<a href=#180 id=180 data-nosnippet>180</a>
<a href=#181 id=181 data-nosnippet>181</a>    <span class="macro">info!</span>(<span class="string">"初始化系统组件..."</span>);
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a>    <span class="comment">// 初始化存储管理器
<a href=#184 id=184 data-nosnippet>184</a>    </span><span class="macro">info!</span>(<span class="string">"正在初始化存储管理器，数据库URL: {}"</span>, config.database_url);
<a href=#185 id=185 data-nosnippet>185</a>    <span class="kw">let </span>storage_manager = storage::StorageManager::new(<span class="kw-2">&amp;</span>config.database_url).<span class="kw">await
<a href=#186 id=186 data-nosnippet>186</a>        </span>.map_err(|e| {
<a href=#187 id=187 data-nosnippet>187</a>            <span class="macro">error!</span>(<span class="string">"存储管理器初始化失败: {}"</span>, e);
<a href=#188 id=188 data-nosnippet>188</a>            e
<a href=#189 id=189 data-nosnippet>189</a>        })<span class="question-mark">?</span>;
<a href=#190 id=190 data-nosnippet>190</a>    <span class="kw-2">*</span>state.storage_manager.lock().<span class="kw">await </span>= <span class="prelude-val">Some</span>(storage_manager);
<a href=#191 id=191 data-nosnippet>191</a>    <span class="macro">info!</span>(<span class="string">"存储管理器初始化完成"</span>);
<a href=#192 id=192 data-nosnippet>192</a>
<a href=#193 id=193 data-nosnippet>193</a>    <span class="comment">// 初始化代理管理器
<a href=#194 id=194 data-nosnippet>194</a>    </span><span class="kw">let </span>proxy_manager = proxy::ProxyManager::new(config.proxy_pool_size).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#195 id=195 data-nosnippet>195</a>    <span class="kw-2">*</span>state.proxy_manager.lock().<span class="kw">await </span>= <span class="prelude-val">Some</span>(proxy_manager);
<a href=#196 id=196 data-nosnippet>196</a>    <span class="macro">info!</span>(<span class="string">"代理管理器初始化完成"</span>);
<a href=#197 id=197 data-nosnippet>197</a>
<a href=#198 id=198 data-nosnippet>198</a>    <span class="comment">// 初始化账号管理器
<a href=#199 id=199 data-nosnippet>199</a>    </span><span class="kw">let </span>account_manager = account::AccountManager::new(config.account_pool_size).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#200 id=200 data-nosnippet>200</a>    <span class="kw-2">*</span>state.account_manager.lock().<span class="kw">await </span>= <span class="prelude-val">Some</span>(account_manager);
<a href=#201 id=201 data-nosnippet>201</a>    <span class="macro">info!</span>(<span class="string">"账号管理器初始化完成"</span>);
<a href=#202 id=202 data-nosnippet>202</a>
<a href=#203 id=203 data-nosnippet>203</a>    <span class="comment">// 初始化任务调度器
<a href=#204 id=204 data-nosnippet>204</a>    </span><span class="kw">let </span>task_scheduler = scheduler::TaskScheduler::new(config.max_concurrent_tasks).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#205 id=205 data-nosnippet>205</a>    <span class="kw-2">*</span>state.task_scheduler.lock().<span class="kw">await </span>= <span class="prelude-val">Some</span>(task_scheduler);
<a href=#206 id=206 data-nosnippet>206</a>    <span class="macro">info!</span>(<span class="string">"任务调度器初始化完成"</span>);
<a href=#207 id=207 data-nosnippet>207</a>
<a href=#208 id=208 data-nosnippet>208</a>    <span class="comment">// 初始化爬虫引擎
<a href=#209 id=209 data-nosnippet>209</a>    </span><span class="kw">let </span>crawler_engine = crawler::CrawlerEngine::new().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#210 id=210 data-nosnippet>210</a>    <span class="kw-2">*</span>state.crawler_engine.lock().<span class="kw">await </span>= <span class="prelude-val">Some</span>(crawler_engine);
<a href=#211 id=211 data-nosnippet>211</a>    <span class="macro">info!</span>(<span class="string">"爬虫引擎初始化完成"</span>);
<a href=#212 id=212 data-nosnippet>212</a>
<a href=#213 id=213 data-nosnippet>213</a>    <span class="comment">// 初始化系统监控
<a href=#214 id=214 data-nosnippet>214</a>    </span><span class="kw">let </span>system_monitor = monitor::SystemMonitor::new().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#215 id=215 data-nosnippet>215</a>    <span class="kw-2">*</span>state.system_monitor.lock().<span class="kw">await </span>= <span class="prelude-val">Some</span>(system_monitor);
<a href=#216 id=216 data-nosnippet>216</a>    <span class="macro">info!</span>(<span class="string">"系统监控初始化完成"</span>);
<a href=#217 id=217 data-nosnippet>217</a>
<a href=#218 id=218 data-nosnippet>218</a>    <span class="comment">// 初始化浏览器管理器
<a href=#219 id=219 data-nosnippet>219</a>    </span><span class="kw">if let </span><span class="prelude-val">Err</span>(e) = browser::initialize_browser().<span class="kw">await </span>{
<a href=#220 id=220 data-nosnippet>220</a>        <span class="macro">error!</span>(<span class="string">"浏览器管理器初始化失败: {}"</span>, e);
<a href=#221 id=221 data-nosnippet>221</a>        <span class="comment">// 浏览器初始化失败不应该阻止应用启动
<a href=#222 id=222 data-nosnippet>222</a>    </span>} <span class="kw">else </span>{
<a href=#223 id=223 data-nosnippet>223</a>        <span class="macro">info!</span>(<span class="string">"浏览器管理器初始化完成"</span>);
<a href=#224 id=224 data-nosnippet>224</a>    }
<a href=#225 id=225 data-nosnippet>225</a>
<a href=#226 id=226 data-nosnippet>226</a>    <span class="macro">info!</span>(<span class="string">"所有系统组件初始化完成"</span>);
<a href=#227 id=227 data-nosnippet>227</a>    <span class="prelude-val">Ok</span>(())
<a href=#228 id=228 data-nosnippet>228</a>}
<a href=#229 id=229 data-nosnippet>229</a>
<a href=#230 id=230 data-nosnippet>230</a><span class="comment">/*
<a href=#231 id=231 data-nosnippet>231</a>// 系统托盘功能暂时禁用，等待后续完善
<a href=#232 id=232 data-nosnippet>232</a>// 创建系统托盘
<a href=#233 id=233 data-nosnippet>233</a>fn create_system_tray() -&gt; SystemTray {
<a href=#234 id=234 data-nosnippet>234</a>    // ... 系统托盘代码
<a href=#235 id=235 data-nosnippet>235</a>}
<a href=#236 id=236 data-nosnippet>236</a>
<a href=#237 id=237 data-nosnippet>237</a>// 处理系统托盘事件
<a href=#238 id=238 data-nosnippet>238</a>fn handle_system_tray_event(app: &amp;AppHandle, event: SystemTrayEvent) {
<a href=#239 id=239 data-nosnippet>239</a>    // ... 托盘事件处理代码
<a href=#240 id=240 data-nosnippet>240</a>}
<a href=#241 id=241 data-nosnippet>241</a>
<a href=#242 id=242 data-nosnippet>242</a>// 处理托盘菜单点击
<a href=#243 id=243 data-nosnippet>243</a>fn handle_tray_menu_click(app: &amp;AppHandle, menu_id: &amp;str) {
<a href=#244 id=244 data-nosnippet>244</a>    // ... 菜单点击处理代码
<a href=#245 id=245 data-nosnippet>245</a>}
<a href=#246 id=246 data-nosnippet>246</a>*/
<a href=#247 id=247 data-nosnippet>247</a>
<a href=#248 id=248 data-nosnippet>248</a>// 显示关于对话框
<a href=#249 id=249 data-nosnippet>249</a></span><span class="kw">fn </span>show_about_dialog(app: <span class="kw-2">&amp;</span>AppHandle) {
<a href=#250 id=250 data-nosnippet>250</a>    <span class="kw">let </span>version = app.package_info().version.to_string();
<a href=#251 id=251 data-nosnippet>251</a>    <span class="kw">let </span>message = <span class="macro">format!</span>(
<a href=#252 id=252 data-nosnippet>252</a>        <span class="string">"微博爬虫节点 v{}\n\n一个基于 Rust + React 的高性能分布式微博舆情分析系统\n\n© 2024 微博舆情分析团队"</span>,
<a href=#253 id=253 data-nosnippet>253</a>        version
<a href=#254 id=254 data-nosnippet>254</a>    );
<a href=#255 id=255 data-nosnippet>255</a>
<a href=#256 id=256 data-nosnippet>256</a>    <span class="comment">// 使用JavaScript显示对话框
<a href=#257 id=257 data-nosnippet>257</a>    </span><span class="kw">if let </span><span class="prelude-val">Some</span>(window) = app.get_window(<span class="string">"main"</span>) {
<a href=#258 id=258 data-nosnippet>258</a>        <span class="kw">let </span>script = <span class="macro">format!</span>(
<a href=#259 id=259 data-nosnippet>259</a>            <span class="string">r#"alert("{}");"#</span>,
<a href=#260 id=260 data-nosnippet>260</a>            message.replace(<span class="string">'\n'</span>, <span class="string">"\\n"</span>).replace(<span class="string">'"'</span>, <span class="string">"\\\""</span>)
<a href=#261 id=261 data-nosnippet>261</a>        );
<a href=#262 id=262 data-nosnippet>262</a>        <span class="kw">let _ </span>= window.eval(<span class="kw-2">&amp;</span>script);
<a href=#263 id=263 data-nosnippet>263</a>    }
<a href=#264 id=264 data-nosnippet>264</a>}
<a href=#265 id=265 data-nosnippet>265</a>
<a href=#266 id=266 data-nosnippet>266</a><span class="comment">// 设置全局快捷键 (已禁用)
<a href=#267 id=267 data-nosnippet>267</a>/*
<a href=#268 id=268 data-nosnippet>268</a>fn setup_global_shortcuts(app: &amp;AppHandle) -&gt; Result&lt;()&gt; {
<a href=#269 id=269 data-nosnippet>269</a>    let mut shortcut_manager = app.global_shortcut_manager();
<a href=#270 id=270 data-nosnippet>270</a>
<a href=#271 id=271 data-nosnippet>271</a>    // 尝试注册快捷键，如果失败则尝试其他组合
<a href=#272 id=272 data-nosnippet>272</a>    let shortcuts_to_try = vec![
<a href=#273 id=273 data-nosnippet>273</a>        ("CmdOrCtrl+Alt+W", "显示/隐藏主窗口"),
<a href=#274 id=274 data-nosnippet>274</a>        ("CmdOrCtrl+Alt+Shift+W", "显示/隐藏主窗口"),
<a href=#275 id=275 data-nosnippet>275</a>        ("F12", "显示/隐藏主窗口"),
<a href=#276 id=276 data-nosnippet>276</a>    ];
<a href=#277 id=277 data-nosnippet>277</a>
<a href=#278 id=278 data-nosnippet>278</a>    let mut registered_toggle = false;
<a href=#279 id=279 data-nosnippet>279</a>    for (shortcut, description) in shortcuts_to_try {
<a href=#280 id=280 data-nosnippet>280</a>        let app_handle = app.clone();
<a href=#281 id=281 data-nosnippet>281</a>        match shortcut_manager.register(shortcut, move || {
<a href=#282 id=282 data-nosnippet>282</a>            if let Some(window) = app_handle.get_window("main") {
<a href=#283 id=283 data-nosnippet>283</a>                if window.is_visible().unwrap_or(false) {
<a href=#284 id=284 data-nosnippet>284</a>                    let _ = window.hide();
<a href=#285 id=285 data-nosnippet>285</a>                } else {
<a href=#286 id=286 data-nosnippet>286</a>                    let _ = window.show();
<a href=#287 id=287 data-nosnippet>287</a>                    let _ = window.set_focus();
<a href=#288 id=288 data-nosnippet>288</a>                }
<a href=#289 id=289 data-nosnippet>289</a>            }
<a href=#290 id=290 data-nosnippet>290</a>        }) {
<a href=#291 id=291 data-nosnippet>291</a>            Ok(_) =&gt; {
<a href=#292 id=292 data-nosnippet>292</a>                info!("成功注册全局快捷键: {} - {}", shortcut, description);
<a href=#293 id=293 data-nosnippet>293</a>                registered_toggle = true;
<a href=#294 id=294 data-nosnippet>294</a>                break;
<a href=#295 id=295 data-nosnippet>295</a>            }
<a href=#296 id=296 data-nosnippet>296</a>            Err(e) =&gt; {
<a href=#297 id=297 data-nosnippet>297</a>                warn!("注册快捷键 {} 失败: {}", shortcut, e);
<a href=#298 id=298 data-nosnippet>298</a>            }
<a href=#299 id=299 data-nosnippet>299</a>        }
<a href=#300 id=300 data-nosnippet>300</a>    }
<a href=#301 id=301 data-nosnippet>301</a>
<a href=#302 id=302 data-nosnippet>302</a>    if !registered_toggle {
<a href=#303 id=303 data-nosnippet>303</a>        warn!("无法注册显示/隐藏窗口的全局快捷键，将跳过此功能");
<a href=#304 id=304 data-nosnippet>304</a>    }
<a href=#305 id=305 data-nosnippet>305</a>
<a href=#306 id=306 data-nosnippet>306</a>    // 尝试注册退出快捷键
<a href=#307 id=307 data-nosnippet>307</a>    let exit_shortcuts = vec![
<a href=#308 id=308 data-nosnippet>308</a>        ("CmdOrCtrl+Alt+Q", "快速退出"),
<a href=#309 id=309 data-nosnippet>309</a>        ("CmdOrCtrl+Alt+Shift+Q", "快速退出"),
<a href=#310 id=310 data-nosnippet>310</a>    ];
<a href=#311 id=311 data-nosnippet>311</a>
<a href=#312 id=312 data-nosnippet>312</a>    let mut registered_exit = false;
<a href=#313 id=313 data-nosnippet>313</a>    for (shortcut, description) in exit_shortcuts {
<a href=#314 id=314 data-nosnippet>314</a>        let app_handle = app.clone();
<a href=#315 id=315 data-nosnippet>315</a>        match shortcut_manager.register(shortcut, move || {
<a href=#316 id=316 data-nosnippet>316</a>            app_handle.exit(0);
<a href=#317 id=317 data-nosnippet>317</a>        }) {
<a href=#318 id=318 data-nosnippet>318</a>            Ok(_) =&gt; {
<a href=#319 id=319 data-nosnippet>319</a>                info!("成功注册全局快捷键: {} - {}", shortcut, description);
<a href=#320 id=320 data-nosnippet>320</a>                registered_exit = true;
<a href=#321 id=321 data-nosnippet>321</a>                break;
<a href=#322 id=322 data-nosnippet>322</a>            }
<a href=#323 id=323 data-nosnippet>323</a>            Err(e) =&gt; {
<a href=#324 id=324 data-nosnippet>324</a>                warn!("注册快捷键 {} 失败: {}", shortcut, e);
<a href=#325 id=325 data-nosnippet>325</a>            }
<a href=#326 id=326 data-nosnippet>326</a>        }
<a href=#327 id=327 data-nosnippet>327</a>    }
<a href=#328 id=328 data-nosnippet>328</a>
<a href=#329 id=329 data-nosnippet>329</a>    if !registered_exit {
<a href=#330 id=330 data-nosnippet>330</a>        warn!("无法注册退出的全局快捷键，将跳过此功能");
<a href=#331 id=331 data-nosnippet>331</a>    }
<a href=#332 id=332 data-nosnippet>332</a>
<a href=#333 id=333 data-nosnippet>333</a>    if registered_toggle || registered_exit {
<a href=#334 id=334 data-nosnippet>334</a>        info!("全局快捷键设置完成");
<a href=#335 id=335 data-nosnippet>335</a>    } else {
<a href=#336 id=336 data-nosnippet>336</a>        warn!("所有全局快捷键注册失败，可能是权限问题或快捷键冲突");
<a href=#337 id=337 data-nosnippet>337</a>    }
<a href=#338 id=338 data-nosnippet>338</a>
<a href=#339 id=339 data-nosnippet>339</a>    Ok(())
<a href=#340 id=340 data-nosnippet>340</a>}
<a href=#341 id=341 data-nosnippet>341</a>*/
<a href=#342 id=342 data-nosnippet>342</a>
<a href=#343 id=343 data-nosnippet>343</a>// 窗口事件处理
<a href=#344 id=344 data-nosnippet>344</a></span><span class="kw">fn </span>handle_window_event(event: <span class="kw-2">&amp;</span>tauri::WindowEvent) {
<a href=#345 id=345 data-nosnippet>345</a>    <span class="kw">match </span>event {
<a href=#346 id=346 data-nosnippet>346</a>        tauri::WindowEvent::CloseRequested { api, .. } =&gt; {
<a href=#347 id=347 data-nosnippet>347</a>            <span class="comment">// 阻止窗口关闭，改为隐藏到系统托盘
<a href=#348 id=348 data-nosnippet>348</a>            </span>api.prevent_close();
<a href=#349 id=349 data-nosnippet>349</a>            <span class="comment">// 注意：这里无法直接从事件获取窗口引用，需要其他方式处理
<a href=#350 id=350 data-nosnippet>350</a>        </span>}
<a href=#351 id=351 data-nosnippet>351</a>        tauri::WindowEvent::Focused(focused) =&gt; {
<a href=#352 id=352 data-nosnippet>352</a>            <span class="kw">if </span><span class="kw-2">*</span>focused {
<a href=#353 id=353 data-nosnippet>353</a>                <span class="macro">info!</span>(<span class="string">"窗口获得焦点"</span>);
<a href=#354 id=354 data-nosnippet>354</a>            } <span class="kw">else </span>{
<a href=#355 id=355 data-nosnippet>355</a>                <span class="macro">info!</span>(<span class="string">"窗口失去焦点"</span>);
<a href=#356 id=356 data-nosnippet>356</a>            }
<a href=#357 id=357 data-nosnippet>357</a>        }
<a href=#358 id=358 data-nosnippet>358</a>        <span class="kw">_ </span>=&gt; {}
<a href=#359 id=359 data-nosnippet>359</a>    }
<a href=#360 id=360 data-nosnippet>360</a>}
<a href=#361 id=361 data-nosnippet>361</a>
<a href=#362 id=362 data-nosnippet>362</a><span class="comment">// 应用启动时的初始化
<a href=#363 id=363 data-nosnippet>363</a></span><span class="kw">fn </span>setup_app_initialization(app: <span class="kw-2">&amp;</span>AppHandle) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#364 id=364 data-nosnippet>364</a>    <span class="comment">// 移除全局快捷键功能
<a href=#365 id=365 data-nosnippet>365</a>    // if let Err(e) = setup_global_shortcuts(app) {
<a href=#366 id=366 data-nosnippet>366</a>    //     warn!("设置全局快捷键失败: {}", e);
<a href=#367 id=367 data-nosnippet>367</a>    // }
<a href=#368 id=368 data-nosnippet>368</a>
<a href=#369 id=369 data-nosnippet>369</a>    // 设置窗口事件监听
<a href=#370 id=370 data-nosnippet>370</a>    </span><span class="kw">if let </span><span class="prelude-val">Some</span>(window) = app.get_window(<span class="string">"main"</span>) {
<a href=#371 id=371 data-nosnippet>371</a>        window.on_window_event(<span class="kw">move </span>|event| {
<a href=#372 id=372 data-nosnippet>372</a>            handle_window_event(<span class="kw-2">&amp;</span>event);
<a href=#373 id=373 data-nosnippet>373</a>        });
<a href=#374 id=374 data-nosnippet>374</a>
<a href=#375 id=375 data-nosnippet>375</a>        <span class="comment">// 设置窗口初始状态
<a href=#376 id=376 data-nosnippet>376</a>        </span><span class="kw">let _ </span>= window.set_title(<span class="string">"Weibo Crawler Node - Distributed Data Collection System"</span>);
<a href=#377 id=377 data-nosnippet>377</a>
<a href=#378 id=378 data-nosnippet>378</a>        <span class="comment">// 如果是首次启动，显示欢迎信息
<a href=#379 id=379 data-nosnippet>379</a>        </span><span class="kw">let _ </span>= window.eval(<span class="string">r#"
<a href=#380 id=380 data-nosnippet>380</a>            console.log('Weibo Crawler Node started');
<a href=#381 id=381 data-nosnippet>381</a>            if (window.location.hash === '') {
<a href=#382 id=382 data-nosnippet>382</a>                window.location.hash = '#/';
<a href=#383 id=383 data-nosnippet>383</a>            }
<a href=#384 id=384 data-nosnippet>384</a>        "#</span>);
<a href=#385 id=385 data-nosnippet>385</a>    }
<a href=#386 id=386 data-nosnippet>386</a>
<a href=#387 id=387 data-nosnippet>387</a>    <span class="macro">info!</span>(<span class="string">"应用初始化完成"</span>);
<a href=#388 id=388 data-nosnippet>388</a>    <span class="prelude-val">Ok</span>(())
<a href=#389 id=389 data-nosnippet>389</a>}
<a href=#390 id=390 data-nosnippet>390</a>
<a href=#391 id=391 data-nosnippet>391</a><span class="comment">// 应用退出时的清理
<a href=#392 id=392 data-nosnippet>392</a></span><span class="kw">async fn </span>cleanup_on_exit(app_handle: <span class="kw-2">&amp;</span>AppHandle) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#393 id=393 data-nosnippet>393</a>    <span class="macro">info!</span>(<span class="string">"开始应用清理..."</span>);
<a href=#394 id=394 data-nosnippet>394</a>
<a href=#395 id=395 data-nosnippet>395</a>    <span class="comment">// 移除全局快捷键清理
<a href=#396 id=396 data-nosnippet>396</a>    // let mut shortcut_manager = app_handle.global_shortcut_manager();
<a href=#397 id=397 data-nosnippet>397</a>    // shortcut_manager.unregister_all().map_err(|e| {
<a href=#398 id=398 data-nosnippet>398</a>    //     error::AppError::Other(format!("清理全局快捷键失败: {}", e))
<a href=#399 id=399 data-nosnippet>399</a>    // })?;
<a href=#400 id=400 data-nosnippet>400</a>
<a href=#401 id=401 data-nosnippet>401</a>    // 停止所有服务
<a href=#402 id=402 data-nosnippet>402</a>    </span><span class="kw">let </span>_state = app_handle.state::&lt;AppState&gt;();
<a href=#403 id=403 data-nosnippet>403</a>
<a href=#404 id=404 data-nosnippet>404</a>    <span class="comment">// 清理浏览器资源
<a href=#405 id=405 data-nosnippet>405</a>    </span><span class="kw">if let </span><span class="prelude-val">Err</span>(e) = browser::shutdown_browser().<span class="kw">await </span>{
<a href=#406 id=406 data-nosnippet>406</a>        <span class="macro">error!</span>(<span class="string">"浏览器清理失败: {}"</span>, e);
<a href=#407 id=407 data-nosnippet>407</a>    } <span class="kw">else </span>{
<a href=#408 id=408 data-nosnippet>408</a>        <span class="macro">info!</span>(<span class="string">"浏览器资源清理完成"</span>);
<a href=#409 id=409 data-nosnippet>409</a>    }
<a href=#410 id=410 data-nosnippet>410</a>
<a href=#411 id=411 data-nosnippet>411</a>    <span class="comment">// 这里可以添加更多清理逻辑
<a href=#412 id=412 data-nosnippet>412</a>    // 比如保存当前状态、关闭数据库连接等
<a href=#413 id=413 data-nosnippet>413</a>
<a href=#414 id=414 data-nosnippet>414</a>    </span><span class="macro">info!</span>(<span class="string">"应用清理完成"</span>);
<a href=#415 id=415 data-nosnippet>415</a>    <span class="prelude-val">Ok</span>(())
<a href=#416 id=416 data-nosnippet>416</a>}</code></pre></div></section></main></body></html>