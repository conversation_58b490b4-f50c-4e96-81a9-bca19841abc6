
/// A map of entity names to their codepoints. The second codepoint will
/// be 0 if the entity contains a single codepoint. Entities have their preceeding '&' removed.
///
/// # Examples
///
/// ```
/// use markup5ever::data::NAMED_ENTITIES;
///
/// assert_eq!(NAMED_ENTITIES.get("gt;").unwrap(), &(62, 0));
/// ```

pub static NAMED_ENTITIES: Map<&'static str, (u32, u32)> = ::phf::Map {
    key: 12913932095322966823,
    disps: &[
        (0, 147),
        (0, 2),
        (0, 236),
        (0, 9),
        (0, 66),
        (0, 0),
        (0, 38),
        (0, 154),
        (0, 0),
        (0, 2),
        (0, 777),
        (0, 73),
        (0, 65),
        (0, 0),
        (0, 1),
        (0, 0),
        (0, 24),
        (0, 131),
        (0, 0),
        (0, 5),
        (0, 0),
        (0, 193),
        (0, 25),
        (0, 7),
        (0, 442),
        (0, 154),
        (0, 240),
        (0, 8),
        (0, 6),
        (0, 53),
        (0, 17),
        (0, 4),
        (0, 35),
        (0, 346),
        (0, 91),
        (0, 85),
        (0, 89),
        (0, 0),
        (0, 787),
        (0, 27),
        (0, 1),
        (0, 4),
        (0, 7),
        (0, 79),
        (0, 7),
        (0, 44),
        (0, 82),
        (0, 437),
        (0, 4),
        (0, 58),
        (0, 3),
        (0, 51),
        (0, 1),
        (0, 65),
        (0, 249),
        (0, 9),
        (0, 1),
        (0, 66),
        (0, 30),
        (0, 1244),
        (0, 21),
        (0, 1),
        (0, 15),
        (0, 510),
        (0, 5),
        (0, 0),
        (0, 9),
        (0, 0),
        (0, 5),
        (0, 86),
        (0, 46),
        (0, 118),
        (0, 28),
        (0, 30),
        (0, 0),
        (0, 0),
        (0, 0),
        (0, 1009),
        (0, 1),
        (0, 10),
        (0, 259),
        (0, 326),
        (0, 29),
        (0, 1),
        (0, 0),
        (0, 889),
        (0, 9),
        (0, 165),
        (0, 73),
        (0, 19),
        (0, 69),
        (0, 237),
        (0, 6),
        (0, 686),
        (0, 2),
        (0, 6),
        (0, 83),
        (0, 1955),
        (0, 116),
        (0, 161),
        (0, 2),
        (0, 88),
        (0, 10),
        (0, 1066),
        (0, 1),
        (0, 1414),
        (0, 3),
        (0, 57),
        (0, 10),
        (0, 9),
        (0, 276),
        (0, 464),
        (0, 26),
        (0, 458),
        (0, 9),
        (0, 699),
        (0, 117),
        (0, 1136),
        (0, 26),
        (0, 218),
        (0, 31),
        (0, 46),
        (0, 261),
        (0, 183),
        (0, 319),
        (0, 1553),
        (0, 232),
        (0, 1),
        (0, 1279),
        (0, 13),
        (0, 8),
        (0, 1),
        (0, 1728),
        (0, 600),
        (0, 4),
        (0, 584),
        (0, 1),
        (0, 146),
        (0, 342),
        (0, 1442),
        (0, 223),
        (0, 473),
        (0, 230),
        (0, 510),
        (0, 19),
        (0, 255),
        (0, 90),
        (0, 152),
        (0, 1),
        (0, 1773),
        (0, 3333),
        (0, 1),
        (0, 1855),
        (0, 30),
        (0, 1151),
        (0, 132),
        (0, 980),
        (0, 0),
        (0, 11),
        (0, 1021),
        (0, 398),
        (0, 2985),
        (0, 936),
        (0, 26),
        (0, 1563),
        (0, 15),
        (0, 26),
        (0, 2),
        (0, 570),
        (0, 868),
        (0, 200),
        (0, 302),
        (0, 27),
        (0, 686),
        (0, 196),
        (0, 16),
        (0, 33),
        (0, 19),
        (0, 162),
        (0, 11),
        (0, 24),
        (0, 8),
        (0, 7),
        (0, 83),
        (0, 766),
        (0, 8),
        (0, 1639),
        (0, 555),
        (0, 191),
        (0, 0),
        (0, 4),
        (0, 28),
        (0, 32),
        (0, 25),
        (0, 564),
        (0, 11),
        (0, 3),
        (0, 8),
        (0, 25),
        (0, 3),
        (0, 225),
        (0, 47),
        (0, 274),
        (0, 51),
        (0, 60),
        (0, 2),
        (0, 142),
        (0, 84),
        (0, 1236),
        (0, 374),
        (0, 528),
        (0, 742),
        (0, 156),
        (0, 1),
        (0, 0),
        (0, 148),
        (0, 7),
        (0, 1),
        (0, 5),
        (0, 174),
        (0, 479),
        (0, 465),
        (0, 125),
        (0, 94),
        (0, 8),
        (0, 63),
        (0, 119),
        (0, 844),
        (0, 147),
        (0, 1),
        (0, 325),
        (0, 224),
        (0, 1),
        (0, 757),
        (0, 4),
        (0, 2),
        (0, 69),
        (0, 17),
        (0, 165),
        (0, 118),
        (0, 20),
        (0, 88),
        (0, 12),
        (0, 184),
        (0, 1),
        (0, 349),
        (0, 319),
        (0, 22),
        (0, 247),
        (0, 15),
        (0, 255),
        (0, 0),
        (0, 0),
        (0, 4),
        (0, 0),
        (0, 1951),
        (0, 18),
        (0, 58),
        (0, 620),
        (0, 495),
        (0, 5),
        (0, 2),
        (0, 1166),
        (0, 2),
        (0, 0),
        (0, 29),
        (0, 2),
        (0, 51),
        (0, 7),
        (0, 1075),
        (0, 48),
        (0, 680),
        (0, 2970),
        (0, 1),
        (0, 255),
        (0, 16),
        (0, 57),
        (0, 13),
        (0, 2),
        (0, 364),
        (0, 75),
        (0, 1821),
        (0, 1),
        (0, 0),
        (0, 2),
        (0, 10),
        (0, 372),
        (0, 6),
        (0, 123),
        (0, 1753),
        (0, 103),
        (0, 2),
        (0, 2),
        (0, 441),
        (0, 38),
        (0, 3),
        (0, 4),
        (0, 79),
        (0, 0),
        (0, 1),
        (0, 272),
        (0, 3),
        (0, 2),
        (0, 0),
        (0, 42),
        (0, 522),
        (0, 42),
        (0, 462),
        (0, 31),
        (0, 3),
        (0, 18),
        (0, 88),
        (0, 13),
        (0, 47),
        (0, 2005),
        (0, 76),
        (0, 88),
        (0, 18),
        (0, 36),
        (0, 26),
        (0, 29),
        (0, 7),
        (0, 47),
        (0, 14),
        (0, 46),
        (0, 169),
        (0, 205),
        (0, 1),
        (0, 100),
        (0, 523),
        (0, 93),
        (0, 23),
        (0, 6),
        (0, 74),
        (0, 1),
        (0, 4),
        (0, 4),
        (0, 115),
        (0, 6),
        (0, 5511),
        (0, 6),
        (0, 815),
        (0, 2),
        (0, 123),
        (0, 332),
        (0, 693),
        (0, 328),
        (0, 752),
        (0, 39),
        (0, 13),
        (0, 101),
        (0, 0),
        (0, 2031),
        (0, 65),
        (0, 842),
        (0, 13),
        (0, 5),
        (0, 1114),
        (0, 25),
        (0, 167),
        (0, 457),
        (0, 828),
        (0, 156),
        (0, 2),
        (0, 500),
        (0, 0),
        (0, 348),
        (0, 0),
        (0, 15),
        (0, 4),
        (0, 7),
        (0, 0),
        (0, 596),
        (0, 531),
        (0, 92),
        (0, 79),
        (0, 1122),
        (0, 108),
        (0, 44),
        (0, 75),
        (0, 225),
        (0, 438),
        (0, 37),
        (0, 12),
        (0, 1),
        (0, 1095),
        (0, 131),
        (0, 36),
        (0, 299),
        (0, 784),
        (0, 1576),
        (0, 396),
        (0, 6),
        (0, 0),
        (0, 219),
        (0, 6),
        (0, 233),
        (0, 726),
        (0, 22),
        (0, 301),
        (0, 28),
        (0, 1363),
        (0, 45),
        (0, 1761),
        (0, 0),
        (0, 744),
        (0, 25),
        (0, 2),
        (0, 10),
        (0, 6),
        (0, 2),
        (0, 1417),
        (0, 822),
        (0, 101),
        (0, 4),
        (0, 60),
        (0, 226),
        (0, 28),
        (0, 13),
        (0, 598),
        (0, 4),
        (0, 75),
        (0, 2555),
        (0, 6),
        (0, 0),
        (0, 74),
        (0, 256),
        (0, 11),
        (0, 0),
        (0, 231),
        (0, 0),
        (0, 4),
        (0, 409),
        (0, 68),
        (0, 19),
        (0, 176),
        (0, 1),
        (0, 416),
        (0, 903),
        (0, 28),
        (0, 4),
        (0, 287),
        (0, 1781),
        (0, 2163),
        (0, 19),
        (0, 3),
        (0, 173),
        (0, 1),
        (0, 195),
        (0, 67),
        (0, 14),
        (0, 157),
        (0, 1),
        (0, 130),
        (0, 44),
        (0, 68),
        (0, 19),
        (0, 11),
        (0, 14),
        (0, 198),
        (0, 2),
        (0, 30),
        (0, 457),
        (0, 634),
        (0, 365),
        (0, 10),
        (0, 68),
        (0, 63),
        (0, 850),
        (0, 28),
        (0, 57),
        (0, 638),
        (0, 33),
        (0, 250),
        (0, 228),
        (0, 2719),
        (0, 21),
        (0, 1),
        (0, 71),
        (0, 0),
        (0, 132),
        (0, 551),
        (0, 23),
        (0, 0),
        (0, 831),
        (0, 3),
        (0, 2),
        (0, 0),
        (0, 7),
        (0, 4873),
        (0, 285),
        (0, 21),
        (0, 232),
        (0, 1950),
        (0, 164),
        (0, 33),
        (0, 237),
        (0, 0),
        (0, 1755),
        (0, 0),
        (0, 1922),
        (0, 80),
        (0, 4),
        (0, 319),
        (0, 6),
        (0, 65),
        (0, 1664),
        (0, 319),
        (0, 176),
        (0, 784),
        (0, 4),
        (0, 15),
        (0, 15),
        (0, 4),
        (0, 1),
        (0, 8),
        (0, 1009),
        (0, 123),
        (0, 83),
        (0, 0),
        (0, 25),
        (0, 66),
        (0, 11),
        (0, 18),
        (0, 7),
        (0, 136),
        (0, 0),
        (0, 6),
        (0, 648),
        (0, 424),
        (0, 0),
        (0, 100),
        (0, 579),
        (0, 74),
        (0, 6),
        (0, 188),
        (0, 3469),
        (0, 293),
        (0, 1),
        (0, 145),
        (0, 87),
        (0, 46),
        (0, 1088),
        (0, 45),
        (0, 131),
        (0, 2375),
        (0, 87),
        (0, 2),
        (0, 0),
        (0, 1),
        (0, 0),
        (0, 167),
        (0, 29),
        (0, 121),
        (0, 249),
        (0, 9),
        (0, 260),
        (0, 24),
        (0, 143),
        (0, 59),
        (0, 321),
        (0, 273),
        (0, 86),
        (0, 457),
        (0, 1028),
        (0, 1327),
        (0, 1093),
        (0, 53),
        (0, 324),
        (0, 17),
        (0, 7),
        (0, 441),
        (0, 312),
        (0, 701),
        (0, 10),
        (0, 473),
        (0, 2398),
        (0, 460),
        (0, 0),
        (0, 364),
        (0, 394),
        (0, 334),
        (0, 156),
        (0, 560),
        (0, 366),
        (0, 456),
        (0, 609),
        (0, 224),
        (0, 51),
        (0, 972),
        (0, 48),
        (0, 964),
        (0, 0),
        (0, 0),
        (0, 1371),
        (0, 104),
        (0, 73),
        (0, 463),
        (0, 1),
        (0, 24),
        (0, 40),
        (0, 2),
        (0, 162),
        (0, 1),
        (0, 49),
        (0, 0),
        (0, 0),
        (0, 352),
        (0, 243),
        (0, 679),
        (0, 64),
        (0, 473),
        (0, 95),
        (0, 215),
        (0, 312),
        (0, 141),
        (0, 4),
        (0, 1024),
        (0, 0),
        (0, 1211),
        (0, 11),
        (0, 8),
        (0, 25),
        (0, 194),
        (0, 261),
        (0, 25),
        (0, 145),
        (0, 69),
        (0, 125),
        (0, 1),
        (0, 2),
        (0, 198),
        (0, 180),
        (0, 350),
        (0, 1),
        (0, 36),
        (0, 340),
        (0, 29),
        (0, 99),
        (0, 868),
        (0, 0),
        (0, 542),
        (0, 255),
        (0, 273),
        (0, 12),
        (0, 975),
        (0, 3664),
        (0, 206),
        (0, 240),
        (0, 2),
        (0, 838),
        (0, 316),
        (0, 107),
        (0, 1),
        (0, 53),
        (0, 62),
        (0, 3),
        (0, 1500),
        (0, 1009),
        (0, 145),
        (0, 243),
        (0, 1418),
        (0, 6),
        (0, 51),
        (0, 225),
        (0, 85),
        (0, 10),
        (0, 1),
        (0, 69),
        (0, 36),
        (0, 22),
        (0, 3),
        (0, 69),
        (0, 8),
        (0, 47),
        (0, 1351),
        (0, 283),
        (0, 12),
        (0, 226),
        (0, 603),
        (0, 62),
        (0, 2070),
        (0, 8),
        (0, 2),
        (0, 631),
        (0, 276),
        (0, 449),
        (0, 115),
        (0, 240),
        (0, 27),
        (0, 1106),
        (0, 7),
        (0, 1),
        (0, 85),
        (0, 0),
        (0, 42),
        (0, 7),
        (0, 1),
        (0, 1611),
        (0, 17),
        (0, 0),
        (0, 1),
        (0, 143),
        (0, 257),
        (0, 220),
        (0, 397),
        (0, 353),
        (0, 1),
        (0, 5),
        (0, 935),
        (0, 1),
        (0, 9),
        (0, 5),
        (0, 53),
        (0, 4),
        (0, 2559),
        (0, 37),
        (0, 75),
        (0, 540),
        (0, 2488),
        (0, 136),
        (0, 236),
        (0, 57),
        (0, 12),
        (0, 54),
        (0, 14),
        (0, 648),
        (0, 4),
        (0, 0),
        (0, 9),
        (0, 635),
        (0, 0),
        (0, 357),
        (0, 115),
        (0, 91),
        (0, 7),
        (0, 221),
        (0, 892),
        (0, 2383),
        (0, 551),
        (0, 29),
        (0, 18),
        (0, 1416),
        (0, 2697),
        (0, 69),
        (0, 131),
        (0, 97),
        (0, 1671),
        (0, 39),
        (0, 0),
        (0, 179),
        (0, 0),
        (0, 360),
        (0, 144),
        (0, 649),
        (0, 1516),
        (0, 163),
        (0, 52),
        (0, 5),
        (0, 531),
        (0, 1028),
        (0, 121),
        (0, 204),
        (0, 364),
        (0, 867),
        (0, 123),
        (0, 38),
        (0, 8),
        (0, 2334),
        (0, 9),
        (0, 34),
        (0, 351),
        (0, 166),
        (0, 1),
        (0, 902),
        (0, 832),
        (0, 117),
        (0, 256),
        (0, 41),
        (0, 4846),
        (0, 444),
        (0, 225),
        (0, 25),
        (0, 0),
        (0, 1963),
        (0, 0),
        (0, 1834),
        (0, 90),
        (0, 30),
        (0, 1786),
        (0, 6),
        (0, 42),
        (0, 284),
        (0, 281),
        (0, 192),
        (0, 11),
        (0, 187),
        (0, 2),
        (0, 7),
        (0, 4),
        (0, 33),
        (0, 847),
        (0, 1),
        (0, 0),
        (0, 223),
        (0, 52),
        (0, 979),
        (0, 11),
        (0, 6),
        (0, 121),
        (0, 337),
        (0, 92),
        (0, 49),
        (0, 87),
        (0, 596),
        (0, 58),
        (0, 0),
        (0, 0),
        (0, 30),
        (0, 19),
        (0, 4),
        (0, 849),
        (0, 0),
        (0, 12),
        (0, 145),
        (0, 857),
        (0, 1),
        (0, 177),
        (0, 45),
        (0, 3),
        (0, 18),
        (0, 5),
        (0, 59),
        (0, 1254),
        (0, 335),
        (0, 16),
        (0, 26),
        (0, 15),
        (0, 1733),
        (0, 1),
        (0, 0),
        (0, 1006),
        (0, 1001),
        (0, 219),
        (0, 12),
        (0, 271),
        (0, 5),
        (0, 34),
        (0, 7),
        (0, 11),
        (0, 1),
        (0, 12),
        (0, 102),
        (0, 31),
        (0, 65),
        (0, 408),
        (0, 12),
        (0, 2),
        (0, 88),
        (0, 3280),
        (0, 93),
        (0, 4546),
        (0, 1530),
        (0, 14),
        (0, 26),
        (0, 34),
        (0, 4),
        (0, 1),
        (0, 279),
        (0, 696),
        (0, 64),
        (0, 221),
        (0, 39),
        (0, 71),
        (0, 81),
        (0, 0),
        (0, 15),
        (0, 210),
        (0, 72),
        (0, 696),
        (0, 2337),
        (0, 954),
        (0, 170),
        (0, 1415),
        (0, 180),
        (0, 2104),
        (0, 4),
        (0, 38),
        (0, 2),
        (0, 5),
        (0, 14),
        (0, 98),
        (0, 1),
        (0, 193),
        (0, 188),
        (0, 61),
        (0, 91),
        (0, 1085),
        (0, 586),
        (0, 20),
        (0, 611),
        (0, 33),
        (0, 5063),
        (0, 0),
        (0, 3200),
        (0, 22),
        (0, 757),
        (0, 51),
        (0, 1),
        (0, 6),
        (0, 150),
        (0, 7),
        (0, 1),
        (0, 51),
        (0, 526),
        (0, 30),
        (0, 0),
        (0, 193),
        (0, 482),
        (0, 38),
        (0, 2391),
        (0, 2),
        (0, 27),
        (0, 30),
        (0, 35),
        (0, 0),
        (0, 0),
        (0, 1),
        (0, 131),
        (0, 2952),
        (0, 38),
        (0, 215),
        (0, 5),
        (0, 0),
        (0, 1659),
        (0, 181),
        (0, 39),
        (0, 23),
        (0, 14),
        (0, 3862),
        (0, 197),
        (0, 1),
        (0, 0),
        (0, 2428),
        (0, 239),
        (0, 1),
        (0, 0),
        (0, 106),
        (0, 0),
        (0, 9),
        (0, 0),
        (0, 85),
        (0, 867),
        (0, 205),
        (0, 138),
        (0, 1413),
        (0, 70),
        (0, 498),
        (0, 1),
        (0, 2269),
        (0, 16),
        (0, 6),
        (0, 0),
        (0, 2),
        (0, 30),
        (0, 282),
        (0, 566),
        (0, 54),
        (0, 54),
        (0, 182),
        (0, 131),
        (0, 1240),
        (0, 89),
        (0, 15),
        (0, 36),
        (0, 450),
        (0, 9),
        (0, 79),
        (0, 2435),
        (0, 5),
        (0, 1589),
        (0, 2),
        (0, 97),
        (0, 962),
        (0, 21),
        (0, 36),
        (0, 409),
        (0, 537),
        (0, 1068),
        (0, 9),
        (0, 126),
        (0, 830),
        (0, 1),
        (0, 1392),
        (0, 1),
        (0, 55),
        (0, 2),
        (0, 1),
        (0, 305),
        (0, 3260),
        (0, 427),
        (0, 262),
        (0, 5),
        (0, 253),
        (0, 879),
        (0, 52),
        (0, 160),
        (0, 4056),
        (0, 3806),
        (0, 6),
        (0, 1539),
        (0, 9),
        (0, 580),
        (0, 198),
        (0, 617),
        (0, 65),
        (0, 14),
        (0, 2),
        (0, 50),
        (0, 0),
        (0, 50),
        (0, 32),
        (0, 6),
        (0, 1402),
        (0, 1074),
        (0, 4601),
        (0, 230),
        (0, 23),
        (0, 40),
        (0, 789),
        (0, 938),
        (0, 1555),
        (0, 2),
        (0, 594),
        (0, 2),
        (0, 14),
        (0, 223),
        (0, 8),
        (0, 75),
        (0, 1198),
        (0, 136),
        (0, 0),
        (0, 3),
        (0, 864),
        (0, 137),
        (0, 9290),
        (0, 0),
        (0, 209),
        (0, 165),
        (0, 836),
        (0, 103),
        (0, 720),
        (0, 708),
        (0, 2),
        (0, 0),
        (0, 150),
        (0, 1021),
        (0, 41),
        (0, 3329),
        (0, 1),
        (0, 191),
        (0, 1),
        (0, 148),
        (0, 27),
        (0, 154),
        (0, 1498),
        (0, 733),
        (0, 154),
        (0, 1),
        (0, 3982),
        (0, 8095),
        (0, 3),
        (0, 67),
        (0, 1001),
        (0, 703),
        (0, 756),
        (0, 76),
        (0, 34),
        (0, 963),
        (0, 1197),
        (0, 0),
        (0, 2),
        (0, 121),
        (0, 1062),
        (0, 776),
        (0, 145),
        (0, 276),
        (0, 0),
        (0, 20),
        (0, 5030),
        (0, 100),
        (0, 3104),
        (0, 1),
        (0, 1),
        (0, 15),
        (0, 0),
        (0, 2313),
        (0, 31),
        (0, 5277),
        (0, 11),
        (0, 434),
        (0, 112),
        (0, 2),
        (0, 635),
        (0, 339),
        (0, 1103),
        (0, 23),
        (0, 86),
        (0, 1),
        (0, 145),
        (0, 61),
        (0, 1242),
        (0, 0),
        (0, 0),
        (0, 2),
        (0, 4),
        (0, 28),
        (0, 7),
        (0, 5580),
        (0, 22),
        (0, 1125),
        (0, 20),
        (0, 137),
        (0, 3609),
        (0, 131),
        (0, 6),
        (0, 0),
        (0, 1289),
        (0, 2),
        (0, 1559),
        (0, 598),
        (0, 11),
        (0, 1909),
        (0, 1591),
        (0, 22),
        (0, 0),
        (0, 23),
        (0, 296),
        (0, 19),
        (0, 8441),
        (0, 0),
        (0, 9),
        (0, 8),
        (0, 2),
        (0, 1993),
        (0, 69),
        (0, 19),
        (0, 19),
        (0, 5),
        (0, 331),
        (0, 16),
        (0, 6),
        (0, 3347),
        (0, 49),
        (0, 17),
        (0, 1),
        (0, 516),
        (0, 156),
        (0, 26),
        (0, 501),
        (0, 72),
        (0, 106),
        (0, 2),
        (0, 336),
        (0, 22),
        (0, 0),
        (0, 107),
        (0, 939),
        (0, 341),
        (0, 19),
        (0, 2877),
        (0, 6),
        (0, 177),
        (0, 821),
        (0, 1100),
        (0, 194),
        (0, 24),
        (0, 599),
        (0, 18),
        (0, 21),
        (0, 17),
        (0, 42),
        (0, 7),
        (0, 86),
        (0, 0),
        (0, 238),
        (0, 8),
        (0, 5),
        (0, 1),
        (0, 68),
        (0, 9193),
        (0, 11),
        (0, 6763),
        (0, 158),
        (0, 386),
        (0, 4041),
        (0, 63),
        (0, 1819),
        (0, 18),
        (0, 0),
        (0, 2036),
        (0, 345),
        (0, 2203),
        (0, 113),
        (0, 6),
        (0, 371),
        (0, 7),
        (0, 194),
        (0, 26),
        (0, 3184),
        (0, 185),
        (0, 136),
        (0, 35),
        (0, 1883),
        (0, 981),
        (0, 1497),
        (0, 140),
        (0, 6),
        (0, 0),
        (0, 5),
        (0, 4246),
        (0, 121),
        (0, 404),
        (0, 398),
        (0, 260),
        (0, 368),
        (0, 212),
        (0, 2),
        (0, 30),
        (0, 45),
        (0, 988),
        (0, 82),
        (0, 79),
        (0, 1556),
        (0, 5),
        (0, 168),
        (0, 0),
        (0, 336),
        (0, 1699),
        (0, 141),
        (0, 13),
        (0, 0),
        (0, 242),
        (0, 40),
        (0, 263),
        (0, 6464),
        (0, 1332),
        (0, 4),
        (0, 459),
        (0, 7),
        (0, 20),
        (0, 1),
        (0, 3523),
        (0, 19),
        (0, 0),
        (0, 11),
        (0, 8),
        (0, 24),
        (0, 608),
        (0, 8),
        (0, 50),
        (0, 1),
        (0, 2),
        (0, 1723),
        (0, 157),
        (1, 108),
        (0, 3),
        (0, 0),
        (0, 7122),
        (0, 151),
        (0, 4),
        (0, 0),
        (0, 1),
        (0, 30),
        (0, 5289),
        (0, 2),
        (0, 122),
        (0, 66),
        (0, 3),
        (0, 7),
        (0, 8258),
        (0, 227),
        (0, 3),
        (0, 652),
        (0, 574),
        (0, 376),
        (0, 522),
        (0, 110),
        (0, 1),
        (0, 920),
        (0, 2012),
        (0, 4754),
        (0, 412),
        (0, 2774),
        (0, 12),
        (0, 1262),
        (0, 144),
        (0, 181),
        (0, 94),
        (0, 1903),
        (0, 249),
        (0, 8),
        (0, 9),
        (0, 96),
        (0, 1323),
        (0, 65),
        (0, 20),
        (0, 1270),
        (0, 0),
        (0, 1551),
        (0, 13),
        (0, 0),
        (0, 5090),
        (0, 2007),
        (0, 6405),
        (1, 891),
        (0, 2327),
        (0, 2029),
        (0, 1078),
        (0, 2),
        (0, 3),
        (0, 1),
        (0, 661),
        (0, 71),
        (0, 43),
        (0, 73),
        (0, 12),
        (0, 223),
        (0, 90),
        (0, 1360),
        (0, 5),
        (0, 329),
        (0, 29),
        (0, 398),
        (0, 25),
        (0, 14),
        (0, 980),
        (0, 271),
        (0, 0),
        (0, 466),
        (0, 6142),
        (0, 247),
        (0, 172),
        (0, 47),
        (0, 38),
        (0, 1576),
        (0, 364),
        (0, 4167),
        (0, 3656),
        (0, 3934),
        (0, 721),
        (0, 1329),
        (0, 96),
        (0, 625),
        (0, 82),
        (0, 72),
        (0, 1763),
        (0, 20),
        (0, 947),
        (0, 62),
        (0, 478),
        (0, 0),
        (0, 1),
        (0, 5),
        (0, 1),
        (0, 272),
        (0, 1522),
        (0, 857),
        (0, 10),
        (0, 4),
        (0, 51),
        (0, 252),
        (0, 4),
        (0, 90),
        (0, 65),
        (0, 1148),
        (0, 990),
        (0, 39),
        (0, 6),
        (0, 3926),
        (0, 49),
        (0, 1362),
        (0, 9),
        (0, 35),
        (0, 0),
        (0, 3880),
        (0, 330),
        (0, 32),
        (0, 16),
        (0, 5919),
        (0, 22),
        (0, 122),
        (0, 3527),
        (0, 16),
        (0, 1085),
        (0, 308),
        (0, 134),
        (0, 185),
        (0, 138),
        (0, 2443),
        (0, 754),
        (0, 506),
        (0, 184),
        (0, 6121),
        (0, 1),
        (0, 162),
        (0, 17),
        (0, 28),
        (0, 17),
        (0, 3843),
        (0, 5),
        (0, 158),
        (0, 29),
        (0, 42),
        (0, 664),
        (0, 388),
        (0, 1209),
        (0, 0),
        (0, 1640),
        (0, 58),
        (0, 1),
        (0, 18),
        (0, 41),
        (0, 7498),
        (0, 7138),
        (0, 5),
        (0, 134),
        (0, 9),
        (0, 1),
        (0, 207),
        (0, 82),
        (0, 7984),
        (0, 42),
        (0, 903),
        (0, 258),
        (0, 9392),
        (0, 36),
        (0, 46),
        (0, 0),
        (0, 228),
        (0, 1633),
        (0, 2445),
        (0, 163),
        (0, 885),
        (0, 2340),
        (0, 1173),
        (0, 137),
        (0, 5859),
        (0, 202),
        (0, 12),
        (0, 1147),
        (0, 3825),
        (0, 525),
        (0, 198),
        (0, 182),
        (0, 326),
        (0, 210),
        (0, 3107),
        (0, 50),
        (0, 257),
        (0, 47),
        (0, 832),
        (0, 4166),
        (0, 1592),
        (0, 16),
        (0, 10),
        (1, 6970),
        (0, 36),
        (0, 16),
        (0, 1),
        (0, 9),
        (0, 31),
        (0, 4294),
        (0, 4069),
        (0, 28),
        (0, 476),
        (0, 2810),
        (0, 403),
        (0, 225),
        (0, 583),
        (0, 29),
        (0, 261),
        (0, 705),
        (0, 5),
        (0, 1938),
        (0, 948),
        (0, 7),
        (0, 3653),
        (0, 612),
        (0, 7244),
        (0, 29),
        (0, 154),
        (0, 111),
        (0, 164),
        (0, 61),
        (0, 36),
        (0, 226),
        (0, 7905),
        (0, 767),
        (0, 0),
        (0, 32),
        (0, 6),
        (0, 949),
        (0, 2011),
        (0, 25),
        (0, 0),
        (0, 27),
        (0, 993),
        (0, 108),
        (0, 496),
        (0, 2591),
        (0, 2225),
        (1, 1620),
        (0, 182),
        (0, 369),
        (0, 2765),
        (0, 165),
        (0, 78),
        (0, 313),
        (0, 163),
        (0, 26),
        (0, 5609),
        (0, 123),
        (0, 8),
        (0, 750),
        (0, 3621),
        (0, 272),
        (0, 1),
        (0, 0),
        (0, 2785),
        (0, 1),
        (0, 9118),
        (0, 1166),
        (0, 1564),
        (0, 0),
        (0, 2468),
        (0, 20),
        (0, 4),
        (0, 1506),
        (1, 371),
        (0, 533),
        (0, 7),
        (0, 38),
        (0, 1051),
        (0, 4),
        (0, 7),
        (0, 5),
        (0, 1),
        (0, 1177),
        (0, 6404),
        (0, 64),
        (0, 51),
        (0, 17),
        (0, 4),
        (0, 3),
        (0, 0),
        (0, 31),
        (0, 367),
        (0, 211),
        (0, 1066),
        (0, 693),
        (0, 74),
        (0, 1175),
        (0, 25),
        (0, 900),
        (0, 1848),
        (0, 19),
        (0, 64),
        (0, 18),
        (0, 28),
        (1, 4212),
        (0, 1909),
        (0, 1480),
        (0, 176),
        (0, 33),
        (0, 0),
        (0, 223),
        (0, 2),
        (0, 34),
        (0, 444),
        (0, 1564),
        (0, 25),
        (0, 1085),
        (0, 808),
        (0, 5),
        (0, 0),
        (0, 27),
        (0, 6328),
        (0, 9),
        (0, 69),
        (0, 282),
        (0, 1832),
        (1, 8685),
        (0, 299),
        (0, 6813),
        (0, 4),
        (0, 167),
        (0, 348),
        (0, 4403),
        (1, 3801),
        (0, 66),
        (0, 6324),
        (0, 1879),
        (0, 33),
        (0, 145),
        (0, 180),
        (0, 4675),
        (0, 11),
        (0, 0),
        (0, 38),
        (0, 1119),
        (0, 100),
        (0, 430),
        (0, 843),
        (0, 77),
        (0, 8663),
        (0, 7),
        (0, 6805),
        (1, 2379),
        (0, 1556),
        (0, 6029),
        (0, 6596),
        (0, 99),
        (0, 6554),
        (0, 1025),
        (0, 1223),
        (0, 58),
        (0, 7),
        (0, 737),
        (0, 443),
        (0, 407),
        (0, 219),
        (0, 2227),
        (0, 2286),
        (0, 4165),
        (0, 54),
        (0, 298),
        (2, 3184),
        (0, 185),
        (0, 8251),
        (0, 0),
        (0, 33),
        (0, 30),
        (0, 2),
        (0, 310),
        (0, 802),
        (0, 5196),
        (0, 2985),
        (0, 211),
        (0, 66),
        (0, 74),
        (0, 97),
        (0, 40),
        (0, 1),
        (0, 521),
        (0, 8),
        (0, 813),
        (0, 974),
        (0, 3396),
        (1, 5828),
        (0, 40),
        (0, 277),
        (0, 17),
        (0, 597),
        (0, 718),
        (0, 280),
        (0, 9),
        (0, 4),
        (0, 340),
        (0, 0),
        (0, 552),
        (0, 1),
        (0, 275),
        (1, 5278),
        (0, 58),
        (0, 3),
        (0, 5371),
        (0, 1),
        (0, 16),
        (0, 162),
        (1, 8009),
        (0, 835),
        (0, 4572),
        (0, 242),
        (0, 510),
        (0, 1),
        (0, 52),
        (0, 57),
        (0, 517),
        (0, 4415),
        (0, 16),
        (0, 3225),
        (0, 31),
        (0, 99),
        (0, 0),
        (0, 146),
        (0, 0),
        (0, 161),
        (0, 4),
        (0, 4),
        (0, 216),
        (0, 0),
        (0, 1160),
        (0, 5675),
        (0, 154),
        (0, 112),
        (0, 132),
        (0, 2),
        (0, 66),
        (0, 53),
        (0, 51),
        (0, 0),
        (0, 325),
        (0, 2296),
        (0, 109),
        (0, 7514),
        (0, 2850),
        (0, 44),
        (0, 1530),
        (0, 248),
        (0, 654),
        (0, 44),
        (0, 2282),
        (0, 299),
        (0, 2151),
        (0, 2886),
        (0, 114),
        (0, 5823),
        (0, 182),
        (0, 872),
        (0, 0),
        (0, 3170),
        (0, 0),
        (0, 50),
        (0, 71),
        (0, 33),
        (0, 11),
        (0, 1),
        (0, 1258),
        (0, 14),
        (0, 3255),
        (0, 216),
        (0, 6127),
        (0, 2144),
        (0, 0),
        (0, 2588),
        (0, 1169),
        (0, 1311),
        (0, 588),
        (0, 5),
        (0, 174),
        (0, 1393),
        (0, 63),
        (0, 50),
        (0, 3955),
        (0, 473),
        (0, 1519),
        (0, 0),
        (0, 327),
        (0, 2475),
        (0, 15),
        (0, 0),
        (2, 2826),
        (0, 2896),
        (0, 0),
        (0, 7005),
        (1, 2910),
        (0, 217),
        (0, 2121),
        (0, 75),
        (0, 336),
        (0, 9830),
        (1, 1127),
        (0, 26),
        (0, 250),
        (0, 1550),
        (0, 31),
        (0, 170),
        (0, 21),
        (0, 3383),
        (0, 3241),
        (0, 8864),
        (0, 426),
        (0, 45),
        (0, 5113),
        (0, 622),
        (0, 288),
        (0, 3),
        (0, 3236),
        (0, 23),
        (0, 3996),
        (0, 2),
        (0, 2),
        (0, 584),
        (0, 0),
        (0, 4),
        (0, 0),
        (0, 6088),
        (0, 5672),
        (0, 3725),
        (0, 1803),
        (0, 230),
        (0, 251),
        (0, 725),
        (0, 2059),
        (0, 421),
        (0, 86),
        (0, 5),
        (0, 6924),
        (0, 28),
        (0, 1440),
        (0, 1976),
        (0, 1836),
        (0, 2),
        (1, 1615),
        (0, 4462),
        (0, 4846),
        (0, 5436),
        (0, 123),
        (1, 519),
        (0, 87),
        (0, 1595),
        (0, 2401),
        (0, 5),
        (0, 0),
        (1, 341),
        (0, 4),
        (0, 899),
        (0, 1939),
        (0, 2),
        (0, 1105),
        (0, 1273),
        (0, 74),
        (0, 56),
        (0, 1685),
        (0, 1154),
        (0, 1130),
        (0, 19),
        (0, 5971),
        (0, 2940),
        (0, 8020),
        (0, 0),
        (0, 116),
        (2, 5930),
        (0, 41),
        (0, 162),
        (0, 57),
        (0, 1375),
        (0, 398),
        (0, 4008),
        (0, 9287),
        (0, 817),
        (0, 0),
        (0, 2793),
        (0, 556),
        (0, 997),
        (0, 282),
        (0, 0),
        (0, 553),
        (0, 5742),
        (0, 382),
        (0, 6670),
        (0, 5301),
        (0, 0),
        (2, 9658),
        (0, 463),
        (1, 2981),
        (0, 3716),
        (0, 4894),
        (0, 35),
        (1, 75),
        (0, 86),
        (0, 3384),
        (0, 0),
        (0, 73),
        (0, 635),
        (0, 2),
        (0, 7793),
        (1, 126),
        (0, 21),
        (0, 173),
        (0, 505),
        (0, 1558),
        (0, 45),
        (0, 3137),
        (2, 9424),
        (0, 4030),
        (0, 1749),
        (0, 172),
        (0, 1309),
        (0, 240),
        (0, 735),
        (0, 400),
        (0, 4),
        (0, 160),
        (0, 0),
        (0, 1),
        (0, 105),
        (0, 1),
        (0, 5051),
        (0, 365),
        (0, 146),
        (0, 30),
        (0, 3),
        (0, 470),
        (0, 452),
        (0, 1824),
        (0, 1716),
        (0, 15),
        (0, 1028),
        (0, 332),
        (0, 3606),
        (0, 19),
        (0, 6),
        (0, 405),
        (0, 7770),
        (0, 4300),
        (0, 247),
        (0, 7550),
        (0, 6643),
        (2, 1261),
        (0, 7147),
        (0, 30),
        (0, 9),
        (0, 223),
        (0, 403),
        (0, 372),
        (3, 4276),
        (0, 9288),
        (0, 6212),
        (0, 714),
        (4, 6377),
        (0, 4),
        (0, 46),
        (1, 8403),
        (0, 142),
        (0, 4462),
        (0, 7581),
        (0, 12),
        (0, 0),
        (0, 4864),
        (0, 2961),
        (0, 265),
        (0, 19),
        (0, 119),
        (0, 5454),
        (2, 2981),
        (0, 3230),
        (0, 3),
        (0, 0),
        (0, 6000),
        (1, 9805),
        (0, 354),
        (0, 61),
        (0, 8529),
        (0, 0),
        (0, 2076),
        (0, 629),
        (1, 1194),
        (0, 3377),
        (0, 0),
        (0, 1956),
        (0, 1749),
        (3, 3998),
        (0, 109),
        (0, 16),
        (0, 1467),
        (0, 5195),
        (0, 35),
        (0, 9700),
        (0, 4738),
    ],
    entries: &[
        ("blacktrianglel", (0, 0)),
        ("sharp", (0, 0)),
        ("bowtie", (0, 0)),
        ("NotPrecedesSlantEqual;", (8928, 0)),
        ("Aci", (0, 0)),
        ("Atilde;", (195, 0)),
        ("Zacute;", (377, 0)),
        ("gvertn", (0, 0)),
        ("ordm", (186, 0)),
        ("LeftV", (0, 0)),
        ("circlearrowl", (0, 0)),
        ("updo", (0, 0)),
        ("wcirc", (0, 0)),
        ("gtlPar;", (10645, 0)),
        ("leftharpoondow", (0, 0)),
        ("NotGreaterLess", (0, 0)),
        ("curlyeqpr", (0, 0)),
        ("Downarrow", (0, 0)),
        ("mdas", (0, 0)),
        ("timesd;", (10800, 0)),
        ("smash", (0, 0)),
        ("VerticalL", (0, 0)),
        ("LessSlantE", (0, 0)),
        ("Lamb", (0, 0)),
        ("prap;", (10935, 0)),
        ("Updownarro", (0, 0)),
        ("right", (0, 0)),
        ("bull", (0, 0)),
        ("plusdo", (0, 0)),
        ("Iuk", (0, 0)),
        ("xmap;", (10236, 0)),
        ("We", (0, 0)),
        ("iiiin", (0, 0)),
        ("Rsh;", (8625, 0)),
        ("sac", (0, 0)),
        ("nVDash;", (8879, 0)),
        ("ns", (0, 0)),
        ("NotNestedGr", (0, 0)),
        ("RightCe", (0, 0)),
        ("lce", (0, 0)),
        ("lb", (0, 0)),
        ("dArr;", (8659, 0)),
        ("Hfr;", (8460, 0)),
        ("lnE", (0, 0)),
        ("emsp14", (0, 0)),
        ("Ubre", (0, 0)),
        ("bumpE", (0, 0)),
        ("DoubleDo", (0, 0)),
        ("g", (0, 0)),
        ("grave", (0, 0)),
        ("DoubleDownAr", (0, 0)),
        ("bigt", (0, 0)),
        ("nbu", (0, 0)),
        ("Becau", (0, 0)),
        ("curvearrowright;", (8631, 0)),
        ("RightDownVector", (0, 0)),
        ("xwedge;", (8896, 0)),
        ("CircleTi", (0, 0)),
        ("dashv", (0, 0)),
        ("CupCa", (0, 0)),
        ("vpro", (0, 0)),
        ("scsim", (0, 0)),
        ("kgr", (0, 0)),
        ("fpartint", (0, 0)),
        ("ShortDo", (0, 0)),
        ("iiot", (0, 0)),
        ("escr", (0, 0)),
        ("lmidot", (0, 0)),
        ("na", (0, 0)),
        ("bigcup;", (8899, 0)),
        ("bn", (0, 0)),
        ("gneq", (0, 0)),
        ("rBar", (0, 0)),
        ("preceq;", (10927, 0)),
        ("MediumSp", (0, 0)),
        ("Ha", (0, 0)),
        ("xve", (0, 0)),
        ("tce", (0, 0)),
        ("bsim", (0, 0)),
        ("NotGreaterSlantEq", (0, 0)),
        ("LeftAngleBra", (0, 0)),
        ("nat", (0, 0)),
        ("Acirc", (194, 0)),
        ("Kappa", (0, 0)),
        ("Uscr", (0, 0)),
        ("intca", (0, 0)),
        ("cylct", (0, 0)),
        ("blk14", (0, 0)),
        ("vdash", (0, 0)),
        ("Laplacetr", (0, 0)),
        ("Imaginar", (0, 0)),
        ("Uma", (0, 0)),
        ("DoubleLongRightArrow", (0, 0)),
        ("lurd", (0, 0)),
        ("icirc", (238, 0)),
        ("lsquor;", (8218, 0)),
        ("Idot", (0, 0)),
        ("LessFull", (0, 0)),
        ("ShortRightAr", (0, 0)),
        ("DownLeftTeeVe", (0, 0)),
        ("iiiint;", (10764, 0)),
        ("ods", (0, 0)),
        ("cwcon", (0, 0)),
        ("ruluha", (0, 0)),
        ("leftrightsquiga", (0, 0)),
        ("odash", (0, 0)),
        ("gel", (0, 0)),
        ("Bu", (0, 0)),
        ("xscr;", (120013, 0)),
        ("succn", (0, 0)),
        ("capdot;", (10816, 0)),
        ("rat", (0, 0)),
        ("topfor", (0, 0)),
        ("NestedG", (0, 0)),
        ("ContourIn", (0, 0)),
        ("EqualT", (0, 0)),
        ("Gscr;", (119970, 0)),
        ("UpperLeftAr", (0, 0)),
        ("pc", (0, 0)),
        ("drcro", (0, 0)),
        ("Vsc", (0, 0)),
        ("thetasy", (0, 0)),
        ("Clockwise", (0, 0)),
        ("bigvee;", (8897, 0)),
        ("jopf;", (120155, 0)),
        ("Hcir", (0, 0)),
        ("sw", (0, 0)),
        ("Upsilon", (0, 0)),
        ("CounterClockwiseContourIntegral;", (8755, 0)),
        ("plusdu", (0, 0)),
        ("searr;", (8600, 0)),
        ("mnplus;", (8723, 0)),
        ("shchc", (0, 0)),
        ("clubsuit", (0, 0)),
        ("epars", (0, 0)),
        ("SquareUni", (0, 0)),
        ("NotSubs", (0, 0)),
        ("nltr", (0, 0)),
        ("curly", (0, 0)),
        ("NotGreater", (0, 0)),
        ("DoubleLe", (0, 0)),
        ("DoubleLongLeftRigh", (0, 0)),
        ("ori", (0, 0)),
        ("LeftAngl", (0, 0)),
        ("jma", (0, 0)),
        ("SquareSubs", (0, 0)),
        ("Or", (0, 0)),
        ("gna", (0, 0)),
        ("circledast", (0, 0)),
        ("RightUpVe", (0, 0)),
        ("leftt", (0, 0)),
        ("EmptySmallS", (0, 0)),
        ("natu", (0, 0)),
        ("succe", (0, 0)),
        ("downdownar", (0, 0)),
        ("Ogr", (0, 0)),
        ("GT", (62, 0)),
        ("YAcy;", (1071, 0)),
        ("lcedil", (0, 0)),
        ("clu", (0, 0)),
        ("sd", (0, 0)),
        ("nprec;", (8832, 0)),
        ("NotSucceedsEq", (0, 0)),
        ("slar", (0, 0)),
        ("LongLeftRightA", (0, 0)),
        ("smt;", (10922, 0)),
        ("UnionPlus", (0, 0)),
        ("col", (0, 0)),
        ("clubsu", (0, 0)),
        ("ruluh", (0, 0)),
        ("vprop;", (8733, 0)),
        ("gtreqles", (0, 0)),
        ("succcurl", (0, 0)),
        ("dotminus;", (8760, 0)),
        ("DoubleLeftTee", (0, 0)),
        ("duhar;", (10607, 0)),
        ("Suc", (0, 0)),
        ("DiacriticalDoubleAc", (0, 0)),
        ("eqcolon;", (8789, 0)),
        ("upharpoo", (0, 0)),
        ("looparrow", (0, 0)),
        ("ycirc;", (375, 0)),
        ("gtra", (0, 0)),
        ("succnsim;", (8937, 0)),
        ("LeftRightV", (0, 0)),
        ("dag", (0, 0)),
        ("NotLessSlantEqual", (0, 0)),
        ("varsigm", (0, 0)),
        ("m", (0, 0)),
        ("ouml", (246, 0)),
        ("hkse", (0, 0)),
        ("NotEqualTilde", (0, 0)),
        ("NotGreaterTi", (0, 0)),
        ("approxe", (0, 0)),
        ("LeftTeeVe", (0, 0)),
        ("sem", (0, 0)),
        ("ou", (0, 0)),
        ("straight", (0, 0)),
        ("scc", (0, 0)),
        ("ReverseElem", (0, 0)),
        ("curlyeqprec;", (8926, 0)),
        ("efr;", (120098, 0)),
        ("ropl", (0, 0)),
        ("downarro", (0, 0)),
        ("Tild", (0, 0)),
        ("SquareSubsetEqual;", (8849, 0)),
        ("NotV", (0, 0)),
        ("Ea", (0, 0)),
        ("Lopf", (0, 0)),
        ("swarr;", (8601, 0)),
        ("rtimes;", (8906, 0)),
        ("h", (0, 0)),
        ("ntlg;", (8824, 0)),
        ("Omicron;", (927, 0)),
        ("Map;", (10501, 0)),
        ("Gce", (0, 0)),
        ("ZeroWidthSpac", (0, 0)),
        ("Topf;", (120139, 0)),
        ("ic", (0, 0)),
        ("uarr;", (8593, 0)),
        ("util", (0, 0)),
        ("frac13", (0, 0)),
        ("Longleftrightarro", (0, 0)),
        ("Ufr", (0, 0)),
        ("infin", (0, 0)),
        ("Eq", (0, 0)),
        ("cudarrl;", (10552, 0)),
        ("Beta", (0, 0)),
        ("NotEl", (0, 0)),
        ("KHcy;", (1061, 0)),
        ("diamonds", (0, 0)),
        ("cca", (0, 0)),
        ("LessF", (0, 0)),
        ("nshortparallel;", (8742, 0)),
        ("vsc", (0, 0)),
        ("OverParenthe", (0, 0)),
        ("Diamond", (0, 0)),
        ("RightTri", (0, 0)),
        ("iscr", (0, 0)),
        ("HumpEqu", (0, 0)),
        ("centerdot;", (183, 0)),
        ("DownArrowUpA", (0, 0)),
        ("lagran;", (8466, 0)),
        ("dotpl", (0, 0)),
        ("ulcorne", (0, 0)),
        ("UpperLeftArr", (0, 0)),
        ("lAarr", (0, 0)),
        ("lhb", (0, 0)),
        ("supnE", (0, 0)),
        ("Brev", (0, 0)),
        ("ulco", (0, 0)),
        ("ccap", (0, 0)),
        ("OverBracket", (0, 0)),
        ("lArr", (0, 0)),
        ("supplus", (0, 0)),
        ("expe", (0, 0)),
        ("expecta", (0, 0)),
        ("om", (0, 0)),
        ("eg;", (10906, 0)),
        ("Ici", (0, 0)),
        ("leftthreetime", (0, 0)),
        ("iprod", (0, 0)),
        ("ssetmn;", (8726, 0)),
        ("A", (0, 0)),
        ("ntrianglelefteq;", (8940, 0)),
        ("Laplac", (0, 0)),
        ("smeparsl;", (10724, 0)),
        ("RightT", (0, 0)),
        ("rsq", (0, 0)),
        ("LeftUpDownVector;", (10577, 0)),
        ("Rcedil;", (342, 0)),
        ("Iot", (0, 0)),
        ("NotSucceeds;", (8833, 0)),
        ("uum", (0, 0)),
        ("Llef", (0, 0)),
        ("NegativeThinS", (0, 0)),
        ("fj", (0, 0)),
        ("breve", (0, 0)),
        ("tbr", (0, 0)),
        ("bkarow;", (10509, 0)),
        ("NotSquare", (0, 0)),
        ("RightArrowBar;", (8677, 0)),
        ("RightDownVecto", (0, 0)),
        ("Fi", (0, 0)),
        ("Kfr", (0, 0)),
        ("rightarrow;", (8594, 0)),
        ("UpEquilib", (0, 0)),
        ("leftrightsqu", (0, 0)),
        ("succcurlye", (0, 0)),
        ("blacktrianglele", (0, 0)),
        ("Lcedi", (0, 0)),
        ("djcy", (0, 0)),
        ("ndash", (0, 0)),
        ("LeftUpDownV", (0, 0)),
        ("LessSlantEqu", (0, 0)),
        ("RightDownTe", (0, 0)),
        ("Ycy", (0, 0)),
        ("exc", (0, 0)),
        ("lef", (0, 0)),
        ("ratai", (0, 0)),
        ("LessFullEqual", (0, 0)),
        ("icirc;", (238, 0)),
        ("Nf", (0, 0)),
        ("NotLessGr", (0, 0)),
        ("gtreq", (0, 0)),
        ("nvDa", (0, 0)),
        ("Gcirc", (0, 0)),
        ("otil", (0, 0)),
        ("Hf", (0, 0)),
        ("LeftUpTe", (0, 0)),
        ("ThinSpace;", (8201, 0)),
        ("el;", (10905, 0)),
        ("Qf", (0, 0)),
        ("backprime", (0, 0)),
        ("LeftTr", (0, 0)),
        ("DownRig", (0, 0)),
        ("xhArr;", (10234, 0)),
        ("Rop", (0, 0)),
        ("DownTeeArrow", (0, 0)),
        ("lozeng", (0, 0)),
        ("CloseCurlyDoubleQu", (0, 0)),
        ("Kcy", (0, 0)),
        ("gsc", (0, 0)),
        ("ReverseEquilibriu", (0, 0)),
        ("GreaterSla", (0, 0)),
        ("larrsim", (0, 0)),
        ("vangrt;", (10652, 0)),
        ("Rcaro", (0, 0)),
        ("nco", (0, 0)),
        ("LessEqual", (0, 0)),
        ("uHar;", (10595, 0)),
        ("andd", (0, 0)),
        ("hstrok;", (295, 0)),
        ("hyb", (0, 0)),
        ("PrecedesTild", (0, 0)),
        ("rangle;", (10217, 0)),
        ("ContourIntegr", (0, 0)),
        ("RBarr", (0, 0)),
        ("Vscr", (0, 0)),
        ("Negativ", (0, 0)),
        ("Yo", (0, 0)),
        ("cula", (0, 0)),
        ("nGt", (0, 0)),
        ("lesseqqg", (0, 0)),
        ("rscr;", (120007, 0)),
        ("nesim", (0, 0)),
        ("LeftDownTeeVector;", (10593, 0)),
        ("barv", (0, 0)),
        ("succnapprox;", (10938, 0)),
        ("lbrksld;", (10639, 0)),
        ("triangl", (0, 0)),
        ("CircleDo", (0, 0)),
        ("xlarr", (0, 0)),
        ("larrbf", (0, 0)),
        ("larr", (0, 0)),
        ("fal", (0, 0)),
        ("Triple", (0, 0)),
        ("opar", (0, 0)),
        ("Uci", (0, 0)),
        ("Roun", (0, 0)),
        ("DoubleRightAr", (0, 0)),
        ("planckh", (0, 0)),
        ("EmptyVer", (0, 0)),
        ("Prim", (0, 0)),
        ("apo", (0, 0)),
        ("RightTriangleEqua", (0, 0)),
        ("blackt", (0, 0)),
        ("Diac", (0, 0)),
        ("UnderBracket", (0, 0)),
        ("larrlp", (0, 0)),
        ("Dot;", (168, 0)),
        ("asympeq", (0, 0)),
        ("mDDo", (0, 0)),
        ("Uuml;", (220, 0)),
        ("Tce", (0, 0)),
        ("Integral", (0, 0)),
        ("llcor", (0, 0)),
        ("Prece", (0, 0)),
        ("Part", (0, 0)),
        ("herco", (0, 0)),
        ("cacute", (0, 0)),
        ("profli", (0, 0)),
        ("of", (0, 0)),
        ("uopf", (0, 0)),
        ("Ti", (0, 0)),
        ("edot;", (279, 0)),
        ("drcor", (0, 0)),
        ("RightArrowLeftArro", (0, 0)),
        ("rightsquigarrow;", (8605, 0)),
        ("upuparr", (0, 0)),
        ("queste", (0, 0)),
        ("Longleft", (0, 0)),
        ("laemptyv", (0, 0)),
        ("trie", (0, 0)),
        ("hAr", (0, 0)),
        ("xutr", (0, 0)),
        ("siml;", (10909, 0)),
        ("sup1", (185, 0)),
        ("PrecedesSlan", (0, 0)),
        ("Ntilde", (209, 0)),
        ("uwa", (0, 0)),
        ("frac34", (190, 0)),
        ("boxVR;", (9568, 0)),
        ("zig", (0, 0)),
        ("dotsqua", (0, 0)),
        ("dtr", (0, 0)),
        ("Diacritic", (0, 0)),
        ("gjcy;", (1107, 0)),
        ("epsiv;", (1013, 0)),
        ("IE", (0, 0)),
        ("boxDR", (0, 0)),
        ("fa", (0, 0)),
        ("npara", (0, 0)),
        ("frac45", (0, 0)),
        ("pi;", (960, 0)),
        ("cirmi", (0, 0)),
        ("leftthreeti", (0, 0)),
        ("bigotimes", (0, 0)),
        ("bigsqc", (0, 0)),
        ("xlarr;", (10229, 0)),
        ("lhbl", (0, 0)),
        ("ldc", (0, 0)),
        ("ugrave;", (249, 0)),
        ("NotHumpDownHump;", (8782, 824)),
        ("Rho;", (929, 0)),
        ("rarrs", (0, 0)),
        ("NotLessS", (0, 0)),
        ("inca", (0, 0)),
        ("RoundImplies", (0, 0)),
        ("FilledVerySmallS", (0, 0)),
        ("esi", (0, 0)),
        ("orslo", (0, 0)),
        ("ddag", (0, 0)),
        ("curre", (0, 0)),
        ("uuarr;", (8648, 0)),
        ("up", (0, 0)),
        ("ccar", (0, 0)),
        ("ri", (0, 0)),
        ("lhard", (0, 0)),
        ("nles", (0, 0)),
        ("Utild", (0, 0)),
        ("eqcirc;", (8790, 0)),
        ("ltime", (0, 0)),
        ("yacy;", (1103, 0)),
        ("malt;", (10016, 0)),
        ("aac", (0, 0)),
        ("RoundImp", (0, 0)),
        ("phmmat", (0, 0)),
        ("RightDownTeeVect", (0, 0)),
        ("cap;", (8745, 0)),
        ("jm", (0, 0)),
        ("looparrowle", (0, 0)),
        ("profline;", (8978, 0)),
        ("plus", (0, 0)),
        ("straightepsi", (0, 0)),
        ("RightCeili", (0, 0)),
        ("awconin", (0, 0)),
        ("mapstodown", (0, 0)),
        ("triangleleft", (0, 0)),
        ("gc", (0, 0)),
        ("oo", (0, 0)),
        ("zet", (0, 0)),
        ("setmin", (0, 0)),
        ("twix", (0, 0)),
        ("mapstoleft", (0, 0)),
        ("LowerLeftArrow", (0, 0)),
        ("sqsub", (0, 0)),
        ("iocy", (0, 0)),
        ("imagpar", (0, 0)),
        ("softcy", (0, 0)),
        ("acd;", (8767, 0)),
        ("Ium", (0, 0)),
        ("DoubleLongRightArrow;", (10233, 0)),
        ("Pri", (0, 0)),
        ("tfr", (0, 0)),
        ("DoubleLo", (0, 0)),
        ("Tilde;", (8764, 0)),
        ("Equal", (0, 0)),
        ("Iu", (0, 0)),
        ("Hs", (0, 0)),
        ("nvl", (0, 0)),
        ("ClockwiseContourIntegra", (0, 0)),
        ("RightTriangleBar", (0, 0)),
        ("downharpoonle", (0, 0)),
        ("expec", (0, 0)),
        ("bigotim", (0, 0)),
        ("AMP;", (38, 0)),
        ("boxH;", (9552, 0)),
        ("Intersectio", (0, 0)),
        ("aum", (0, 0)),
        ("niv", (0, 0)),
        ("nlefta", (0, 0)),
        ("lesseq", (0, 0)),
        ("mar", (0, 0)),
        ("male", (0, 0)),
        ("curlyeqpre", (0, 0)),
        ("LowerRight", (0, 0)),
        ("lon", (0, 0)),
        ("Exponenti", (0, 0)),
        ("nexist", (0, 0)),
        ("siml", (0, 0)),
        ("NotGreaterT", (0, 0)),
        ("rfloor;", (8971, 0)),
        ("Bsc", (0, 0)),
        ("Otim", (0, 0)),
        ("capbrcup", (0, 0)),
        ("varkappa;", (1008, 0)),
        ("circ", (0, 0)),
        ("plan", (0, 0)),
        ("Sacu", (0, 0)),
        ("varsig", (0, 0)),
        ("RoundImpli", (0, 0)),
        ("jsercy;", (1112, 0)),
        ("di", (0, 0)),
        ("bepsi", (0, 0)),
        ("hearts", (0, 0)),
        ("nleqsla", (0, 0)),
        ("RuleDel", (0, 0)),
        ("boxhd", (0, 0)),
        ("profl", (0, 0)),
        ("downharpo", (0, 0)),
        ("raemptyv;", (10675, 0)),
        ("ominus", (0, 0)),
        ("bsolb;", (10693, 0)),
        ("straightepsilon;", (1013, 0)),
        ("nsub", (0, 0)),
        ("DoubleLongLeftArro", (0, 0)),
        ("ensp", (0, 0)),
        ("Aopf", (0, 0)),
        ("gacute", (0, 0)),
        ("Colone;", (10868, 0)),
        ("zw", (0, 0)),
        ("leftharpoo", (0, 0)),
        ("UpArrowDownAr", (0, 0)),
        ("IEcy", (0, 0)),
        ("shortpar", (0, 0)),
        ("nGtv;", (8811, 824)),
        ("downh", (0, 0)),
        ("commat;", (64, 0)),
        ("ContourIntegral;", (8750, 0)),
        ("eacut", (0, 0)),
        ("gnapprox;", (10890, 0)),
        ("breve;", (728, 0)),
        ("iinf", (0, 0)),
        ("hom", (0, 0)),
        ("nlA", (0, 0)),
        ("cupdo", (0, 0)),
        ("ee", (0, 0)),
        ("TScy;", (1062, 0)),
        ("LeftDoubl", (0, 0)),
        ("Succee", (0, 0)),
        ("curlyv", (0, 0)),
        ("rightarro", (0, 0)),
        ("rbrace", (0, 0)),
        ("NotGreaterFullE", (0, 0)),
        ("quest;", (63, 0)),
        ("cuvee", (0, 0)),
        ("Cou", (0, 0)),
        ("ReverseE", (0, 0)),
        ("barwedg", (0, 0)),
        ("succc", (0, 0)),
        ("B", (0, 0)),
        ("Uring", (0, 0)),
        ("supne;", (8843, 0)),
        ("capca", (0, 0)),
        ("ohba", (0, 0)),
        ("CloseCurlyQuote", (0, 0)),
        ("nleqs", (0, 0)),
        ("thksi", (0, 0)),
        ("dol", (0, 0)),
        ("ro", (0, 0)),
        ("NotSucceedsSlantEqu", (0, 0)),
        ("VerticalTi", (0, 0)),
        ("LessGrea", (0, 0)),
        ("copro", (0, 0)),
        ("LessSlantEqual", (0, 0)),
        ("upsilo", (0, 0)),
        ("osol", (0, 0)),
        ("peri", (0, 0)),
        ("NotTildeE", (0, 0)),
        ("varsupse", (0, 0)),
        ("Eogon;", (280, 0)),
        ("Ys", (0, 0)),
        ("NotGreaterSlant", (0, 0)),
        ("PrecedesEqual;", (10927, 0)),
        ("FilledSmall", (0, 0)),
        ("updownarrow;", (8597, 0)),
        ("cupdot;", (8845, 0)),
        ("Verb", (0, 0)),
        ("subse", (0, 0)),
        ("swar", (0, 0)),
        ("pop", (0, 0)),
        ("YAcy", (0, 0)),
        ("PrecedesEqual", (0, 0)),
        ("rHa", (0, 0)),
        ("Bernoullis;", (8492, 0)),
        ("DownRigh", (0, 0)),
        ("circlearrowlef", (0, 0)),
        ("hair", (0, 0)),
        ("odi", (0, 0)),
        ("NotTildeFullEqu", (0, 0)),
        ("ntrianglele", (0, 0)),
        ("Verbar;", (8214, 0)),
        ("llcorner;", (8990, 0)),
        ("AMP", (38, 0)),
        ("hardc", (0, 0)),
        ("LessFullEqu", (0, 0)),
        ("ucy", (0, 0)),
        ("rtri", (0, 0)),
        ("Longleftarrow;", (10232, 0)),
        ("SquareSupersetE", (0, 0)),
        ("gesles;", (10900, 0)),
        ("gg", (0, 0)),
        ("Nscr;", (119977, 0)),
        ("sqsubsete", (0, 0)),
        ("twoheadrig", (0, 0)),
        ("curvear", (0, 0)),
        ("eqcirc", (0, 0)),
        ("ClockwiseContourInt", (0, 0)),
        ("Mop", (0, 0)),
        ("pointint", (0, 0)),
        ("NotTild", (0, 0)),
        ("empt", (0, 0)),
        ("Sma", (0, 0)),
        ("aleph;", (8501, 0)),
        ("copysr;", (8471, 0)),
        ("la", (0, 0)),
        ("lAa", (0, 0)),
        ("Iop", (0, 0)),
        ("CloseCurlyDoubleQuot", (0, 0)),
        ("ltrie;", (8884, 0)),
        ("rul", (0, 0)),
        ("Umacr;", (362, 0)),
        ("NJcy;", (1034, 0)),
        ("NotSupersetEqual;", (8841, 0)),
        ("rlar", (0, 0)),
        ("NotPrecedesEqual", (0, 0)),
        ("sopf;", (120164, 0)),
        ("fork;", (8916, 0)),
        ("Jopf;", (120129, 0)),
        ("nsccue", (0, 0)),
        ("leftrightarrow;", (8596, 0)),
        ("ForAll", (0, 0)),
        ("urin", (0, 0)),
        ("nvap", (0, 0)),
        ("squf;", (9642, 0)),
        ("Gbreve", (0, 0)),
        ("lmidot;", (320, 0)),
        ("mapstoleft;", (8612, 0)),
        ("er", (0, 0)),
        ("LeftDownTeeVe", (0, 0)),
        ("ominu", (0, 0)),
        ("sharp;", (9839, 0)),
        ("ccaps;", (10829, 0)),
        ("Thet", (0, 0)),
        ("Upsilo", (0, 0)),
        ("rightharpoonup;", (8640, 0)),
        ("spades;", (9824, 0)),
        ("rightharpoond", (0, 0)),
        ("SquareSubset;", (8847, 0)),
        ("yscr;", (120014, 0)),
        ("iac", (0, 0)),
        ("permil", (0, 0)),
        ("Rsh", (0, 0)),
        ("rangd", (0, 0)),
        ("els", (0, 0)),
        ("cuepr", (0, 0)),
        ("Ama", (0, 0)),
        ("TripleDot;", (8411, 0)),
        ("OpenCurlyDoubleQuote;", (8220, 0)),
        ("gesl;", (8923, 65024)),
        ("yci", (0, 0)),
        ("nlAr", (0, 0)),
        ("notnivc", (0, 0)),
        ("gscr;", (8458, 0)),
        ("NegativeMe", (0, 0)),
        ("twi", (0, 0)),
        ("lvertneqq", (0, 0)),
        ("eth", (240, 0)),
        ("Pi", (0, 0)),
        ("MediumSpac", (0, 0)),
        ("ultri", (0, 0)),
        ("LongLeftA", (0, 0)),
        ("SquareSupers", (0, 0)),
        ("TildeT", (0, 0)),
        ("FilledSmallSquare;", (9724, 0)),
        ("nvrAr", (0, 0)),
        ("nsho", (0, 0)),
        ("RightAngl", (0, 0)),
        ("dcy;", (1076, 0)),
        ("RightVecto", (0, 0)),
        ("szlig", (223, 0)),
        ("NotSucce", (0, 0)),
        ("lna", (0, 0)),
        ("Not;", (10988, 0)),
        ("InvisibleTimes", (0, 0)),
        ("acir", (0, 0)),
        ("uharr", (0, 0)),
        ("RightFloor", (0, 0)),
        ("npars", (0, 0)),
        ("DownArro", (0, 0)),
        ("squ", (0, 0)),
        ("numer", (0, 0)),
        ("Cc", (0, 0)),
        ("succappr", (0, 0)),
        ("cwco", (0, 0)),
        ("dso", (0, 0)),
        ("bu", (0, 0)),
        ("Alph", (0, 0)),
        ("UpTeeArrow;", (8613, 0)),
        ("upharpoonrig", (0, 0)),
        ("rarrt", (0, 0)),
        ("expectati", (0, 0)),
        ("easter", (0, 0)),
        ("nsime", (0, 0)),
        ("OpenC", (0, 0)),
        ("lparl", (0, 0)),
        ("LongRightA", (0, 0)),
        ("NotGreate", (0, 0)),
        ("LeftTria", (0, 0)),
        ("sscr", (0, 0)),
        ("drbkarow;", (10512, 0)),
        ("Fscr;", (8497, 0)),
        ("scap", (0, 0)),
        ("minusdu", (0, 0)),
        ("ycy", (0, 0)),
        ("curlyve", (0, 0)),
        ("trpezium", (0, 0)),
        ("smtes", (0, 0)),
        ("subr", (0, 0)),
        ("For", (0, 0)),
        ("dzcy;", (1119, 0)),
        ("UpEquilibriu", (0, 0)),
        ("order", (0, 0)),
        ("iogon;", (303, 0)),
        ("racute;", (341, 0)),
        ("shortparallel", (0, 0)),
        ("TildeFul", (0, 0)),
        ("Ro", (0, 0)),
        ("proflin", (0, 0)),
        ("iukc", (0, 0)),
        ("VeryThi", (0, 0)),
        ("blo", (0, 0)),
        ("Lmidot;", (319, 0)),
        ("IJlig", (0, 0)),
        ("LeftAng", (0, 0)),
        ("circe", (0, 0)),
        ("Ts", (0, 0)),
        ("exponentiale;", (8519, 0)),
        ("TildeFullE", (0, 0)),
        ("angmsdab;", (10665, 0)),
        ("Jo", (0, 0)),
        ("Lscr;", (8466, 0)),
        ("dis", (0, 0)),
        ("Ut", (0, 0)),
        ("odo", (0, 0)),
        ("uwangle;", (10663, 0)),
        ("apaci", (0, 0)),
        ("comm", (0, 0)),
        ("cirs", (0, 0)),
        ("risin", (0, 0)),
        ("NotSquareSupe", (0, 0)),
        ("uparrow", (0, 0)),
        ("UnderBracke", (0, 0)),
        ("xlar", (0, 0)),
        ("NotPrecedesEqual;", (10927, 824)),
        ("Zscr;", (119989, 0)),
        ("barwedge", (0, 0)),
        ("Oop", (0, 0)),
        ("bigsq", (0, 0)),
        ("llcorn", (0, 0)),
        ("backepsil", (0, 0)),
        ("LJc", (0, 0)),
        ("kh", (0, 0)),
        ("varsupsetneq;", (8843, 65024)),
        ("ve", (0, 0)),
        ("omicron;", (959, 0)),
        ("leftrightarro", (0, 0)),
        ("leftrightarr", (0, 0)),
        ("caret", (0, 0)),
        ("topf", (0, 0)),
        ("sdot", (0, 0)),
        ("thickappr", (0, 0)),
        ("radi", (0, 0)),
        ("dopf;", (120149, 0)),
        ("nex", (0, 0)),
        ("copy", (169, 0)),
        ("NotLessE", (0, 0)),
        ("vltri", (0, 0)),
        ("comma;", (44, 0)),
        ("hoar", (0, 0)),
        ("orig", (0, 0)),
        ("nshortparal", (0, 0)),
        ("rightsquiga", (0, 0)),
        ("RightDown", (0, 0)),
        ("ap", (0, 0)),
        ("nltrie", (0, 0)),
        ("HumpDow", (0, 0)),
        ("straightph", (0, 0)),
        ("circlearrowleft;", (8634, 0)),
        ("tw", (0, 0)),
        ("olci", (0, 0)),
        ("phmm", (0, 0)),
        ("SOFT", (0, 0)),
        ("gopf", (0, 0)),
        ("dotsquare", (0, 0)),
        ("Bumpeq", (0, 0)),
        ("erar", (0, 0)),
        ("boxbo", (0, 0)),
        ("nVDas", (0, 0)),
        ("shch", (0, 0)),
        ("OverBra", (0, 0)),
        ("angmsdac", (0, 0)),
        ("RightArrowLef", (0, 0)),
        ("bigca", (0, 0)),
        ("NotReverseElemen", (0, 0)),
        ("perp", (0, 0)),
        ("appr", (0, 0)),
        ("js", (0, 0)),
        ("profline", (0, 0)),
        ("Wedge", (0, 0)),
        ("LeftArrow;", (8592, 0)),
        ("nwnear;", (10535, 0)),
        ("eqvparsl", (0, 0)),
        ("Vbar;", (10987, 0)),
        ("lpa", (0, 0)),
        ("searrow", (0, 0)),
        ("DJ", (0, 0)),
        ("nlarr;", (8602, 0)),
        ("bigci", (0, 0)),
        ("Clockwi", (0, 0)),
        ("Sups", (0, 0)),
        ("loarr", (0, 0)),
        ("Cro", (0, 0)),
        ("Beca", (0, 0)),
        ("cirf", (0, 0)),
        ("NotLeftTriangleEqu", (0, 0)),
        ("vartriangl", (0, 0)),
        ("real", (0, 0)),
        ("ZeroWidthSp", (0, 0)),
        ("lba", (0, 0)),
        ("NotRightTriangleBar", (0, 0)),
        ("oplus;", (8853, 0)),
        ("times", (215, 0)),
        ("rightr", (0, 0)),
        ("leftri", (0, 0)),
        ("Dcaron", (0, 0)),
        ("rarrbfs;", (10528, 0)),
        ("nLeftrightarr", (0, 0)),
        ("ReverseUpE", (0, 0)),
        ("DownLeftRightVector;", (10576, 0)),
        ("pert", (0, 0)),
        ("DoubleLongRightArro", (0, 0)),
        ("iukcy;", (1110, 0)),
        ("lowa", (0, 0)),
        ("og", (0, 0)),
        ("Cente", (0, 0)),
        ("kcedil;", (311, 0)),
        ("DoubleLongLeftRightArro", (0, 0)),
        ("bnot", (0, 0)),
        ("nrarrw;", (8605, 824)),
        ("rfr;", (120111, 0)),
        ("oti", (0, 0)),
        ("Left", (0, 0)),
        ("OverParenthesis;", (9180, 0)),
        ("measuredangle", (0, 0)),
        ("fpart", (0, 0)),
        ("dotsquar", (0, 0)),
        ("NotSquareSubsetEq", (0, 0)),
        ("DiacriticalAcute", (0, 0)),
        ("DS", (0, 0)),
        ("downharpoonleft;", (8643, 0)),
        ("harrcir", (0, 0)),
        ("DownLeftRightVec", (0, 0)),
        ("Egr", (0, 0)),
        ("Jserc", (0, 0)),
        ("ShortL", (0, 0)),
        ("lessd", (0, 0)),
        ("succcu", (0, 0)),
        ("dotplu", (0, 0)),
        ("PlusM", (0, 0)),
        ("hyph", (0, 0)),
        ("rightleftarrows;", (8644, 0)),
        ("LeftTe", (0, 0)),
        ("EmptySmallSqua", (0, 0)),
        ("NotGreaterFullEq", (0, 0)),
        ("cl", (0, 0)),
        ("rppo", (0, 0)),
        ("ldqu", (0, 0)),
        ("DiacriticalDot", (0, 0)),
        ("nvdas", (0, 0)),
        ("LessTil", (0, 0)),
        ("LowerRightArrow", (0, 0)),
        ("dalet", (0, 0)),
        ("Otilde", (213, 0)),
        ("sh", (0, 0)),
        ("lbrke;", (10635, 0)),
        ("seArr;", (8664, 0)),
        ("oma", (0, 0)),
        ("gEl", (0, 0)),
        ("NonBreakingSpace", (0, 0)),
        ("LessEqualGreat", (0, 0)),
        ("uhblk;", (9600, 0)),
        ("timesba", (0, 0)),
        ("bopf", (0, 0)),
        ("CloseCu", (0, 0)),
        ("curlyvee", (0, 0)),
        ("thksim;", (8764, 0)),
        ("boxvH;", (9578, 0)),
        ("luruha", (0, 0)),
        ("vns", (0, 0)),
        ("Hopf;", (8461, 0)),
        ("CloseCurlyDoub", (0, 0)),
        ("Square;", (9633, 0)),
        ("subs", (0, 0)),
        ("fltn", (0, 0)),
        ("diga", (0, 0)),
        ("rfloo", (0, 0)),
        ("nspar;", (8742, 0)),
        ("rightleftar", (0, 0)),
        ("precnapprox;", (10937, 0)),
        ("leftharpo", (0, 0)),
        ("nleq;", (8816, 0)),
        ("boxVR", (0, 0)),
        ("range", (0, 0)),
        ("robrk", (0, 0)),
        ("xup", (0, 0)),
        ("vangrt", (0, 0)),
        ("nLeftrightarro", (0, 0)),
        ("var", (0, 0)),
        ("nRi", (0, 0)),
        ("gnE", (0, 0)),
        ("cros", (0, 0)),
        ("pla", (0, 0)),
        ("RightF", (0, 0)),
        ("DoubleUpDownArrow;", (8661, 0)),
        ("trianglerighte", (0, 0)),
        ("Sfr", (0, 0)),
        ("copys", (0, 0)),
        ("xcir", (0, 0)),
        ("odbla", (0, 0)),
        ("rHar", (0, 0)),
        ("yuc", (0, 0)),
        ("alefsy", (0, 0)),
        ("bigwedge;", (8896, 0)),
        ("olar", (0, 0)),
        ("Igrave", (204, 0)),
        ("EmptyVerySmallSquar", (0, 0)),
        ("ContourInte", (0, 0)),
        ("vartrianglerigh", (0, 0)),
        ("NegativeThinSpac", (0, 0)),
        ("twoheadrightarrow;", (8608, 0)),
        ("subne", (0, 0)),
        ("NegativeTh", (0, 0)),
        ("lBarr", (0, 0)),
        ("oeli", (0, 0)),
        ("Iota", (0, 0)),
        ("intcal;", (8890, 0)),
        ("equals", (0, 0)),
        ("frac2", (0, 0)),
        ("abreve;", (259, 0)),
        ("upharpoonri", (0, 0)),
        ("DiacriticalDoub", (0, 0)),
        ("pe", (0, 0)),
        ("SquareSubsetEqua", (0, 0)),
        ("Lambda;", (923, 0)),
        ("Dashv;", (10980, 0)),
        ("Za", (0, 0)),
        ("NonBreak", (0, 0)),
        ("LeftUpDownVe", (0, 0)),
        ("setm", (0, 0)),
        ("spadesu", (0, 0)),
        ("Nacut", (0, 0)),
        ("approx;", (8776, 0)),
        ("parsl;", (11005, 0)),
        ("leftrightharpoons", (0, 0)),
        ("rarrap", (0, 0)),
        ("NestedGreaterGreat", (0, 0)),
        ("plusacir;", (10787, 0)),
        ("CircleTimes", (0, 0)),
        ("dwa", (0, 0)),
        ("cul", (0, 0)),
        ("SupersetEqua", (0, 0)),
        ("downha", (0, 0)),
        ("sqcaps;", (8851, 65024)),
        ("Therefo", (0, 0)),
        ("hookrightarrow;", (8618, 0)),
        ("boxDL;", (9559, 0)),
        ("complexes;", (8450, 0)),
        ("dopf", (0, 0)),
        ("nLeftrightarrow;", (8654, 0)),
        ("leftthr", (0, 0)),
        ("rulu", (0, 0)),
        ("NotElemen", (0, 0)),
        ("LessFullE", (0, 0)),
        ("EmptySmallSq", (0, 0)),
        ("rAtail;", (10524, 0)),
        ("zigrar", (0, 0)),
        ("DownArr", (0, 0)),
        ("Yopf", (0, 0)),
        ("boxtimes", (0, 0)),
        ("Upsil", (0, 0)),
        ("nrarrc;", (10547, 824)),
        ("DownLe", (0, 0)),
        ("Bopf", (0, 0)),
        ("minusdu;", (10794, 0)),
        ("TSHc", (0, 0)),
        ("Gdot;", (288, 0)),
        ("LessEqua", (0, 0)),
        ("Re", (0, 0)),
        ("icy;", (1080, 0)),
        ("im", (0, 0)),
        ("bumpE;", (10926, 0)),
        ("Ema", (0, 0)),
        ("or", (0, 0)),
        ("djc", (0, 0)),
        ("phmma", (0, 0)),
        ("ocir", (0, 0)),
        ("profsurf;", (8979, 0)),
        ("looparrowleft;", (8619, 0)),
        ("lefth", (0, 0)),
        ("nabl", (0, 0)),
        ("iq", (0, 0)),
        ("emac", (0, 0)),
        ("odot", (0, 0)),
        ("RightAngleBracke", (0, 0)),
        ("wcir", (0, 0)),
        ("LeftTeeArro", (0, 0)),
        ("oelig;", (339, 0)),
        ("nlef", (0, 0)),
        ("imagline;", (8464, 0)),
        ("ssm", (0, 0)),
        ("NotGrea", (0, 0)),
        ("LowerLef", (0, 0)),
        ("Lfr;", (120079, 0)),
        ("DownRightVecto", (0, 0)),
        ("longmaps", (0, 0)),
        ("sol", (0, 0)),
        ("exponential", (0, 0)),
        ("barwedge;", (8965, 0)),
        ("ltq", (0, 0)),
        ("LongRightArr", (0, 0)),
        ("aa", (0, 0)),
        ("ofr;", (120108, 0)),
        ("numsp;", (8199, 0)),
        ("oscr", (0, 0)),
        ("Iacut", (0, 0)),
        ("lvertneq", (0, 0)),
        ("udar", (0, 0)),
        ("to", (0, 0)),
        ("varsupsetn", (0, 0)),
        ("racute", (0, 0)),
        ("kfr;", (120104, 0)),
        ("mid;", (8739, 0)),
        ("npolint", (0, 0)),
        ("ContourIntegra", (0, 0)),
        ("rightthree", (0, 0)),
        ("velli", (0, 0)),
        ("ngeq", (0, 0)),
        ("nums", (0, 0)),
        ("upd", (0, 0)),
        ("thor", (0, 0)),
        ("rthree;", (8908, 0)),
        ("Pf", (0, 0)),
        ("mapstoup", (0, 0)),
        ("NotGreaterSlantE", (0, 0)),
        ("Poin", (0, 0)),
        ("Laplacetrf", (0, 0)),
        ("roa", (0, 0)),
        ("precn", (0, 0)),
        ("imaglin", (0, 0)),
        ("hookl", (0, 0)),
        ("Ccedil;", (199, 0)),
        ("searhk;", (10533, 0)),
        ("downharpoon", (0, 0)),
        ("RightUpDownVe", (0, 0)),
        ("cf", (0, 0)),
        ("Efr", (0, 0)),
        ("Rrightarrow;", (8667, 0)),
        ("bigodo", (0, 0)),
        ("Wcir", (0, 0)),
        ("loang;", (10220, 0)),
        ("rightsquig", (0, 0)),
        ("langd", (0, 0)),
        ("laem", (0, 0)),
        ("nsqsu", (0, 0)),
        ("smallsetminu", (0, 0)),
        ("dl", (0, 0)),
        ("DiacriticalD", (0, 0)),
        ("DownLeftVe", (0, 0)),
        ("DownLeftVectorBar;", (10582, 0)),
        ("vzig", (0, 0)),
        ("varnothing;", (8709, 0)),
        ("Rri", (0, 0)),
        ("NonBre", (0, 0)),
        ("andv;", (10842, 0)),
        ("NotLessSlantEqual;", (10877, 824)),
        ("isindot", (0, 0)),
        ("nspa", (0, 0)),
        ("complemen", (0, 0)),
        ("frac56;", (8538, 0)),
        ("ReverseUpEqui", (0, 0)),
        ("varsubs", (0, 0)),
        ("sacut", (0, 0)),
        ("bnequi", (0, 0)),
        ("DownArrow", (0, 0)),
        ("UpperRight", (0, 0)),
        ("Jsercy", (0, 0)),
        ("ac", (0, 0)),
        ("lfi", (0, 0)),
        ("natura", (0, 0)),
        ("TildeEqua", (0, 0)),
        ("UpArrowBar;", (10514, 0)),
        ("uparr", (0, 0)),
        ("Uogon;", (370, 0)),
        ("pcy", (0, 0)),
        ("Dcaron;", (270, 0)),
        ("Cconint;", (8752, 0)),
        ("LessFul", (0, 0)),
        ("ReverseUpEq", (0, 0)),
        ("cylc", (0, 0)),
        ("DoubleLeftTee;", (10980, 0)),
        ("oopf;", (120160, 0)),
        ("nR", (0, 0)),
        ("hami", (0, 0)),
        ("nesea", (0, 0)),
        ("mcy", (0, 0)),
        ("boxm", (0, 0)),
        ("RightTeeArro", (0, 0)),
        ("varsubsetneqq", (0, 0)),
        ("lthr", (0, 0)),
        ("dharl", (0, 0)),
        ("mD", (0, 0)),
        ("OpenCurly", (0, 0)),
        ("UpperLeftA", (0, 0)),
        ("DownLeftRightVector", (0, 0)),
        ("lneqq;", (8808, 0)),
        ("rightsq", (0, 0)),
        ("DotE", (0, 0)),
        ("SquareIntersect", (0, 0)),
        ("Succeeds;", (8827, 0)),
        ("ReverseElemen", (0, 0)),
        ("RightArrowLeftArr", (0, 0)),
        ("gla", (0, 0)),
        ("CircleM", (0, 0)),
        ("VeryThinSpac", (0, 0)),
        ("elin", (0, 0)),
        ("xu", (0, 0)),
        ("UpEquil", (0, 0)),
        ("big", (0, 0)),
        ("Bfr;", (120069, 0)),
        ("Cross;", (10799, 0)),
        ("gdo", (0, 0)),
        ("ctdot", (0, 0)),
        ("larrfs", (0, 0)),
        ("vnsub;", (8834, 8402)),
        ("leftharpoonu", (0, 0)),
        ("cdot;", (267, 0)),
        ("NotExist", (0, 0)),
        ("NotCongru", (0, 0)),
        ("lB", (0, 0)),
        ("curlyvee;", (8910, 0)),
        ("ShortLe", (0, 0)),
        ("prece", (0, 0)),
        ("Aring;", (197, 0)),
        ("tar", (0, 0)),
        ("Zdo", (0, 0)),
        ("Bscr", (0, 0)),
        ("upupar", (0, 0)),
        ("NotSquareSuperset;", (8848, 824)),
        ("origof", (0, 0)),
        ("sset", (0, 0)),
        ("Differential", (0, 0)),
        ("NegativeThickSpace", (0, 0)),
        ("CloseCurlyQuote;", (8217, 0)),
        ("SquareIntersection", (0, 0)),
        ("su", (0, 0)),
        ("downarr", (0, 0)),
        ("GreaterEq", (0, 0)),
        ("leftrightharpo", (0, 0)),
        ("LessFu", (0, 0)),
        ("ecolon;", (8789, 0)),
        ("LeftVe", (0, 0)),
        ("ks", (0, 0)),
        ("Theref", (0, 0)),
        ("Sci", (0, 0)),
        ("lrhar", (0, 0)),
        ("ntilde;", (241, 0)),
        ("iop", (0, 0)),
        ("suphso", (0, 0)),
        ("DoubleLongLeftRightArr", (0, 0)),
        ("hyp", (0, 0)),
        ("Ntild", (0, 0)),
        ("rdldh", (0, 0)),
        ("notni", (0, 0)),
        ("rdca;", (10551, 0)),
        ("Ccedi", (0, 0)),
        ("leftarrow", (0, 0)),
        ("Leftarrow", (0, 0)),
        ("Dia", (0, 0)),
        ("IOcy", (0, 0)),
        ("Vcy;", (1042, 0)),
        ("zd", (0, 0)),
        ("QUOT;", (34, 0)),
        ("YAc", (0, 0)),
        ("angz", (0, 0)),
        ("co", (0, 0)),
        ("Lleftarro", (0, 0)),
        ("odiv;", (10808, 0)),
        ("Leftri", (0, 0)),
        ("Horizo", (0, 0)),
        ("Longlefta", (0, 0)),
        ("LeftVecto", (0, 0)),
        ("Nscr", (0, 0)),
        ("Aa", (0, 0)),
        ("RightUpVectorBar;", (10580, 0)),
        ("Con", (0, 0)),
        ("ldrdhar", (0, 0)),
        ("Iuml", (207, 0)),
        ("sacu", (0, 0)),
        ("nLt", (0, 0)),
        ("circlearr", (0, 0)),
        ("AEli", (0, 0)),
        ("Lacute", (0, 0)),
        ("NotSuc", (0, 0)),
        ("llarr", (0, 0)),
        ("Negat", (0, 0)),
        ("intercal", (0, 0)),
        ("twoheadrighta", (0, 0)),
        ("lefthar", (0, 0)),
        ("middot", (183, 0)),
        ("prc", (0, 0)),
        ("Minu", (0, 0)),
        ("tau", (0, 0)),
        ("yacy", (0, 0)),
        ("andand;", (10837, 0)),
        ("LeftDownTeeVecto", (0, 0)),
        ("ntriangl", (0, 0)),
        ("scpolin", (0, 0)),
        ("DownArrowUpArr", (0, 0)),
        ("jmath;", (567, 0)),
        ("ique", (0, 0)),
        ("Ho", (0, 0)),
        ("Ari", (0, 0)),
        ("nscr;", (120003, 0)),
        ("utilde;", (361, 0)),
        ("succnsim", (0, 0)),
        ("Bopf;", (120121, 0)),
        ("InvisibleCo", (0, 0)),
        ("barwe", (0, 0)),
        ("lam", (0, 0)),
        ("capcu", (0, 0)),
        ("NotCu", (0, 0)),
        ("rarrhk;", (8618, 0)),
        ("bf", (0, 0)),
        ("wfr;", (120116, 0)),
        ("GreaterSl", (0, 0)),
        ("wreat", (0, 0)),
        ("leftthreetimes;", (8907, 0)),
        ("a", (0, 0)),
        ("mapsto", (0, 0)),
        ("acute", (180, 0)),
        ("mldr", (0, 0)),
        ("VerticalLi", (0, 0)),
        ("rnmid", (0, 0)),
        ("Succ", (0, 0)),
        ("LeftTriangle", (0, 0)),
        ("dz", (0, 0)),
        ("hy", (0, 0)),
        ("LongLeftRig", (0, 0)),
        ("LeftDoubleBrac", (0, 0)),
        ("NonB", (0, 0)),
        ("DownLeftTe", (0, 0)),
        ("frac18;", (8539, 0)),
        ("disi", (0, 0)),
        ("Longleftrightarrow;", (10234, 0)),
        ("lambda;", (955, 0)),
        ("rBarr;", (10511, 0)),
        ("boxt", (0, 0)),
        ("nvle", (0, 0)),
        ("ograv", (0, 0)),
        ("poin", (0, 0)),
        ("xlArr;", (10232, 0)),
        ("rdquor;", (8221, 0)),
        ("Iukcy", (0, 0)),
        ("tp", (0, 0)),
        ("ge;", (8805, 0)),
        ("rceil;", (8969, 0)),
        ("Lt;", (8810, 0)),
        ("acirc;", (226, 0)),
        ("bullet;", (8226, 0)),
        ("sm", (0, 0)),
        ("LJ", (0, 0)),
        ("rightha", (0, 0)),
        ("NotGreaterSlantEqual", (0, 0)),
        ("hookleftarrow;", (8617, 0)),
        ("qopf", (0, 0)),
        ("ShortRightArrow", (0, 0)),
        ("rppol", (0, 0)),
        ("caps", (0, 0)),
        ("quo", (0, 0)),
        ("ycirc", (0, 0)),
        ("digamm", (0, 0)),
        ("LT", (60, 0)),
        ("rlarr", (0, 0)),
        ("Udblac", (0, 0)),
        ("yen;", (165, 0)),
        ("luruhar", (0, 0)),
        ("ddarr", (0, 0)),
        ("nexist;", (8708, 0)),
        ("RoundImplie", (0, 0)),
        ("rightharpoondo", (0, 0)),
        ("nsqsupe;", (8931, 0)),
        ("Minus", (0, 0)),
        ("bigv", (0, 0)),
        ("Backs", (0, 0)),
        ("nleftrigh", (0, 0)),
        ("plusa", (0, 0)),
        ("rtr", (0, 0)),
        ("NotLessG", (0, 0)),
        ("Barw", (0, 0)),
        ("ShortRightA", (0, 0)),
        ("vrt", (0, 0)),
        ("epl", (0, 0)),
        ("lbra", (0, 0)),
        ("nh", (0, 0)),
        ("bigtriangledown", (0, 0)),
        ("longmapst", (0, 0)),
        ("LeftTriangleEqu", (0, 0)),
        ("zac", (0, 0)),
        ("bigtriangleup", (0, 0)),
        ("lesdot;", (10879, 0)),
        ("hai", (0, 0)),
        ("llar", (0, 0)),
        ("sma", (0, 0)),
        ("fltns", (0, 0)),
        ("Icirc", (206, 0)),
        ("MediumSpa", (0, 0)),
        ("cwconint;", (8754, 0)),
        ("blacklo", (0, 0)),
        ("Cl", (0, 0)),
        ("ncedil;", (326, 0)),
        ("lopf", (0, 0)),
        ("Is", (0, 0)),
        ("eqslan", (0, 0)),
        ("ohbar", (0, 0)),
        ("xodot;", (10752, 0)),
        ("rarrb", (0, 0)),
        ("supds", (0, 0)),
        ("down", (0, 0)),
        ("Differentia", (0, 0)),
        ("nhA", (0, 0)),
        ("LeftCeiling", (0, 0)),
        ("measure", (0, 0)),
        ("NotLessSlantE", (0, 0)),
        ("supseteq", (0, 0)),
        ("Ecir", (0, 0)),
        ("Afr;", (120068, 0)),
        ("barve", (0, 0)),
        ("LeftDoubleBracket", (0, 0)),
        ("downdowna", (0, 0)),
        ("planckh;", (8462, 0)),
        ("Aogon", (0, 0)),
        ("plusb;", (8862, 0)),
        ("center", (0, 0)),
        ("PrecedesEqu", (0, 0)),
        ("rsh;", (8625, 0)),
        ("Mellin", (0, 0)),
        ("SubsetEqu", (0, 0)),
        ("gve", (0, 0)),
        ("LeftArrowRightArrow", (0, 0)),
        ("Ar", (0, 0)),
        ("loang", (0, 0)),
        ("supmul", (0, 0)),
        ("Horizontal", (0, 0)),
        ("Cac", (0, 0)),
        ("supe;", (8839, 0)),
        ("LeftRightArrow", (0, 0)),
        ("gbreve;", (287, 0)),
        ("LeftVect", (0, 0)),
        ("micro;", (181, 0)),
        ("fl", (0, 0)),
        ("RightCeiling", (0, 0)),
        ("em", (0, 0)),
        ("Rca", (0, 0)),
        ("OpenCurlyDoubl", (0, 0)),
        ("bcong;", (8780, 0)),
        ("HorizontalLin", (0, 0)),
        ("nvHa", (0, 0)),
        ("CloseCurlyDoubleQuo", (0, 0)),
        ("rAr", (0, 0)),
        ("RightDownVectorB", (0, 0)),
        ("NotNestedGreaterGr", (0, 0)),
        ("rightthreetime", (0, 0)),
        ("nlArr;", (8653, 0)),
        ("DownArrowUpAr", (0, 0)),
        ("Iti", (0, 0)),
        ("nsupseteqq", (0, 0)),
        ("nrarr;", (8603, 0)),
        ("lesdoto;", (10881, 0)),
        ("angmsdah;", (10671, 0)),
        ("OverPare", (0, 0)),
        ("kfr", (0, 0)),
        ("lsi", (0, 0)),
        ("Be", (0, 0)),
        ("ReverseU", (0, 0)),
        ("low", (0, 0)),
        ("asympeq;", (8781, 0)),
        ("leftarrowtail", (0, 0)),
        ("andv", (0, 0)),
        ("iquest;", (191, 0)),
        ("heartsuit", (0, 0)),
        ("blacktrian", (0, 0)),
        ("UnderBrack", (0, 0)),
        ("vert", (0, 0)),
        ("quat", (0, 0)),
        ("cir", (0, 0)),
        ("wf", (0, 0)),
        ("downdownarrows", (0, 0)),
        ("smallset", (0, 0)),
        ("sccue", (0, 0)),
        ("RightTriangleEqual;", (8885, 0)),
        ("quaternio", (0, 0)),
        ("DownRightTeeVecto", (0, 0)),
        ("eqv", (0, 0)),
        ("npreceq", (0, 0)),
        ("Zac", (0, 0)),
        ("oli", (0, 0)),
        ("parsim", (0, 0)),
        ("supdsub", (0, 0)),
        ("equiv;", (8801, 0)),
        ("ola", (0, 0)),
        ("dtdot;", (8945, 0)),
        ("raempt", (0, 0)),
        ("LeftAngle", (0, 0)),
        ("nshort", (0, 0)),
        ("Rarrtl", (0, 0)),
        ("frac4", (0, 0)),
        ("ltrP", (0, 0)),
        ("curvearrow", (0, 0)),
        ("mdash", (0, 0)),
        ("nearhk", (0, 0)),
        ("suplar", (0, 0)),
        ("GreaterEqualLes", (0, 0)),
        ("kjcy", (0, 0)),
        ("vartheta;", (977, 0)),
        ("llco", (0, 0)),
        ("boxHU;", (9577, 0)),
        ("rightthr", (0, 0)),
        ("nharr", (0, 0)),
        ("para", (182, 0)),
        ("W", (0, 0)),
        ("andslope", (0, 0)),
        ("sqsupsete", (0, 0)),
        ("CircleMinus;", (8854, 0)),
        ("nexi", (0, 0)),
        ("Hilber", (0, 0)),
        ("iex", (0, 0)),
        ("UnionPlu", (0, 0)),
        ("Bumpe", (0, 0)),
        ("acu", (0, 0)),
        ("cw", (0, 0)),
        ("Eop", (0, 0)),
        ("bigupl", (0, 0)),
        ("mapstodown;", (8615, 0)),
        ("hslash", (0, 0)),
        ("Congruent", (0, 0)),
        ("nGg", (0, 0)),
        ("supE;", (10950, 0)),
        ("parsl", (0, 0)),
        ("lsa", (0, 0)),
        ("spadesuit;", (9824, 0)),
        ("ju", (0, 0)),
        ("Juk", (0, 0)),
        ("cwint;", (8753, 0)),
        ("Equilibrium;", (8652, 0)),
        ("Lon", (0, 0)),
        ("NotNestedLe", (0, 0)),
        ("succsim", (0, 0)),
        ("varr", (0, 0)),
        ("rbrke", (0, 0)),
        ("Precedes", (0, 0)),
        ("angmsd;", (8737, 0)),
        ("lth", (0, 0)),
        ("dal", (0, 0)),
        ("GJc", (0, 0)),
        ("ldrushar", (0, 0)),
        ("Upsi;", (978, 0)),
        ("nLtv", (0, 0)),
        ("rationa", (0, 0)),
        ("downharpoonr", (0, 0)),
        ("DoubleDownArrow", (0, 0)),
        ("urcorner", (0, 0)),
        ("tscy;", (1094, 0)),
        ("upharpoonleft;", (8639, 0)),
        ("vBarv", (0, 0)),
        ("square;", (9633, 0)),
        ("HumpEqua", (0, 0)),
        ("jcy;", (1081, 0)),
        ("gtlPar", (0, 0)),
        ("nsucceq", (0, 0)),
        ("sacute;", (347, 0)),
        ("eta;", (951, 0)),
        ("bop", (0, 0)),
        ("PrecedesSlantEqual;", (8828, 0)),
        ("VerticalB", (0, 0)),
        ("NotSucceedsSlantEq", (0, 0)),
        ("vsupnE;", (10956, 65024)),
        ("hoa", (0, 0)),
        ("ea", (0, 0)),
        ("leftrighth", (0, 0)),
        ("LT;", (60, 0)),
        ("divo", (0, 0)),
        ("sra", (0, 0)),
        ("DownLeftR", (0, 0)),
        ("lopa", (0, 0)),
        ("Esim;", (10867, 0)),
        ("rhov", (0, 0)),
        ("xha", (0, 0)),
        ("NotNestedLessLess", (0, 0)),
        ("DoubleLongLeftRightAr", (0, 0)),
        ("kcy", (0, 0)),
        ("Zop", (0, 0)),
        ("nvdash;", (8876, 0)),
        ("NonBreakin", (0, 0)),
        ("NotLeftTriangleE", (0, 0)),
        ("subedot", (0, 0)),
        ("LeftDouble", (0, 0)),
        ("hba", (0, 0)),
        ("DoubleUpArr", (0, 0)),
        ("D", (0, 0)),
        ("bull;", (8226, 0)),
        ("EmptySmallSqu", (0, 0)),
        ("Rule", (0, 0)),
        ("RightTeeV", (0, 0)),
        ("Imag", (0, 0)),
        ("Sh", (0, 0)),
        ("lesdo", (0, 0)),
        ("EmptyVery", (0, 0)),
        ("nla", (0, 0)),
        ("nv", (0, 0)),
        ("botto", (0, 0)),
        ("NotEle", (0, 0)),
        ("InvisibleComm", (0, 0)),
        ("cen", (0, 0)),
        ("QU", (0, 0)),
        ("llt", (0, 0)),
        ("boxhu", (0, 0)),
        ("psi", (0, 0)),
        ("LongLeftArr", (0, 0)),
        ("NotSucceedsEqual;", (10928, 824)),
        ("bempt", (0, 0)),
        ("mcom", (0, 0)),
        ("p", (0, 0)),
        ("Abrev", (0, 0)),
        ("Longl", (0, 0)),
        ("ClockwiseContour", (0, 0)),
        ("NotSucceedsTi", (0, 0)),
        ("curlyeqsuc", (0, 0)),
        ("rpargt", (0, 0)),
        ("lacut", (0, 0)),
        ("TildeFullEqual;", (8773, 0)),
        ("NotRightTri", (0, 0)),
        ("rsaqu", (0, 0)),
        ("shortparall", (0, 0)),
        ("homt", (0, 0)),
        ("nvg", (0, 0)),
        ("rlhar", (0, 0)),
        ("nr", (0, 0)),
        ("bprime;", (8245, 0)),
        ("zeta;", (950, 0)),
        ("LeftTriangleBa", (0, 0)),
        ("supsetn", (0, 0)),
        ("kgreen;", (312, 0)),
        ("uua", (0, 0)),
        ("exi", (0, 0)),
        ("LongLeftRightArro", (0, 0)),
        ("dzc", (0, 0)),
        ("Pcy;", (1055, 0)),
        ("SquareSupersetEq", (0, 0)),
        ("rba", (0, 0)),
        ("Clockwis", (0, 0)),
        ("DJc", (0, 0)),
        ("fpartint;", (10765, 0)),
        ("gesles", (0, 0)),
        ("CloseCurly", (0, 0)),
        ("lowbar;", (95, 0)),
        ("toea", (0, 0)),
        ("OverParenthesis", (0, 0)),
        ("measu", (0, 0)),
        ("inodo", (0, 0)),
        ("jop", (0, 0)),
        ("Psi", (0, 0)),
        ("Yscr", (0, 0)),
        ("RightUpVect", (0, 0)),
        ("NotGreaterGreat", (0, 0)),
        ("acy;", (1072, 0)),
        ("ulcor", (0, 0)),
        ("gla;", (10917, 0)),
        ("emacr", (0, 0)),
        ("nsupe", (0, 0)),
        ("lltri;", (9722, 0)),
        ("COPY;", (169, 0)),
        ("NotSucceedsSl", (0, 0)),
        ("lagran", (0, 0)),
        ("thetav", (0, 0)),
        ("boxVL", (0, 0)),
        ("risingdo", (0, 0)),
        ("NotSucceedsTil", (0, 0)),
        ("blan", (0, 0)),
        ("NotTildeFullEqual", (0, 0)),
        ("DoubleLongLef", (0, 0)),
        ("top", (0, 0)),
        ("tin", (0, 0)),
        ("olcross", (0, 0)),
        ("intege", (0, 0)),
        ("YIcy", (0, 0)),
        ("dw", (0, 0)),
        ("nleftrightar", (0, 0)),
        ("doteq", (0, 0)),
        ("S", (0, 0)),
        ("egs", (0, 0)),
        ("jmat", (0, 0)),
        ("vdas", (0, 0)),
        ("vsup", (0, 0)),
        ("DZcy;", (1039, 0)),
        ("rational", (0, 0)),
        ("rthr", (0, 0)),
        ("imagpart", (0, 0)),
        ("RightUpDownVect", (0, 0)),
        ("GreaterGrea", (0, 0)),
        ("Uri", (0, 0)),
        ("blacktriang", (0, 0)),
        ("GreaterSlantE", (0, 0)),
        ("ncy", (0, 0)),
        ("colone;", (8788, 0)),
        ("rpargt;", (10644, 0)),
        ("lH", (0, 0)),
        ("lgE", (0, 0)),
        ("ari", (0, 0)),
        ("Vf", (0, 0)),
        ("Oma", (0, 0)),
        ("lAtail;", (10523, 0)),
        ("escr;", (8495, 0)),
        ("cylcty", (0, 0)),
        ("Updownarr", (0, 0)),
        ("spades", (0, 0)),
        ("DDotrahd;", (10513, 0)),
        ("AEl", (0, 0)),
        ("fem", (0, 0)),
        ("image;", (8465, 0)),
        ("CapitalDifferential", (0, 0)),
        ("leftrightsquig", (0, 0)),
        ("prurel", (0, 0)),
        ("fallin", (0, 0)),
        ("agr", (0, 0)),
        ("DoubleUpA", (0, 0)),
        ("uhar", (0, 0)),
        ("DownLeftVectorB", (0, 0)),
        ("RightAngleBrac", (0, 0)),
        ("dsol;", (10742, 0)),
        ("vcy;", (1074, 0)),
        ("NestedGr", (0, 0)),
        ("lopar", (0, 0)),
        ("Lmid", (0, 0)),
        ("aeli", (0, 0)),
        ("nsubset;", (8834, 8402)),
        ("LeftRightArr", (0, 0)),
        ("longlef", (0, 0)),
        ("leftleft", (0, 0)),
        ("NewLin", (0, 0)),
        ("dash;", (8208, 0)),
        ("Udbl", (0, 0)),
        ("thickappro", (0, 0)),
        ("LeftUpV", (0, 0)),
        ("awconint", (0, 0)),
        ("DoubleLeftArr", (0, 0)),
        ("qfr", (0, 0)),
        ("DoubleVert", (0, 0)),
        ("rob", (0, 0)),
        ("hArr;", (8660, 0)),
        ("backepsilo", (0, 0)),
        ("DSc", (0, 0)),
        ("RightVe", (0, 0)),
        ("ba", (0, 0)),
        ("bigcup", (0, 0)),
        ("lv", (0, 0)),
        ("Rev", (0, 0)),
        ("fltns;", (9649, 0)),
        ("lAtail", (0, 0)),
        ("OElig", (0, 0)),
        ("looparrowlef", (0, 0)),
        ("swarr", (0, 0)),
        ("olt;", (10688, 0)),
        ("Zc", (0, 0)),
        ("rightlefta", (0, 0)),
        ("OEl", (0, 0)),
        ("triangleright;", (9657, 0)),
        ("cupbrca", (0, 0)),
        ("square", (0, 0)),
        ("Cr", (0, 0)),
        ("longleftrig", (0, 0)),
        ("lced", (0, 0)),
        ("As", (0, 0)),
        ("eacute", (233, 0)),
        ("backsimeq;", (8909, 0)),
        ("rightharp", (0, 0)),
        ("not;", (172, 0)),
        ("ord", (0, 0)),
        ("Gbreve;", (286, 0)),
        ("lve", (0, 0)),
        ("rarrb;", (8677, 0)),
        ("hairsp", (0, 0)),
        ("Pi;", (928, 0)),
        ("ltc", (0, 0)),
        ("Dcar", (0, 0)),
        ("nvlA", (0, 0)),
        ("Dag", (0, 0)),
        ("Imacr;", (298, 0)),
        ("fi", (0, 0)),
        ("leftarrowtai", (0, 0)),
        ("Ascr", (0, 0)),
        ("midast;", (42, 0)),
        ("aogo", (0, 0)),
        ("AElig", (198, 0)),
        ("RightArrowL", (0, 0)),
        ("oh", (0, 0)),
        ("I", (0, 0)),
        ("DiacriticalG", (0, 0)),
        ("NestedLessLess;", (8810, 0)),
        ("Int;", (8748, 0)),
        ("capdo", (0, 0)),
        ("straighte", (0, 0)),
        ("NotExis", (0, 0)),
        ("CupC", (0, 0)),
        ("napprox", (0, 0)),
        ("NotSquareSubsetEqual;", (8930, 0)),
        ("ssmi", (0, 0)),
        ("loti", (0, 0)),
        ("SucceedsEqu", (0, 0)),
        ("hf", (0, 0)),
        ("Dash", (0, 0)),
        ("lceil;", (8968, 0)),
        ("sim", (0, 0)),
        ("tcar", (0, 0)),
        ("ccaps", (0, 0)),
        ("frac12;", (189, 0)),
        ("ufr", (0, 0)),
        ("iff", (0, 0)),
        ("noti", (0, 0)),
        ("Das", (0, 0)),
        ("Tfr", (0, 0)),
        ("apE;", (10864, 0)),
        ("equa", (0, 0)),
        ("leftleftarr", (0, 0)),
        ("NewL", (0, 0)),
        ("ShortRi", (0, 0)),
        ("ddagge", (0, 0)),
        ("diamondsu", (0, 0)),
        ("Ya", (0, 0)),
        ("ul", (0, 0)),
        ("LongRightArrow", (0, 0)),
        ("Hilbe", (0, 0)),
        ("ntgl;", (8825, 0)),
        ("DoubleLeftRigh", (0, 0)),
        ("sstarf", (0, 0)),
        ("blank", (0, 0)),
        ("vopf;", (120167, 0)),
        ("uhbl", (0, 0)),
        ("NotLessEqual", (0, 0)),
        ("NotElem", (0, 0)),
        ("NotLeftTria", (0, 0)),
        ("DoubleContou", (0, 0)),
        ("vopf", (0, 0)),
        ("Xo", (0, 0)),
        ("lmoust", (0, 0)),
        ("NestedGreaterGrea", (0, 0)),
        ("nt", (0, 0)),
        ("NegativeV", (0, 0)),
        ("ShortUpArr", (0, 0)),
        ("alep", (0, 0)),
        ("uopf;", (120166, 0)),
        ("LeftDoubleBrack", (0, 0)),
        ("ngE", (0, 0)),
        ("NJc", (0, 0)),
        ("Precede", (0, 0)),
        ("hybull", (0, 0)),
        ("hybu", (0, 0)),
        ("vartrianglele", (0, 0)),
        ("DoubleLeftRightArr", (0, 0)),
        ("Ccaron;", (268, 0)),
        ("range;", (10661, 0)),
        ("angle", (0, 0)),
        ("Cco", (0, 0)),
        ("SucceedsEqua", (0, 0)),
        ("nisd;", (8954, 0)),
        ("NotSubset", (0, 0)),
        ("rcedi", (0, 0)),
        ("kjcy;", (1116, 0)),
        ("Wedge;", (8896, 0)),
        ("nleftarrow;", (8602, 0)),
        ("there4", (0, 0)),
        ("Iogon;", (302, 0)),
        ("minusb", (0, 0)),
        ("longl", (0, 0)),
        ("NotSupersetEqua", (0, 0)),
        ("trianglele", (0, 0)),
        ("hel", (0, 0)),
        ("ltrPar;", (10646, 0)),
        ("NotGreaterFullEqua", (0, 0)),
        ("CloseCur", (0, 0)),
        ("aelig", (230, 0)),
        ("ltrie", (0, 0)),
        ("tstrok;", (359, 0)),
        ("fopf", (0, 0)),
        ("upharpoonl", (0, 0)),
        ("tshc", (0, 0)),
        ("imo", (0, 0)),
        ("rthree", (0, 0)),
        ("iinfin", (0, 0)),
        ("Longleftri", (0, 0)),
        ("oda", (0, 0)),
        ("EmptyVerySma", (0, 0)),
        ("lang;", (10216, 0)),
        ("rbbrk;", (10099, 0)),
        ("nbump", (0, 0)),
        ("Wscr", (0, 0)),
        ("LeftRightA", (0, 0)),
        ("sesw", (0, 0)),
        ("oint", (0, 0)),
        ("pm;", (177, 0)),
        ("nparal", (0, 0)),
        ("subsup;", (10963, 0)),
        ("Tced", (0, 0)),
        ("xnis", (0, 0)),
        ("lst", (0, 0)),
        ("utdot;", (8944, 0)),
        ("Yfr", (0, 0)),
        ("NegativeThin", (0, 0)),
        ("orv", (0, 0)),
        ("Mu;", (924, 0)),
        ("agrave", (224, 0)),
        ("del", (0, 0)),
        ("GreaterEqualL", (0, 0)),
        ("dwangl", (0, 0)),
        ("approx", (0, 0)),
        ("ClockwiseC", (0, 0)),
        ("Dsc", (0, 0)),
        ("doublebarwedg", (0, 0)),
        ("DownArrowU", (0, 0)),
        ("lrtri", (0, 0)),
        ("NegativeVeryThinSpace;", (8203, 0)),
        ("bump;", (8782, 0)),
        ("bigcirc", (0, 0)),
        ("divideontime", (0, 0)),
        ("uph", (0, 0)),
        ("upharpoonleft", (0, 0)),
        ("bigwed", (0, 0)),
        ("GreaterLess", (0, 0)),
        ("Invisible", (0, 0)),
        ("RightTee", (0, 0)),
        ("measuredan", (0, 0)),
        ("DoubleLef", (0, 0)),
        ("NestedGre", (0, 0)),
        ("eDDot;", (10871, 0)),
        ("Ccirc;", (264, 0)),
        ("RightUpD", (0, 0)),
        ("Sig", (0, 0)),
        ("Scedi", (0, 0)),
        ("NotNestedGrea", (0, 0)),
        ("cwconi", (0, 0)),
        ("DoubleContourInteg", (0, 0)),
        ("curvearrowri", (0, 0)),
        ("lfr;", (120105, 0)),
        ("olin", (0, 0)),
        ("vlt", (0, 0)),
        ("RuleDela", (0, 0)),
        ("imof;", (8887, 0)),
        ("RightTeeVect", (0, 0)),
        ("Mu", (0, 0)),
        ("gescc", (0, 0)),
        ("NotSuperset;", (8835, 8402)),
        ("curlyeqsu", (0, 0)),
        ("Element", (0, 0)),
        ("ut", (0, 0)),
        ("boxminu", (0, 0)),
        ("DoubleUpDownA", (0, 0)),
        ("xoplus", (0, 0)),
        ("varkapp", (0, 0)),
        ("Clock", (0, 0)),
        ("Superset", (0, 0)),
        ("sha", (0, 0)),
        ("cap", (0, 0)),
        ("Ucy", (0, 0)),
        ("neAr", (0, 0)),
        ("isind", (0, 0)),
        ("npolint;", (10772, 0)),
        ("bemptyv", (0, 0)),
        ("cuw", (0, 0)),
        ("Leftarro", (0, 0)),
        ("EmptyVerySmallSqu", (0, 0)),
        ("Cu", (0, 0)),
        ("squar", (0, 0)),
        ("Bec", (0, 0)),
        ("Longleftarrow", (0, 0)),
        ("LeftTriangl", (0, 0)),
        ("nva", (0, 0)),
        ("odsold", (0, 0)),
        ("straightepsil", (0, 0)),
        ("circlearrowleft", (0, 0)),
        ("Cros", (0, 0)),
        ("OEli", (0, 0)),
        ("boxUL", (0, 0)),
        ("DoubleLongR", (0, 0)),
        ("NotTildeT", (0, 0)),
        ("boxVl;", (9570, 0)),
        ("gE;", (8807, 0)),
        ("ThickSpac", (0, 0)),
        ("NotL", (0, 0)),
        ("longri", (0, 0)),
        ("eas", (0, 0)),
        ("ThickSpace", (0, 0)),
        ("Rfr", (0, 0)),
        ("Prod", (0, 0)),
        ("pr;", (8826, 0)),
        ("dot;", (729, 0)),
        ("duarr;", (8693, 0)),
        ("T", (0, 0)),
        ("hbar", (0, 0)),
        ("ange", (0, 0)),
        ("duarr", (0, 0)),
        ("ufish", (0, 0)),
        ("Del;", (8711, 0)),
        ("xv", (0, 0)),
        ("SucceedsSlant", (0, 0)),
        ("ofcir;", (10687, 0)),
        ("forall;", (8704, 0)),
        ("reals;", (8477, 0)),
        ("afr;", (120094, 0)),
        ("rightrigh", (0, 0)),
        ("cirfni", (0, 0)),
        ("rfisht;", (10621, 0)),
        ("nacut", (0, 0)),
        ("Sa", (0, 0)),
        ("VeryThin", (0, 0)),
        ("emp", (0, 0)),
        ("angrtvbd", (0, 0)),
        ("iot", (0, 0)),
        ("cup;", (8746, 0)),
        ("ngsim;", (8821, 0)),
        ("latail", (0, 0)),
        ("dtd", (0, 0)),
        ("neA", (0, 0)),
        ("NotSupersetEqual", (0, 0)),
        ("Lang", (0, 0)),
        ("Exp", (0, 0)),
        ("supedot;", (10948, 0)),
        ("SquareSupe", (0, 0)),
        ("eta", (0, 0)),
        ("slarr;", (8592, 0)),
        ("xdtr", (0, 0)),
        ("euro;", (8364, 0)),
        ("nvrA", (0, 0)),
        ("NotSuccee", (0, 0)),
        ("mnplus", (0, 0)),
        ("emptyset", (0, 0)),
        ("gopf;", (120152, 0)),
        ("profsu", (0, 0)),
        ("RightDoubleBra", (0, 0)),
        ("angmsdab", (0, 0)),
        ("Leftrightar", (0, 0)),
        ("zcaron;", (382, 0)),
        ("timesb;", (8864, 0)),
        ("curlywedg", (0, 0)),
        ("QUO", (0, 0)),
        ("NotReverse", (0, 0)),
        ("ogra", (0, 0)),
        ("hstr", (0, 0)),
        ("NotNe", (0, 0)),
        ("l", (0, 0)),
        ("nab", (0, 0)),
        ("bepsi;", (1014, 0)),
        ("upar", (0, 0)),
        ("Subse", (0, 0)),
        ("rs", (0, 0)),
        ("Congruent;", (8801, 0)),
        ("NotGreaterLes", (0, 0)),
        ("juk", (0, 0)),
        ("Cfr;", (8493, 0)),
        ("nleftrightarrow;", (8622, 0)),
        ("gel;", (8923, 0)),
        ("Leftrig", (0, 0)),
        ("Uarroci", (0, 0)),
        ("fnof", (0, 0)),
        ("angmsdaf;", (10669, 0)),
        ("YUc", (0, 0)),
        ("Tcedi", (0, 0)),
        ("les", (0, 0)),
        ("loze", (0, 0)),
        ("lesseqqgtr", (0, 0)),
        ("percnt;", (37, 0)),
        ("bne;", (61, 8421)),
        ("nrightarrow", (0, 0)),
        ("piv;", (982, 0)),
        ("lsim;", (8818, 0)),
        ("COP", (0, 0)),
        ("blacksquar", (0, 0)),
        ("DoubleVertica", (0, 0)),
        ("Reverse", (0, 0)),
        ("vsupne", (0, 0)),
        ("It", (0, 0)),
        ("Popf;", (8473, 0)),
        ("GreaterGr", (0, 0)),
        ("bbrk;", (9141, 0)),
        ("Pl", (0, 0)),
        ("NotCupCa", (0, 0)),
        ("simd", (0, 0)),
        ("boxtim", (0, 0)),
        ("GreaterSlantEqu", (0, 0)),
        ("GreaterLes", (0, 0)),
        ("LessEqualG", (0, 0)),
        ("ntrianglerighteq", (0, 0)),
        ("ho", (0, 0)),
        ("HumpEqual", (0, 0)),
        ("hookleftarro", (0, 0)),
        ("smid", (0, 0)),
        ("shchcy;", (1097, 0)),
        ("rbra", (0, 0)),
        ("LeftArrowRig", (0, 0)),
        ("Doub", (0, 0)),
        ("bernou;", (8492, 0)),
        ("Vdas", (0, 0)),
        ("dote", (0, 0)),
        ("eac", (0, 0)),
        ("gneqq", (0, 0)),
        ("ApplyFunc", (0, 0)),
        ("rationals;", (8474, 0)),
        ("kapp", (0, 0)),
        ("NotG", (0, 0)),
        ("ag", (0, 0)),
        ("supplu", (0, 0)),
        ("sec", (0, 0)),
        ("Expone", (0, 0)),
        ("ctdo", (0, 0)),
        ("pound", (163, 0)),
        ("NotGreaterGreater;", (8811, 824)),
        ("simplu", (0, 0)),
        ("Exi", (0, 0)),
        ("st", (0, 0)),
        ("kopf;", (120156, 0)),
        ("zwnj;", (8204, 0)),
        ("UnionPlus;", (8846, 0)),
        ("ntriangleri", (0, 0)),
        ("xdtri", (0, 0)),
        ("supmult", (0, 0)),
        ("The", (0, 0)),
        ("blacklozenge", (0, 0)),
        ("ngeqsl", (0, 0)),
        ("iio", (0, 0)),
        ("fallingdotseq;", (8786, 0)),
        ("Co", (0, 0)),
        ("bcong", (0, 0)),
        ("lvertneqq;", (8808, 65024)),
        ("LeftUpTeeVec", (0, 0)),
        ("triangledow", (0, 0)),
        ("Uparrow;", (8657, 0)),
        ("varep", (0, 0)),
        ("R", (0, 0)),
        ("alefsym", (0, 0)),
        ("RBar", (0, 0)),
        ("lceil", (0, 0)),
        ("DownRightVector", (0, 0)),
        ("suph", (0, 0)),
        ("DownAr", (0, 0)),
        ("ZeroWidth", (0, 0)),
        ("NotRi", (0, 0)),
        ("precsim", (0, 0)),
        ("xop", (0, 0)),
        ("Mfr", (0, 0)),
        ("pluss", (0, 0)),
        ("ogt;", (10689, 0)),
        ("gtlP", (0, 0)),
        ("nwArr;", (8662, 0)),
        ("rarrl", (0, 0)),
        ("larrp", (0, 0)),
        ("ShortRigh", (0, 0)),
        ("lneqq", (0, 0)),
        ("Cconint", (0, 0)),
        ("CounterClockwi", (0, 0)),
        ("inodot;", (305, 0)),
        ("planc", (0, 0)),
        ("nsupseteqq;", (10950, 824)),
        ("Updownarrow;", (8661, 0)),
        ("DD;", (8517, 0)),
        ("black", (0, 0)),
        ("gtcir;", (10874, 0)),
        ("righta", (0, 0)),
        ("Uum", (0, 0)),
        ("NotLess;", (8814, 0)),
        ("pu", (0, 0)),
        ("SquareIntersecti", (0, 0)),
        ("rarr;", (8594, 0)),
        ("capand", (0, 0)),
        ("nrtri;", (8939, 0)),
        ("LeftUpDownVect", (0, 0)),
        ("trade;", (8482, 0)),
        ("Eacut", (0, 0)),
        ("sqsups", (0, 0)),
        ("CircleD", (0, 0)),
        ("rsaquo;", (8250, 0)),
        ("sum", (0, 0)),
        ("vell", (0, 0)),
        ("ZeroWid", (0, 0)),
        ("hscr", (0, 0)),
        ("Subset", (0, 0)),
        ("Gcirc;", (284, 0)),
        ("awconi", (0, 0)),
        ("VDas", (0, 0)),
        ("Ouml", (214, 0)),
        ("Im", (0, 0)),
        ("sdo", (0, 0)),
        ("Ecirc", (202, 0)),
        ("supsetneqq;", (10956, 0)),
        ("Scar", (0, 0)),
        ("kgreen", (0, 0)),
        ("ncaron;", (328, 0)),
        ("doublebarwedge", (0, 0)),
        ("femal", (0, 0)),
        ("gammad;", (989, 0)),
        ("dlcorn", (0, 0)),
        ("twoh", (0, 0)),
        ("RuleDelay", (0, 0)),
        ("submul", (0, 0)),
        ("mapsto;", (8614, 0)),
        ("rtime", (0, 0)),
        ("scar", (0, 0)),
        ("Ecaron;", (282, 0)),
        ("late;", (10925, 0)),
        ("QUOT", (34, 0)),
        ("aring", (229, 0)),
        ("Rscr;", (8475, 0)),
        ("Hstro", (0, 0)),
        ("ZHcy", (0, 0)),
        ("anda", (0, 0)),
        ("Elem", (0, 0)),
        ("nsucc", (0, 0)),
        ("prna", (0, 0)),
        ("NotHumpDownHum", (0, 0)),
        ("succapp", (0, 0)),
        ("DoubleVerticalBar", (0, 0)),
        ("sigmav;", (962, 0)),
        ("TRA", (0, 0)),
        ("ofci", (0, 0)),
        ("UpTee;", (8869, 0)),
        ("zdo", (0, 0)),
        ("NotDoubleVer", (0, 0)),
        ("delta", (0, 0)),
        ("rbrke;", (10636, 0)),
        ("zopf", (0, 0)),
        ("lesdotor;", (10883, 0)),
        ("sig", (0, 0)),
        ("epa", (0, 0)),
        ("divideontimes;", (8903, 0)),
        ("NotTilde;", (8769, 0)),
        ("NotPrecedesSlantEq", (0, 0)),
        ("Eo", (0, 0)),
        ("UpDownA", (0, 0)),
        ("frown;", (8994, 0)),
        ("DotEqual", (0, 0)),
        ("IO", (0, 0)),
        ("iuml", (239, 0)),
        ("lharul", (0, 0)),
        ("falli", (0, 0)),
        ("udhar;", (10606, 0)),
        ("dfr;", (120097, 0)),
        ("Qopf;", (8474, 0)),
        ("frac14;", (188, 0)),
        ("raemptyv", (0, 0)),
        ("nprece", (0, 0)),
        ("rdquor", (0, 0)),
        ("vnsup;", (8835, 8402)),
        ("iacu", (0, 0)),
        ("longrightarrow", (0, 0)),
        ("leftrightarrows", (0, 0)),
        ("prop", (0, 0)),
        ("orde", (0, 0)),
        ("otimesa", (0, 0)),
        ("suplarr;", (10619, 0)),
        ("macr;", (175, 0)),
        ("grav", (0, 0)),
        ("napos", (0, 0)),
        ("weierp;", (8472, 0)),
        ("leftharpoon", (0, 0)),
        ("RightTeeVec", (0, 0)),
        ("GreaterEqualLe", (0, 0)),
        ("SO", (0, 0)),
        ("bdquo;", (8222, 0)),
        ("NegativeMediumSpac", (0, 0)),
        ("vscr", (0, 0)),
        ("nop", (0, 0)),
        ("nsups", (0, 0)),
        ("boxhd;", (9516, 0)),
        ("nea", (0, 0)),
        ("Ne", (0, 0)),
        ("EmptyVerySm", (0, 0)),
        ("zh", (0, 0)),
        ("Smal", (0, 0)),
        ("oopf", (0, 0)),
        ("hA", (0, 0)),
        ("Au", (0, 0)),
        ("apE", (0, 0)),
        ("Eogo", (0, 0)),
        ("int;", (8747, 0)),
        ("Urin", (0, 0)),
        ("fras", (0, 0)),
        ("leftrightha", (0, 0)),
        ("rtrie;", (8885, 0)),
        ("nvsi", (0, 0)),
        ("eogon;", (281, 0)),
        ("OpenCurlyQuot", (0, 0)),
        ("lscr", (0, 0)),
        ("ufisht;", (10622, 0)),
        ("Gbr", (0, 0)),
        ("NotSquareSupersetEq", (0, 0)),
        ("capcup;", (10823, 0)),
        ("DoubleLeft", (0, 0)),
        ("oplus", (0, 0)),
        ("equivDD", (0, 0)),
        ("LeftRightVec", (0, 0)),
        ("dj", (0, 0)),
        ("DownArrowUp", (0, 0)),
        ("Epsilon;", (917, 0)),
        ("Ubrev", (0, 0)),
        ("RightDownVect", (0, 0)),
        ("Lang;", (10218, 0)),
        ("laq", (0, 0)),
        ("Poincarep", (0, 0)),
        ("vpr", (0, 0)),
        ("straighteps", (0, 0)),
        ("phi", (0, 0)),
        ("quaternions;", (8461, 0)),
        ("nsqsupe", (0, 0)),
        ("RB", (0, 0)),
        ("varnoth", (0, 0)),
        ("realine", (0, 0)),
        ("rB", (0, 0)),
        ("curvea", (0, 0)),
        ("UpperL", (0, 0)),
        ("Io", (0, 0)),
        ("RightUpTeeVector", (0, 0)),
        ("NotRightTriangle", (0, 0)),
        ("ltdo", (0, 0)),
        ("nVd", (0, 0)),
        ("bc", (0, 0)),
        ("nshortpa", (0, 0)),
        ("nu", (0, 0)),
        ("midas", (0, 0)),
        ("angmsd", (0, 0)),
        ("cup", (0, 0)),
        ("heartsu", (0, 0)),
        ("Tstro", (0, 0)),
        ("dfis", (0, 0)),
        ("lesseqgtr;", (8922, 0)),
        ("bnequiv;", (8801, 8421)),
        ("rcub", (0, 0)),
        ("orsl", (0, 0)),
        ("Lstr", (0, 0)),
        ("ForAl", (0, 0)),
        ("downdo", (0, 0)),
        ("npar;", (8742, 0)),
        ("rx;", (8478, 0)),
        ("curarr;", (8631, 0)),
        ("Emacr", (0, 0)),
        ("sigma", (0, 0)),
        ("topc", (0, 0)),
        ("Gcy;", (1043, 0)),
        ("nLef", (0, 0)),
        ("spad", (0, 0)),
        ("NotRightTriangleEqu", (0, 0)),
        ("gi", (0, 0)),
        ("vfr", (0, 0)),
        ("CounterClockwiseContou", (0, 0)),
        ("lsc", (0, 0)),
        ("downd", (0, 0)),
        ("ap;", (8776, 0)),
        ("ubrcy", (0, 0)),
        ("piv", (0, 0)),
        ("backpr", (0, 0)),
        ("DiacriticalTil", (0, 0)),
        ("Hori", (0, 0)),
        ("supdsub;", (10968, 0)),
        ("scns", (0, 0)),
        ("precnsi", (0, 0)),
        ("Horizonta", (0, 0)),
        ("VerticalSeparat", (0, 0)),
        ("angst", (0, 0)),
        ("VeryThinS", (0, 0)),
        ("uring", (0, 0)),
        ("Itild", (0, 0)),
        ("ShortDownArr", (0, 0)),
        ("nd", (0, 0)),
        ("Preced", (0, 0)),
        ("glE", (0, 0)),
        ("G", (0, 0)),
        ("plustwo;", (10791, 0)),
        ("notindo", (0, 0)),
        ("CounterC", (0, 0)),
        ("Iacute;", (205, 0)),
        ("looparrowrigh", (0, 0)),
        ("Hfr", (0, 0)),
        ("isinE;", (8953, 0)),
        ("gtques", (0, 0)),
        ("Oscr", (0, 0)),
        ("Ps", (0, 0)),
        ("DoubleLeftArro", (0, 0)),
        ("Ot", (0, 0)),
        ("Qs", (0, 0)),
        ("curarr", (0, 0)),
        ("RightUpT", (0, 0)),
        ("check;", (10003, 0)),
        ("trpezi", (0, 0)),
        ("bla", (0, 0)),
        ("intcal", (0, 0)),
        ("LeftUpVe", (0, 0)),
        ("Fscr", (0, 0)),
        ("uplu", (0, 0)),
        ("npoli", (0, 0)),
        ("lfisht;", (10620, 0)),
        ("precc", (0, 0)),
        ("lotime", (0, 0)),
        ("nleftar", (0, 0)),
        ("Tr", (0, 0)),
        ("quot;", (34, 0)),
        ("gimel;", (8503, 0)),
        ("twoheadlefta", (0, 0)),
        ("jcir", (0, 0)),
        ("Dstr", (0, 0)),
        ("Backslash;", (8726, 0)),
        ("hookleftarrow", (0, 0)),
        ("Oslash", (216, 0)),
        ("orslop", (0, 0)),
        ("Wfr;", (120090, 0)),
        ("NestedLe", (0, 0)),
        ("nvap;", (8781, 8402)),
        ("Vopf", (0, 0)),
        ("strns;", (175, 0)),
        ("nLl;", (8920, 824)),
        ("varkap", (0, 0)),
        ("backp", (0, 0)),
        ("ffilig", (0, 0)),
        ("image", (0, 0)),
        ("Zcaron;", (381, 0)),
        ("OE", (0, 0)),
        ("frown", (0, 0)),
        ("Implies;", (8658, 0)),
        ("dca", (0, 0)),
        ("ApplyFunction", (0, 0)),
        ("DoubleLongRig", (0, 0)),
        ("oas", (0, 0)),
        ("prs", (0, 0)),
        ("HAR", (0, 0)),
        ("SmallCirc", (0, 0)),
        ("succnappro", (0, 0)),
        ("Unde", (0, 0)),
        ("RightDownTeeV", (0, 0)),
        ("star;", (9734, 0)),
        ("notinvb;", (8951, 0)),
        ("nvlAr", (0, 0)),
        ("NotRightTriangl", (0, 0)),
        ("prure", (0, 0)),
        ("DownRightT", (0, 0)),
        ("P", (0, 0)),
        ("subpl", (0, 0)),
        ("Thic", (0, 0)),
        ("TRADE", (0, 0)),
        ("rH", (0, 0)),
        ("elinter", (0, 0)),
        ("M", (0, 0)),
        ("boxvL", (0, 0)),
        ("NotSubsetEq", (0, 0)),
        ("lagr", (0, 0)),
        ("vDa", (0, 0)),
        ("itilde;", (297, 0)),
        ("blacktrianglerig", (0, 0)),
        ("agrave;", (224, 0)),
        ("Lcy;", (1051, 0)),
        ("apacir;", (10863, 0)),
        ("sqsube;", (8849, 0)),
        ("roan", (0, 0)),
        ("ts", (0, 0)),
        ("lesge", (0, 0)),
        ("vartrianglerig", (0, 0)),
        ("cemptyv;", (10674, 0)),
        ("nscc", (0, 0)),
        ("verb", (0, 0)),
        ("ubrcy;", (1118, 0)),
        ("dotminu", (0, 0)),
        ("dstr", (0, 0)),
        ("auml;", (228, 0)),
        ("rtrif", (0, 0)),
        ("NotGreaterG", (0, 0)),
        ("nvltrie", (0, 0)),
        ("Leftrightarrow;", (8660, 0)),
        ("CHcy;", (1063, 0)),
        ("curvearrowr", (0, 0)),
        ("NotLessGreater;", (8824, 0)),
        ("suppl", (0, 0)),
        ("FilledSmallSquar", (0, 0)),
        ("Horiz", (0, 0)),
        ("CloseC", (0, 0)),
        ("precappr", (0, 0)),
        ("Atil", (0, 0)),
        ("opl", (0, 0)),
        ("nLeft", (0, 0)),
        ("Ba", (0, 0)),
        ("LeftVector", (0, 0)),
        ("UpArrowB", (0, 0)),
        ("InvisibleC", (0, 0)),
        ("kgree", (0, 0)),
        ("Dfr;", (120071, 0)),
        ("bigcirc;", (9711, 0)),
        ("NotCong", (0, 0)),
        ("gesdotol", (0, 0)),
        ("DownLeftRight", (0, 0)),
        ("hcir", (0, 0)),
        ("sqsubseteq;", (8849, 0)),
        ("succnappr", (0, 0)),
        ("rang;", (10217, 0)),
        ("leg", (0, 0)),
        ("Utilde", (0, 0)),
        ("eqvparsl;", (10725, 0)),
        ("LeftAngleBracket;", (10216, 0)),
        ("pcy;", (1087, 0)),
        ("RightDownVector;", (8642, 0)),
        ("vartrianglel", (0, 0)),
        ("HorizontalLine;", (9472, 0)),
        ("downar", (0, 0)),
        ("backepsilon;", (1014, 0)),
        ("t", (0, 0)),
        ("spadesuit", (0, 0)),
        ("nsubE", (0, 0)),
        ("rightt", (0, 0)),
        ("LessTild", (0, 0)),
        ("rightharpoondow", (0, 0)),
        ("Umac", (0, 0)),
        ("ange;", (10660, 0)),
        ("circlearrowri", (0, 0)),
        ("lrhar;", (8651, 0)),
        ("NotEqualT", (0, 0)),
        ("squ;", (9633, 0)),
        ("tstrok", (0, 0)),
        ("lmoustac", (0, 0)),
        ("dcaron", (0, 0)),
        ("succappro", (0, 0)),
        ("gcirc;", (285, 0)),
        ("RBarr;", (10512, 0)),
        ("multimap;", (8888, 0)),
        ("cempt", (0, 0)),
        ("Right", (0, 0)),
        ("Atilde", (195, 0)),
        ("nvltr", (0, 0)),
        ("NotLeftTrian", (0, 0)),
        ("nV", (0, 0)),
        ("Rarrtl;", (10518, 0)),
        ("drco", (0, 0)),
        ("GT;", (62, 0)),
        ("Al", (0, 0)),
        ("shortparal", (0, 0)),
        ("ljc", (0, 0)),
        ("sse", (0, 0)),
        ("MinusP", (0, 0)),
        ("Cayle", (0, 0)),
        ("bigtriangledo", (0, 0)),
        ("erDo", (0, 0)),
        ("mcomm", (0, 0)),
        ("intpr", (0, 0)),
        ("Scirc", (0, 0)),
        ("Kf", (0, 0)),
        ("Coni", (0, 0)),
        ("llha", (0, 0)),
        ("xuplus", (0, 0)),
        ("leftrightharpoons;", (8651, 0)),
        ("dzigra", (0, 0)),
        ("Lcedil;", (315, 0)),
        ("diam;", (8900, 0)),
        ("CircleTim", (0, 0)),
        ("nwArr", (0, 0)),
        ("pa", (0, 0)),
        ("ordf;", (170, 0)),
        ("tra", (0, 0)),
        ("kce", (0, 0)),
        ("ocirc;", (244, 0)),
        ("Itil", (0, 0)),
        ("Nfr;", (120081, 0)),
        ("RightUpDownVector;", (10575, 0)),
        ("cupcap;", (10822, 0)),
        ("intpro", (0, 0)),
        ("SubsetEqual;", (8838, 0)),
        ("nleftri", (0, 0)),
        ("easter;", (10862, 0)),
        ("erDot", (0, 0)),
        ("NotLeftTriangleEqua", (0, 0)),
        ("betwe", (0, 0)),
        ("cirmid;", (10991, 0)),
        ("gtrar", (0, 0)),
        ("SquareI", (0, 0)),
        ("boxmi", (0, 0)),
        ("realpart;", (8476, 0)),
        ("varsups", (0, 0)),
        ("hArr", (0, 0)),
        ("NotNestedGreaterG", (0, 0)),
        ("par;", (8741, 0)),
        ("DoubleUpDo", (0, 0)),
        ("zhc", (0, 0)),
        ("DoubleContourInte", (0, 0)),
        ("cop", (0, 0)),
        ("xscr", (0, 0)),
        ("nvgt;", (62, 8402)),
        ("rightarr", (0, 0)),
        ("xcap", (0, 0)),
        ("blacktriangleleft;", (9666, 0)),
        ("Ascr;", (119964, 0)),
        ("pscr;", (120005, 0)),
        ("straightepsilo", (0, 0)),
        ("Upd", (0, 0)),
        ("rnm", (0, 0)),
        ("nright", (0, 0)),
        ("NotNestedLessLe", (0, 0)),
        ("cupc", (0, 0)),
        ("rightthreet", (0, 0)),
        ("ordf", (170, 0)),
        ("notind", (0, 0)),
        ("lBa", (0, 0)),
        ("varsubsetne", (0, 0)),
        ("Pr;", (10939, 0)),
        ("Scaro", (0, 0)),
        ("odot;", (8857, 0)),
        ("OverPa", (0, 0)),
        ("TSHcy", (0, 0)),
        ("drcrop", (0, 0)),
        ("Clos", (0, 0)),
        ("UnderPar", (0, 0)),
        ("dagge", (0, 0)),
        ("nvsim", (0, 0)),
        ("lowast;", (8727, 0)),
        ("succa", (0, 0)),
        ("ncup;", (10818, 0)),
        ("v", (0, 0)),
        ("backsime", (0, 0)),
        ("frac25", (0, 0)),
        ("backcong;", (8780, 0)),
        ("DownRightTeeVe", (0, 0)),
        ("Sc", (0, 0)),
        ("odblac;", (337, 0)),
        ("oslash", (248, 0)),
        ("HumpDownHump;", (8782, 0)),
        ("dHar;", (10597, 0)),
        ("Circle", (0, 0)),
        ("xcup", (0, 0)),
        ("centerdo", (0, 0)),
        ("yc", (0, 0)),
        ("searhk", (0, 0)),
        ("aw", (0, 0)),
        ("Ograve;", (210, 0)),
        ("HumpDo", (0, 0)),
        ("nLe", (0, 0)),
        ("DoubleUpArrow", (0, 0)),
        ("DownRightVec", (0, 0)),
        ("Nfr", (0, 0)),
        ("lambd", (0, 0)),
        ("Exis", (0, 0)),
        ("CupCap;", (8781, 0)),
        ("angl", (0, 0)),
        ("trade", (0, 0)),
        ("straightp", (0, 0)),
        ("swarhk", (0, 0)),
        ("llhard;", (10603, 0)),
        ("fallingdotseq", (0, 0)),
        ("roti", (0, 0)),
        ("excl", (0, 0)),
        ("NegativeMediumS", (0, 0)),
        ("Righ", (0, 0)),
        ("Uparro", (0, 0)),
        ("Barwed;", (8966, 0)),
        ("LeftDownTeeV", (0, 0)),
        ("demptyv;", (10673, 0)),
        ("udblac;", (369, 0)),
        ("sub;", (8834, 0)),
        ("urtr", (0, 0)),
        ("vartriang", (0, 0)),
        ("race;", (8765, 817)),
        ("LeftRight", (0, 0)),
        ("doubleb", (0, 0)),
        ("Delt", (0, 0)),
        ("DoubleRightA", (0, 0)),
        ("RightDownVectorBar;", (10581, 0)),
        ("DD", (0, 0)),
        ("boxUL;", (9565, 0)),
        ("uacut", (0, 0)),
        ("NonBreaking", (0, 0)),
        ("aacute", (225, 0)),
        ("rarrfs;", (10526, 0)),
        ("Prime;", (8243, 0)),
        ("blk34", (0, 0)),
        ("capcap;", (10827, 0)),
        ("sext", (0, 0)),
        ("cups;", (8746, 65024)),
        ("LeftArrowBa", (0, 0)),
        ("upharpoon", (0, 0)),
        ("gl", (0, 0)),
        ("ngs", (0, 0)),
        ("lsh", (0, 0)),
        ("NegativeThi", (0, 0)),
        ("circledast;", (8859, 0)),
        ("GreaterEqual", (0, 0)),
        ("paral", (0, 0)),
        ("Bernoul", (0, 0)),
        ("Vert;", (8214, 0)),
        ("swarh", (0, 0)),
        ("spade", (0, 0)),
        ("Aacute;", (193, 0)),
        ("boxhU", (0, 0)),
        ("LowerLeftAr", (0, 0)),
        ("NotGreaterEq", (0, 0)),
        ("csup;", (10960, 0)),
        ("vee;", (8744, 0)),
        ("DownTeeAr", (0, 0)),
        ("Pr", (0, 0)),
        ("gvertneqq;", (8809, 65024)),
        ("NotPrecedesS", (0, 0)),
        ("boxV", (0, 0)),
        ("ngsim", (0, 0)),
        ("NotGreaterF", (0, 0)),
        ("vArr", (0, 0)),
        ("origof;", (8886, 0)),
        ("RightUpDown", (0, 0)),
        ("DoubleCont", (0, 0)),
        ("pitchfork;", (8916, 0)),
        ("Iacu", (0, 0)),
        ("rl", (0, 0)),
        ("precneqq", (0, 0)),
        ("larrlp;", (8619, 0)),
        ("geqq", (0, 0)),
        ("nb", (0, 0)),
        ("simne;", (8774, 0)),
        ("ldrdhar;", (10599, 0)),
        ("nshortp", (0, 0)),
        ("DownLeftVector", (0, 0)),
        ("wp;", (8472, 0)),
        ("LeftDownVectorBa", (0, 0)),
        ("Cacute", (0, 0)),
        ("swarhk;", (10534, 0)),
        ("Rrightarr", (0, 0)),
        ("NegativeMediumSp", (0, 0)),
        ("Qscr", (0, 0)),
        ("GJcy", (0, 0)),
        ("imagpart;", (8465, 0)),
        ("rmous", (0, 0)),
        ("there4;", (8756, 0)),
        ("larrb", (0, 0)),
        ("angzar", (0, 0)),
        ("me", (0, 0)),
        ("Nacute", (0, 0)),
        ("rbrkslu;", (10640, 0)),
        ("RightUpTeeVec", (0, 0)),
        ("EmptySmal", (0, 0)),
        ("NotGreat", (0, 0)),
        ("VerticalTild", (0, 0)),
        ("pitchf", (0, 0)),
        ("nexists", (0, 0)),
        ("notinva;", (8713, 0)),
        ("Conto", (0, 0)),
        ("clubs", (0, 0)),
        ("bigtriangleu", (0, 0)),
        ("harrc", (0, 0)),
        ("nwarr", (0, 0)),
        ("wopf;", (120168, 0)),
        ("downarrow;", (8595, 0)),
        ("Partia", (0, 0)),
        ("nvrArr", (0, 0)),
        ("supse", (0, 0)),
        ("And;", (10835, 0)),
        ("thks", (0, 0)),
        ("triangleq", (0, 0)),
        ("Pc", (0, 0)),
        ("GreaterTild", (0, 0)),
        ("CounterClockwiseConto", (0, 0)),
        ("szl", (0, 0)),
        ("preccurlyeq", (0, 0)),
        ("NotPrecedesSla", (0, 0)),
        ("ac;", (8766, 0)),
        ("lfloor", (0, 0)),
        ("eplus", (0, 0)),
        ("rbrace;", (125, 0)),
        ("rarrap;", (10613, 0)),
        ("awin", (0, 0)),
        ("eqc", (0, 0)),
        ("fscr;", (119995, 0)),
        ("hairsp;", (8202, 0)),
        ("searro", (0, 0)),
        ("DoubleContourIntegra", (0, 0)),
        ("VerticalSeparator", (0, 0)),
        ("Cedil", (0, 0)),
        ("smepars", (0, 0)),
        ("LowerRig", (0, 0)),
        ("NotPr", (0, 0)),
        ("rha", (0, 0)),
        ("boxhD;", (9573, 0)),
        ("ems", (0, 0)),
        ("efDot", (0, 0)),
        ("shortmi", (0, 0)),
        ("lmoustache", (0, 0)),
        ("backs", (0, 0)),
        ("rdldha", (0, 0)),
        ("bsolhsub", (0, 0)),
        ("mapstoup;", (8613, 0)),
        ("fla", (0, 0)),
        ("circledcir", (0, 0)),
        ("uscr", (0, 0)),
        ("bigopl", (0, 0)),
        ("niv;", (8715, 0)),
        ("RoundI", (0, 0)),
        ("boxplus", (0, 0)),
        ("dha", (0, 0)),
        ("smtes;", (10924, 65024)),
        ("vB", (0, 0)),
        ("imagli", (0, 0)),
        ("bfr;", (120095, 0)),
        ("HilbertS", (0, 0)),
        ("GreaterFullEqu", (0, 0)),
        ("uri", (0, 0)),
        ("circlearrow", (0, 0)),
        ("Me", (0, 0)),
        ("RightUpVector", (0, 0)),
        ("njcy", (0, 0)),
        ("Lcaron", (0, 0)),
        ("ncaron", (0, 0)),
        ("bigtri", (0, 0)),
        ("nlsi", (0, 0)),
        ("reg", (174, 0)),
        ("lescc;", (10920, 0)),
        ("gbrev", (0, 0)),
        ("prnE;", (10933, 0)),
        ("leq;", (8804, 0)),
        ("Reve", (0, 0)),
        ("cwconin", (0, 0)),
        ("RightV", (0, 0)),
        ("becaus;", (8757, 0)),
        ("Racu", (0, 0)),
        ("Ncedil;", (325, 0)),
        ("SquareInte", (0, 0)),
        ("hookrightarrow", (0, 0)),
        ("qprim", (0, 0)),
        ("Kced", (0, 0)),
        ("nrtrie", (0, 0)),
        ("lowast", (0, 0)),
        ("ltquest;", (10875, 0)),
        ("SucceedsTil", (0, 0)),
        ("Amacr", (0, 0)),
        ("Hilb", (0, 0)),
        ("NotHumpDownH", (0, 0)),
        ("sqsupse", (0, 0)),
        ("bep", (0, 0)),
        ("boxu", (0, 0)),
        ("Ma", (0, 0)),
        ("bem", (0, 0)),
        ("ApplyFunctio", (0, 0)),
        ("dagger;", (8224, 0)),
        ("Colone", (0, 0)),
        ("lesd", (0, 0)),
        ("SubsetEq", (0, 0)),
        ("khcy;", (1093, 0)),
        ("nvrtr", (0, 0)),
        ("Oopf;", (120134, 0)),
        ("otimes;", (8855, 0)),
        ("trianglelefteq;", (8884, 0)),
        ("nVdash", (0, 0)),
        ("profala", (0, 0)),
        ("risingdotseq;", (8787, 0)),
        ("NotReverseElement", (0, 0)),
        ("nRighta", (0, 0)),
        ("langle;", (10216, 0)),
        ("Kc", (0, 0)),
        ("numero;", (8470, 0)),
        ("UpEquilibrium;", (10606, 0)),
        ("DiacriticalDoubleA", (0, 0)),
        ("Xs", (0, 0)),
        ("lrc", (0, 0)),
        ("Abre", (0, 0)),
        ("iota;", (953, 0)),
        ("Rcy", (0, 0)),
        ("simlE;", (10911, 0)),
        ("Os", (0, 0)),
        ("ugr", (0, 0)),
        ("PrecedesEqua", (0, 0)),
        ("eo", (0, 0)),
        ("kjc", (0, 0)),
        ("SquareUnio", (0, 0)),
        ("lacute", (0, 0)),
        ("GreaterEqualLess", (0, 0)),
        ("bec", (0, 0)),
        ("backsim;", (8765, 0)),
        ("DoubleLongLeftA", (0, 0)),
        ("longmap", (0, 0)),
        ("tho", (0, 0)),
        ("ApplyFu", (0, 0)),
        ("equ", (0, 0)),
        ("Kopf", (0, 0)),
        ("Agr", (0, 0)),
        ("LongLeftArro", (0, 0)),
        ("Scaron;", (352, 0)),
        ("SquareSubsetEqual", (0, 0)),
        ("frac18", (0, 0)),
        ("succns", (0, 0)),
        ("bigcap;", (8898, 0)),
        ("plusdu;", (10789, 0)),
        ("nsm", (0, 0)),
        ("real;", (8476, 0)),
        ("NJcy", (0, 0)),
        ("blacklozen", (0, 0)),
        ("ncaro", (0, 0)),
        ("bkar", (0, 0)),
        ("gtrap", (0, 0)),
        ("rdq", (0, 0)),
        ("SquareInt", (0, 0)),
        ("rpar", (0, 0)),
        ("VD", (0, 0)),
        ("CapitalDiffere", (0, 0)),
        ("Gam", (0, 0)),
        ("geqslant", (0, 0)),
        ("leq", (0, 0)),
        ("dia", (0, 0)),
        ("straightepsilon", (0, 0)),
        ("ncap", (0, 0)),
        ("vDash", (0, 0)),
        ("ugrave", (249, 0)),
        ("rightleftharpoons;", (8652, 0)),
        ("approxeq", (0, 0)),
        ("RightTriang", (0, 0)),
        ("odiv", (0, 0)),
        ("egsdo", (0, 0)),
        ("Exist", (0, 0)),
        ("RightTeeArr", (0, 0)),
        ("Equilibriu", (0, 0)),
        ("Qsc", (0, 0)),
        ("rightleftharp", (0, 0)),
        ("srar", (0, 0)),
        ("gl;", (8823, 0)),
        ("rmoust;", (9137, 0)),
        ("Bern", (0, 0)),
        ("hksearow", (0, 0)),
        ("oacute", (243, 0)),
        ("Xscr", (0, 0)),
        ("gfr;", (120100, 0)),
        ("bsime", (0, 0)),
        ("ordero", (0, 0)),
        ("uAr", (0, 0)),
        ("UnderBrace", (0, 0)),
        ("NotLes", (0, 0)),
        ("lscr;", (120001, 0)),
        ("ntrianglerigh", (0, 0)),
        ("nleftright", (0, 0)),
        ("rh", (0, 0)),
        ("pra", (0, 0)),
        ("SucceedsE", (0, 0)),
        ("RightTriangleB", (0, 0)),
        ("per", (0, 0)),
        ("eqvpar", (0, 0)),
        ("mlcp", (0, 0)),
        ("NegativeVeryThi", (0, 0)),
        ("TildeEqual", (0, 0)),
        ("api", (0, 0)),
        ("therefore;", (8756, 0)),
        ("gdot;", (289, 0)),
        ("ntrianglelefte", (0, 0)),
        ("scpo", (0, 0)),
        ("Ifr;", (8465, 0)),
        ("Tst", (0, 0)),
        ("Invisib", (0, 0)),
        ("csu", (0, 0)),
        ("RightD", (0, 0)),
        ("Differe", (0, 0)),
        ("nra", (0, 0)),
        ("lesges;", (10899, 0)),
        ("Psc", (0, 0)),
        ("io", (0, 0)),
        ("iprod;", (10812, 0)),
        ("Int", (0, 0)),
        ("DownLeftTeeVector;", (10590, 0)),
        ("LeftDownVec", (0, 0)),
        ("ml", (0, 0)),
        ("DownLeftT", (0, 0)),
        ("ReverseElement;", (8715, 0)),
        ("lsim", (0, 0)),
        ("nlar", (0, 0)),
        ("CapitalDifferenti", (0, 0)),
        ("Ua", (0, 0)),
        ("uml", (168, 0)),
        ("nmi", (0, 0)),
        ("sce", (0, 0)),
        ("Hacek;", (711, 0)),
        ("Coun", (0, 0)),
        ("poun", (0, 0)),
        ("rhov;", (1009, 0)),
        ("NotDoubleVe", (0, 0)),
        ("UpTeeAr", (0, 0)),
        ("larrf", (0, 0)),
        ("Bump", (0, 0)),
        ("nLeftarro", (0, 0)),
        ("ddo", (0, 0)),
        ("lflo", (0, 0)),
        ("Wc", (0, 0)),
        ("ovbar;", (9021, 0)),
        ("starf", (0, 0)),
        ("Wci", (0, 0)),
        ("Longrightarr", (0, 0)),
        ("looparr", (0, 0)),
        ("Appl", (0, 0)),
        ("ascr;", (119990, 0)),
        ("NotNestedGreaterGreat", (0, 0)),
        ("NotVertica", (0, 0)),
        ("subset", (0, 0)),
        ("npol", (0, 0)),
        ("Ou", (0, 0)),
        ("nsupseteq", (0, 0)),
        ("Iscr;", (8464, 0)),
        ("nscr", (0, 0)),
        ("Q", (0, 0)),
        ("lnap", (0, 0)),
        ("sla", (0, 0)),
        ("DiacriticalDoubleAcute", (0, 0)),
        ("RightUpVectorB", (0, 0)),
        ("NotPrecedesSlant", (0, 0)),
        ("DoubleLongRightArr", (0, 0)),
        ("LeftDownV", (0, 0)),
        ("Ecirc;", (202, 0)),
        ("lbrksld", (0, 0)),
        ("NotGreaterLe", (0, 0)),
        ("LongLeftRi", (0, 0)),
        ("cyl", (0, 0)),
        ("Longrightarro", (0, 0)),
        ("DDotrahd", (0, 0)),
        ("tscy", (0, 0)),
        ("If", (0, 0)),
        ("plustwo", (0, 0)),
        ("rcu", (0, 0)),
        ("ch", (0, 0)),
        ("Diacritica", (0, 0)),
        ("Hace", (0, 0)),
        ("InvisibleT", (0, 0)),
        ("Dfr", (0, 0)),
        ("Yuml;", (376, 0)),
        ("bcy", (0, 0)),
        ("udhar", (0, 0)),
        ("Propo", (0, 0)),
        ("dashv;", (8867, 0)),
        ("nprcue;", (8928, 0)),
        ("Asc", (0, 0)),
        ("commat", (0, 0)),
        ("Grea", (0, 0)),
        ("xvee;", (8897, 0)),
        ("rbrksl", (0, 0)),
        ("Tsc", (0, 0)),
        ("ccupss", (0, 0)),
        ("nesear", (0, 0)),
        ("rightlefthar", (0, 0)),
        ("DoubleLongRightAr", (0, 0)),
        ("min", (0, 0)),
        ("meas", (0, 0)),
        ("npolin", (0, 0)),
        ("swn", (0, 0)),
        ("bd", (0, 0)),
        ("nsuc", (0, 0)),
        ("fra", (0, 0)),
        ("dzigr", (0, 0)),
        ("usc", (0, 0)),
        ("risingdotse", (0, 0)),
        ("ocirc", (244, 0)),
        ("csup", (0, 0)),
        ("tscr;", (120009, 0)),
        ("hor", (0, 0)),
        ("cuesc", (0, 0)),
        ("cudarrr", (0, 0)),
        ("boxDL", (0, 0)),
        ("ncongdo", (0, 0)),
        ("Mscr", (0, 0)),
        ("DownLeftVec", (0, 0)),
        ("Cay", (0, 0)),
        ("cupb", (0, 0)),
        ("checkma", (0, 0)),
        ("gjc", (0, 0)),
        ("lm", (0, 0)),
        ("Gced", (0, 0)),
        ("vee", (0, 0)),
        ("xi", (0, 0)),
        ("preccu", (0, 0)),
        ("EmptyVerySmallS", (0, 0)),
        ("Rightarrow", (0, 0)),
        ("longleftr", (0, 0)),
        ("ipro", (0, 0)),
        ("frac23;", (8532, 0)),
        ("lA", (0, 0)),
        ("Cconin", (0, 0)),
        ("Vba", (0, 0)),
        ("ddar", (0, 0)),
        ("mstpos;", (8766, 0)),
        ("angsph;", (8738, 0)),
        ("HARD", (0, 0)),
        ("sup;", (8835, 0)),
        ("HumpDown", (0, 0)),
        ("Edot;", (278, 0)),
        ("NotSucceedsTilde;", (8831, 824)),
        ("Uog", (0, 0)),
        ("NestedLessLe", (0, 0)),
        ("Ocir", (0, 0)),
        ("heart", (0, 0)),
        ("Agrave;", (192, 0)),
        ("SquareIntersection;", (8851, 0)),
        ("uparro", (0, 0)),
        ("strn", (0, 0)),
        ("rsh", (0, 0)),
        ("lHa", (0, 0)),
        ("hci", (0, 0)),
        ("bigotime", (0, 0)),
        ("LessLe", (0, 0)),
        ("Capit", (0, 0)),
        ("Equ", (0, 0)),
        ("nrt", (0, 0)),
        ("Lam", (0, 0)),
        ("centerdot", (0, 0)),
        ("realpa", (0, 0)),
        ("gammad", (0, 0)),
        ("NotLessTi", (0, 0)),
        ("OverBrace", (0, 0)),
        ("vda", (0, 0)),
        ("twoheadrightarr", (0, 0)),
        ("ClockwiseContourInteg", (0, 0)),
        ("dot", (0, 0)),
        ("ii", (0, 0)),
        ("curvearrowrigh", (0, 0)),
        ("PrecedesSl", (0, 0)),
        ("zi", (0, 0)),
        ("tsh", (0, 0)),
        ("vnsub", (0, 0)),
        ("rb", (0, 0)),
        ("lcub;", (123, 0)),
        ("betwee", (0, 0)),
        ("Super", (0, 0)),
        ("NotGreaterTilde;", (8821, 0)),
        ("dua", (0, 0)),
        ("Great", (0, 0)),
        ("NotS", (0, 0)),
        ("nle", (0, 0)),
        ("lharu", (0, 0)),
        ("rin", (0, 0)),
        ("Sacute;", (346, 0)),
        ("rbrack;", (93, 0)),
        ("DoubleDot;", (168, 0)),
        ("Jcy;", (1049, 0)),
        ("xsqcup;", (10758, 0)),
        ("UpArrowDownArrow", (0, 0)),
        ("Laplacet", (0, 0)),
        ("Map", (0, 0)),
        ("HARDc", (0, 0)),
        ("bfr", (0, 0)),
        ("preceq", (0, 0)),
        ("rdsh", (0, 0)),
        ("Mellintrf;", (8499, 0)),
        ("LeftRightVector", (0, 0)),
        ("Poinca", (0, 0)),
        ("imp", (0, 0)),
        ("varsubsetneqq;", (10955, 65024)),
        ("nsupset;", (8835, 8402)),
        ("exp", (0, 0)),
        ("rmoust", (0, 0)),
        ("NegativeVeryThinS", (0, 0)),
        ("NotHumpEqual", (0, 0)),
        ("rdquo;", (8221, 0)),
        ("TildeEqu", (0, 0)),
        ("sccue;", (8829, 0)),
        ("ThinSpa", (0, 0)),
        ("hairs", (0, 0)),
        ("nes", (0, 0)),
        ("Hat", (0, 0)),
        ("Rcaron", (0, 0)),
        ("TildeE", (0, 0)),
        ("hslas", (0, 0)),
        ("bcy;", (1073, 0)),
        ("amalg", (0, 0)),
        ("SquareSubse", (0, 0)),
        ("NotGreater;", (8815, 0)),
        ("bullet", (0, 0)),
        ("otimesas;", (10806, 0)),
        ("ed", (0, 0)),
        ("NegativeVeryThinSpac", (0, 0)),
        ("jukc", (0, 0)),
        ("planck;", (8463, 0)),
        ("VerticalLine", (0, 0)),
        ("nvD", (0, 0)),
        ("oel", (0, 0)),
        ("nparall", (0, 0)),
        ("qpr", (0, 0)),
        ("capa", (0, 0)),
        ("gtrd", (0, 0)),
        ("nsupseteq;", (8841, 0)),
        ("Conint;", (8751, 0)),
        ("NonBr", (0, 0)),
        ("twoheadrightar", (0, 0)),
        ("Iuml;", (207, 0)),
        ("rlarr;", (8644, 0)),
        ("epar;", (8917, 0)),
        ("LeftUpVectorBar;", (10584, 0)),
        ("To", (0, 0)),
        ("LessEqualGrea", (0, 0)),
        ("jc", (0, 0)),
        ("yacute", (253, 0)),
        ("leftrightsquigar", (0, 0)),
        ("targe", (0, 0)),
        ("uArr", (0, 0)),
        ("EmptyVerySmallSquare;", (9643, 0)),
        ("Rang", (0, 0)),
        ("uwangle", (0, 0)),
        ("iopf", (0, 0)),
        ("gtrappr", (0, 0)),
        ("smte", (0, 0)),
        ("hslash;", (8463, 0)),
        ("Impli", (0, 0)),
        ("nrig", (0, 0)),
        ("imag", (0, 0)),
        ("od", (0, 0)),
        ("Thi", (0, 0)),
        ("O", (0, 0)),
        ("RightUpDownVector", (0, 0)),
        ("vltri;", (8882, 0)),
        ("ShortRight", (0, 0)),
        ("ntr", (0, 0)),
        ("ddotseq;", (10871, 0)),
        ("LessLess", (0, 0)),
        ("toea;", (10536, 0)),
        ("macr", (175, 0)),
        ("LeftUpVectorBa", (0, 0)),
        ("tsc", (0, 0)),
        ("ldru", (0, 0)),
        ("simpl", (0, 0)),
        ("bigtriangled", (0, 0)),
        ("DownRightVect", (0, 0)),
        ("zfr", (0, 0)),
        ("CloseCurlyQu", (0, 0)),
        ("bdqu", (0, 0)),
        ("lessap", (0, 0)),
        ("NegativeThinSpa", (0, 0)),
        ("verba", (0, 0)),
        ("gsi", (0, 0)),
        ("ClockwiseContou", (0, 0)),
        ("phone", (0, 0)),
        ("Zopf;", (8484, 0)),
        ("lbrke", (0, 0)),
        ("wopf", (0, 0)),
        ("clubs;", (9827, 0)),
        ("parallel;", (8741, 0)),
        ("mapstole", (0, 0)),
        ("sfrown;", (8994, 0)),
        ("rAar", (0, 0)),
        ("lrhard;", (10605, 0)),
        ("Nopf;", (8469, 0)),
        ("rppolint", (0, 0)),
        ("notniv", (0, 0)),
        ("gtr", (0, 0)),
        ("NotGre", (0, 0)),
        ("ENG;", (330, 0)),
        ("inter", (0, 0)),
        ("Jcirc", (0, 0)),
        ("Intersecti", (0, 0)),
        ("bscr", (0, 0)),
        ("order;", (8500, 0)),
        ("fll", (0, 0)),
        ("xc", (0, 0)),
        ("supdo", (0, 0)),
        ("DDotra", (0, 0)),
        ("cylcty;", (9005, 0)),
        ("DownRightTee", (0, 0)),
        ("supseteq;", (8839, 0)),
        ("gim", (0, 0)),
        ("ctdot;", (8943, 0)),
        ("mu;", (956, 0)),
        ("part;", (8706, 0)),
        ("Bc", (0, 0)),
        ("npreceq;", (10927, 824)),
        ("cupor;", (10821, 0)),
        ("looparrowl", (0, 0)),
        ("boxvR;", (9566, 0)),
        ("doubl", (0, 0)),
        ("ldquo;", (8220, 0)),
        ("Ecy", (0, 0)),
        ("minusb;", (8863, 0)),
        ("RightUpDow", (0, 0)),
        ("oin", (0, 0)),
        ("lneq;", (10887, 0)),
        ("DoubleLongLeftRi", (0, 0)),
        ("awc", (0, 0)),
        ("latail;", (10521, 0)),
        ("blacktriangle", (0, 0)),
        ("sfro", (0, 0)),
        ("questeq;", (8799, 0)),
        ("bigtria", (0, 0)),
        ("ZeroWidthSpace", (0, 0)),
        ("NotCupCap;", (8813, 0)),
        ("urc", (0, 0)),
        ("Hsc", (0, 0)),
        ("llcorne", (0, 0)),
        ("rscr", (0, 0)),
        ("DoubleLeftT", (0, 0)),
        ("LongRightArro", (0, 0)),
        ("PlusMinus;", (177, 0)),
        ("Lacu", (0, 0)),
        ("SquareUnion", (0, 0)),
        ("bne", (0, 0)),
        ("Ccirc", (0, 0)),
        ("FilledVerySmallSquare;", (9642, 0)),
        ("rightrightarro", (0, 0)),
        ("HARDcy;", (1066, 0)),
        ("Barwe", (0, 0)),
        ("Very", (0, 0)),
        ("CircleDot;", (8857, 0)),
        ("jo", (0, 0)),
        ("lata", (0, 0)),
        ("nge;", (8817, 0)),
        ("imat", (0, 0)),
        ("boxUR;", (9562, 0)),
        ("DoubleContourIntegral", (0, 0)),
        ("notin", (0, 0)),
        ("curlyeqp", (0, 0)),
        ("equi", (0, 0)),
        ("npre;", (10927, 824)),
        ("diamo", (0, 0)),
        ("LeftDoub", (0, 0)),
        ("Yu", (0, 0)),
        ("leftarrowtail;", (8610, 0)),
        ("CounterClockwiseContourInte", (0, 0)),
        ("Ri", (0, 0)),
        ("ThickSpace;", (8287, 8202)),
        ("hscr;", (119997, 0)),
        ("fcy;", (1092, 0)),
        ("rang", (0, 0)),
        ("longrigh", (0, 0)),
        ("Ncaro", (0, 0)),
        ("ses", (0, 0)),
        ("LeftArrowRightArr", (0, 0)),
        ("DoubleDown", (0, 0)),
        ("lessgt", (0, 0)),
        ("ReverseEquilibri", (0, 0)),
        ("RightCei", (0, 0)),
        ("fall", (0, 0)),
        ("de", (0, 0)),
        ("precnappr", (0, 0)),
        ("NonBreaki", (0, 0)),
        ("xoplus;", (10753, 0)),
        ("jserc", (0, 0)),
        ("updownar", (0, 0)),
        ("N", (0, 0)),
        ("frac38;", (8540, 0)),
        ("Nes", (0, 0)),
        ("subseteqq", (0, 0)),
        ("NotLessTilde", (0, 0)),
        ("NotHumpE", (0, 0)),
        ("OverBa", (0, 0)),
        ("Prop", (0, 0)),
        ("bbrk", (0, 0)),
        ("bno", (0, 0)),
        ("nLt;", (8810, 8402)),
        ("ltqu", (0, 0)),
        ("looparrowrig", (0, 0)),
        ("lsqb;", (91, 0)),
        ("efDot;", (8786, 0)),
        ("gamma", (0, 0)),
        ("Rrightarro", (0, 0)),
        ("longleft", (0, 0)),
        ("TildeEq", (0, 0)),
        ("mho;", (8487, 0)),
        ("longrig", (0, 0)),
        ("Kce", (0, 0)),
        ("scnap", (0, 0)),
        ("Scedil;", (350, 0)),
        ("nVdas", (0, 0)),
        ("Om", (0, 0)),
        ("xwedg", (0, 0)),
        ("vzigzag;", (10650, 0)),
        ("Exists", (0, 0)),
        ("rightleftharpo", (0, 0)),
        ("gsiml;", (10896, 0)),
        ("rsquor;", (8217, 0)),
        ("LongLef", (0, 0)),
        ("Capi", (0, 0)),
        ("isinE", (0, 0)),
        ("triangle;", (9653, 0)),
        ("SucceedsSlantEqual", (0, 0)),
        ("twoheadright", (0, 0)),
        ("jcy", (0, 0)),
        ("OverPar", (0, 0)),
        ("Of", (0, 0)),
        ("frac13;", (8531, 0)),
        ("toe", (0, 0)),
        ("lamb", (0, 0)),
        ("nLeftri", (0, 0)),
        ("stra", (0, 0)),
        ("RightTe", (0, 0)),
        ("rp", (0, 0)),
        ("DownB", (0, 0)),
        ("els;", (10901, 0)),
        ("larrbfs", (0, 0)),
        ("CH", (0, 0)),
        ("bco", (0, 0)),
        ("naturals", (0, 0)),
        ("odsol", (0, 0)),
        ("nsu", (0, 0)),
        ("DiacriticalAcu", (0, 0)),
        ("phi;", (966, 0)),
        ("RuleDelaye", (0, 0)),
        ("frac56", (0, 0)),
        ("rtrilt", (0, 0)),
        ("nsupE;", (10950, 824)),
        ("doubleba", (0, 0)),
        ("yac", (0, 0)),
        ("curlywed", (0, 0)),
        ("Hstrok;", (294, 0)),
        ("cuda", (0, 0)),
        ("capc", (0, 0)),
        ("Kscr;", (119974, 0)),
        ("seArr", (0, 0)),
        ("LongRigh", (0, 0)),
        ("larrl", (0, 0)),
        ("ThinSp", (0, 0)),
        ("scpol", (0, 0)),
        ("raq", (0, 0)),
        ("Tscr;", (119983, 0)),
        ("rlm;", (8207, 0)),
        ("xsqcup", (0, 0)),
        ("varepsilo", (0, 0)),
        ("RightUpDownVec", (0, 0)),
        ("prn", (0, 0)),
        ("NotRightTrian", (0, 0)),
        ("Racute;", (340, 0)),
        ("lacu", (0, 0)),
        ("eg", (0, 0)),
        ("scpolint;", (10771, 0)),
        ("gbre", (0, 0)),
        ("TScy", (0, 0)),
        ("curvearrowleft;", (8630, 0)),
        ("nedot", (0, 0)),
        ("angzarr", (0, 0)),
        ("Top", (0, 0)),
        ("dolla", (0, 0)),
        ("CapitalDifferent", (0, 0)),
        ("amacr", (0, 0)),
        ("ldquor;", (8222, 0)),
        ("ecolo", (0, 0)),
        ("notinE", (0, 0)),
        ("Clo", (0, 0)),
        ("nvinfi", (0, 0)),
        ("succapprox", (0, 0)),
        ("lesssim", (0, 0)),
        ("para;", (182, 0)),
        ("plankv;", (8463, 0)),
        ("ClockwiseContourIn", (0, 0)),
        ("nrarr", (0, 0)),
        ("searh", (0, 0)),
        ("precneq", (0, 0)),
        ("ensp;", (8194, 0)),
        ("RightC", (0, 0)),
        ("Ce", (0, 0)),
        ("rtriltri", (0, 0)),
        ("LeftFlo", (0, 0)),
        ("RightAngleBracket;", (10217, 0)),
        ("boxdR", (0, 0)),
        ("Contou", (0, 0)),
        ("boxUR", (0, 0)),
        ("Rarrt", (0, 0)),
        ("FilledVerySmallSquar", (0, 0)),
        ("Diacrit", (0, 0)),
        ("NotLeftTriangleEq", (0, 0)),
        ("dots", (0, 0)),
        ("ijli", (0, 0)),
        ("Od", (0, 0)),
        ("ReverseUp", (0, 0)),
        ("Colon", (0, 0)),
        ("zc", (0, 0)),
        ("DownLeftTeeVect", (0, 0)),
        ("ecy;", (1101, 0)),
        ("afr", (0, 0)),
        ("emsp13", (0, 0)),
        ("ulcrop;", (8975, 0)),
        ("rarrw", (0, 0)),
        ("nvsim;", (8764, 8402)),
        ("rc", (0, 0)),
        ("vartrian", (0, 0)),
        ("dfish", (0, 0)),
        ("acute;", (180, 0)),
        ("edot", (0, 0)),
        ("Vvda", (0, 0)),
        ("RightUpVectorBa", (0, 0)),
        ("LeftTee", (0, 0)),
        ("notniva", (0, 0)),
        ("Iota;", (921, 0)),
        ("iocy;", (1105, 0)),
        ("NotTildeTi", (0, 0)),
        ("scnsim;", (8937, 0)),
        ("PrecedesTilde;", (8830, 0)),
        ("nsupe;", (8841, 0)),
        ("bsim;", (8765, 0)),
        ("se", (0, 0)),
        ("asymp", (0, 0)),
        ("EmptyVerySmallSq", (0, 0)),
        ("omid;", (10678, 0)),
        ("circledR", (0, 0)),
        ("NotGreaterFull", (0, 0)),
        ("acE", (0, 0)),
        ("Congr", (0, 0)),
        ("olarr;", (8634, 0)),
        ("bigtrian", (0, 0)),
        ("X", (0, 0)),
        ("nl", (0, 0)),
        ("lAarr;", (8666, 0)),
        ("isinsv", (0, 0)),
        ("SquareIntersec", (0, 0)),
        ("udblac", (0, 0)),
        ("Xfr", (0, 0)),
        ("vBarv;", (10985, 0)),
        ("capbrc", (0, 0)),
        ("simrarr;", (10610, 0)),
        ("prsim", (0, 0)),
        ("CirclePlus", (0, 0)),
        ("spadesui", (0, 0)),
        ("varsigma;", (962, 0)),
        ("TR", (0, 0)),
        ("oper", (0, 0)),
        ("diamondsuit;", (9830, 0)),
        ("sqcap", (0, 0)),
        ("ltl", (0, 0)),
        ("NotRightTriang", (0, 0)),
        ("ici", (0, 0)),
        ("gtrappro", (0, 0)),
        ("HorizontalL", (0, 0)),
        ("boxHU", (0, 0)),
        ("Imacr", (0, 0)),
        ("rect;", (9645, 0)),
        ("GJ", (0, 0)),
        ("boxplu", (0, 0)),
        ("circledd", (0, 0)),
        ("gnsim", (0, 0)),
        ("eque", (0, 0)),
        ("boxHd", (0, 0)),
        ("swAr", (0, 0)),
        ("boxb", (0, 0)),
        ("LessSlant", (0, 0)),
        ("Ll;", (8920, 0)),
        ("larr;", (8592, 0)),
        ("Lowe", (0, 0)),
        ("ma", (0, 0)),
        ("vop", (0, 0)),
        ("eog", (0, 0)),
        ("Sac", (0, 0)),
        ("nvrt", (0, 0)),
        ("precnsim", (0, 0)),
        ("fopf;", (120151, 0)),
        ("loarr;", (8701, 0)),
        ("lg;", (8822, 0)),
        ("iiiint", (0, 0)),
        ("approxeq;", (8778, 0)),
        ("hookright", (0, 0)),
        ("NotDoubl", (0, 0)),
        ("nsc", (0, 0)),
        ("Sscr;", (119982, 0)),
        ("Diacriti", (0, 0)),
        ("gtreqqless", (0, 0)),
        ("NotGreaterSlan", (0, 0)),
        ("icir", (0, 0)),
        ("Sub", (0, 0)),
        ("twoheadrightarrow", (0, 0)),
        ("DoubleUpDown", (0, 0)),
        ("NotSucceedsSlantE", (0, 0)),
        ("subseteq", (0, 0)),
        ("nsubE;", (10949, 824)),
        ("rtri;", (9657, 0)),
        ("LongLeft", (0, 0)),
        ("Tca", (0, 0)),
        ("vsupne;", (8843, 65024)),
        ("Updow", (0, 0)),
        ("xrArr;", (10233, 0)),
        ("prec;", (8826, 0)),
        ("efr", (0, 0)),
        ("VerticalSeparator;", (10072, 0)),
        ("OpenCurlyDoub", (0, 0)),
        ("Qop", (0, 0)),
        ("homtht", (0, 0)),
        ("Ido", (0, 0)),
        ("rightrig", (0, 0)),
        ("andslo", (0, 0)),
        ("lesc", (0, 0)),
        ("Scy;", (1057, 0)),
        ("LeftTriangleEq", (0, 0)),
        ("simdot;", (10858, 0)),
        ("ffllig", (0, 0)),
        ("trim", (0, 0)),
        ("larrtl", (0, 0)),
        ("nri", (0, 0)),
        ("GreaterSlantEqua", (0, 0)),
        ("rightarrowta", (0, 0)),
        ("nshortpar", (0, 0)),
        ("ncedi", (0, 0)),
        ("RightArrowLe", (0, 0)),
        ("simdot", (0, 0)),
        ("GreaterFu", (0, 0)),
        ("RightUpTeeVector;", (10588, 0)),
        ("gtrsim;", (8819, 0)),
        ("lates;", (10925, 65024)),
        ("NotTildeF", (0, 0)),
        ("simra", (0, 0)),
        ("zcaro", (0, 0)),
        ("LeftTriangleE", (0, 0)),
        ("DoubleVertical", (0, 0)),
        ("awi", (0, 0)),
        ("Ucir", (0, 0)),
        ("qua", (0, 0)),
        ("LessTilde;", (8818, 0)),
        ("propto", (0, 0)),
        ("precsi", (0, 0)),
        ("compf", (0, 0)),
        ("lsq", (0, 0)),
        ("Wo", (0, 0)),
        ("ltlarr", (0, 0)),
        ("andand", (0, 0)),
        ("LongRight", (0, 0)),
        ("cire", (0, 0)),
        ("DownTee", (0, 0)),
        ("Jukcy", (0, 0)),
        ("simr", (0, 0)),
        ("infinti", (0, 0)),
        ("upuparrow", (0, 0)),
        ("SquareSubset", (0, 0)),
        ("expon", (0, 0)),
        ("DiacriticalT", (0, 0)),
        ("apac", (0, 0)),
        ("Superset;", (8835, 0)),
        ("Iogon", (0, 0)),
        ("NoBrea", (0, 0)),
        ("circledc", (0, 0)),
        ("LeftArrowRi", (0, 0)),
        ("LongLeftRight", (0, 0)),
        ("LeftUpDownVec", (0, 0)),
        ("FilledSmallS", (0, 0)),
        ("Vvdash", (0, 0)),
        ("fflli", (0, 0)),
        ("thkap;", (8776, 0)),
        ("frac16;", (8537, 0)),
        ("RightAr", (0, 0)),
        ("DoubleRightArro", (0, 0)),
        ("ur", (0, 0)),
        ("DownLeftRig", (0, 0)),
        ("DoubleLeftAr", (0, 0)),
        ("rsaquo", (0, 0)),
        ("succ;", (8827, 0)),
        ("NotHumpDownHu", (0, 0)),
        ("rightrightarrow", (0, 0)),
        ("lrha", (0, 0)),
        ("gap;", (10886, 0)),
        ("Xf", (0, 0)),
        ("bul", (0, 0)),
        ("RuleDelayed;", (10740, 0)),
        ("eparsl;", (10723, 0)),
        ("gimel", (0, 0)),
        ("telrec;", (8981, 0)),
        ("lowas", (0, 0)),
        ("PlusMinus", (0, 0)),
        ("nleqq;", (8806, 824)),
        ("precapprox;", (10935, 0)),
        ("vartriangleright;", (8883, 0)),
        ("uu", (0, 0)),
        ("NegativeVeryThin", (0, 0)),
        ("frac78;", (8542, 0)),
        ("Iopf", (0, 0)),
        ("LowerRightA", (0, 0)),
        ("NotExi", (0, 0)),
        ("xot", (0, 0)),
        ("CapitalDifferentialD;", (8517, 0)),
        ("lacute;", (314, 0)),
        ("Cap;", (8914, 0)),
        ("zeetrf", (0, 0)),
        ("NotRevers", (0, 0)),
        ("Ao", (0, 0)),
        ("ulc", (0, 0)),
        ("blacktrianglelef", (0, 0)),
        ("eum", (0, 0)),
        ("ltquest", (0, 0)),
        ("mida", (0, 0)),
        ("uda", (0, 0)),
        ("DiacriticalDoubl", (0, 0)),
        ("ltrif", (0, 0)),
        ("rightharpoonu", (0, 0)),
        ("OpenCurlyQu", (0, 0)),
        ("Pcy", (0, 0)),
        ("sdote", (0, 0)),
        ("SubsetE", (0, 0)),
        ("mst", (0, 0)),
        ("NestedGreaterGreate", (0, 0)),
        ("Ntil", (0, 0)),
        ("Amac", (0, 0)),
        ("nbumpe", (0, 0)),
        ("Equa", (0, 0)),
        ("eqsim", (0, 0)),
        ("Cent", (0, 0)),
        ("Aum", (0, 0)),
        ("gnsim;", (8935, 0)),
        ("Because", (0, 0)),
        ("xotim", (0, 0)),
        ("supset;", (8835, 0)),
        ("simn", (0, 0)),
        ("sqsubs", (0, 0)),
        ("Equili", (0, 0)),
        ("LeftArrowBar", (0, 0)),
        ("nless", (0, 0)),
        ("NotGreaterGreater", (0, 0)),
        ("gvnE;", (8809, 65024)),
        ("varth", (0, 0)),
        ("veeeq;", (8794, 0)),
        ("Dagge", (0, 0)),
        ("bigtriangl", (0, 0)),
        ("SuchThat", (0, 0)),
        ("OverParenthesi", (0, 0)),
        ("DoubleUp", (0, 0)),
        ("cups", (0, 0)),
        ("DScy;", (1029, 0)),
        ("dAr", (0, 0)),
        ("emsp13;", (8196, 0)),
        ("Yacute", (221, 0)),
        ("lsimg", (0, 0)),
        ("mfr;", (120106, 0)),
        ("nvin", (0, 0)),
        ("osl", (0, 0)),
        ("vnsup", (0, 0)),
        ("RightArrowLeftAr", (0, 0)),
        ("weier", (0, 0)),
        ("zop", (0, 0)),
        ("lessgtr", (0, 0)),
        ("leftleftarro", (0, 0)),
        ("lessa", (0, 0)),
        ("varpi;", (982, 0)),
        ("Gopf;", (120126, 0)),
        ("OverBracket;", (9140, 0)),
        ("isins;", (8948, 0)),
        ("wedgeq;", (8793, 0)),
        ("simplus", (0, 0)),
        ("ufisht", (0, 0)),
        ("lurds", (0, 0)),
        ("verbar;", (124, 0)),
        ("urco", (0, 0)),
        ("Uuml", (220, 0)),
        ("Cacut", (0, 0)),
        ("NotSucceedsSlant", (0, 0)),
        ("ReverseUpEquili", (0, 0)),
        ("NotDoubleVert", (0, 0)),
        ("sst", (0, 0)),
        ("pertenk;", (8241, 0)),
        ("blacksq", (0, 0)),
        ("hal", (0, 0)),
        ("NotNested", (0, 0)),
        ("DiacriticalGrav", (0, 0)),
        ("rharul;", (10604, 0)),
        ("Cca", (0, 0)),
        ("yuml", (255, 0)),
        ("incare;", (8453, 0)),
        ("varsubse", (0, 0)),
        ("ru", (0, 0)),
        ("vDas", (0, 0)),
        ("ifr;", (120102, 0)),
        ("LeftDoubleBracke", (0, 0)),
        ("ThinSpace", (0, 0)),
        ("supsetne", (0, 0)),
        ("sqsupset", (0, 0)),
        ("Uarrocir", (0, 0)),
        ("nacute;", (324, 0)),
        ("xsq", (0, 0)),
        ("succnapp", (0, 0)),
        ("topfork", (0, 0)),
        ("tritim", (0, 0)),
        ("nrtr", (0, 0)),
        ("quot", (34, 0)),
        ("ep", (0, 0)),
        ("mho", (0, 0)),
        ("Proportiona", (0, 0)),
        ("NestedGreaterGre", (0, 0)),
        ("DoubleCon", (0, 0)),
        ("cire;", (8791, 0)),
        ("RightTeeArrow", (0, 0)),
        ("nedot;", (8784, 824)),
        ("OpenCurlyDouble", (0, 0)),
        ("NotGr", (0, 0)),
        ("sea", (0, 0)),
        ("geqq;", (8807, 0)),
        ("um", (0, 0)),
        ("rlh", (0, 0)),
        ("jf", (0, 0)),
        ("lc", (0, 0)),
        ("das", (0, 0)),
        ("swArr;", (8665, 0)),
        ("DownLeftVectorBa", (0, 0)),
        ("ssetmn", (0, 0)),
        ("Eleme", (0, 0)),
        ("upa", (0, 0)),
        ("angsph", (0, 0)),
        ("com", (0, 0)),
        ("cirE;", (10691, 0)),
        ("loan", (0, 0)),
        ("Yacute;", (221, 0)),
        ("dollar;", (36, 0)),
        ("Contour", (0, 0)),
        ("gvert", (0, 0)),
        ("rceil", (0, 0)),
        ("cuepr;", (8926, 0)),
        ("La", (0, 0)),
        ("CircleMi", (0, 0)),
        ("DoubleLongLeftRight", (0, 0)),
        ("nm", (0, 0)),
        ("ltimes;", (8905, 0)),
        ("UpperRigh", (0, 0)),
        ("rae", (0, 0)),
        ("Ocirc", (212, 0)),
        ("gesl", (0, 0)),
        ("pointi", (0, 0)),
        ("rsqb;", (93, 0)),
        ("ko", (0, 0)),
        ("nhpar", (0, 0)),
        ("scy", (0, 0)),
        ("urcorner;", (8989, 0)),
        ("ImaginaryI;", (8520, 0)),
        ("Equil", (0, 0)),
        ("vert;", (124, 0)),
        ("leftlef", (0, 0)),
        ("boxVH;", (9580, 0)),
        ("yscr", (0, 0)),
        ("uacute", (250, 0)),
        ("RightArrowLeftArrow;", (8644, 0)),
        ("Negative", (0, 0)),
        ("Diamond;", (8900, 0)),
        ("punc", (0, 0)),
        ("Aogon;", (260, 0)),
        ("LeftCe", (0, 0)),
        ("NegativeMed", (0, 0)),
        ("iques", (0, 0)),
        ("forall", (0, 0)),
        ("Supse", (0, 0)),
        ("boxVH", (0, 0)),
        ("NotRightTriangleBar;", (10704, 824)),
        ("NotNestedLessLess;", (10913, 824)),
        ("Diamo", (0, 0)),
        ("NotRever", (0, 0)),
        ("profsur", (0, 0)),
        ("LeftTeeArrow", (0, 0)),
        ("rarrlp;", (8620, 0)),
        ("filig;", (64257, 0)),
        ("Ex", (0, 0)),
        ("nearh", (0, 0)),
        ("ssc", (0, 0)),
        ("Qfr", (0, 0)),
        ("ecolon", (0, 0)),
        ("Product", (0, 0)),
        ("triangleright", (0, 0)),
        ("mapstod", (0, 0)),
        ("igrave;", (236, 0)),
        ("bigstar;", (9733, 0)),
        ("Uni", (0, 0)),
        ("rightthreetim", (0, 0)),
        ("Poincarepl", (0, 0)),
        ("Gammad", (0, 0)),
        ("bigoplu", (0, 0)),
        ("varnothi", (0, 0)),
        ("DownLeftVectorBar", (0, 0)),
        ("fscr", (0, 0)),
        ("Jscr", (0, 0)),
        ("nsup", (0, 0)),
        ("smil", (0, 0)),
        ("Updowna", (0, 0)),
        ("NotDoubleVertica", (0, 0)),
        ("ropf", (0, 0)),
        ("uH", (0, 0)),
        ("hks", (0, 0)),
        ("sce;", (10928, 0)),
        ("xmap", (0, 0)),
        ("loopar", (0, 0)),
        ("SquareSupersetEqual;", (8850, 0)),
        ("iota", (0, 0)),
        ("delta;", (948, 0)),
        ("xopf;", (120169, 0)),
        ("wp", (0, 0)),
        ("amal", (0, 0)),
        ("nlt;", (8814, 0)),
        ("NoB", (0, 0)),
        ("yuml;", (255, 0)),
        ("neArr;", (8663, 0)),
        ("Cf", (0, 0)),
        ("Gbrev", (0, 0)),
        ("doll", (0, 0)),
        ("oc", (0, 0)),
        ("ulcorn", (0, 0)),
        ("UnderP", (0, 0)),
        ("nabla", (0, 0)),
        ("leftleftarrows", (0, 0)),
        ("Coproduct;", (8720, 0)),
        ("nsqsube", (0, 0)),
        ("angmsdaa", (0, 0)),
        ("Dst", (0, 0)),
        ("CapitalDifferentialD", (0, 0)),
        ("shortm", (0, 0)),
        ("doteqdot;", (8785, 0)),
        ("SHcy", (0, 0)),
        ("SOFTc", (0, 0)),
        ("Gbre", (0, 0)),
        ("quate", (0, 0)),
        ("subnE;", (10955, 0)),
        ("LessTilde", (0, 0)),
        ("supd", (0, 0)),
        ("doublebarwedge;", (8966, 0)),
        ("iff;", (8660, 0)),
        ("awcon", (0, 0)),
        ("lfl", (0, 0)),
        ("FilledSmallSqua", (0, 0)),
        ("VeryThinSpace;", (8202, 0)),
        ("subrarr;", (10617, 0)),
        ("alpha", (0, 0)),
        ("Csc", (0, 0)),
        ("ovbar", (0, 0)),
        ("lra", (0, 0)),
        ("ubreve;", (365, 0)),
        ("NotLessSla", (0, 0)),
        ("Rce", (0, 0)),
        ("nLeftrighta", (0, 0)),
        ("NegativeVeryT", (0, 0)),
        ("lrtr", (0, 0)),
        ("Yuml", (0, 0)),
        ("CloseCurlyDou", (0, 0)),
        ("fork", (0, 0)),
        ("ruluhar;", (10600, 0)),
        ("rBa", (0, 0)),
        ("varr;", (8597, 0)),
        ("smallsetminus;", (8726, 0)),
        ("lsqb", (0, 0)),
        ("yu", (0, 0)),
        ("olcr", (0, 0)),
        ("bigcap", (0, 0)),
        ("NotSubsetEqua", (0, 0)),
        ("NotSucceedsSlantEqua", (0, 0)),
        ("DoubleLongRightA", (0, 0)),
        ("interca", (0, 0)),
        ("larrbfs;", (10527, 0)),
        ("apa", (0, 0)),
        ("NotSquareSuperset", (0, 0)),
        ("nvdash", (0, 0)),
        ("DownTee;", (8868, 0)),
        ("xuplus;", (10756, 0)),
        ("OverBrace;", (9182, 0)),
        ("tridot", (0, 0)),
        ("bigtr", (0, 0)),
        ("Osl", (0, 0)),
        ("lmo", (0, 0)),
        ("Sscr", (0, 0)),
        ("OverBrac", (0, 0)),
        ("rightleftarr", (0, 0)),
        ("nsime;", (8772, 0)),
        ("sqcups;", (8852, 65024)),
        ("NotTildeFullEq", (0, 0)),
        ("nsc;", (8833, 0)),
        ("notinvc", (0, 0)),
        ("erD", (0, 0)),
        ("lcar", (0, 0)),
        ("CounterClockwiseContourInt", (0, 0)),
        ("Fouriertrf;", (8497, 0)),
        ("Rh", (0, 0)),
        ("near", (0, 0)),
        ("Mo", (0, 0)),
        ("DotD", (0, 0)),
        ("sof", (0, 0)),
        ("ngt;", (8815, 0)),
        ("ccups", (0, 0)),
        ("appro", (0, 0)),
        ("LeftDownVector;", (8643, 0)),
        ("beca", (0, 0)),
        ("trpez", (0, 0)),
        ("blacktriangleri", (0, 0)),
        ("congd", (0, 0)),
        ("sacute", (0, 0)),
        ("b", (0, 0)),
        ("Sop", (0, 0)),
        ("fllig", (0, 0)),
        ("doteqdot", (0, 0)),
        ("rpp", (0, 0)),
        ("cente", (0, 0)),
        ("bse", (0, 0)),
        ("NotDoubleVerticalBar", (0, 0)),
        ("brvbar", (166, 0)),
        ("backprim", (0, 0)),
        ("ReverseUpEquil", (0, 0)),
        ("crarr", (0, 0)),
        ("Zd", (0, 0)),
        ("Afr", (0, 0)),
        ("nvinfin;", (10718, 0)),
        ("csub", (0, 0)),
        ("lEg;", (10891, 0)),
        ("eopf", (0, 0)),
        ("scaro", (0, 0)),
        ("rra", (0, 0)),
        ("ncongd", (0, 0)),
        ("abrev", (0, 0)),
        ("Ther", (0, 0)),
        ("DiacriticalDoubleAcute;", (733, 0)),
        ("NotElement", (0, 0)),
        ("updownarrow", (0, 0)),
        ("ShortDownArro", (0, 0)),
        ("ig", (0, 0)),
        ("vangr", (0, 0)),
        ("cuesc;", (8927, 0)),
        ("Yac", (0, 0)),
        ("ljcy", (0, 0)),
        ("sqsubseteq", (0, 0)),
        ("map;", (8614, 0)),
        ("ljcy;", (1113, 0)),
        ("Under", (0, 0)),
        ("LeftTeeVector", (0, 0)),
        ("TildeTil", (0, 0)),
        ("quati", (0, 0)),
        ("DoubleVerticalBa", (0, 0)),
        ("longleftar", (0, 0)),
        ("Auml;", (196, 0)),
        ("tcaro", (0, 0)),
        ("equal", (0, 0)),
        ("NotGreaterFullEqual;", (8807, 824)),
        ("nsupE", (0, 0)),
        ("Proportio", (0, 0)),
        ("epsilon", (0, 0)),
        ("dbkar", (0, 0)),
        ("upharpoonright", (0, 0)),
        ("rata", (0, 0)),
        ("NotNestedGreaterGreate", (0, 0)),
        ("lrm;", (8206, 0)),
        ("ael", (0, 0)),
        ("frac23", (0, 0)),
        ("Emac", (0, 0)),
        ("Counter", (0, 0)),
        ("capcup", (0, 0)),
        ("Wfr", (0, 0)),
        ("VerticalSe", (0, 0)),
        ("rAta", (0, 0)),
        ("NotSupersetEqu", (0, 0)),
        ("angmsda", (0, 0)),
        ("subm", (0, 0)),
        ("LowerLeftArro", (0, 0)),
        ("scnap;", (10938, 0)),
        ("rotimes;", (10805, 0)),
        ("Lacut", (0, 0)),
        ("bigodot", (0, 0)),
        ("veebar;", (8891, 0)),
        ("leg;", (8922, 0)),
        ("du", (0, 0)),
        ("aopf", (0, 0)),
        ("hbar;", (8463, 0)),
        ("ouml;", (246, 0)),
        ("tbrk;", (9140, 0)),
        ("lnapprox", (0, 0)),
        ("lharu;", (8636, 0)),
        ("upsih", (0, 0)),
        ("nsi", (0, 0)),
        ("nwne", (0, 0)),
        ("Nest", (0, 0)),
        ("plusacir", (0, 0)),
        ("LeftTriangleBar;", (10703, 0)),
        ("bsolhsub;", (10184, 0)),
        ("Egra", (0, 0)),
        ("ccaron;", (269, 0)),
        ("vcy", (0, 0)),
        ("Proportional", (0, 0)),
        ("ShortLeftArrow", (0, 0)),
        ("SubsetEqua", (0, 0)),
        ("hearts;", (9829, 0)),
        ("because;", (8757, 0)),
        ("mcy;", (1084, 0)),
        ("shortpa", (0, 0)),
        ("NotGreaterS", (0, 0)),
        ("lAr", (0, 0)),
        ("realp", (0, 0)),
        ("Cayley", (0, 0)),
        ("RightTriangleBar;", (10704, 0)),
        ("aop", (0, 0)),
        ("NotLess", (0, 0)),
        ("RightA", (0, 0)),
        ("looparro", (0, 0)),
        ("NotVe", (0, 0)),
        ("equivD", (0, 0)),
        ("Circl", (0, 0)),
        ("leftthreetim", (0, 0)),
        ("popf;", (120161, 0)),
        ("DoubleVer", (0, 0)),
        ("raqu", (0, 0)),
        ("nbum", (0, 0)),
        ("Larr", (0, 0)),
        ("NotReverseElement;", (8716, 0)),
        ("NotPrecedesSl", (0, 0)),
        ("downharpoonl", (0, 0)),
        ("nVDash", (0, 0)),
        ("loopa", (0, 0)),
        ("plusci", (0, 0)),
        ("Eopf;", (120124, 0)),
        ("larrhk;", (8617, 0)),
        ("orarr", (0, 0)),
        ("subnE", (0, 0)),
        ("leftthreet", (0, 0)),
        ("prop;", (8733, 0)),
        ("nlsim", (0, 0)),
        ("care", (0, 0)),
        ("Inter", (0, 0)),
        ("cc", (0, 0)),
        ("CounterClo", (0, 0)),
        ("nrar", (0, 0)),
        ("DoubleLeftRightArro", (0, 0)),
        ("permil;", (8240, 0)),
        ("ps", (0, 0)),
        ("Back", (0, 0)),
        ("hookle", (0, 0)),
        ("cs", (0, 0)),
        ("ddagger", (0, 0)),
        ("Succeeds", (0, 0)),
        ("eqslantl", (0, 0)),
        ("rbr", (0, 0)),
        ("LeftArrowBar;", (8676, 0)),
        ("Amacr;", (256, 0)),
        ("angmsdaf", (0, 0)),
        ("ImaginaryI", (0, 0)),
        ("Ed", (0, 0)),
        ("becau", (0, 0)),
        ("Backslash", (0, 0)),
        ("NotPrec", (0, 0)),
        ("Rs", (0, 0)),
        ("Rcedil", (0, 0)),
        ("succcur", (0, 0)),
        ("NotHumpEqual;", (8783, 824)),
        ("Ssc", (0, 0)),
        ("nsubsete", (0, 0)),
        ("con", (0, 0)),
        ("cdo", (0, 0)),
        ("Fopf", (0, 0)),
        ("Rrightarrow", (0, 0)),
        ("scp", (0, 0)),
        ("NotTildeEqua", (0, 0)),
        ("napi", (0, 0)),
        ("ab", (0, 0)),
        ("Cap", (0, 0)),
        ("lrh", (0, 0)),
        ("NotHu", (0, 0)),
        ("osla", (0, 0)),
        ("rightsquigarro", (0, 0)),
        ("exist", (0, 0)),
        ("veeeq", (0, 0)),
        ("DoubleLongLeftArr", (0, 0)),
        ("Leftar", (0, 0)),
        ("twoheadri", (0, 0)),
        ("for", (0, 0)),
        ("NotGreaterFullEqual", (0, 0)),
        ("RightTr", (0, 0)),
        ("Mellintrf", (0, 0)),
        ("YIc", (0, 0)),
        ("it;", (8290, 0)),
        ("Uac", (0, 0)),
        ("herc", (0, 0)),
        ("mlcp;", (10971, 0)),
        ("sqsupset;", (8848, 0)),
        ("mp", (0, 0)),
        ("rightthreetimes;", (8908, 0)),
        ("MediumS", (0, 0)),
        ("dharl;", (8643, 0)),
        ("leftrightharp", (0, 0)),
        ("cular", (0, 0)),
        ("Sho", (0, 0)),
        ("iexcl", (161, 0)),
        ("pitc", (0, 0)),
        ("L", (0, 0)),
        ("LeftRightArro", (0, 0)),
        ("Uring;", (366, 0)),
        ("pars", (0, 0)),
        ("asymp;", (8776, 0)),
        ("Ati", (0, 0)),
        ("angmsdad", (0, 0)),
        ("malt", (0, 0)),
        ("realin", (0, 0)),
        ("RightVectorBar;", (10579, 0)),
        ("Sc;", (10940, 0)),
        ("NotTildeFull", (0, 0)),
        ("Oci", (0, 0)),
        ("rightharpoondown;", (8641, 0)),
        ("trido", (0, 0)),
        ("submult", (0, 0)),
        ("elinters;", (9191, 0)),
        ("cedil", (184, 0)),
        ("eq", (0, 0)),
        ("varepsi", (0, 0)),
        ("circleddash;", (8861, 0)),
        ("hoarr;", (8703, 0)),
        ("ucirc", (251, 0)),
        ("measureda", (0, 0)),
        ("ContourInt", (0, 0)),
        ("LowerLe", (0, 0)),
        ("dzigrar", (0, 0)),
        ("boxtimes;", (8864, 0)),
        ("percnt", (0, 0)),
        ("ogt", (0, 0)),
        ("Iukc", (0, 0)),
        ("DownTeeArrow;", (8615, 0)),
        ("Agrav", (0, 0)),
        ("gtrles", (0, 0)),
        ("erDot;", (8787, 0)),
        ("Mellint", (0, 0)),
        ("PrecedesE", (0, 0)),
        ("as", (0, 0)),
        ("NotEx", (0, 0)),
        ("sqsube", (0, 0)),
        ("precsim;", (8830, 0)),
        ("fs", (0, 0)),
        ("realpart", (0, 0)),
        ("Ropf", (0, 0)),
        ("barvee;", (8893, 0)),
        ("leftthre", (0, 0)),
        ("NotSucceedsE", (0, 0)),
        ("frac25;", (8534, 0)),
        ("backe", (0, 0)),
        ("Colo", (0, 0)),
        ("vd", (0, 0)),
        ("OverBar;", (8254, 0)),
        ("exist;", (8707, 0)),
        ("Cup;", (8915, 0)),
        ("paralle", (0, 0)),
        ("OverB", (0, 0)),
        ("LessSlantEqual;", (10877, 0)),
        ("zscr;", (120015, 0)),
        ("DownL", (0, 0)),
        ("mDD", (0, 0)),
        ("boxH", (0, 0)),
        ("fjl", (0, 0)),
        ("Bre", (0, 0)),
        ("NotLessEq", (0, 0)),
        ("bso", (0, 0)),
        ("blacklozenge;", (10731, 0)),
        ("Rig", (0, 0)),
        ("rparg", (0, 0)),
        ("EmptyVerySmallSquare", (0, 0)),
        ("smid;", (8739, 0)),
        ("nis;", (8956, 0)),
        ("DZcy", (0, 0)),
        ("bigtriang", (0, 0)),
        ("khc", (0, 0)),
        ("lltr", (0, 0)),
        ("OpenCurlyDo", (0, 0)),
        ("NotRightTr", (0, 0)),
        ("DiacriticalDoubleAcu", (0, 0)),
        ("backepsi", (0, 0)),
        ("OpenCurlyQ", (0, 0)),
        ("trianglerigh", (0, 0)),
        ("supe", (0, 0)),
        ("Equi", (0, 0)),
        ("NotHumpEqu", (0, 0)),
        ("Usc", (0, 0)),
        ("Msc", (0, 0)),
        ("upsilon;", (965, 0)),
        ("sex", (0, 0)),
        ("FilledVerySmallSqu", (0, 0)),
        ("RightUpDownV", (0, 0)),
        ("copysr", (0, 0)),
        ("ShortLeft", (0, 0)),
        ("ultri;", (9720, 0)),
        ("lsaquo", (0, 0)),
        ("LeftUpT", (0, 0)),
        ("De", (0, 0)),
        ("twoheadleftarrow", (0, 0)),
        ("nesear;", (10536, 0)),
        ("NonBreakingS", (0, 0)),
        ("thicksim;", (8764, 0)),
        ("alefs", (0, 0)),
        ("cupcap", (0, 0)),
        ("hcirc", (0, 0)),
        ("Kcedi", (0, 0)),
        ("angrtvbd;", (10653, 0)),
        ("capbr", (0, 0)),
        ("UnderParen", (0, 0)),
        ("GreaterS", (0, 0)),
        ("solbar;", (9023, 0)),
        ("fn", (0, 0)),
        ("cularr;", (8630, 0)),
        ("zee", (0, 0)),
        ("laquo;", (171, 0)),
        ("poi", (0, 0)),
        ("roang;", (10221, 0)),
        ("rHar;", (10596, 0)),
        ("natur;", (9838, 0)),
        ("Fill", (0, 0)),
        ("y", (0, 0)),
        ("NotSquareSubsetEqu", (0, 0)),
        ("deg", (176, 0)),
        ("ecy", (0, 0)),
        ("aogon;", (261, 0)),
        ("raemp", (0, 0)),
        ("xlA", (0, 0)),
        ("cupbrc", (0, 0)),
        ("Imp", (0, 0)),
        ("nang;", (8736, 8402)),
        ("gtq", (0, 0)),
        ("leftha", (0, 0)),
        ("KHcy", (0, 0)),
        ("xotime;", (10754, 0)),
        ("nce", (0, 0)),
        ("SquareIntersectio", (0, 0)),
        ("varepsilon;", (1013, 0)),
        ("DownBreve;", (785, 0)),
        ("ulcr", (0, 0)),
        ("vrtr", (0, 0)),
        ("Js", (0, 0)),
        ("gtrs", (0, 0)),
        ("gtrl", (0, 0)),
        ("VerticalSepara", (0, 0)),
        ("blacktriangle;", (9652, 0)),
        ("circlearrowright", (0, 0)),
        ("bneq", (0, 0)),
        ("frac", (0, 0)),
        ("NotLessEqual;", (8816, 0)),
        ("nrarrw", (0, 0)),
        ("dzigrarr", (0, 0)),
        ("cr", (0, 0)),
        ("Xscr;", (119987, 0)),
        ("rbbr", (0, 0)),
        ("gb", (0, 0)),
        ("hell", (0, 0)),
        ("ntriangleleft;", (8938, 0)),
        ("OverParent", (0, 0)),
        ("longleftrightarr", (0, 0)),
        ("rightar", (0, 0)),
        ("pointint;", (10773, 0)),
        ("ther", (0, 0)),
        ("angsp", (0, 0)),
        ("gime", (0, 0)),
        ("Us", (0, 0)),
        ("ropf;", (120163, 0)),
        ("Df", (0, 0)),
        ("dempty", (0, 0)),
        ("complexe", (0, 0)),
        ("Star;", (8902, 0)),
        ("Idot;", (304, 0)),
        ("soft", (0, 0)),
        ("ccedil", (231, 0)),
        ("nwarr;", (8598, 0)),
        ("dfisht", (0, 0)),
        ("Vert", (0, 0)),
        ("dbk", (0, 0)),
        ("gne", (0, 0)),
        ("neq", (0, 0)),
        ("boxHd;", (9572, 0)),
        ("rfl", (0, 0)),
        ("bigwedge", (0, 0)),
        ("operp;", (10681, 0)),
        ("lnapprox;", (10889, 0)),
        ("times;", (215, 0)),
        ("NestedGreaterGr", (0, 0)),
        ("Ms", (0, 0)),
        ("Vscr;", (119985, 0)),
        ("UpEquilibr", (0, 0)),
        ("inc", (0, 0)),
        ("downdow", (0, 0)),
        ("ApplyFuncti", (0, 0)),
        ("lbrack", (0, 0)),
        ("rce", (0, 0)),
        ("ldrush", (0, 0)),
        ("backsim", (0, 0)),
        ("ph", (0, 0)),
        ("LeftVector;", (8636, 0)),
        ("umac", (0, 0)),
        ("hsc", (0, 0)),
        ("kscr", (0, 0)),
        ("lrhard", (0, 0)),
        ("NotEqual", (0, 0)),
        ("map", (0, 0)),
        ("fjlig", (0, 0)),
        ("lsquo;", (8216, 0)),
        ("sect", (167, 0)),
        ("lbrkslu", (0, 0)),
        ("Isc", (0, 0)),
        ("K", (0, 0)),
        ("ddagg", (0, 0)),
        ("ReverseEquilibr", (0, 0)),
        ("leftharpoonup", (0, 0)),
        ("seswar;", (10537, 0)),
        ("complement", (0, 0)),
        ("Lleftarr", (0, 0)),
        ("iuml;", (239, 0)),
        ("simg", (0, 0)),
        ("boxbox", (0, 0)),
        ("Gg", (0, 0)),
        ("cwc", (0, 0)),
        ("NotSquareSupersetEqual", (0, 0)),
        ("weie", (0, 0)),
        ("agrav", (0, 0)),
        ("UpEquilibrium", (0, 0)),
        ("RightCeil", (0, 0)),
        ("op", (0, 0)),
        ("boxvl", (0, 0)),
        ("dtri;", (9663, 0)),
        ("nhp", (0, 0)),
        ("rbrac", (0, 0)),
        ("Leftrightarro", (0, 0)),
        ("ccedil;", (231, 0)),
        ("UnderBar;", (95, 0)),
        ("UpperLe", (0, 0)),
        ("pre", (0, 0)),
        ("middot;", (183, 0)),
        ("Hst", (0, 0)),
        ("Downarro", (0, 0)),
        ("Four", (0, 0)),
        ("nshortmid", (0, 0)),
        ("lesdoto", (0, 0)),
        ("rnmid;", (10990, 0)),
        ("leftlefta", (0, 0)),
        ("rightleft", (0, 0)),
        ("Bernoulli", (0, 0)),
        ("qint;", (10764, 0)),
        ("quatint", (0, 0)),
        ("nsubseteqq;", (10949, 824)),
        ("rot", (0, 0)),
        ("Mscr;", (8499, 0)),
        ("quatern", (0, 0)),
        ("boxD", (0, 0)),
        ("supmult;", (10946, 0)),
        ("NotLeftTriangle", (0, 0)),
        ("Gcedil", (0, 0)),
        ("LeftUpDownVector", (0, 0)),
        ("NotRightTria", (0, 0)),
        ("wedbar;", (10847, 0)),
        ("horbar", (0, 0)),
        ("RightDownTee", (0, 0)),
        ("Eog", (0, 0)),
        ("boxVr", (0, 0)),
        ("lesseqqgt", (0, 0)),
        ("OverP", (0, 0)),
        ("Eopf", (0, 0)),
        ("Ograv", (0, 0)),
        ("nearrow;", (8599, 0)),
        ("ima", (0, 0)),
        ("ffll", (0, 0)),
        ("DownRightVector;", (8641, 0)),
        ("dscy;", (1109, 0)),
        ("Expon", (0, 0)),
        ("DownRightTeeV", (0, 0)),
        ("Darr", (0, 0)),
        ("Tstrok;", (358, 0)),
        ("boxHu", (0, 0)),
        ("nearro", (0, 0)),
        ("thkap", (0, 0)),
        ("vartrianglelef", (0, 0)),
        ("downdownarrow", (0, 0)),
        ("sz", (0, 0)),
        ("hookleftarr", (0, 0)),
        ("Ll", (0, 0)),
        ("vn", (0, 0)),
        ("quaternion", (0, 0)),
        ("omac", (0, 0)),
        ("SquareSupersetEqua", (0, 0)),
        ("thins", (0, 0)),
        ("Lstrok;", (321, 0)),
        ("dscr", (0, 0)),
        ("longleftarrow;", (10229, 0)),
        ("DoubleContourIn", (0, 0)),
        ("LongLeftRightArrow;", (10231, 0)),
        ("LongLeftArrow;", (10229, 0)),
        ("trianglerighteq", (0, 0)),
        ("lfloor;", (8970, 0)),
        ("subseteqq;", (10949, 0)),
        ("laquo", (171, 0)),
        ("RightTeeA", (0, 0)),
        ("brv", (0, 0)),
        ("ltdot;", (8918, 0)),
        ("brvba", (0, 0)),
        ("fpartin", (0, 0)),
        ("Downarr", (0, 0)),
        ("swarrow", (0, 0)),
        ("Assign", (0, 0)),
        ("UpperRightArro", (0, 0)),
        ("alefsym;", (8501, 0)),
        ("DoubleLongLe", (0, 0)),
        ("Lefta", (0, 0)),
        ("khcy", (0, 0)),
        ("NotSucceedsEqual", (0, 0)),
        ("nsubseteq", (0, 0)),
        ("drb", (0, 0)),
        ("NonBreakingSp", (0, 0)),
        ("Ta", (0, 0)),
        ("succ", (0, 0)),
        ("utd", (0, 0)),
        ("exponentiale", (0, 0)),
        ("gt;", (62, 0)),
        ("eDDot", (0, 0)),
        ("rlm", (0, 0)),
        ("PrecedesSlantEq", (0, 0)),
        ("DownArrowUpArrow", (0, 0)),
        ("DDo", (0, 0)),
        ("solb;", (10692, 0)),
        ("laemptyv;", (10676, 0)),
        ("bigve", (0, 0)),
        ("Fourier", (0, 0)),
        ("Epsilon", (0, 0)),
        ("subd", (0, 0)),
        ("CapitalDiffer", (0, 0)),
        ("SHc", (0, 0)),
        ("Gg;", (8921, 0)),
        ("boxtime", (0, 0)),
        ("OpenCurlyD", (0, 0)),
        ("simrarr", (0, 0)),
        ("Ksc", (0, 0)),
        ("angrtvb;", (8894, 0)),
        ("sbquo", (0, 0)),
        ("precapp", (0, 0)),
        ("Ef", (0, 0)),
        ("OverBr", (0, 0)),
        ("gns", (0, 0)),
        ("Tstrok", (0, 0)),
        ("setmi", (0, 0)),
        ("LeftCeil", (0, 0)),
        ("RightVectorBar", (0, 0)),
        ("boxul", (0, 0)),
        ("Lmidot", (0, 0)),
        ("smepar", (0, 0)),
        ("Zet", (0, 0)),
        ("rising", (0, 0)),
        ("ncedil", (0, 0)),
        ("Longleftar", (0, 0)),
        ("lsquo", (0, 0)),
        ("Hop", (0, 0)),
        ("elsdot;", (10903, 0)),
        ("sigmaf", (0, 0)),
        ("varsu", (0, 0)),
        ("Ks", (0, 0)),
        ("twoheadle", (0, 0)),
        ("tri", (0, 0)),
        ("blacks", (0, 0)),
        ("wfr", (0, 0)),
        ("ZeroWidthS", (0, 0)),
        ("ZHc", (0, 0)),
        ("ddotseq", (0, 0)),
        ("DoubleLeftArrow", (0, 0)),
        ("ijlig", (0, 0)),
        ("straightep", (0, 0)),
        ("Uarro", (0, 0)),
        ("lj", (0, 0)),
        ("LeftDownT", (0, 0)),
        ("zeta", (0, 0)),
        ("ssta", (0, 0)),
        ("Un", (0, 0)),
        ("mf", (0, 0)),
        ("harrcir;", (10568, 0)),
        ("UnderBar", (0, 0)),
        ("midd", (0, 0)),
        ("bsol", (0, 0)),
        ("lrcorn", (0, 0)),
        ("hooklefta", (0, 0)),
        ("bsemi;", (8271, 0)),
        ("searrow;", (8600, 0)),
        ("thickapprox", (0, 0)),
        ("scnE;", (10934, 0)),
        ("elsdo", (0, 0)),
        ("ao", (0, 0)),
        ("Min", (0, 0)),
        ("nlE", (0, 0)),
        ("bigs", (0, 0)),
        ("disin;", (8946, 0)),
        ("NotTildeTil", (0, 0)),
        ("Zacut", (0, 0)),
        ("nwarrow", (0, 0)),
        ("NotExists", (0, 0)),
        ("oS", (0, 0)),
        ("Ocy;", (1054, 0)),
        ("scedil", (0, 0)),
        ("there", (0, 0)),
        ("notinv", (0, 0)),
        ("Nacu", (0, 0)),
        ("Lca", (0, 0)),
        ("lha", (0, 0)),
        ("vAr", (0, 0)),
        ("Ugrave;", (217, 0)),
        ("ntriangleleft", (0, 0)),
        ("mid", (0, 0)),
        ("DoubleC", (0, 0)),
        ("GreaterSlantEqual;", (10878, 0)),
        ("sc;", (8827, 0)),
        ("NotTildeTild", (0, 0)),
        ("LessEqualGreater", (0, 0)),
        ("swA", (0, 0)),
        ("ordm;", (186, 0)),
        ("ominus;", (8854, 0)),
        ("DotDot;", (8412, 0)),
        ("LowerL", (0, 0)),
        ("lbbr", (0, 0)),
        ("lcu", (0, 0)),
        ("vsub", (0, 0)),
        ("Util", (0, 0)),
        ("iog", (0, 0)),
        ("ex", (0, 0)),
        ("DotEq", (0, 0)),
        ("scnsi", (0, 0)),
        ("rmo", (0, 0)),
        ("Sopf", (0, 0)),
        ("empty;", (8709, 0)),
        ("nang", (0, 0)),
        ("upsilon", (0, 0)),
        ("rightharpo", (0, 0)),
        ("ee;", (8519, 0)),
        ("vltr", (0, 0)),
        ("yacute;", (253, 0)),
        ("pscr", (0, 0)),
        ("supla", (0, 0)),
        ("sopf", (0, 0)),
        ("varnothing", (0, 0)),
        ("supdot;", (10942, 0)),
        ("RightDoubl", (0, 0)),
        ("RBa", (0, 0)),
        ("precnap", (0, 0)),
        ("hookrighta", (0, 0)),
        ("setmn;", (8726, 0)),
        ("angmsdah", (0, 0)),
        ("Imaginary", (0, 0)),
        ("ccedi", (0, 0)),
        ("Union", (0, 0)),
        ("xuplu", (0, 0)),
        ("sum;", (8721, 0)),
        ("trisb", (0, 0)),
        ("amp;", (38, 0)),
        ("rightle", (0, 0)),
        ("theta", (0, 0)),
        ("nleqq", (0, 0)),
        ("preccur", (0, 0)),
        ("lob", (0, 0)),
        ("erarr", (0, 0)),
        ("Icir", (0, 0)),
        ("bum", (0, 0)),
        ("amp", (38, 0)),
        ("EmptySmallSquar", (0, 0)),
        ("sp", (0, 0)),
        ("Longleftrightarrow", (0, 0)),
        ("hybul", (0, 0)),
        ("Tcedil", (0, 0)),
        ("tosa", (0, 0)),
        ("SucceedsSl", (0, 0)),
        ("Berno", (0, 0)),
        ("RightDownVectorBar", (0, 0)),
        ("half", (0, 0)),
        ("emptys", (0, 0)),
        ("rhard;", (8641, 0)),
        ("nsim;", (8769, 0)),
        ("solb", (0, 0)),
        ("between", (0, 0)),
        ("nwar", (0, 0)),
        ("larrsim;", (10611, 0)),
        ("UpEqui", (0, 0)),
        ("ngtr;", (8815, 0)),
        ("FilledSmallSquare", (0, 0)),
        ("uogon", (0, 0)),
        ("mi", (0, 0)),
        ("cwin", (0, 0)),
        ("varsupsetneq", (0, 0)),
        ("LeftUpVec", (0, 0)),
        ("nprcu", (0, 0)),
        ("nan", (0, 0)),
        ("blacksquare;", (9642, 0)),
        ("rcedil", (0, 0)),
        ("ShortDownArrow;", (8595, 0)),
        ("rarrsim;", (10612, 0)),
        ("Xopf;", (120143, 0)),
        ("cross", (0, 0)),
        ("DownTeeArr", (0, 0)),
        ("Otild", (0, 0)),
        ("Jop", (0, 0)),
        ("NotGreaterFullEqu", (0, 0)),
        ("bbr", (0, 0)),
        ("biguplu", (0, 0)),
        ("uop", (0, 0)),
        ("Thin", (0, 0)),
        ("ov", (0, 0)),
        ("boxuR;", (9560, 0)),
        ("nRightarrow;", (8655, 0)),
        ("Iacute", (205, 0)),
        ("reali", (0, 0)),
        ("cupcup", (0, 0)),
        ("LongRightAr", (0, 0)),
        ("nleftr", (0, 0)),
        ("clubsui", (0, 0)),
        ("andan", (0, 0)),
        ("ycy;", (1099, 0)),
        ("nvinfin", (0, 0)),
        ("Ac", (0, 0)),
        ("longmapsto;", (10236, 0)),
        ("RightDoubleBracket;", (10215, 0)),
        ("blacksqua", (0, 0)),
        ("DoubleContour", (0, 0)),
        ("isins", (0, 0)),
        ("varsupsetne", (0, 0)),
        ("LeftUpVect", (0, 0)),
        ("nearr", (0, 0)),
        ("Lle", (0, 0)),
        ("Dscr;", (119967, 0)),
        ("SucceedsSlan", (0, 0)),
        ("Ass", (0, 0)),
        ("sime", (0, 0)),
        ("popf", (0, 0)),
        ("LJcy;", (1033, 0)),
        ("backeps", (0, 0)),
        ("disin", (0, 0)),
        ("ReverseEq", (0, 0)),
        ("LeftAngleBr", (0, 0)),
        ("UnderB", (0, 0)),
        ("fallingdots", (0, 0)),
        ("Verba", (0, 0)),
        ("nrArr;", (8655, 0)),
        ("Unio", (0, 0)),
        ("Vc", (0, 0)),
        ("female;", (9792, 0)),
        ("topcir", (0, 0)),
        ("NotSquareSubs", (0, 0)),
        ("intl", (0, 0)),
        ("her", (0, 0)),
        ("prsi", (0, 0)),
        ("downdownarr", (0, 0)),
        ("Plus", (0, 0)),
        ("elsd", (0, 0)),
        ("lvertne", (0, 0)),
        ("ccaron", (0, 0)),
        ("lang", (0, 0)),
        ("loar", (0, 0)),
        ("LessS", (0, 0)),
        ("LeftRigh", (0, 0)),
        ("eqslantg", (0, 0)),
        ("zwn", (0, 0)),
        ("nsimeq", (0, 0)),
        ("urcro", (0, 0)),
        ("CHcy", (0, 0)),
        ("Lapl", (0, 0)),
        ("leqsl", (0, 0)),
        ("LeftDownTeeVect", (0, 0)),
        ("shortparalle", (0, 0)),
        ("nvHar", (0, 0)),
        ("Different", (0, 0)),
        ("scE;", (10932, 0)),
        ("Su", (0, 0)),
        ("rcar", (0, 0)),
        ("utri;", (9653, 0)),
        ("UpTe", (0, 0)),
        ("GreaterTilde;", (8819, 0)),
        ("isin;", (8712, 0)),
        ("eur", (0, 0)),
        ("darr;", (8595, 0)),
        ("longleftright", (0, 0)),
        ("euro", (0, 0)),
        ("NotDoubleVerticalBa", (0, 0)),
        ("orslope", (0, 0)),
        ("Icy;", (1048, 0)),
        ("af;", (8289, 0)),
        ("blacktriangleright;", (9656, 0)),
        ("rsa", (0, 0)),
        ("lE", (0, 0)),
        ("Longleftrighta", (0, 0)),
        ("esdot;", (8784, 0)),
        ("lmi", (0, 0)),
        ("backprime;", (8245, 0)),
        ("NotSuperse", (0, 0)),
        ("curlyeqsucc", (0, 0)),
        ("dwangle;", (10662, 0)),
        ("uacute;", (250, 0)),
        ("Longleftr", (0, 0)),
        ("DotDo", (0, 0)),
        ("Hstrok", (0, 0)),
        ("thick", (0, 0)),
        ("Longright", (0, 0)),
        ("NotHumpDown", (0, 0)),
        ("TH", (0, 0)),
        ("lbrkslu;", (10637, 0)),
        ("gvn", (0, 0)),
        ("imagl", (0, 0)),
        ("Proporti", (0, 0)),
        ("sup", (0, 0)),
        ("EmptyVerySmal", (0, 0)),
        ("npr;", (8832, 0)),
        ("circeq;", (8791, 0)),
        ("suphs", (0, 0)),
        ("angle;", (8736, 0)),
        ("rAtai", (0, 0)),
        ("rop", (0, 0)),
        ("NegativeM", (0, 0)),
        ("Longle", (0, 0)),
        ("Invisibl", (0, 0)),
        ("angza", (0, 0)),
        ("bigu", (0, 0)),
        ("NotHumpEq", (0, 0)),
        ("DoubleLongLeftRightArrow", (0, 0)),
        ("VDash;", (8875, 0)),
        ("nGg;", (8921, 824)),
        ("Gre", (0, 0)),
        ("cempty", (0, 0)),
        ("NegativeThick", (0, 0)),
        ("leftl", (0, 0)),
        ("Ucirc", (219, 0)),
        ("tr", (0, 0)),
        ("easte", (0, 0)),
        ("lo", (0, 0)),
        ("COPY", (169, 0)),
        ("Poi", (0, 0)),
        ("Congruen", (0, 0)),
        ("smepa", (0, 0)),
        ("rfi", (0, 0)),
        ("eplus;", (10865, 0)),
        ("LeftArrowRightArro", (0, 0)),
        ("CounterClockwiseCo", (0, 0)),
        ("frac34;", (190, 0)),
        ("bopf;", (120147, 0)),
        ("rightleftha", (0, 0)),
        ("blacktriangledow", (0, 0)),
        ("xutri;", (9651, 0)),
        ("eqvp", (0, 0)),
        ("Rho", (0, 0)),
        ("profa", (0, 0)),
        ("circleda", (0, 0)),
        ("frac58", (0, 0)),
        ("prsim;", (8830, 0)),
        ("scsim;", (8831, 0)),
        ("leftarrowta", (0, 0)),
        ("LeftUpDown", (0, 0)),
        ("eqco", (0, 0)),
        ("NestedGreaterGreater", (0, 0)),
        ("nwarhk;", (10531, 0)),
        ("epsilo", (0, 0)),
        ("Eg", (0, 0)),
        ("dtdo", (0, 0)),
        ("es", (0, 0)),
        ("uwangl", (0, 0)),
        ("zwj;", (8205, 0)),
        ("LeftVec", (0, 0)),
        ("succnap", (0, 0)),
        ("iec", (0, 0)),
        ("softc", (0, 0)),
        ("topfo", (0, 0)),
        ("vf", (0, 0)),
        ("Hilbert", (0, 0)),
        ("pro", (0, 0)),
        ("gs", (0, 0)),
        ("ReverseEquili", (0, 0)),
        ("Wscr;", (119986, 0)),
        ("DownLeftRigh", (0, 0)),
        ("LeftRightAr", (0, 0)),
        ("rmoustac", (0, 0)),
        ("nprec", (0, 0)),
        ("Trip", (0, 0)),
        ("circlearrowright;", (8635, 0)),
        ("SucceedsS", (0, 0)),
        ("NoBr", (0, 0)),
        ("xod", (0, 0)),
        ("DoubleDot", (0, 0)),
        ("mapstou", (0, 0)),
        ("ncap;", (10819, 0)),
        ("Diacri", (0, 0)),
        ("subset;", (8834, 0)),
        ("YA", (0, 0)),
        ("Sigm", (0, 0)),
        ("gtrarr;", (10616, 0)),
        ("DoubleLongRigh", (0, 0)),
        ("Beta;", (914, 0)),
        ("gtrarr", (0, 0)),
        ("nsup;", (8837, 0)),
        ("LeftTeeVect", (0, 0)),
        ("plank", (0, 0)),
        ("omid", (0, 0)),
        ("DownRightVectorB", (0, 0)),
        ("lmoustach", (0, 0)),
        ("OpenCu", (0, 0)),
        ("Lt", (0, 0)),
        ("gtreqqless;", (10892, 0)),
        ("twohea", (0, 0)),
        ("ni", (0, 0)),
        ("laemp", (0, 0)),
        ("lhblk;", (9604, 0)),
        ("Ud", (0, 0)),
        ("PrecedesS", (0, 0)),
        ("MediumSpace", (0, 0)),
        ("varnot", (0, 0)),
        ("divonx", (0, 0)),
        ("bar", (0, 0)),
        ("RightUpTeeV", (0, 0)),
        ("bigst", (0, 0)),
        ("ExponentialE", (0, 0)),
        ("rightthre", (0, 0)),
        ("prod", (0, 0)),
        ("npa", (0, 0)),
        ("ufr;", (120114, 0)),
        ("preccurl", (0, 0)),
        ("oi", (0, 0)),
        ("sf", (0, 0)),
        ("IOc", (0, 0)),
        ("loplu", (0, 0)),
        ("Uo", (0, 0)),
        ("rharu;", (8640, 0)),
        ("LessG", (0, 0)),
        ("doteqd", (0, 0)),
        ("chc", (0, 0)),
        ("Ubreve;", (364, 0)),
        ("ris", (0, 0)),
        ("nGtv", (0, 0)),
        ("boxdr", (0, 0)),
        ("rarrp", (0, 0)),
        ("eco", (0, 0)),
        ("hamil", (0, 0)),
        ("ord;", (10845, 0)),
        ("wedb", (0, 0)),
        ("nsucc;", (8833, 0)),
        ("zcy;", (1079, 0)),
        ("NotLef", (0, 0)),
        ("Ocy", (0, 0)),
        ("Intersection", (0, 0)),
        ("Ntilde;", (209, 0)),
        ("vartriangleri", (0, 0)),
        ("uuar", (0, 0)),
        ("suphsol;", (10185, 0)),
        ("verbar", (0, 0)),
        ("exponentia", (0, 0)),
        ("eop", (0, 0)),
        ("boxU", (0, 0)),
        ("twoheadlef", (0, 0)),
        ("dotmin", (0, 0)),
        ("rho", (0, 0)),
        ("Ubreve", (0, 0)),
        ("Jfr;", (120077, 0)),
        ("LessGreate", (0, 0)),
        ("Rced", (0, 0)),
        ("vBar;", (10984, 0)),
        ("Longleftarro", (0, 0)),
        ("wsc", (0, 0)),
        ("yen", (165, 0)),
        ("NotGreaterFul", (0, 0)),
        ("gcirc", (0, 0)),
        ("qpri", (0, 0)),
        ("napo", (0, 0)),
        ("Aacu", (0, 0)),
        ("UpDow", (0, 0)),
        ("Assign;", (8788, 0)),
        ("Chi", (0, 0)),
        ("blank;", (9251, 0)),
        ("VerticalSep", (0, 0)),
        ("vars", (0, 0)),
        ("SuchTh", (0, 0)),
        ("NotSucceedsS", (0, 0)),
        ("Ced", (0, 0)),
        ("NotSquareS", (0, 0)),
        ("LowerR", (0, 0)),
        ("DoubleRight", (0, 0)),
        ("NotDoub", (0, 0)),
        ("subrar", (0, 0)),
        ("boxpl", (0, 0)),
        ("ogon", (0, 0)),
        ("lescc", (0, 0)),
        ("capan", (0, 0)),
        ("egrave", (232, 0)),
        ("VerticalBa", (0, 0)),
        ("ltr", (0, 0)),
        ("Fourie", (0, 0)),
        ("Precedes;", (8826, 0)),
        ("rarr", (0, 0)),
        ("cuv", (0, 0)),
        ("ct", (0, 0)),
        ("Longleftrig", (0, 0)),
        ("VDash", (0, 0)),
        ("nsimeq;", (8772, 0)),
        ("natural", (0, 0)),
        ("TildeFullEqua", (0, 0)),
        ("Updo", (0, 0)),
        ("hamilt", (0, 0)),
        ("OpenCur", (0, 0)),
        ("Ve", (0, 0)),
        ("Cdot;", (266, 0)),
        ("updow", (0, 0)),
        ("nleftarrow", (0, 0)),
        ("jsercy", (0, 0)),
        ("upharpoonr", (0, 0)),
        ("ang;", (8736, 0)),
        ("blacktriangledown;", (9662, 0)),
        ("Im;", (8465, 0)),
        ("curlyeq", (0, 0)),
        ("cularrp", (0, 0)),
        ("lrcor", (0, 0)),
        ("comp", (0, 0)),
        ("sstarf;", (8902, 0)),
        ("apid;", (8779, 0)),
        ("curvearrowlef", (0, 0)),
        ("GreaterFull", (0, 0)),
        ("xr", (0, 0)),
        ("SupersetEqual", (0, 0)),
        ("Ph", (0, 0)),
        ("rightsquigarrow", (0, 0)),
        ("divideonti", (0, 0)),
        ("euml", (235, 0)),
        ("Kappa;", (922, 0)),
        ("npre", (0, 0)),
        ("njcy;", (1114, 0)),
        ("xla", (0, 0)),
        ("blacktriangl", (0, 0)),
        ("RightDownT", (0, 0)),
        ("ReverseEle", (0, 0)),
        ("DoubleLongRi", (0, 0)),
        ("wreath;", (8768, 0)),
        ("race", (0, 0)),
        ("NotSquareSu", (0, 0)),
        ("nbsp", (160, 0)),
        ("ZeroWidt", (0, 0)),
        ("wedg", (0, 0)),
        ("he", (0, 0)),
        ("UpperRightAr", (0, 0)),
        ("xcirc;", (9711, 0)),
        ("ges;", (10878, 0)),
        ("trian", (0, 0)),
        ("nsucceq;", (10928, 824)),
        ("j", (0, 0)),
        ("Cayl", (0, 0)),
        ("Xsc", (0, 0)),
        ("circle", (0, 0)),
        ("yo", (0, 0)),
        ("rcedil;", (343, 0)),
        ("notn", (0, 0)),
        ("NotCup", (0, 0)),
        ("cirmid", (0, 0)),
        ("o", (0, 0)),
        ("UpperR", (0, 0)),
        ("smallsetmin", (0, 0)),
        ("boxvh;", (9532, 0)),
        ("boxhU;", (9576, 0)),
        ("ForA", (0, 0)),
        ("RightDoub", (0, 0)),
        ("nvHarr;", (10500, 0)),
        ("Kcedil;", (310, 0)),
        ("mdash;", (8212, 0)),
        ("LeftUpTee", (0, 0)),
        ("prnsi", (0, 0)),
        ("rarrbf", (0, 0)),
        ("thickapp", (0, 0)),
        ("simg;", (10910, 0)),
        ("Low", (0, 0)),
        ("oast", (0, 0)),
        ("rppoli", (0, 0)),
        ("CloseCurlyDoubleQuote", (0, 0)),
        ("RE", (0, 0)),
        ("rbrk", (0, 0)),
        ("Gop", (0, 0)),
        ("epsi", (0, 0)),
        ("RightU", (0, 0)),
        ("aring;", (229, 0)),
        ("eps", (0, 0)),
        ("amalg;", (10815, 0)),
        ("SquareInterse", (0, 0)),
        ("Omic", (0, 0)),
        ("bigo", (0, 0)),
        ("rightleftharpoons", (0, 0)),
        ("uog", (0, 0)),
        ("ShortUpAr", (0, 0)),
        ("Ropf;", (8477, 0)),
        ("UpperLeft", (0, 0)),
        ("downarrow", (0, 0)),
        ("succsi", (0, 0)),
        ("otime", (0, 0)),
        ("Til", (0, 0)),
        ("suphsub;", (10967, 0)),
        ("llhard", (0, 0)),
        ("Lop", (0, 0)),
        ("ShortLeftA", (0, 0)),
        ("NotPrecede", (0, 0)),
        ("Gscr", (0, 0)),
        ("NotD", (0, 0)),
        ("boxminus", (0, 0)),
        ("qi", (0, 0)),
        ("iiint;", (8749, 0)),
        ("NotEqualTild", (0, 0)),
        ("Phi;", (934, 0)),
        ("Clockw", (0, 0)),
        ("cudar", (0, 0)),
        ("Inte", (0, 0)),
        ("midcir;", (10992, 0)),
        ("UpE", (0, 0)),
        ("lbar", (0, 0)),
        ("ZeroWidthSpa", (0, 0)),
        ("Zdot", (0, 0)),
        ("lbrk", (0, 0)),
        ("ReverseUpEquilibri", (0, 0)),
        ("Capital", (0, 0)),
        ("nhpa", (0, 0)),
        ("precns", (0, 0)),
        ("NotSquareSubset;", (8847, 824)),
        ("Hi", (0, 0)),
        ("eacute;", (233, 0)),
        ("smile", (0, 0)),
        ("RightUpTeeVect", (0, 0)),
        ("pfr;", (120109, 0)),
        ("Ncy", (0, 0)),
        ("opa", (0, 0)),
        ("xo", (0, 0)),
        ("ntriangler", (0, 0)),
        ("lotim", (0, 0)),
        ("Element;", (8712, 0)),
        ("Mcy;", (1052, 0)),
        ("vdash;", (8866, 0)),
        ("ExponentialE;", (8519, 0)),
        ("vA", (0, 0)),
        ("precnappro", (0, 0)),
        ("Cci", (0, 0)),
        ("ome", (0, 0)),
        ("blk12", (0, 0)),
        ("yf", (0, 0)),
        ("plust", (0, 0)),
        ("LeftDoubleBr", (0, 0)),
        ("angrtv", (0, 0)),
        ("cupor", (0, 0)),
        ("gvnE", (0, 0)),
        ("LessSlan", (0, 0)),
        ("triangle", (0, 0)),
        ("Lsh;", (8624, 0)),
        ("ecir;", (8790, 0)),
        ("NotPrecedes;", (8832, 0)),
        ("Y", (0, 0)),
        ("edo", (0, 0)),
        ("PlusMin", (0, 0)),
        ("DoubleLongL", (0, 0)),
        ("racu", (0, 0)),
        ("SHCH", (0, 0)),
        ("Rrightar", (0, 0)),
        ("u", (0, 0)),
        ("cur", (0, 0)),
        ("Copr", (0, 0)),
        ("llh", (0, 0)),
        ("NotCupC", (0, 0)),
        ("Vee", (0, 0)),
        ("lsquor", (0, 0)),
        ("sop", (0, 0)),
        ("nese", (0, 0)),
        ("shar", (0, 0)),
        ("hc", (0, 0)),
        ("har", (0, 0)),
        ("RuleDe", (0, 0)),
        ("RightVec", (0, 0)),
        ("bbrktb", (0, 0)),
        ("boxDR;", (9556, 0)),
        ("Lfr", (0, 0)),
        ("Zfr;", (8488, 0)),
        ("it", (0, 0)),
        ("expectation;", (8496, 0)),
        ("Lstro", (0, 0)),
        ("sol;", (47, 0)),
        ("ShortLef", (0, 0)),
        ("tel", (0, 0)),
        ("mul", (0, 0)),
        ("uac", (0, 0)),
        ("dblac;", (733, 0)),
        ("Esim", (0, 0)),
        ("nappr", (0, 0)),
        ("ulcorn;", (8988, 0)),
        ("lotimes;", (10804, 0)),
        ("SucceedsEq", (0, 0)),
        ("nti", (0, 0)),
        ("ffl", (0, 0)),
        ("beth;", (8502, 0)),
        ("circledas", (0, 0)),
        ("nVda", (0, 0)),
        ("udh", (0, 0)),
        ("ET", (0, 0)),
        ("luru", (0, 0)),
        ("fp", (0, 0)),
        ("simne", (0, 0)),
        ("NotCongruent;", (8802, 0)),
        ("less", (0, 0)),
        ("thetav;", (977, 0)),
        ("lopar;", (10629, 0)),
        ("zeetrf;", (8488, 0)),
        ("uwang", (0, 0)),
        ("nearhk;", (10532, 0)),
        ("rect", (0, 0)),
        ("IJlig;", (306, 0)),
        ("mscr", (0, 0)),
        ("crarr;", (8629, 0)),
        ("vartriangler", (0, 0)),
        ("tcy", (0, 0)),
        ("varpr", (0, 0)),
        ("InvisibleTimes;", (8290, 0)),
        ("urtri;", (9721, 0)),
        ("Hci", (0, 0)),
        ("UpEq", (0, 0)),
        ("LeftVectorB", (0, 0)),
        ("rmoustache;", (9137, 0)),
        ("ngeqsla", (0, 0)),
        ("NotEqual;", (8800, 0)),
        ("imac", (0, 0)),
        ("quater", (0, 0)),
        ("Tf", (0, 0)),
        ("nvge", (0, 0)),
        ("LeftUpTeeV", (0, 0)),
        ("fallingd", (0, 0)),
        ("Zs", (0, 0)),
        ("Fouriertr", (0, 0)),
        ("lessdot", (0, 0)),
        ("Ec", (0, 0)),
        ("nsce;", (10928, 824)),
        ("tcaron;", (357, 0)),
        ("seAr", (0, 0)),
        ("agra", (0, 0)),
        ("GreaterGreater", (0, 0)),
        ("ug", (0, 0)),
        ("NotSucceedsTilde", (0, 0)),
        ("UpAr", (0, 0)),
        ("hopf;", (120153, 0)),
        ("NotLessSl", (0, 0)),
        ("iinfin;", (10716, 0)),
        ("dlcro", (0, 0)),
        ("bet", (0, 0)),
        ("cir;", (9675, 0)),
        ("iacute", (237, 0)),
        ("gtl", (0, 0)),
        ("olcross;", (10683, 0)),
        ("Non", (0, 0)),
        ("impe", (0, 0)),
        ("Exponential", (0, 0)),
        ("VeryThinSpace", (0, 0)),
        ("CloseCurlyD", (0, 0)),
        ("ef", (0, 0)),
        ("Conin", (0, 0)),
        ("za", (0, 0)),
        ("DoubleLongLeftArrow;", (10232, 0)),
        ("dsol", (0, 0)),
        ("ra", (0, 0)),
        ("hellip;", (8230, 0)),
        ("loz;", (9674, 0)),
        ("Cayleys", (0, 0)),
        ("RightDownTeeVe", (0, 0)),
        ("Si", (0, 0)),
        ("ohbar;", (10677, 0)),
        ("U", (0, 0)),
        ("trit", (0, 0)),
        ("pl", (0, 0)),
        ("timesd", (0, 0)),
        ("nless;", (8814, 0)),
        ("sqsupseteq;", (8850, 0)),
        ("Vda", (0, 0)),
        ("SquareSupersetEqual", (0, 0)),
        ("ShortDownArrow", (0, 0)),
        ("Edo", (0, 0)),
        ("DownRightTeeVector;", (10591, 0)),
        ("CounterClockwiseContourInteg", (0, 0)),
        ("lap;", (10885, 0)),
        ("SquareSuperset", (0, 0)),
        ("Lower", (0, 0)),
        ("check", (0, 0)),
        ("Ca", (0, 0)),
        ("looparrowright;", (8620, 0)),
        ("CapitalDif", (0, 0)),
        ("ncongdot", (0, 0)),
        ("ub", (0, 0)),
        ("nwa", (0, 0)),
        ("Mellintr", (0, 0)),
        ("elinters", (0, 0)),
        ("lessappr", (0, 0)),
        ("perm", (0, 0)),
        ("Succeed", (0, 0)),
        ("odsold;", (10684, 0)),
        ("Ep", (0, 0)),
        ("DZc", (0, 0)),
        ("wei", (0, 0)),
        ("Cong", (0, 0)),
        ("preccurlye", (0, 0)),
        ("nparallel;", (8742, 0)),
        ("NotLeftTriangleBar;", (10703, 824)),
        ("Pfr;", (120083, 0)),
        ("GreaterGreater;", (10914, 0)),
        ("Odblac", (0, 0)),
        ("xd", (0, 0)),
        ("puncsp", (0, 0)),
        ("ntrianglelef", (0, 0)),
        ("gtcc;", (10919, 0)),
        ("scnE", (0, 0)),
        ("osc", (0, 0)),
        ("rAarr", (0, 0)),
        ("boxdr;", (9484, 0)),
        ("rarrsi", (0, 0)),
        ("Sacute", (0, 0)),
        ("NegativeThinSpace", (0, 0)),
        ("Nega", (0, 0)),
        ("LeftArrowRightArrow;", (8646, 0)),
        ("ecaron;", (283, 0)),
        ("Les", (0, 0)),
        ("sim;", (8764, 0)),
        ("bot;", (8869, 0)),
        ("ShortRightArr", (0, 0)),
        ("blacktrianglerigh", (0, 0)),
        ("zsc", (0, 0)),
        ("expone", (0, 0)),
        ("NotLessL", (0, 0)),
        ("ors", (0, 0)),
        ("bNot;", (10989, 0)),
        ("Upper", (0, 0)),
        ("Le", (0, 0)),
        ("gtrapprox", (0, 0)),
        ("tprime", (0, 0)),
        ("veeb", (0, 0)),
        ("nvinf", (0, 0)),
        ("ffi", (0, 0)),
        ("Dagger", (0, 0)),
        ("Kscr", (0, 0)),
        ("vartr", (0, 0)),
        ("smeparsl", (0, 0)),
        ("star", (0, 0)),
        ("rightsqu", (0, 0)),
        ("die;", (168, 0)),
        ("Pop", (0, 0)),
        ("divon", (0, 0)),
        ("nle;", (8816, 0)),
        ("larrsi", (0, 0)),
        ("nequ", (0, 0)),
        ("rightleftarrows", (0, 0)),
        ("q", (0, 0)),
        ("Abreve;", (258, 0)),
        ("dop", (0, 0)),
        ("longlefta", (0, 0)),
        ("LeftRi", (0, 0)),
        ("bulle", (0, 0)),
        ("nvrtri", (0, 0)),
        ("Mopf;", (120132, 0)),
        ("hcirc;", (293, 0)),
        ("incar", (0, 0)),
        ("RightTrian", (0, 0)),
        ("precne", (0, 0)),
        ("target;", (8982, 0)),
        ("Ocirc;", (212, 0)),
        ("ama", (0, 0)),
        ("prcue", (0, 0)),
        ("uHar", (0, 0)),
        ("IJ", (0, 0)),
        ("lsimg;", (10895, 0)),
        ("simeq;", (8771, 0)),
        ("TS", (0, 0)),
        ("solbar", (0, 0)),
        ("rarrsim", (0, 0)),
        ("asym", (0, 0)),
        ("ggg;", (8921, 0)),
        ("raempty", (0, 0)),
        ("hookrightarr", (0, 0)),
        ("bow", (0, 0)),
        ("CircleMinu", (0, 0)),
        ("nshortpara", (0, 0)),
        ("nrightar", (0, 0)),
        ("straigh", (0, 0)),
        ("DoubleLeftRightArrow;", (8660, 0)),
        ("gesdo", (0, 0)),
        ("bbrkt", (0, 0)),
        ("DoubleRightTee;", (8872, 0)),
        ("rrar", (0, 0)),
        ("SquareUnion;", (8852, 0)),
        ("NotGreaterGre", (0, 0)),
        ("sqcap;", (8851, 0)),
        ("DotEqua", (0, 0)),
        ("RightArr", (0, 0)),
        ("Ju", (0, 0)),
        ("sqsupseteq", (0, 0)),
        ("ltrPa", (0, 0)),
        ("NotSquareSuper", (0, 0)),
        ("orv;", (10843, 0)),
        ("varsigma", (0, 0)),
        ("andslop", (0, 0)),
        ("nwarrow;", (8598, 0)),
        ("CloseCurl", (0, 0)),
        ("copf", (0, 0)),
        ("UpDown", (0, 0)),
        ("bk", (0, 0)),
        ("Pro", (0, 0)),
        ("NegativeVeryThinSpace", (0, 0)),
        ("tripl", (0, 0)),
        ("frac14", (188, 0)),
        ("Lambd", (0, 0)),
        ("Jcirc;", (308, 0)),
        ("npo", (0, 0)),
        ("yfr", (0, 0)),
        ("DoubleU", (0, 0)),
        ("mstp", (0, 0)),
        ("Rightarr", (0, 0)),
        ("que", (0, 0)),
        ("divide", (247, 0)),
        ("Cced", (0, 0)),
        ("bNo", (0, 0)),
        ("swnwar", (0, 0)),
        ("dlcor", (0, 0)),
        ("time", (0, 0)),
        ("thk", (0, 0)),
        ("ltcir", (0, 0)),
        ("sup3", (179, 0)),
        ("Ncaron", (0, 0)),
        ("Therefore;", (8756, 0)),
        ("CloseCurlyQuo", (0, 0)),
        ("NotSubset;", (8834, 8402)),
        ("ruluhar", (0, 0)),
        ("blk1", (0, 0)),
        ("lcub", (0, 0)),
        ("period;", (46, 0)),
        ("vzigz", (0, 0)),
        ("tos", (0, 0)),
        ("nwn", (0, 0)),
        ("Id", (0, 0)),
        ("ddots", (0, 0)),
        ("eqvpa", (0, 0)),
        ("intprod", (0, 0)),
        ("scna", (0, 0)),
        ("Medium", (0, 0)),
        ("ffr", (0, 0)),
        ("ii;", (8520, 0)),
        ("CounterClockwiseCont", (0, 0)),
        ("boxur;", (9492, 0)),
        ("colo", (0, 0)),
        ("trianglerig", (0, 0)),
        ("latai", (0, 0)),
        ("IEc", (0, 0)),
        ("awco", (0, 0)),
        ("rightharpoondown", (0, 0)),
        ("shortmid", (0, 0)),
        ("LeftUpTeeVecto", (0, 0)),
        ("Longleftrigh", (0, 0)),
        ("Tcar", (0, 0)),
        ("LeftUpDo", (0, 0)),
        ("checkmark", (0, 0)),
        ("Rarr;", (8608, 0)),
        ("isindot;", (8949, 0)),
        ("Rac", (0, 0)),
        ("solba", (0, 0)),
        ("dda", (0, 0)),
        ("sc", (0, 0)),
        ("infin;", (8734, 0)),
        ("npr", (0, 0)),
        ("prnE", (0, 0)),
        ("NotRightT", (0, 0)),
        ("NegativeMediumSpace;", (8203, 0)),
        ("alpha;", (945, 0)),
        ("Ccaro", (0, 0)),
        ("NotTildeTilde;", (8777, 0)),
        ("rca", (0, 0)),
        ("geqslant;", (10878, 0)),
        ("bsolh", (0, 0)),
        ("mapstodo", (0, 0)),
        ("NotLessGrea", (0, 0)),
        ("RightAngleBr", (0, 0)),
        ("middo", (0, 0)),
        ("lcaron;", (318, 0)),
        ("supedo", (0, 0)),
        ("iogon", (0, 0)),
        ("GreaterTilde", (0, 0)),
        ("PrecedesSlant", (0, 0)),
        ("HA", (0, 0)),
        ("leftrighta", (0, 0)),
        ("boxdl", (0, 0)),
        ("NotN", (0, 0)),
        ("LeftVectorBa", (0, 0)),
        ("Cacute;", (262, 0)),
        ("ae", (0, 0)),
        ("suped", (0, 0)),
        ("infi", (0, 0)),
        ("Esi", (0, 0)),
        ("gnapprox", (0, 0)),
        ("uha", (0, 0)),
        ("Br", (0, 0)),
        ("divideontimes", (0, 0)),
        ("heartsui", (0, 0)),
        ("Dc", (0, 0)),
        ("boxvr", (0, 0)),
        ("EmptyS", (0, 0)),
        ("cirscir;", (10690, 0)),
        ("egsd", (0, 0)),
        ("hookleft", (0, 0)),
        ("nvr", (0, 0)),
        ("lne", (0, 0)),
        ("ati", (0, 0)),
        ("NotTildeEqual", (0, 0)),
        ("NotSucceeds", (0, 0)),
        ("LeftDownVector", (0, 0)),
        ("Ffr;", (120073, 0)),
        ("Sfr;", (120086, 0)),
        ("eng;", (331, 0)),
        ("lurdshar;", (10570, 0)),
        ("NotVer", (0, 0)),
        ("frac78", (0, 0)),
        ("LeftUpTeeVector", (0, 0)),
        ("longleftrightarro", (0, 0)),
        ("nleftarr", (0, 0)),
        ("Thick", (0, 0)),
        ("Llefta", (0, 0)),
        ("VerticalSepa", (0, 0)),
        ("Sq", (0, 0)),
        ("TRADE;", (8482, 0)),
        ("Diamon", (0, 0)),
        ("ell;", (8467, 0)),
        ("Cop", (0, 0)),
        ("Zfr", (0, 0)),
        ("succsim;", (8831, 0)),
        ("NonBrea", (0, 0)),
        ("Rcaron;", (344, 0)),
        ("rth", (0, 0)),
        ("nparalle", (0, 0)),
        ("Wcirc;", (372, 0)),
        ("jukcy", (0, 0)),
        ("dwang", (0, 0)),
        ("SuchTha", (0, 0)),
        ("varsubset", (0, 0)),
        ("Vfr;", (120089, 0)),
        ("nsubse", (0, 0)),
        ("hop", (0, 0)),
        ("multimap", (0, 0)),
        ("nvd", (0, 0)),
        ("NotLeftTri", (0, 0)),
        ("curvearrowl", (0, 0)),
        ("tilde", (0, 0)),
        ("zacu", (0, 0)),
        ("zcy", (0, 0)),
        ("PrecedesTilde", (0, 0)),
        ("DoubleLeftR", (0, 0)),
        ("Larr;", (8606, 0)),
        ("Ss", (0, 0)),
        ("zacut", (0, 0)),
        ("Poincarepla", (0, 0)),
        ("urcorn;", (8989, 0)),
        ("boxUl", (0, 0)),
        ("Rc", (0, 0)),
        ("lbarr", (0, 0)),
        ("vartriangleright", (0, 0)),
        ("eqcolon", (0, 0)),
        ("CapitalDiffe", (0, 0)),
        ("subsetneqq;", (10955, 0)),
        ("Dcaro", (0, 0)),
        ("ldsh;", (8626, 0)),
        ("ccup", (0, 0)),
        ("CircleTimes;", (8855, 0)),
        ("coprod;", (8720, 0)),
        ("RightDownVec", (0, 0)),
        ("SquareSup", (0, 0)),
        ("nprc", (0, 0)),
        ("sr", (0, 0)),
        ("rea", (0, 0)),
        ("LeftUpTeeVector;", (10592, 0)),
        ("midci", (0, 0)),
        ("upl", (0, 0)),
        ("DoubleContourIntegral;", (8751, 0)),
        ("RightDownVectorBa", (0, 0)),
        ("Zcy", (0, 0)),
        ("NotSquareSupersetE", (0, 0)),
        ("UpA", (0, 0)),
        ("circlearro", (0, 0)),
        ("gescc;", (10921, 0)),
        ("dtrif;", (9662, 0)),
        ("Ig", (0, 0)),
        ("NotGreaterSlantEqual;", (10878, 824)),
        ("die", (0, 0)),
        ("NotSupersetE", (0, 0)),
        ("dollar", (0, 0)),
        ("tritime;", (10811, 0)),
        ("digamma", (0, 0)),
        ("CapitalDifferen", (0, 0)),
        ("ffili", (0, 0)),
        ("scedil;", (351, 0)),
        ("delt", (0, 0)),
        ("sqsupe", (0, 0)),
        ("nc", (0, 0)),
        ("NotSucceedsEqu", (0, 0)),
        ("NoBreak;", (8288, 0)),
        ("NegativeVeryThinSp", (0, 0)),
        ("ShortLeftArr", (0, 0)),
        ("SOF", (0, 0)),
        ("egr", (0, 0)),
        ("CHc", (0, 0)),
        ("NestedLessLess", (0, 0)),
        ("leftr", (0, 0)),
        ("SucceedsTilde", (0, 0)),
        ("Square", (0, 0)),
        ("Oo", (0, 0)),
        ("d", (0, 0)),
        ("varsupsetneqq", (0, 0)),
        ("varsi", (0, 0)),
        ("nlE;", (8806, 824)),
        ("Lm", (0, 0)),
        ("rtriltri;", (10702, 0)),
        ("dfisht;", (10623, 0)),
        ("DoubleRightTee", (0, 0)),
        ("ltcc", (0, 0)),
        ("triti", (0, 0)),
        ("loa", (0, 0)),
        ("Dscr", (0, 0)),
        ("rtriltr", (0, 0)),
        ("nRightarr", (0, 0)),
        ("ngsi", (0, 0)),
        ("curlyeqsucc;", (8927, 0)),
        ("horbar;", (8213, 0)),
        ("Kcy;", (1050, 0)),
        ("LeftUpVectorBar", (0, 0)),
        ("DoubleVerticalB", (0, 0)),
        ("subdot", (0, 0)),
        ("fro", (0, 0)),
        ("ThinSpac", (0, 0)),
        ("Ograve", (210, 0)),
        ("lti", (0, 0)),
        ("copy;", (169, 0)),
        ("cro", (0, 0)),
        ("DoubleVertic", (0, 0)),
        ("prnsim;", (8936, 0)),
        ("kj", (0, 0)),
        ("lsqu", (0, 0)),
        ("Igrave;", (204, 0)),
        ("DoubleRightT", (0, 0)),
        ("Alpha;", (913, 0)),
        ("Lan", (0, 0)),
        ("llcorner", (0, 0)),
        ("risingdotseq", (0, 0)),
        ("olcro", (0, 0)),
        ("MinusPlu", (0, 0)),
        ("gtdot", (0, 0)),
        ("bsolhs", (0, 0)),
        ("hb", (0, 0)),
        ("hkswarow;", (10534, 0)),
        ("dbkaro", (0, 0)),
        ("SquareSubsetEqu", (0, 0)),
        ("lmous", (0, 0)),
        ("AM", (0, 0)),
        ("tpri", (0, 0)),
        ("ofr", (0, 0)),
        ("ZHcy;", (1046, 0)),
        ("equiv", (0, 0)),
        ("EmptyVe", (0, 0)),
        ("eqslantless;", (10901, 0)),
        ("upharpoonrigh", (0, 0)),
        ("eng", (0, 0)),
        ("Lo", (0, 0)),
        ("Invisi", (0, 0)),
        ("larrhk", (0, 0)),
        ("nbumpe;", (8783, 824)),
        ("nshortparalle", (0, 0)),
        ("lsaquo;", (8249, 0)),
        ("nLeftrig", (0, 0)),
        ("omi", (0, 0)),
        ("lu", (0, 0)),
        ("Cedi", (0, 0)),
        ("bnot;", (8976, 0)),
        ("Dca", (0, 0)),
        ("xcap;", (8898, 0)),
        ("ca", (0, 0)),
        ("fpa", (0, 0)),
        ("UpDownArro", (0, 0)),
        ("NotTildeFullEqual;", (8775, 0)),
        ("Pa", (0, 0)),
        ("iiint", (0, 0)),
        ("plankv", (0, 0)),
        ("curr", (0, 0)),
        ("shc", (0, 0)),
        ("At", (0, 0)),
        ("plusmn", (177, 0)),
        ("lates", (0, 0)),
        ("cent;", (162, 0)),
        ("dbka", (0, 0)),
        ("Sum", (0, 0)),
        ("NotCo", (0, 0)),
        ("lurdsh", (0, 0)),
        ("nhAr", (0, 0)),
        ("UnderBa", (0, 0)),
        ("vsubn", (0, 0)),
        ("rdsh;", (8627, 0)),
        ("leftth", (0, 0)),
        ("SubsetEqual", (0, 0)),
        ("EqualTilde;", (8770, 0)),
        ("lnE;", (8808, 0)),
        ("dou", (0, 0)),
        ("telre", (0, 0)),
        ("DownArrowB", (0, 0)),
        ("nsccu", (0, 0)),
        ("Colon;", (8759, 0)),
        ("bac", (0, 0)),
        ("UpperRi", (0, 0)),
        ("longleftrigh", (0, 0)),
        ("Eta;", (919, 0)),
        ("NotNestedGre", (0, 0)),
        ("Nopf", (0, 0)),
        ("Upar", (0, 0)),
        ("circleddas", (0, 0)),
        ("rcaron;", (345, 0)),
        ("ApplyF", (0, 0)),
        ("djcy;", (1106, 0)),
        ("succnsi", (0, 0)),
        ("xra", (0, 0)),
        ("frac3", (0, 0)),
        ("succneq", (0, 0)),
        ("eqsla", (0, 0)),
        ("NotNestedG", (0, 0)),
        ("upsi;", (965, 0)),
        ("CounterClockwiseContourIntegr", (0, 0)),
        ("subdo", (0, 0)),
        ("Leftright", (0, 0)),
        ("GreaterEqua", (0, 0)),
        ("Lsh", (0, 0)),
        ("itilde", (0, 0)),
        ("SmallCircle;", (8728, 0)),
        ("diamond", (0, 0)),
        ("ApplyFunction;", (8289, 0)),
        ("iiota;", (8489, 0)),
        ("upharpo", (0, 0)),
        ("ContourIntegral", (0, 0)),
        ("ffilig;", (64259, 0)),
        ("NotEqualTi", (0, 0)),
        ("gvertneq", (0, 0)),
        ("NotPre", (0, 0)),
        ("fflig", (0, 0)),
        ("varsubsetneq;", (8842, 65024)),
        ("NotSquareSup", (0, 0)),
        ("models", (0, 0)),
        ("nLefta", (0, 0)),
        ("drbkarow", (0, 0)),
        ("DoubleUpDownArr", (0, 0)),
        ("xcirc", (0, 0)),
        ("empty", (0, 0)),
        ("CounterClockw", (0, 0)),
        ("quatint;", (10774, 0)),
        ("LeftDou", (0, 0)),
        ("LeftTeeVecto", (0, 0)),
        ("dotsquare;", (8865, 0)),
        ("bernou", (0, 0)),
        ("Omicro", (0, 0)),
        ("colone", (0, 0)),
        ("xh", (0, 0)),
        ("varnothin", (0, 0)),
        ("pound;", (163, 0)),
        ("gtc", (0, 0)),
        ("LowerLeftA", (0, 0)),
        ("xoplu", (0, 0)),
        ("geqsla", (0, 0)),
        ("uogo", (0, 0)),
        ("epar", (0, 0)),
        ("cacut", (0, 0)),
        ("apos", (0, 0)),
        ("xdtri;", (9661, 0)),
        ("CupCap", (0, 0)),
        ("iti", (0, 0)),
        ("Breve", (0, 0)),
        ("utilde", (0, 0)),
        ("Ycirc", (0, 0)),
        ("Rcy;", (1056, 0)),
        ("NotSubse", (0, 0)),
        ("napE;", (10864, 824)),
        ("lat", (0, 0)),
        ("UpDownArrow;", (8597, 0)),
        ("pluse", (0, 0)),
        ("ShortDow", (0, 0)),
        ("OverBracke", (0, 0)),
        ("vprop", (0, 0)),
        ("flt", (0, 0)),
        ("Rf", (0, 0)),
        ("lbr", (0, 0)),
        ("DoubleRightTe", (0, 0)),
        ("conint", (0, 0)),
        ("SucceedsSlantEqual;", (8829, 0)),
        ("DownArrow;", (8595, 0)),
        ("LessGreat", (0, 0)),
        ("Ru", (0, 0)),
        ("exis", (0, 0)),
        ("csub;", (10959, 0)),
        ("lcaron", (0, 0)),
        ("ntrianglel", (0, 0)),
        ("ip", (0, 0)),
        ("Aopf;", (120120, 0)),
        ("Phi", (0, 0)),
        ("TildeTild", (0, 0)),
        ("rightright", (0, 0)),
        ("ffr;", (120099, 0)),
        ("ograve", (242, 0)),
        ("cacute;", (263, 0)),
        ("NotVert", (0, 0)),
        ("scedi", (0, 0)),
        ("succcurlyeq", (0, 0)),
        ("Bs", (0, 0)),
        ("weierp", (0, 0)),
        ("lesss", (0, 0)),
        ("backc", (0, 0)),
        ("rpar;", (41, 0)),
        ("LeftFloor", (0, 0)),
        ("yic", (0, 0)),
        ("Omacr;", (332, 0)),
        ("nabla;", (8711, 0)),
        ("gne;", (10888, 0)),
        ("nvDas", (0, 0)),
        ("reg;", (174, 0)),
        ("CounterClockwiseContourI", (0, 0)),
        ("geqsl", (0, 0)),
        ("lobrk;", (10214, 0)),
        ("oslas", (0, 0)),
        ("Ds", (0, 0)),
        ("Odbla", (0, 0)),
        ("bigstar", (0, 0)),
        ("olc", (0, 0)),
        ("perte", (0, 0)),
        ("dotp", (0, 0)),
        ("DoubleUpArro", (0, 0)),
        ("gtrdot;", (8919, 0)),
        ("Jscr;", (119973, 0)),
        ("tcedil", (0, 0)),
        ("Mel", (0, 0)),
        ("gtrapprox;", (10886, 0)),
        ("dotmi", (0, 0)),
        ("harrw", (0, 0)),
        ("ntg", (0, 0)),
        ("EmptySmall", (0, 0)),
        ("CapitalD", (0, 0)),
        ("cudarr", (0, 0)),
        ("lvert", (0, 0)),
        ("subdot;", (10941, 0)),
        ("sup3;", (179, 0)),
        ("minu", (0, 0)),
        ("yicy", (0, 0)),
        ("boxVr;", (9567, 0)),
        ("Ncar", (0, 0)),
        ("otild", (0, 0)),
        ("RightTriangl", (0, 0)),
        ("UpperRightArrow", (0, 0)),
        ("subedo", (0, 0)),
        ("dr", (0, 0)),
        ("GreaterFullEqual;", (8807, 0)),
        ("RightAngleBrack", (0, 0)),
        ("we", (0, 0)),
        ("DiacriticalGrave", (0, 0)),
        ("Oscr;", (119978, 0)),
        ("zigrarr;", (8669, 0)),
        ("Eci", (0, 0)),
        ("LeftTriangleBar", (0, 0)),
        ("lozf", (0, 0)),
        ("Ns", (0, 0)),
        ("omeg", (0, 0)),
        ("sime;", (8771, 0)),
        ("gtrless;", (8823, 0)),
        ("Cen", (0, 0)),
        ("Ia", (0, 0)),
        ("mnplu", (0, 0)),
        ("lae", (0, 0)),
        ("primes;", (8473, 0)),
        ("acd", (0, 0)),
        ("NotNeste", (0, 0)),
        ("nhArr", (0, 0)),
        ("ltcir;", (10873, 0)),
        ("thinsp;", (8201, 0)),
        ("lt", (60, 0)),
        ("ApplyFunct", (0, 0)),
        ("nsubseteq;", (8840, 0)),
        ("lesssim;", (8818, 0)),
        ("dstro", (0, 0)),
        ("Tab", (0, 0)),
        ("circlearrowle", (0, 0)),
        ("LessLes", (0, 0)),
        ("Squa", (0, 0)),
        ("Topf", (0, 0)),
        ("eqvpars", (0, 0)),
        ("UnderBrac", (0, 0)),
        ("quaterni", (0, 0)),
        ("curlye", (0, 0)),
        ("dagg", (0, 0)),
        ("profalar;", (9006, 0)),
        ("frac15;", (8533, 0)),
        ("Longrightarrow", (0, 0)),
        ("bigsqcup;", (10758, 0)),
        ("Uppe", (0, 0)),
        ("GreaterSlan", (0, 0)),
        ("Lleftar", (0, 0)),
        ("trpe", (0, 0)),
        ("SucceedsEqual;", (10928, 0)),
        ("NegativeMediumSpace", (0, 0)),
        ("wedba", (0, 0)),
        ("leftle", (0, 0)),
        ("RightVector", (0, 0)),
        ("bsol;", (92, 0)),
        ("Acy", (0, 0)),
        ("dzigrarr;", (10239, 0)),
        ("double", (0, 0)),
        ("SquareIn", (0, 0)),
        ("emsp", (0, 0)),
        ("thetasym;", (977, 0)),
        ("Xop", (0, 0)),
        ("cwi", (0, 0)),
        ("Mcy", (0, 0)),
        ("ltcc;", (10918, 0)),
        ("vsubne;", (8842, 65024)),
        ("cfr;", (120096, 0)),
        ("thorn;", (254, 0)),
        ("Lar", (0, 0)),
        ("fllig;", (64258, 0)),
        ("foral", (0, 0)),
        ("boxvh", (0, 0)),
        ("boxdl;", (9488, 0)),
        ("DoubleContourInt", (0, 0)),
        ("GJcy;", (1027, 0)),
        ("shchcy", (0, 0)),
        ("Uopf", (0, 0)),
        ("ze", (0, 0)),
        ("at", (0, 0)),
        ("model", (0, 0)),
        ("uharl", (0, 0)),
        ("prec", (0, 0)),
        ("UnderParenth", (0, 0)),
        ("NotSucceedsSlantEqual", (0, 0)),
        ("curve", (0, 0)),
        ("rnmi", (0, 0)),
        ("ClockwiseCon", (0, 0)),
        ("Rar", (0, 0)),
        ("nsubs", (0, 0)),
        ("sccu", (0, 0)),
        ("SucceedsSla", (0, 0)),
        ("atild", (0, 0)),
        ("ddotse", (0, 0)),
        ("curarrm;", (10556, 0)),
        ("LeftDoubleB", (0, 0)),
        ("exponenti", (0, 0)),
        ("apid", (0, 0)),
        ("awconint;", (8755, 0)),
        ("te", (0, 0)),
        ("ult", (0, 0)),
        ("LessT", (0, 0)),
        ("uarr", (0, 0)),
        ("Jfr", (0, 0)),
        ("radic", (0, 0)),
        ("NotLeftTriang", (0, 0)),
        ("intprod;", (10812, 0)),
        ("xf", (0, 0)),
        ("GreaterSlantEq", (0, 0)),
        ("ngeqq;", (8807, 824)),
        ("prcue;", (8828, 0)),
        ("bigwedg", (0, 0)),
        ("dH", (0, 0)),
        ("gesdotol;", (10884, 0)),
        ("ShortDownA", (0, 0)),
        ("div;", (247, 0)),
        ("dfi", (0, 0)),
        ("larrt", (0, 0)),
        ("varrho", (0, 0)),
        ("NotTildeFullE", (0, 0)),
        ("intlarhk;", (10775, 0)),
        ("upsi", (0, 0)),
        ("dcaro", (0, 0)),
        ("Gfr", (0, 0)),
        ("nequiv", (0, 0)),
        ("lobr", (0, 0)),
        ("Euml;", (203, 0)),
        ("NotCongrue", (0, 0)),
        ("nG", (0, 0)),
        ("vzi", (0, 0)),
        ("NegativeMediumSpa", (0, 0)),
        ("Plu", (0, 0)),
        ("Ccaron", (0, 0)),
        ("nj", (0, 0)),
        ("roarr;", (8702, 0)),
        ("rtril", (0, 0)),
        ("nwnear", (0, 0)),
        ("hksear", (0, 0)),
        ("ssmile;", (8995, 0)),
        ("numero", (0, 0)),
        ("Kapp", (0, 0)),
        ("parsim;", (10995, 0)),
        ("ccupssm", (0, 0)),
        ("AE", (0, 0)),
        ("Nc", (0, 0)),
        ("Supe", (0, 0)),
        ("angrt", (0, 0)),
        ("RightTriangleEq", (0, 0)),
        ("DoubleLongLeftR", (0, 0)),
        ("lcedi", (0, 0)),
        ("in", (0, 0)),
        ("rightri", (0, 0)),
        ("RightFl", (0, 0)),
        ("LessEqualGreater;", (8922, 0)),
        ("upsih;", (978, 0)),
        ("frac38", (0, 0)),
        ("EmptyVeryS", (0, 0)),
        ("hook", (0, 0)),
        ("blk3", (0, 0)),
        ("longrightarrow;", (10230, 0)),
        ("NotLessGreat", (0, 0)),
        ("RightArrow;", (8594, 0)),
        ("EqualTilde", (0, 0)),
        ("Par", (0, 0)),
        ("rcaro", (0, 0)),
        ("sqcups", (0, 0)),
        ("comma", (0, 0)),
        ("cirm", (0, 0)),
        ("InvisibleCom", (0, 0)),
        ("dblac", (0, 0)),
        ("PrecedesSla", (0, 0)),
        ("NotLe", (0, 0)),
        ("xotime", (0, 0)),
        ("Vdash;", (8873, 0)),
        ("HumpDownHu", (0, 0)),
        ("lf", (0, 0)),
        ("sbq", (0, 0)),
        ("succeq", (0, 0)),
        ("DownLeftVector;", (8637, 0)),
        ("gvertne", (0, 0)),
        ("NegativeMedium", (0, 0)),
        ("lesdot", (0, 0)),
        ("expo", (0, 0)),
        ("nldr;", (8229, 0)),
        ("So", (0, 0)),
        ("lobrk", (0, 0)),
        ("chec", (0, 0)),
        ("RightUpTe", (0, 0)),
        ("xhA", (0, 0)),
        ("ntrianglelefteq", (0, 0)),
        ("GreaterFullE", (0, 0)),
        ("SHC", (0, 0)),
        ("Fouriert", (0, 0)),
        ("str", (0, 0)),
        ("IJli", (0, 0)),
        ("imath;", (305, 0)),
        ("lmoustache;", (9136, 0)),
        ("minusd", (0, 0)),
        ("uogon;", (371, 0)),
        ("erarr;", (10609, 0)),
        ("Cup", (0, 0)),
        ("and", (0, 0)),
        ("dbkarow;", (10511, 0)),
        ("cularr", (0, 0)),
        ("NJ", (0, 0)),
        ("succcurlyeq;", (8829, 0)),
        ("drcr", (0, 0)),
        ("nlarr", (0, 0)),
        ("PlusMinu", (0, 0)),
        ("nacute", (0, 0)),
        ("SHcy;", (1064, 0)),
        ("SupersetEqual;", (8839, 0)),
        ("Lleftarrow;", (8666, 0)),
        ("DoubleL", (0, 0)),
        ("Ecaro", (0, 0)),
        ("uh", (0, 0)),
        ("oast;", (8859, 0)),
        ("IJl", (0, 0)),
        ("sup2;", (178, 0)),
        ("curvearrowleft", (0, 0)),
        ("vscr;", (120011, 0)),
        ("equest", (0, 0)),
        ("RightVect", (0, 0)),
        ("varsubsetneq", (0, 0)),
        ("Yci", (0, 0)),
        ("UnderPare", (0, 0)),
        ("lot", (0, 0)),
        ("triplu", (0, 0)),
        ("Oslash;", (216, 0)),
        ("Down", (0, 0)),
        ("RightTeeVecto", (0, 0)),
        ("dh", (0, 0)),
        ("supl", (0, 0)),
        ("nisd", (0, 0)),
        ("nacu", (0, 0)),
        ("xrArr", (0, 0)),
        ("vart", (0, 0)),
        ("ntri", (0, 0)),
        ("frac1", (0, 0)),
        ("bsi", (0, 0)),
        ("ya", (0, 0)),
        ("DoubleLongLeft", (0, 0)),
        ("Icirc;", (206, 0)),
        ("notindot;", (8949, 824)),
        ("two", (0, 0)),
        ("twohe", (0, 0)),
        ("SupersetEqu", (0, 0)),
        ("gacu", (0, 0)),
        ("leftrightsquigarrow", (0, 0)),
        ("ratail", (0, 0)),
        ("UnionPl", (0, 0)),
        ("bemp", (0, 0)),
        ("notni;", (8716, 0)),
        ("hellip", (0, 0)),
        ("pun", (0, 0)),
        ("DoubleLongLeftRightA", (0, 0)),
        ("eqslantgtr", (0, 0)),
        ("n", (0, 0)),
        ("longmapsto", (0, 0)),
        ("longrightarro", (0, 0)),
        ("NotReve", (0, 0)),
        ("brvbar;", (166, 0)),
        ("Gt;", (8811, 0)),
        ("tcedil;", (355, 0)),
        ("RightFloo", (0, 0)),
        ("Sup", (0, 0)),
        ("Ugr", (0, 0)),
        ("Esc", (0, 0)),
        ("circledR;", (174, 0)),
        ("hksw", (0, 0)),
        ("Diacr", (0, 0)),
        ("RightAngle", (0, 0)),
        ("precap", (0, 0)),
        ("topbot;", (9014, 0)),
        ("oe", (0, 0)),
        ("NotReverseEl", (0, 0)),
        ("supset", (0, 0)),
        ("lesg;", (8922, 65024)),
        ("EmptySmallSquare", (0, 0)),
        ("isc", (0, 0)),
        ("tosa;", (10537, 0)),
        ("EmptyV", (0, 0)),
        ("yucy", (0, 0)),
        ("NotRightTriangleEqual", (0, 0)),
        ("InvisibleComma", (0, 0)),
        ("OverParen", (0, 0)),
        ("ltques", (0, 0)),
        ("Otimes", (0, 0)),
        ("inf", (0, 0)),
        ("prime", (0, 0)),
        ("dempt", (0, 0)),
        ("Tau;", (932, 0)),
        ("simrar", (0, 0)),
        ("FilledSma", (0, 0)),
        ("ia", (0, 0)),
        ("qo", (0, 0)),
        ("minus;", (8722, 0)),
        ("NotSquareSuperse", (0, 0)),
        ("gbreve", (0, 0)),
        ("thickapprox;", (8776, 0)),
        ("ultr", (0, 0)),
        ("simeq", (0, 0)),
        ("Exponen", (0, 0)),
        ("nsp", (0, 0)),
        ("Iopf;", (120128, 0)),
        ("ocy;", (1086, 0)),
        ("subed", (0, 0)),
        ("drcrop;", (8972, 0)),
        ("twoheadl", (0, 0)),
        ("vo", (0, 0)),
        ("LeftDownVe", (0, 0)),
        ("Acy;", (1040, 0)),
        ("Nested", (0, 0)),
        ("Fc", (0, 0)),
        ("Eac", (0, 0)),
        ("nfr", (0, 0)),
        ("subsetneq;", (8842, 0)),
        ("nearr;", (8599, 0)),
        ("DownLeftVecto", (0, 0)),
        ("Gopf", (0, 0)),
        ("thinsp", (0, 0)),
        ("DownTeeA", (0, 0)),
        ("Ofr;", (120082, 0)),
        ("nvlt", (0, 0)),
        ("circleddash", (0, 0)),
        ("ldr", (0, 0)),
        ("aleph", (0, 0)),
        ("doub", (0, 0)),
        ("nac", (0, 0)),
        ("Zscr", (0, 0)),
        ("LeftCei", (0, 0)),
        ("LeftArrowR", (0, 0)),
        ("NotGreaterGrea", (0, 0)),
        ("rangl", (0, 0)),
        ("NotReverseEleme", (0, 0)),
        ("puncsp;", (8200, 0)),
        ("lArr;", (8656, 0)),
        ("hookrightarro", (0, 0)),
        ("CenterDot", (0, 0)),
        ("LowerLeftArrow;", (8601, 0)),
        ("bsem", (0, 0)),
        ("zigrarr", (0, 0)),
        ("ecol", (0, 0)),
        ("rA", (0, 0)),
        ("xhar", (0, 0)),
        ("PrecedesEq", (0, 0)),
        ("Escr", (0, 0)),
        ("uhblk", (0, 0)),
        ("subsetn", (0, 0)),
        ("ApplyFun", (0, 0)),
        ("eacu", (0, 0)),
        ("rightrightar", (0, 0)),
        ("nda", (0, 0)),
        ("GreaterTil", (0, 0)),
        ("DownArrowBa", (0, 0)),
        ("centerd", (0, 0)),
        ("kc", (0, 0)),
        ("lltri", (0, 0)),
        ("DoubleUpArrow;", (8657, 0)),
        ("Zcar", (0, 0)),
        ("tau;", (964, 0)),
        ("angmsdae", (0, 0)),
        ("cupca", (0, 0)),
        ("Invis", (0, 0)),
        ("raquo", (187, 0)),
        ("SucceedsTi", (0, 0)),
        ("LessFullEqual;", (8806, 0)),
        ("sqsu", (0, 0)),
        ("equest;", (8799, 0)),
        ("nleqsl", (0, 0)),
        ("raem", (0, 0)),
        ("Double", (0, 0)),
        ("horba", (0, 0)),
        ("NotCon", (0, 0)),
        ("lE;", (8806, 0)),
        ("Dcy", (0, 0)),
        ("gra", (0, 0)),
        ("simgE", (0, 0)),
        ("semi", (0, 0)),
        ("mic", (0, 0)),
        ("CenterDot;", (183, 0)),
        ("ha", (0, 0)),
        ("setminus;", (8726, 0)),
        ("dotplus", (0, 0)),
        ("suc", (0, 0)),
        ("varpro", (0, 0)),
        ("gsime;", (10894, 0)),
        ("Sum;", (8721, 0)),
        ("iin", (0, 0)),
        ("nbs", (0, 0)),
        ("nvrArr;", (10499, 0)),
        ("ldrdha", (0, 0)),
        ("OpenCurlyDoubleQu", (0, 0)),
        ("ncu", (0, 0)),
        ("dHa", (0, 0)),
        ("boxv", (0, 0)),
        ("brev", (0, 0)),
        ("Tcy;", (1058, 0)),
        ("Oslas", (0, 0)),
        ("PrecedesSlantEqua", (0, 0)),
        ("DiacriticalTilde;", (732, 0)),
        ("Zcaron", (0, 0)),
        ("yucy;", (1102, 0)),
        ("dd;", (8518, 0)),
        ("harr;", (8596, 0)),
        ("DoubleRigh", (0, 0)),
        ("looparrowri", (0, 0)),
        ("orslope;", (10839, 0)),
        ("RuleDelayed", (0, 0)),
        ("xrar", (0, 0)),
        ("acy", (0, 0)),
        ("FilledS", (0, 0)),
        ("DoubleLeftRig", (0, 0)),
        ("varepsil", (0, 0)),
        ("RightDow", (0, 0)),
        ("Updownarrow", (0, 0)),
        ("nha", (0, 0)),
        ("oslash;", (248, 0)),
        ("Cayleys;", (8493, 0)),
        ("iiii", (0, 0)),
        ("subplus", (0, 0)),
        ("cuwed;", (8911, 0)),
        ("NotReverseElem", (0, 0)),
        ("suphsu", (0, 0)),
        ("vang", (0, 0)),
        ("ClockwiseContourI", (0, 0)),
        ("Vd", (0, 0)),
        ("fe", (0, 0)),
        ("Leftarrow;", (8656, 0)),
        ("NotR", (0, 0)),
        ("cra", (0, 0)),
        ("NotNestedLessL", (0, 0)),
        ("rsqb", (0, 0)),
        ("TripleDo", (0, 0)),
        ("gesd", (0, 0)),
        ("Longri", (0, 0)),
        ("block;", (9608, 0)),
        ("NestedGreater", (0, 0)),
        ("RightArrowBa", (0, 0)),
        ("KJc", (0, 0)),
        ("LeftTeeVector;", (10586, 0)),
        ("NotVerticalBar;", (8740, 0)),
        ("GreaterG", (0, 0)),
        ("LeftDown", (0, 0)),
        ("Eum", (0, 0)),
        ("Wop", (0, 0)),
        ("bpri", (0, 0)),
        ("br", (0, 0)),
        ("profsurf", (0, 0)),
        ("rharu", (0, 0)),
        ("SquareU", (0, 0)),
        ("strns", (0, 0)),
        ("boxdR;", (9554, 0)),
        ("larrh", (0, 0)),
        ("TripleDot", (0, 0)),
        ("FilledVerySmallSq", (0, 0)),
        ("scpoli", (0, 0)),
        ("YUcy", (0, 0)),
        ("xrAr", (0, 0)),
        ("Qfr;", (120084, 0)),
        ("NewLine;", (10, 0)),
        ("angmsdac;", (10666, 0)),
        ("nsucce", (0, 0)),
        ("wcirc;", (373, 0)),
        ("dem", (0, 0)),
        ("van", (0, 0)),
        ("NotSquareSupersetEqu", (0, 0)),
        ("Bop", (0, 0)),
        ("Exponent", (0, 0)),
        ("mapstolef", (0, 0)),
        ("prap", (0, 0)),
        ("squf", (0, 0)),
        ("DoubleRi", (0, 0)),
        ("origo", (0, 0)),
        ("plussi", (0, 0)),
        ("Longrig", (0, 0)),
        ("twoheadleftarro", (0, 0)),
        ("Uar", (0, 0)),
        ("Cce", (0, 0)),
        ("Parti", (0, 0)),
        ("drbkar", (0, 0)),
        ("SH", (0, 0)),
        ("nsubset", (0, 0)),
        ("semi;", (59, 0)),
        ("supsim", (0, 0)),
        ("YU", (0, 0)),
        ("igrave", (236, 0)),
        ("Aogo", (0, 0)),
        ("HumpDownH", (0, 0)),
        ("DownRightVectorBar", (0, 0)),
        ("ga", (0, 0)),
        ("geq;", (8805, 0)),
        ("Tau", (0, 0)),
        ("ratio", (0, 0)),
        ("DownRight", (0, 0)),
        ("aci", (0, 0)),
        ("NestedGreaterGreater;", (8811, 0)),
        ("gv", (0, 0)),
        ("Mi", (0, 0)),
        ("Bcy;", (1041, 0)),
        ("Tcaron", (0, 0)),
        ("boxv;", (9474, 0)),
        ("ua", (0, 0)),
        ("LeftDownVecto", (0, 0)),
        ("plusb", (0, 0)),
        ("NotSucceedsT", (0, 0)),
        ("LongLe", (0, 0)),
        ("RightDou", (0, 0)),
        ("bsolhsu", (0, 0)),
        ("cong;", (8773, 0)),
        ("rsqu", (0, 0)),
        ("rfisht", (0, 0)),
        ("app", (0, 0)),
        ("ClockwiseContourIntegral", (0, 0)),
        ("ale", (0, 0)),
        ("eques", (0, 0)),
        ("RightTeeVector", (0, 0)),
        ("leftarro", (0, 0)),
        ("ysc", (0, 0)),
        ("ne", (0, 0)),
        ("Backsla", (0, 0)),
        ("hkswarow", (0, 0)),
        ("excl;", (33, 0)),
        ("nges", (0, 0)),
        ("Dar", (0, 0)),
        ("NotSucceedsSla", (0, 0)),
        ("Ub", (0, 0)),
        ("igra", (0, 0)),
        ("VeryT", (0, 0)),
        ("ntilde", (241, 0)),
        ("copr", (0, 0)),
        ("Lce", (0, 0)),
        ("swnwar;", (10538, 0)),
        ("ecaron", (0, 0)),
        ("bumpeq;", (8783, 0)),
        ("rightsqui", (0, 0)),
        ("bnequiv", (0, 0)),
        ("lhar", (0, 0)),
        ("RightTee;", (8866, 0)),
        ("FilledVer", (0, 0)),
        ("ens", (0, 0)),
        ("leftrightar", (0, 0)),
        ("RightDownV", (0, 0)),
        ("NotRe", (0, 0)),
        ("bowti", (0, 0)),
        ("falling", (0, 0)),
        ("HilbertSp", (0, 0)),
        ("LeftTri", (0, 0)),
        ("gnsi", (0, 0)),
        ("lgE;", (10897, 0)),
        ("ThinS", (0, 0)),
        ("gtdo", (0, 0)),
        ("gacut", (0, 0)),
        ("oro", (0, 0)),
        ("Produc", (0, 0)),
        ("emsp14;", (8197, 0)),
        ("gcy", (0, 0)),
        ("SuchThat;", (8715, 0)),
        ("tstro", (0, 0)),
        ("lesdotor", (0, 0)),
        ("bbrktbrk", (0, 0)),
        ("NotDoubleV", (0, 0)),
        ("Kcedil", (0, 0)),
        ("lessdot;", (8918, 0)),
        ("cirE", (0, 0)),
        ("DiacriticalGra", (0, 0)),
        ("lAta", (0, 0)),
        ("grave;", (96, 0)),
        ("bigc", (0, 0)),
        ("DoubleDownArro", (0, 0)),
        ("elsdot", (0, 0)),
        ("NotSub", (0, 0)),
        ("DoubleVerti", (0, 0)),
        ("VerticalBar", (0, 0)),
        ("Sigma;", (931, 0)),
        ("Ag", (0, 0)),
        ("Fille", (0, 0)),
        ("glE;", (10898, 0)),
        ("lbrac", (0, 0)),
        ("pitchfork", (0, 0)),
        ("doublebarwed", (0, 0)),
        ("num;", (35, 0)),
        ("Uacute;", (218, 0)),
        ("Zf", (0, 0)),
        ("boxhu;", (9524, 0)),
        ("ld", (0, 0)),
        ("SuchT", (0, 0)),
        ("LongRig", (0, 0)),
        ("LowerLeft", (0, 0)),
        ("veee", (0, 0)),
        ("muma", (0, 0)),
        ("thka", (0, 0)),
        ("Ogra", (0, 0)),
        ("SquareSu", (0, 0)),
        ("compleme", (0, 0)),
        ("Jukcy;", (1028, 0)),
        ("Di", (0, 0)),
        ("blk", (0, 0)),
        ("zigra", (0, 0)),
        ("lneq", (0, 0)),
        ("gtquest;", (10876, 0)),
        ("LeftArrowB", (0, 0)),
        ("UpArro", (0, 0)),
        ("succna", (0, 0)),
        ("pluscir;", (10786, 0)),
        ("sfr;", (120112, 0)),
        ("vartriangle", (0, 0)),
        ("Leftrightarrow", (0, 0)),
        ("RightTria", (0, 0)),
        ("upharpoonright;", (8638, 0)),
        ("LeftUpVecto", (0, 0)),
        ("TSc", (0, 0)),
        ("colon;", (58, 0)),
        ("DownLeftRightVe", (0, 0)),
        ("Do", (0, 0)),
        ("DoubleRightArrow;", (8658, 0)),
        ("bump", (0, 0)),
        ("cuvee;", (8910, 0)),
        ("Ycirc;", (374, 0)),
        ("lca", (0, 0)),
        ("Revers", (0, 0)),
        ("tdo", (0, 0)),
        ("gtci", (0, 0)),
        ("mDDot;", (8762, 0)),
        ("nrightarrow;", (8603, 0)),
        ("laqu", (0, 0)),
        ("rpa", (0, 0)),
        ("ned", (0, 0)),
        ("NotCongr", (0, 0)),
        ("Kap", (0, 0)),
        ("eopf;", (120150, 0)),
        ("num", (0, 0)),
        ("plusdo;", (8724, 0)),
        ("hookr", (0, 0)),
        ("egs;", (10902, 0)),
        ("varno", (0, 0)),
        ("Downar", (0, 0)),
        ("boxDr", (0, 0)),
        ("lesseqqgtr;", (10891, 0)),
        ("vl", (0, 0)),
        ("uHa", (0, 0)),
        ("osol;", (8856, 0)),
        ("scaron", (0, 0)),
        ("Ccedil", (199, 0)),
        ("El", (0, 0)),
        ("Coprod", (0, 0)),
        ("divideontim", (0, 0)),
        ("Nt", (0, 0)),
        ("ss", (0, 0)),
        ("lthree", (0, 0)),
        ("utrif", (0, 0)),
        ("RightTeeAr", (0, 0)),
        ("SucceedsEqual", (0, 0)),
        ("rotimes", (0, 0)),
        ("gn", (0, 0)),
        ("eogon", (0, 0)),
        ("boxd", (0, 0)),
        ("nLtv;", (8810, 824)),
        ("nRigh", (0, 0)),
        ("ShortRig", (0, 0)),
        ("curlyeqs", (0, 0)),
        ("Otime", (0, 0)),
        ("UpD", (0, 0)),
        ("theta;", (952, 0)),
        ("Fouri", (0, 0)),
        ("EmptySmallSquare;", (9723, 0)),
        ("lpar;", (40, 0)),
        ("lowb", (0, 0)),
        ("SquareSuperse", (0, 0)),
        ("Jf", (0, 0)),
        ("gnappr", (0, 0)),
        ("sear", (0, 0)),
        ("NotLessEqua", (0, 0)),
        ("DiacriticalDo", (0, 0)),
        ("gtd", (0, 0)),
        ("bowtie;", (8904, 0)),
        ("precna", (0, 0)),
        ("nprcue", (0, 0)),
        ("Aring", (197, 0)),
        ("Vb", (0, 0)),
        ("ThickS", (0, 0)),
        ("Lc", (0, 0)),
        ("DiacriticalTild", (0, 0)),
        ("TildeFullEqual", (0, 0)),
        ("wc", (0, 0)),
        ("Ff", (0, 0)),
        ("dc", (0, 0)),
        ("NotExists;", (8708, 0)),
        ("atilde;", (227, 0)),
        ("doteq;", (8784, 0)),
        ("nvs", (0, 0)),
        ("lbrack;", (91, 0)),
        ("biguplus", (0, 0)),
        ("hyphen", (0, 0)),
        ("Scy", (0, 0)),
        ("lsime", (0, 0)),
        ("tb", (0, 0)),
        ("intla", (0, 0)),
        ("dst", (0, 0)),
        ("back", (0, 0)),
        ("DownBre", (0, 0)),
        ("smile;", (8995, 0)),
        ("ugra", (0, 0)),
        ("Lced", (0, 0)),
        ("H", (0, 0)),
        ("urt", (0, 0)),
        ("NotNest", (0, 0)),
        ("precs", (0, 0)),
        ("smallsetmi", (0, 0)),
        ("DownRightTeeVec", (0, 0)),
        ("Rrighta", (0, 0)),
        ("Ycy;", (1067, 0)),
        ("Oti", (0, 0)),
        ("Xi;", (926, 0)),
        ("leftarrow;", (8592, 0)),
        ("ZH", (0, 0)),
        ("permi", (0, 0)),
        ("NotLessSlant", (0, 0)),
        ("nopf", (0, 0)),
        ("timesb", (0, 0)),
        ("Zer", (0, 0)),
        ("cupbrcap", (0, 0)),
        ("LowerRi", (0, 0)),
        ("late", (0, 0)),
        ("InvisibleComma;", (8291, 0)),
        ("eDD", (0, 0)),
        ("softcy;", (1100, 0)),
        ("nvrtrie;", (8885, 8402)),
        ("xw", (0, 0)),
        ("Tripl", (0, 0)),
        ("rcub;", (125, 0)),
        ("cudarrl", (0, 0)),
        ("DoubleDownArrow;", (8659, 0)),
        ("lag", (0, 0)),
        ("ltlar", (0, 0)),
        ("NoBre", (0, 0)),
        ("Ecaron", (0, 0)),
        ("lessapp", (0, 0)),
        ("submu", (0, 0)),
        ("wedbar", (0, 0)),
        ("NestedGreaterG", (0, 0)),
        ("RightDownVe", (0, 0)),
        ("cu", (0, 0)),
        ("hksearo", (0, 0)),
        ("bigup", (0, 0)),
        ("omic", (0, 0)),
        ("aacut", (0, 0)),
        ("notniva;", (8716, 0)),
        ("dale", (0, 0)),
        ("capbrcu", (0, 0)),
        ("NotGreaterSlantEqu", (0, 0)),
        ("atilde", (227, 0)),
        ("wrea", (0, 0)),
        ("imagp", (0, 0)),
        ("Prec", (0, 0)),
        ("otim", (0, 0)),
        ("Nacute;", (323, 0)),
        ("lesseqg", (0, 0)),
        ("k", (0, 0)),
        ("mod", (0, 0)),
        ("Lleft", (0, 0)),
        ("LessLess;", (10913, 0)),
        ("pre;", (10927, 0)),
        ("blk14;", (9617, 0)),
        ("chcy;", (1095, 0)),
        ("Integra", (0, 0)),
        ("NotGreaterL", (0, 0)),
        ("downa", (0, 0)),
        ("gf", (0, 0)),
        ("conint;", (8750, 0)),
        ("thickap", (0, 0)),
        ("vsubnE", (0, 0)),
        ("boxuR", (0, 0)),
        ("GreaterF", (0, 0)),
        ("ETH", (208, 0)),
        ("ced", (0, 0)),
        ("gtcir", (0, 0)),
        ("DoubleUpD", (0, 0)),
        ("ulcorner", (0, 0)),
        ("UpEqu", (0, 0)),
        ("digamma;", (989, 0)),
        ("rightsquigar", (0, 0)),
        ("berno", (0, 0)),
        ("divideo", (0, 0)),
        ("digam", (0, 0)),
        ("scir", (0, 0)),
        ("exponent", (0, 0)),
        ("apacir", (0, 0)),
        ("i", (0, 0)),
        ("LongLeftRightArrow", (0, 0)),
        ("ngeqs", (0, 0)),
        ("Cloc", (0, 0)),
        ("pf", (0, 0)),
        ("compfn", (0, 0)),
        ("LeftTriangleEqual;", (8884, 0)),
        ("wreath", (0, 0)),
        ("boxVl", (0, 0)),
        ("congdot", (0, 0)),
        ("lstro", (0, 0)),
        ("nopf;", (120159, 0)),
        ("yacut", (0, 0)),
        ("triplus", (0, 0)),
        ("mapstol", (0, 0)),
        ("Intersect", (0, 0)),
        ("DiacriticalTi", (0, 0)),
        ("utr", (0, 0)),
        ("Omi", (0, 0)),
        ("plussim;", (10790, 0)),
        ("aopf;", (120146, 0)),
        ("Oacute;", (211, 0)),
        ("uplus", (0, 0)),
        ("Tilde", (0, 0)),
        ("ctd", (0, 0)),
        ("div", (0, 0)),
        ("gamm", (0, 0)),
        ("NotHumpD", (0, 0)),
        ("SquareS", (0, 0)),
        ("ulcorner;", (8988, 0)),
        ("succnapprox", (0, 0)),
        ("shcy;", (1096, 0)),
        ("MediumSpace;", (8287, 0)),
        ("LeftTriangleEqual", (0, 0)),
        ("iukcy", (0, 0)),
        ("RightUpVector;", (8638, 0)),
        ("bigodot;", (10752, 0)),
        ("c", (0, 0)),
        ("UpArrowDownArr", (0, 0)),
        ("NotGreaterEqua", (0, 0)),
        ("itild", (0, 0)),
        ("Vopf;", (120141, 0)),
        ("lvnE", (0, 0)),
        ("lmoust;", (9136, 0)),
        ("lotimes", (0, 0)),
        ("numsp", (0, 0)),
        ("sfr", (0, 0)),
        ("subsetne", (0, 0)),
        ("chi", (0, 0)),
        ("boxVL;", (9571, 0)),
        ("NotHump", (0, 0)),
        ("ShortRightArro", (0, 0)),
        ("xs", (0, 0)),
        ("squa", (0, 0)),
        ("Rarr", (0, 0)),
        ("cirscir", (0, 0)),
        ("intercal;", (8890, 0)),
        ("UpTeeArr", (0, 0)),
        ("Barwed", (0, 0)),
        ("nsupset", (0, 0)),
        ("Omeg", (0, 0)),
        ("NewLine", (0, 0)),
        ("LeftUpVector;", (8639, 0)),
        ("Xfr;", (120091, 0)),
        ("Tstr", (0, 0)),
        ("nparallel", (0, 0)),
        ("succapprox;", (10936, 0)),
        ("subsub;", (10965, 0)),
        ("lvertn", (0, 0)),
        ("simgE;", (10912, 0)),
        ("qsc", (0, 0)),
        ("emacr;", (275, 0)),
        ("LongL", (0, 0)),
        ("longleftrightarrow", (0, 0)),
        ("boxp", (0, 0)),
        ("nesi", (0, 0)),
        ("zf", (0, 0)),
        ("Long", (0, 0)),
        ("downharpoonlef", (0, 0)),
        ("icy", (0, 0)),
        ("LeftC", (0, 0)),
        ("upupa", (0, 0)),
        ("NotRigh", (0, 0)),
        ("LeftFloor;", (8970, 0)),
        ("Succe", (0, 0)),
        ("box", (0, 0)),
        ("cedi", (0, 0)),
        ("PartialD;", (8706, 0)),
        ("ora", (0, 0)),
        ("UpArrowDownA", (0, 0)),
        ("rmoustach", (0, 0)),
        ("bNot", (0, 0)),
        ("varp", (0, 0)),
        ("lmido", (0, 0)),
        ("Oacut", (0, 0)),
        ("rig", (0, 0)),
        ("vartri", (0, 0)),
        ("odb", (0, 0)),
        ("searr", (0, 0)),
        ("xsqcu", (0, 0)),
        ("SucceedsTild", (0, 0)),
        ("sect;", (167, 0)),
        ("tpr", (0, 0)),
        ("gtreqq", (0, 0)),
        ("Ab", (0, 0)),
        ("isin", (0, 0)),
        ("ntriang", (0, 0)),
        ("leftharpoond", (0, 0)),
        ("shy;", (173, 0)),
        ("par", (0, 0)),
        ("NotSubsetEqu", (0, 0)),
        ("prcu", (0, 0)),
        ("thet", (0, 0)),
        ("UpTeeArro", (0, 0)),
        ("kopf", (0, 0)),
        ("LeftUpDow", (0, 0)),
        ("cupbrcap;", (10824, 0)),
        ("xlArr", (0, 0)),
        ("Darr;", (8609, 0)),
        ("DoubleVerticalBar;", (8741, 0)),
        ("eqcir", (0, 0)),
        ("qscr;", (120006, 0)),
        ("looparrowleft", (0, 0)),
        ("ltlarr;", (10614, 0)),
        ("robr", (0, 0)),
        ("cdot", (0, 0)),
        ("integers", (0, 0)),
        ("Iac", (0, 0)),
        ("micr", (0, 0)),
        ("LeftArrowRightAr", (0, 0)),
        ("supn", (0, 0)),
        ("RightDoubleBracket", (0, 0)),
        ("olarr", (0, 0)),
        ("larrs", (0, 0)),
        ("utri", (0, 0)),
        ("imped;", (437, 0)),
        ("FilledVerySmal", (0, 0)),
        ("leftrightharpoo", (0, 0)),
        ("xrarr;", (10230, 0)),
        ("olcros", (0, 0)),
        ("sqcup", (0, 0)),
        ("hercon;", (8889, 0)),
        ("Em", (0, 0)),
        ("prnsim", (0, 0)),
        ("Nac", (0, 0)),
        ("varrh", (0, 0)),
        ("sfrown", (0, 0)),
        ("dArr", (0, 0)),
        ("longleftrightarrow;", (10231, 0)),
        ("DownLeftTeeVector", (0, 0)),
        ("NotSquareSub", (0, 0)),
        ("NegativeVer", (0, 0)),
        ("egsdot", (0, 0)),
        ("rightrightarrows;", (8649, 0)),
        ("sups", (0, 0)),
        ("DownArrowBar;", (10515, 0)),
        ("drc", (0, 0)),
        ("nsube;", (8840, 0)),
        ("fparti", (0, 0)),
        ("mumap;", (8888, 0)),
        ("Gdo", (0, 0)),
        ("car", (0, 0)),
        ("rotim", (0, 0)),
        ("tilde;", (732, 0)),
        ("sqc", (0, 0)),
        ("yicy;", (1111, 0)),
        ("nvHarr", (0, 0)),
        ("Diam", (0, 0)),
        ("ncar", (0, 0)),
        ("lpar", (0, 0)),
        ("Hc", (0, 0)),
        ("Expo", (0, 0)),
        ("Dagg", (0, 0)),
        ("varsub", (0, 0)),
        ("Sced", (0, 0)),
        ("frac12", (189, 0)),
        ("no", (0, 0)),
        ("npart;", (8706, 824)),
        ("lAar", (0, 0)),
        ("bpr", (0, 0)),
        ("oS;", (9416, 0)),
        ("pru", (0, 0)),
        ("harr", (0, 0)),
        ("Gr", (0, 0)),
        ("Cir", (0, 0)),
        ("DJcy;", (1026, 0)),
        ("zhcy;", (1078, 0)),
        ("rightarrow", (0, 0)),
        ("VerticalS", (0, 0)),
        ("qop", (0, 0)),
        ("ReverseEquilibrium", (0, 0)),
        ("Vo", (0, 0)),
        ("urcrop;", (8974, 0)),
        ("TildeEqual;", (8771, 0)),
        ("kcedil", (0, 0)),
        ("Et", (0, 0)),
        ("nLeftright", (0, 0)),
        ("ReverseUpEquilib", (0, 0)),
        ("tshcy", (0, 0)),
        ("Uparr", (0, 0)),
        ("angmsdae;", (10668, 0)),
        ("Sqr", (0, 0)),
        ("DownRi", (0, 0)),
        ("Eta", (0, 0)),
        ("succap", (0, 0)),
        ("hyphe", (0, 0)),
        ("Jsercy;", (1032, 0)),
        ("GreaterEqualLess;", (8923, 0)),
        ("ij", (0, 0)),
        ("squarf;", (9642, 0)),
        ("GreaterGre", (0, 0)),
        ("OElig;", (338, 0)),
        ("urcorne", (0, 0)),
        ("LeftArr", (0, 0)),
        ("Ecar", (0, 0)),
        ("nles;", (10877, 824)),
        ("pri", (0, 0)),
        ("lessg", (0, 0)),
        ("rbar", (0, 0)),
        ("LessSl", (0, 0)),
        ("md", (0, 0)),
        ("DownBreve", (0, 0)),
        ("succcurly", (0, 0)),
        ("ncup", (0, 0)),
        ("UnionP", (0, 0)),
        ("HumpDownHump", (0, 0)),
        ("sqcaps", (0, 0)),
        ("NotNestedGreaterGre", (0, 0)),
        ("triang", (0, 0)),
        ("eqcol", (0, 0)),
        ("oum", (0, 0)),
        ("lbbrk;", (10098, 0)),
        ("nsupsete", (0, 0)),
        ("Icy", (0, 0)),
        ("EN", (0, 0)),
        ("nvltri", (0, 0)),
        ("Uop", (0, 0)),
        ("inte", (0, 0)),
        ("darr", (0, 0)),
        ("LeftArro", (0, 0)),
        ("Diff", (0, 0)),
        ("Empty", (0, 0)),
        ("rangd;", (10642, 0)),
        ("ln", (0, 0)),
        ("Acir", (0, 0)),
        ("Theta", (0, 0)),
        ("Bscr;", (8492, 0)),
        ("vfr;", (120115, 0)),
        ("shortpara", (0, 0)),
        ("curlywe", (0, 0)),
        ("rarra", (0, 0)),
        ("bigtriangledow", (0, 0)),
        ("triminus;", (10810, 0)),
        ("utdo", (0, 0)),
        ("sme", (0, 0)),
        ("LeftCeili", (0, 0)),
        ("xcu", (0, 0)),
        ("ge", (0, 0)),
        ("smashp;", (10803, 0)),
        ("dsc", (0, 0)),
        ("twoheadleft", (0, 0)),
        ("urtri", (0, 0)),
        ("horb", (0, 0)),
        ("upharpoonlef", (0, 0)),
        ("lessdo", (0, 0)),
        ("curvearrowrig", (0, 0)),
        ("Ap", (0, 0)),
        ("trad", (0, 0)),
        ("LeftDownTeeVec", (0, 0)),
        ("np", (0, 0)),
        ("cirsci", (0, 0)),
        ("prur", (0, 0)),
        ("cirfnint", (0, 0)),
        ("nbump;", (8782, 824)),
        ("UpArrowD", (0, 0)),
        ("GreaterFul", (0, 0)),
        ("ntil", (0, 0)),
        ("DoubleDow", (0, 0)),
        ("Lst", (0, 0)),
        ("GreaterEqual;", (8805, 0)),
        ("mld", (0, 0)),
        ("gtreql", (0, 0)),
        ("thetas", (0, 0)),
        ("Hopf", (0, 0)),
        ("rrarr;", (8649, 0)),
        ("napid", (0, 0)),
        ("glj;", (10916, 0)),
        ("nLeftarrow;", (8653, 0)),
        ("emptyset;", (8709, 0)),
        ("NotEqualTilde;", (8770, 824)),
        ("angmsdad;", (10667, 0)),
        ("njc", (0, 0)),
        ("NotPrecedesSlantE", (0, 0)),
        ("vrtri;", (8883, 0)),
        ("Ws", (0, 0)),
        ("prnap;", (10937, 0)),
        ("nhArr;", (8654, 0)),
        ("cuep", (0, 0)),
        ("Cedill", (0, 0)),
        ("Wopf;", (120142, 0)),
        ("sigm", (0, 0)),
        ("leftrightsq", (0, 0)),
        ("nvlArr;", (10498, 0)),
        ("jukcy;", (1108, 0)),
        ("Gc", (0, 0)),
        ("leftharpoondo", (0, 0)),
        ("DoubleLeftA", (0, 0)),
        ("Bum", (0, 0)),
        ("Omicr", (0, 0)),
        ("Uf", (0, 0)),
        ("rdc", (0, 0)),
        ("DownT", (0, 0)),
        ("ropa", (0, 0)),
        ("DoubleLong", (0, 0)),
        ("Lmido", (0, 0)),
        ("Bernoull", (0, 0)),
        ("NotLeftTriangl", (0, 0)),
        ("NotRightTriangleEqua", (0, 0)),
        ("lparlt;", (10643, 0)),
        ("HumpE", (0, 0)),
        ("Becaus", (0, 0)),
        ("female", (0, 0)),
        ("Wed", (0, 0)),
        ("LessGreater", (0, 0)),
        ("LessE", (0, 0)),
        ("phiv", (0, 0)),
        ("LongLeftRightAr", (0, 0)),
        ("", (0, 0)),
        ("Gt", (0, 0)),
        ("nsqsup", (0, 0)),
        ("gr", (0, 0)),
        ("ape", (0, 0)),
        ("dagger", (0, 0)),
        ("lrarr;", (8646, 0)),
        ("Otimes;", (10807, 0)),
        ("topbot", (0, 0)),
        ("RightAngleB", (0, 0)),
        ("precapprox", (0, 0)),
        ("TSHcy;", (1035, 0)),
        ("HorizontalLine", (0, 0)),
        ("KJ", (0, 0)),
        ("rightrightarr", (0, 0)),
        ("rightrightarrows", (0, 0)),
        ("bigcu", (0, 0)),
        ("nVDa", (0, 0)),
        ("RightVectorB", (0, 0)),
        ("Rever", (0, 0)),
        ("curvearrowle", (0, 0)),
        ("vsubne", (0, 0)),
        ("RightArro", (0, 0)),
        ("gesle", (0, 0)),
        ("Rcedi", (0, 0)),
        ("gdot", (0, 0)),
        ("GreaterL", (0, 0)),
        ("Odblac;", (336, 0)),
        ("NegativeVeryThinSpa", (0, 0)),
        ("abre", (0, 0)),
        ("backcong", (0, 0)),
        ("Vbar", (0, 0)),
        ("dotsq", (0, 0)),
        ("mcomma", (0, 0)),
        ("NotTil", (0, 0)),
        ("NotVerticalBar", (0, 0)),
        ("opar;", (10679, 0)),
        ("rarrbfs", (0, 0)),
        ("Lstrok", (0, 0)),
        ("zfr;", (120119, 0)),
        ("nrtrie;", (8941, 0)),
        ("prof", (0, 0)),
        ("odbl", (0, 0)),
        ("UpArrowDo", (0, 0)),
        ("UpEquili", (0, 0)),
        ("uA", (0, 0)),
        ("Mfr;", (120080, 0)),
        ("lesseqq", (0, 0)),
        ("Ur", (0, 0)),
        ("supedot", (0, 0)),
        ("mldr;", (8230, 0)),
        ("succs", (0, 0)),
        ("congdo", (0, 0)),
        ("pr", (0, 0)),
        ("LeftUpTeeVect", (0, 0)),
        ("cularrp;", (10557, 0)),
        ("lh", (0, 0)),
        ("supplus;", (10944, 0)),
        ("NotNestedGreater", (0, 0)),
        ("leftrights", (0, 0)),
        ("asc", (0, 0)),
        ("Efr;", (120072, 0)),
        ("utdot", (0, 0)),
        ("omicron", (0, 0)),
        ("YIcy;", (1031, 0)),
        ("supsup;", (10966, 0)),
        ("rfis", (0, 0)),
        ("andd;", (10844, 0)),
        ("nrAr", (0, 0)),
        ("nwarro", (0, 0)),
        ("eli", (0, 0)),
        ("gacute;", (501, 0)),
        ("NonBreakingSpace;", (160, 0)),
        ("NotReverseEle", (0, 0)),
        ("uf", (0, 0)),
        ("nshortm", (0, 0)),
        ("ltim", (0, 0)),
        ("angr", (0, 0)),
        ("uplus;", (8846, 0)),
        ("part", (0, 0)),
        ("RightArrowB", (0, 0)),
        ("DoubleUpDownAr", (0, 0)),
        ("LessL", (0, 0)),
        ("Poincareplane;", (8460, 0)),
        ("RightDoubleBrack", (0, 0)),
        ("LeftA", (0, 0)),
        ("ngeqslant;", (10878, 824)),
        ("FilledSmallSq", (0, 0)),
        ("uar", (0, 0)),
        ("dharr", (0, 0)),
        ("rf", (0, 0)),
        ("MinusPlus;", (8723, 0)),
        ("leftthree", (0, 0)),
        ("NotGreaterSla", (0, 0)),
        ("circlearrowrig", (0, 0)),
        ("DoubleRig", (0, 0)),
        ("cupcu", (0, 0)),
        ("lparlt", (0, 0)),
        ("bdq", (0, 0)),
        ("Go", (0, 0)),
        ("xi;", (958, 0)),
        ("Lscr", (0, 0)),
        ("ubreve", (0, 0)),
        ("checkmar", (0, 0)),
        ("LeftDoubleBra", (0, 0)),
        ("NestedLes", (0, 0)),
        ("rec", (0, 0)),
        ("rlha", (0, 0)),
        ("msc", (0, 0)),
        ("CounterClockwiseCon", (0, 0)),
        ("supsete", (0, 0)),
        ("boxDl;", (9558, 0)),
        ("hkswa", (0, 0)),
        ("vDash;", (8872, 0)),
        ("xca", (0, 0)),
        ("DownLeftRi", (0, 0)),
        ("seswa", (0, 0)),
        ("mDDot", (0, 0)),
        ("ubre", (0, 0)),
        ("RightDownTeeVector", (0, 0)),
        ("Gamma;", (915, 0)),
        ("Wsc", (0, 0)),
        ("Ofr", (0, 0)),
        ("blacktriangledo", (0, 0)),
        ("triangledo", (0, 0)),
        ("lnapp", (0, 0)),
        ("upuparrows;", (8648, 0)),
        ("Kop", (0, 0)),
        ("PrecedesTil", (0, 0)),
        ("circledcirc", (0, 0)),
        ("RightTriangle;", (8883, 0)),
        ("copf;", (120148, 0)),
        ("dar", (0, 0)),
        ("sb", (0, 0)),
        ("lvn", (0, 0)),
        ("eqslantles", (0, 0)),
        ("hkswaro", (0, 0)),
        ("eDDo", (0, 0)),
        ("CenterDo", (0, 0)),
        ("LeftAngleB", (0, 0)),
        ("in;", (8712, 0)),
        ("LessEq", (0, 0)),
        ("gnappro", (0, 0)),
        ("LeftD", (0, 0)),
        ("rad", (0, 0)),
        ("Hscr;", (8459, 0)),
        ("Circ", (0, 0)),
        ("curlywedge", (0, 0)),
        ("smalls", (0, 0)),
        ("racut", (0, 0)),
        ("loz", (0, 0)),
        ("xhArr", (0, 0)),
        ("DoubleUpDownArro", (0, 0)),
        ("gtrless", (0, 0)),
        ("DoubleLeftRightA", (0, 0)),
        ("ZeroWi", (0, 0)),
        ("Yfr;", (120092, 0)),
        ("inodot", (0, 0)),
        ("nshortparallel", (0, 0)),
        ("curren", (164, 0)),
        ("Uu", (0, 0)),
        ("Lopf;", (120131, 0)),
        ("Um", (0, 0)),
        ("Del", (0, 0)),
        ("langd;", (10641, 0)),
        ("frac35;", (8535, 0)),
        ("capdot", (0, 0)),
        ("Dstro", (0, 0)),
        ("smi", (0, 0)),
        ("thetasym", (0, 0)),
        ("szlig;", (223, 0)),
        ("rtrif;", (9656, 0)),
        ("NotLessSlan", (0, 0)),
        ("ups", (0, 0)),
        ("becaus", (0, 0)),
        ("bbrktbrk;", (9142, 0)),
        ("NegativeThickSpac", (0, 0)),
        ("rarrh", (0, 0)),
        ("Leftr", (0, 0)),
        ("xoti", (0, 0)),
        ("Union;", (8899, 0)),
        ("ema", (0, 0)),
        ("RightArrowBar", (0, 0)),
        ("rced", (0, 0)),
        ("hea", (0, 0)),
        ("NotNestedLessLes", (0, 0)),
        ("ltimes", (0, 0)),
        ("itil", (0, 0)),
        ("nsube", (0, 0)),
        ("jsc", (0, 0)),
        ("Cross", (0, 0)),
        ("sdotb;", (8865, 0)),
        ("NotDoubleVertical", (0, 0)),
        ("NotVertic", (0, 0)),
        ("drbkaro", (0, 0)),
        ("sqsup", (0, 0)),
        ("Zeta;", (918, 0)),
        ("supsetneq", (0, 0)),
        ("UpArrowDow", (0, 0)),
        ("eqci", (0, 0)),
        ("uti", (0, 0)),
        ("UpTeeA", (0, 0)),
        ("varpropto;", (8733, 0)),
        ("NotTildeFullEqua", (0, 0)),
        ("Dot", (0, 0)),
        ("gver", (0, 0)),
        ("cong", (0, 0)),
        ("measuredangle;", (8737, 0)),
        ("FilledSmal", (0, 0)),
        ("zeet", (0, 0)),
        ("rdquo", (0, 0)),
        ("Proportion;", (8759, 0)),
        ("ldquor", (0, 0)),
        ("Lcaron;", (317, 0)),
        ("ldsh", (0, 0)),
        ("rarrfs", (0, 0)),
        ("PlusMi", (0, 0)),
        ("omicr", (0, 0)),
        ("RoundImplies;", (10608, 0)),
        ("UpDownAr", (0, 0)),
        ("lrcorner", (0, 0)),
        ("roplus", (0, 0)),
        ("uma", (0, 0)),
        ("bigod", (0, 0)),
        ("gap", (0, 0)),
        ("NotSup", (0, 0)),
        ("LeftAn", (0, 0)),
        ("Oc", (0, 0)),
        ("sdotb", (0, 0)),
        ("barwed", (0, 0)),
        ("lnappr", (0, 0)),
        ("fora", (0, 0)),
        ("Kfr;", (120078, 0)),
        ("rightharpoo", (0, 0)),
        ("Therefor", (0, 0)),
        ("au", (0, 0)),
        ("daleth", (0, 0)),
        ("leftrightsquigarr", (0, 0)),
        ("subsetneq", (0, 0)),
        ("iexcl;", (161, 0)),
        ("NegativeVery", (0, 0)),
        ("plusd", (0, 0)),
        ("qprime;", (8279, 0)),
        ("subra", (0, 0)),
        ("hst", (0, 0)),
        ("vr", (0, 0)),
        ("parall", (0, 0)),
        ("Eu", (0, 0)),
        ("TSH", (0, 0)),
        ("trip", (0, 0)),
        ("ssmil", (0, 0)),
        ("cue", (0, 0)),
        ("tint", (0, 0)),
        ("eqsim;", (8770, 0)),
        ("Ucirc;", (219, 0)),
        ("tdot", (0, 0)),
        ("lnsim", (0, 0)),
        ("Wopf", (0, 0)),
        ("NotLeftTriangleB", (0, 0)),
        ("Imagi", (0, 0)),
        ("backpri", (0, 0)),
        ("sube", (0, 0)),
        ("bigcir", (0, 0)),
        ("ngtr", (0, 0)),
        ("mode", (0, 0)),
        ("am", (0, 0)),
        ("trisb;", (10701, 0)),
        ("DoubleLongLeftAr", (0, 0)),
        ("rationals", (0, 0)),
        ("GreaterLe", (0, 0)),
        ("capbrcup;", (10825, 0)),
        ("NotLessSlantEq", (0, 0)),
        ("Emp", (0, 0)),
        ("InvisibleTime", (0, 0)),
        ("ast;", (42, 0)),
        ("therefore", (0, 0)),
        ("Bac", (0, 0)),
        ("LessGreater;", (8822, 0)),
        ("Propor", (0, 0)),
        ("Fopf;", (120125, 0)),
        ("Eacute", (201, 0)),
        ("rbrkslu", (0, 0)),
        ("lowba", (0, 0)),
        ("gg;", (8811, 0)),
        ("Hac", (0, 0)),
        ("ohb", (0, 0)),
        ("lg", (0, 0)),
        ("Longrigh", (0, 0)),
        ("angmsdag", (0, 0)),
        ("backsimeq", (0, 0)),
        ("elinte", (0, 0)),
        ("rdldhar", (0, 0)),
        ("thicksim", (0, 0)),
        ("udbla", (0, 0)),
        ("malte", (0, 0)),
        ("gbr", (0, 0)),
        ("supp", (0, 0)),
        ("Downa", (0, 0)),
        ("NotTi", (0, 0)),
        ("mo", (0, 0)),
        ("ngeqslan", (0, 0)),
        ("nshortparall", (0, 0)),
        ("swArr", (0, 0)),
        ("Ups", (0, 0)),
        ("triangler", (0, 0)),
        ("nRightarro", (0, 0)),
        ("nu;", (957, 0)),
        ("LessSla", (0, 0)),
        ("cced", (0, 0)),
        ("SucceedsTilde;", (8831, 0)),
        ("gesdoto", (0, 0)),
        ("Longlef", (0, 0)),
        ("Egrave", (200, 0)),
        ("ShortUpArro", (0, 0)),
        ("duh", (0, 0)),
        ("Therefore", (0, 0)),
        ("blacktriangledown", (0, 0)),
        ("nhar", (0, 0)),
        ("Gsc", (0, 0)),
        ("nleftrighta", (0, 0)),
        ("csupe;", (10962, 0)),
        ("nRig", (0, 0)),
        ("ofc", (0, 0)),
        ("gscr", (0, 0)),
        ("set", (0, 0)),
        ("RightUpVec", (0, 0)),
        ("Vdashl", (0, 0)),
        ("prod;", (8719, 0)),
        ("Fil", (0, 0)),
        ("jfr;", (120103, 0)),
        ("sdote;", (10854, 0)),
        ("nshortmi", (0, 0)),
        ("naturals;", (8469, 0)),
        ("Zo", (0, 0)),
        ("rightarrowt", (0, 0)),
        ("lozen", (0, 0)),
        ("parsi", (0, 0)),
        ("emptyv;", (8709, 0)),
        ("Count", (0, 0)),
        ("NotLessGreater", (0, 0)),
        ("NotSquareSupersetEqua", (0, 0)),
        ("angrt;", (8735, 0)),
        ("epsil", (0, 0)),
        ("crar", (0, 0)),
        ("LeftCeilin", (0, 0)),
        ("LeftArrowRight", (0, 0)),
        ("Vdashl;", (10982, 0)),
        ("iiota", (0, 0)),
        ("LeftArrow", (0, 0)),
        ("LowerRightArr", (0, 0)),
        ("scirc", (0, 0)),
        ("Hil", (0, 0)),
        ("duar", (0, 0)),
        ("Rightarrow;", (8658, 0)),
        ("Mc", (0, 0)),
        ("SucceedsSlantE", (0, 0)),
        ("Af", (0, 0)),
        ("ran", (0, 0)),
        ("circlearrowr", (0, 0)),
        ("natural;", (9838, 0)),
        ("VerticalTilde", (0, 0)),
        ("NotLessT", (0, 0)),
        ("esim", (0, 0)),
        ("td", (0, 0)),
        ("boxminus;", (8863, 0)),
        ("rightthreeti", (0, 0)),
        ("tridot;", (9708, 0)),
        ("gtreqle", (0, 0)),
        ("LeftUpVector", (0, 0)),
        ("boxvR", (0, 0)),
        ("twoheadrigh", (0, 0)),
        ("OverParenth", (0, 0)),
        ("roarr", (0, 0)),
        ("epsiv", (0, 0)),
        ("There", (0, 0)),
        ("rcy", (0, 0)),
        ("tca", (0, 0)),
        ("DoubleLongRight", (0, 0)),
        ("Yacut", (0, 0)),
        ("CirclePl", (0, 0)),
        ("nLeftrigh", (0, 0)),
        ("dscy", (0, 0)),
        ("laempt", (0, 0)),
        ("mnp", (0, 0)),
        ("upup", (0, 0)),
        ("isi", (0, 0)),
        ("bbrktbr", (0, 0)),
        ("NotPreced", (0, 0)),
        ("NegativeThickS", (0, 0)),
        ("supsetneqq", (0, 0)),
        ("Lacute;", (313, 0)),
        ("Euml", (203, 0)),
        ("Egrave;", (200, 0)),
        ("zwj", (0, 0)),
        ("gnE;", (8809, 0)),
        ("fcy", (0, 0)),
        ("ape;", (8778, 0)),
        ("demp", (0, 0)),
        ("DownLeft", (0, 0)),
        ("nbsp;", (160, 0)),
        ("angs", (0, 0)),
        ("ltri;", (9667, 0)),
        ("natur", (0, 0)),
        ("lessappro", (0, 0)),
        ("frac5", (0, 0)),
        ("FilledVerySmallSquare", (0, 0)),
        ("qprime", (0, 0)),
        ("aogon", (0, 0)),
        ("wo", (0, 0)),
        ("vArr;", (8661, 0)),
        ("omicro", (0, 0)),
        ("HilbertSpa", (0, 0)),
        ("rightarrowtai", (0, 0)),
        ("ifr", (0, 0)),
        ("infintie", (0, 0)),
        ("ti", (0, 0)),
        ("Cscr", (0, 0)),
        ("ot", (0, 0)),
        ("dt", (0, 0)),
        ("ecirc;", (234, 0)),
        ("marker;", (9646, 0)),
        ("NotPrecedesSlantEqua", (0, 0)),
        ("oop", (0, 0)),
        ("Or;", (10836, 0)),
        ("le;", (8804, 0)),
        ("puncs", (0, 0)),
        ("timesbar;", (10801, 0)),
        ("Cont", (0, 0)),
        ("Ome", (0, 0)),
        ("lvnE;", (8808, 65024)),
        ("w", (0, 0)),
        ("sigmav", (0, 0)),
        ("colon", (0, 0)),
        ("Zca", (0, 0)),
        ("leftharpoondown;", (8637, 0)),
        ("ic;", (8291, 0)),
        ("Eca", (0, 0)),
        ("Egrav", (0, 0)),
        ("larrb;", (8676, 0)),
        ("boxul;", (9496, 0)),
        ("upuparro", (0, 0)),
        ("Sopf;", (120138, 0)),
        ("VerticalTil", (0, 0)),
        ("eqs", (0, 0)),
        ("DoubleDownArr", (0, 0)),
        ("NotP", (0, 0)),
        ("Fcy;", (1060, 0)),
        ("bkaro", (0, 0)),
        ("UnderBr", (0, 0)),
        ("lt;", (60, 0)),
        ("Nu;", (925, 0)),
        ("ReverseElement", (0, 0)),
        ("micro", (181, 0)),
        ("Atild", (0, 0)),
        ("exponen", (0, 0)),
        ("egra", (0, 0)),
        ("pluscir", (0, 0)),
        ("perten", (0, 0)),
        ("ShortLeftArro", (0, 0)),
        ("LeftUpD", (0, 0)),
        ("NotPrece", (0, 0)),
        ("NotCongruent", (0, 0)),
        ("NotLessTild", (0, 0)),
        ("trianglelefteq", (0, 0)),
        ("RightArrowLeftArrow", (0, 0)),
        ("notinvb", (0, 0)),
        ("DotEqu", (0, 0)),
        ("NotRightTriangleEqual;", (8941, 0)),
        ("Lf", (0, 0)),
        ("Because;", (8757, 0)),
        ("RightTeeArrow;", (8614, 0)),
        ("NotLessTil", (0, 0)),
        ("bumpe;", (8783, 0)),
        ("ijlig;", (307, 0)),
        ("Ysc", (0, 0)),
        ("theref", (0, 0)),
        ("supnE;", (10956, 0)),
        ("DScy", (0, 0)),
        ("Nop", (0, 0)),
        ("Uogo", (0, 0)),
        ("eqsi", (0, 0)),
        ("CapitalDifferentia", (0, 0)),
        ("laempty", (0, 0)),
        ("Bernou", (0, 0)),
        ("e", (0, 0)),
        ("prE", (0, 0)),
        ("zs", (0, 0)),
        ("csupe", (0, 0)),
        ("Ope", (0, 0)),
        ("block", (0, 0)),
        ("NotLeftTriangle;", (8938, 0)),
        ("xopf", (0, 0)),
        ("NotLessTilde;", (8820, 0)),
        ("ls", (0, 0)),
        ("intlarh", (0, 0)),
        ("ulcrop", (0, 0)),
        ("OverParenthes", (0, 0)),
        ("triangled", (0, 0)),
        ("Tscr", (0, 0)),
        ("rightlef", (0, 0)),
        ("SmallCir", (0, 0)),
        ("LeftArrowRigh", (0, 0)),
        ("sqca", (0, 0)),
        ("cirsc", (0, 0)),
        ("NotNes", (0, 0)),
        ("odblac", (0, 0)),
        ("the", (0, 0)),
        ("boxbox;", (10697, 0)),
        ("LeftAngleBracke", (0, 0)),
        ("Col", (0, 0)),
        ("Yscr;", (119988, 0)),
        ("Ra", (0, 0)),
        ("dzi", (0, 0)),
        ("Bfr", (0, 0)),
        ("nltri;", (8938, 0)),
        ("Igr", (0, 0)),
        ("TildeF", (0, 0)),
        ("Squar", (0, 0)),
        ("Sacut", (0, 0)),
        ("divi", (0, 0)),
        ("Capita", (0, 0)),
        ("wscr;", (120012, 0)),
        ("Wf", (0, 0)),
        ("NotLessSlantEqua", (0, 0)),
        ("dbl", (0, 0)),
        ("rcei", (0, 0)),
        ("rightleftarrow", (0, 0)),
        ("gtrdot", (0, 0)),
        ("gtquest", (0, 0)),
        ("lstr", (0, 0)),
        ("VerticalT", (0, 0)),
        ("rhard", (0, 0)),
        ("NotLessLess;", (8810, 824)),
        ("cac", (0, 0)),
        ("nfr;", (120107, 0)),
        ("caron;", (711, 0)),
        ("maltes", (0, 0)),
        ("NewLi", (0, 0)),
        ("rlhar;", (8652, 0)),
        ("uparrow;", (8593, 0)),
        ("CounterClockwiseContourIntegral", (0, 0)),
        ("Apply", (0, 0)),
        ("ncong;", (8775, 0)),
        ("topbo", (0, 0)),
        ("V", (0, 0)),
        ("dtri", (0, 0)),
        ("ubrev", (0, 0)),
        ("ff", (0, 0)),
        ("LeftDow", (0, 0)),
        ("lfisht", (0, 0)),
        ("emptyv", (0, 0)),
        ("DiacriticalAcut", (0, 0)),
        ("nrightarr", (0, 0)),
        ("upharp", (0, 0)),
        ("Ci", (0, 0)),
        ("uc", (0, 0)),
        ("awint;", (10769, 0)),
        ("ci", (0, 0)),
        ("scap;", (10936, 0)),
        ("ope", (0, 0)),
        ("oplu", (0, 0)),
        ("Neg", (0, 0)),
        ("LowerRightArro", (0, 0)),
        ("SupersetE", (0, 0)),
        ("Yopf;", (120144, 0)),
        ("multi", (0, 0)),
        ("Equilibrium", (0, 0)),
        ("Less", (0, 0)),
        ("NegativeThickSpa", (0, 0)),
        ("sl", (0, 0)),
        ("nshortmid;", (8740, 0)),
        ("OverBar", (0, 0)),
        ("NestedGreate", (0, 0)),
        ("Ka", (0, 0)),
        ("Oacute", (211, 0)),
        ("prE;", (10931, 0)),
        ("ReverseUpEquilibrium;", (10607, 0)),
        ("Updownar", (0, 0)),
        ("simplus;", (10788, 0)),
        ("boxti", (0, 0)),
        ("vsupn", (0, 0)),
        ("FilledVerySmall", (0, 0)),
        ("ol", (0, 0)),
        ("marker", (0, 0)),
        ("df", (0, 0)),
        ("aelig;", (230, 0)),
        ("longrightar", (0, 0)),
        ("upsil", (0, 0)),
        ("squarf", (0, 0)),
        ("nVdash;", (8878, 0)),
        ("do", (0, 0)),
        ("Up", (0, 0)),
        ("daleth;", (8504, 0)),
        ("capd", (0, 0)),
        ("isinv;", (8712, 0)),
        ("NotNestedGreaterGreater;", (10914, 824)),
        ("Tri", (0, 0)),
        ("E", (0, 0)),
        ("nLeftrightar", (0, 0)),
        ("Medi", (0, 0)),
        ("LessEqualGre", (0, 0)),
        ("Rightarro", (0, 0)),
        ("boxV;", (9553, 0)),
        ("gvertneqq", (0, 0)),
        ("ast", (0, 0)),
        ("NotRight", (0, 0)),
        ("asy", (0, 0)),
        ("profal", (0, 0)),
        ("go", (0, 0)),
        ("Copf;", (8450, 0)),
        ("nrighta", (0, 0)),
        ("RightUpDo", (0, 0)),
        ("notnivc;", (8957, 0)),
        ("thic", (0, 0)),
        ("Not", (0, 0)),
        ("wedgeq", (0, 0)),
        ("orderof", (0, 0)),
        ("scnsim", (0, 0)),
        ("Laplacetrf;", (8466, 0)),
        ("subne;", (8842, 0)),
        ("ffli", (0, 0)),
        ("SquareInter", (0, 0)),
        ("Vvd", (0, 0)),
        ("lds", (0, 0)),
        ("rarrc", (0, 0)),
        ("NotLeftTriangleBa", (0, 0)),
        ("Ber", (0, 0)),
        ("Op", (0, 0)),
        ("KH", (0, 0)),
        ("lAt", (0, 0)),
        ("therefor", (0, 0)),
        ("eci", (0, 0)),
        ("llhar", (0, 0)),
        ("phm", (0, 0)),
        ("gesdot;", (10880, 0)),
        ("xni", (0, 0)),
        ("nrigh", (0, 0)),
        ("varsup", (0, 0)),
        ("LessFullEqua", (0, 0)),
        ("ReverseUpEquilibriu", (0, 0)),
        ("boxhD", (0, 0)),
        ("plus;", (43, 0)),
        ("lowbar", (0, 0)),
        ("Omega;", (937, 0)),
        ("notindot", (0, 0)),
        ("lat;", (10923, 0)),
        ("leftrightsquigarro", (0, 0)),
        ("Mopf", (0, 0)),
        ("odso", (0, 0)),
        ("srarr", (0, 0)),
        ("ssmile", (0, 0)),
        ("lrarr", (0, 0)),
        ("emsp1", (0, 0)),
        ("rr", (0, 0)),
        ("measured", (0, 0)),
        ("hoo", (0, 0)),
        ("GreaterSlantEqual", (0, 0)),
        ("ngeqslant", (0, 0)),
        ("ndash;", (8211, 0)),
        ("lns", (0, 0)),
        ("shy", (173, 0)),
        ("triminu", (0, 0)),
        ("Oum", (0, 0)),
        ("updownarr", (0, 0)),
        ("lrt", (0, 0)),
        ("ocir;", (8858, 0)),
        ("DoubleVe", (0, 0)),
        ("Gcedil;", (290, 0)),
        ("nleft", (0, 0)),
        ("gesc", (0, 0)),
        ("bigwe", (0, 0)),
        ("twoheadr", (0, 0)),
        ("drbka", (0, 0)),
        ("GreaterLess;", (8823, 0)),
        ("NotDou", (0, 0)),
        ("comp;", (8705, 0)),
        ("Tcy", (0, 0)),
        ("geq", (0, 0)),
        ("Cconi", (0, 0)),
        ("nsupse", (0, 0)),
        ("LeftDownVectorBar", (0, 0)),
        ("RightUpV", (0, 0)),
        ("Eogon", (0, 0)),
        ("suplarr", (0, 0)),
        ("LeftRightArrow;", (8596, 0)),
        ("topf;", (120165, 0)),
        ("Zero", (0, 0)),
        ("uharr;", (8638, 0)),
        ("rar", (0, 0)),
        ("longleftarr", (0, 0)),
        ("OpenCurlyDoubleQuot", (0, 0)),
        ("lmou", (0, 0)),
        ("nleqslant;", (10877, 824)),
        ("Vertical", (0, 0)),
        ("boxdL", (0, 0)),
        ("yopf", (0, 0)),
        ("Congru", (0, 0)),
        ("gesdot", (0, 0)),
        ("boxDr;", (9555, 0)),
        ("NotSqu", (0, 0)),
        ("ntrianglerig", (0, 0)),
        ("CloseCurlyDoubleQ", (0, 0)),
        ("RightArrow", (0, 0)),
        ("xut", (0, 0)),
        ("ltla", (0, 0)),
        ("Jc", (0, 0)),
        ("zo", (0, 0)),
        ("Racut", (0, 0)),
        ("Jcy", (0, 0)),
        ("EmptySm", (0, 0)),
        ("eDot;", (8785, 0)),
        ("trpezium;", (9186, 0)),
        ("rdld", (0, 0)),
        ("VerticalSeparato", (0, 0)),
        ("ka", (0, 0)),
        ("Sigma", (0, 0)),
        ("RightTeeVector;", (10587, 0)),
        ("xwed", (0, 0)),
        ("DoubleUpDownArrow", (0, 0)),
        ("CirclePlus;", (8853, 0)),
        ("xopl", (0, 0)),
        ("RightAng", (0, 0)),
        ("bumpe", (0, 0)),
        ("Chi;", (935, 0)),
        ("rfish", (0, 0)),
        ("nwarhk", (0, 0)),
        ("fjli", (0, 0)),
        ("rarrw;", (8605, 0)),
        ("supseteqq", (0, 0)),
        ("bsemi", (0, 0)),
        ("DoubleRightArrow", (0, 0)),
        ("robrk;", (10215, 0)),
        ("cscr;", (119992, 0)),
        ("boxvH", (0, 0)),
        ("egrav", (0, 0)),
        ("Yacu", (0, 0)),
        ("roar", (0, 0)),
        ("OpenCurlyQuote;", (8216, 0)),
        ("Yop", (0, 0)),
        ("subE", (0, 0)),
        ("LeftRightVe", (0, 0)),
        ("loo", (0, 0)),
        ("rarrc;", (10547, 0)),
        ("cupd", (0, 0)),
        ("upu", (0, 0)),
        ("xupl", (0, 0)),
        ("rightthreetimes", (0, 0)),
        ("KJcy;", (1036, 0)),
        ("GreaterEqu", (0, 0)),
        ("Med", (0, 0)),
        ("NotSucc", (0, 0)),
        ("bka", (0, 0)),
        ("nrtri", (0, 0)),
        ("qp", (0, 0)),
        ("HilbertSpac", (0, 0)),
        ("vp", (0, 0)),
        ("cupo", (0, 0)),
        ("Ufr;", (120088, 0)),
        ("sub", (0, 0)),
        ("rdldhar;", (10601, 0)),
        ("otilde;", (245, 0)),
        ("maltese", (0, 0)),
        ("Scaron", (0, 0)),
        ("Zacu", (0, 0)),
        ("rbarr", (0, 0)),
        ("bprim", (0, 0)),
        ("loplus;", (10797, 0)),
        ("tbrk", (0, 0)),
        ("epsilon;", (949, 0)),
        ("uml;", (168, 0)),
        ("nparsl;", (11005, 8421)),
        ("Nce", (0, 0)),
        ("UpEquilibri", (0, 0)),
        ("CounterCloc", (0, 0)),
        ("leqq;", (8806, 0)),
        ("SucceedsSlantEqu", (0, 0)),
        ("sced", (0, 0)),
        ("dotplus;", (8724, 0)),
        ("REG", (174, 0)),
        ("Omacr", (0, 0)),
        ("trpeziu", (0, 0)),
        ("LeftTee;", (8867, 0)),
        ("UnderParent", (0, 0)),
        ("UpperLeftArrow;", (8598, 0)),
        ("DownArrowUpArrow;", (8693, 0)),
        ("eD", (0, 0)),
        ("lagra", (0, 0)),
        ("spa", (0, 0)),
        ("sscr;", (120008, 0)),
        ("ETH;", (208, 0)),
        ("rbarr;", (10509, 0)),
        ("phiv;", (981, 0)),
        ("Cedilla", (0, 0)),
        ("andslope;", (10840, 0)),
        ("slarr", (0, 0)),
        ("angzarr;", (9084, 0)),
        ("varthe", (0, 0)),
        ("preccurly", (0, 0)),
        ("triangledown", (0, 0)),
        ("Sta", (0, 0)),
        ("leftrightarrow", (0, 0)),
        ("therefo", (0, 0)),
        ("imagline", (0, 0)),
        ("Udblac;", (368, 0)),
        ("elint", (0, 0)),
        ("uhb", (0, 0)),
        ("NotSucceedsSlan", (0, 0)),
        ("larrfs;", (10525, 0)),
        ("Ze", (0, 0)),
        ("varprop", (0, 0)),
        ("varphi", (0, 0)),
        ("lcaro", (0, 0)),
        ("bre", (0, 0)),
        ("dharr;", (8642, 0)),
        ("Inters", (0, 0)),
        ("ffllig;", (64260, 0)),
        ("int", (0, 0)),
        ("Implies", (0, 0)),
        ("iecy", (0, 0)),
        ("NotNestedGreaterGreater", (0, 0)),
        ("csube;", (10961, 0)),
        ("DDotrah", (0, 0)),
        ("blacktriangleright", (0, 0)),
        ("rsquo", (0, 0)),
        ("Qscr;", (119980, 0)),
        ("RightFloor;", (8971, 0)),
        ("NotCupCap", (0, 0)),
        ("bs", (0, 0)),
        ("FilledVe", (0, 0)),
        ("straig", (0, 0)),
        ("ecaro", (0, 0)),
        ("NotT", (0, 0)),
        ("langle", (0, 0)),
        ("Ga", (0, 0)),
        ("kf", (0, 0)),
        ("Wcirc", (0, 0)),
        ("boxvL;", (9569, 0)),
        ("qin", (0, 0)),
        ("hercon", (0, 0)),
        ("NotLessGre", (0, 0)),
        ("LeftR", (0, 0)),
        ("VerticalSepar", (0, 0)),
        ("NegativeThinSp", (0, 0)),
        ("Greate", (0, 0)),
        ("jcirc;", (309, 0)),
        ("ldquo", (0, 0)),
        ("uphar", (0, 0)),
        ("lambda", (0, 0)),
        ("neArr", (0, 0)),
        ("lbarr;", (10508, 0)),
        ("downharpoonright", (0, 0)),
        ("Lcaro", (0, 0)),
        ("SquareUn", (0, 0)),
        ("lp", (0, 0)),
        ("varsupset", (0, 0)),
        ("fr", (0, 0)),
        ("ncong", (0, 0)),
        ("Dcy;", (1044, 0)),
        ("DownA", (0, 0)),
        ("DiacriticalTilde", (0, 0)),
        ("ReverseEquil", (0, 0)),
        ("NotSucceedsSlantEqual;", (8929, 0)),
        ("eqcolo", (0, 0)),
        ("DoubleContourIntegr", (0, 0)),
        ("HumpD", (0, 0)),
        ("questeq", (0, 0)),
        ("cem", (0, 0)),
        ("Dashv", (0, 0)),
        ("parallel", (0, 0)),
        ("rightleftharpoon", (0, 0)),
        ("ClockwiseConto", (0, 0)),
        ("NotPrecedesEqu", (0, 0)),
        ("DownLeftTee", (0, 0)),
        ("OverBrack", (0, 0)),
        ("NotEleme", (0, 0)),
        ("nf", (0, 0)),
        ("Hu", (0, 0)),
        ("intc", (0, 0)),
        ("Jser", (0, 0)),
        ("circledS;", (9416, 0)),
        ("Gamm", (0, 0)),
        ("iqu", (0, 0)),
        ("Oacu", (0, 0)),
        ("divonx;", (8903, 0)),
        ("lessapprox;", (10885, 0)),
        ("orar", (0, 0)),
        ("TildeFull", (0, 0)),
        ("ShortLeftArrow;", (8592, 0)),
        ("ie", (0, 0)),
        ("NotRig", (0, 0)),
        ("Pscr;", (119979, 0)),
        ("oso", (0, 0)),
        ("alef", (0, 0)),
        ("pou", (0, 0)),
        ("NotRightTriangle;", (8939, 0)),
        ("prnap", (0, 0)),
        ("da", (0, 0)),
        ("RightArrowLeftA", (0, 0)),
        ("utild", (0, 0)),
        ("ReverseEl", (0, 0)),
        ("scE", (0, 0)),
        ("duhar", (0, 0)),
        ("Ycir", (0, 0)),
        ("NotSquareSubsetEqua", (0, 0)),
        ("LongLeftRightArr", (0, 0)),
        ("ShortD", (0, 0)),
        ("male;", (9794, 0)),
        ("sta", (0, 0)),
        ("lharul;", (10602, 0)),
        ("dscr;", (119993, 0)),
        ("VerticalLine;", (124, 0)),
        ("RightDouble", (0, 0)),
        ("NotRightTriangleE", (0, 0)),
        ("Close", (0, 0)),
        ("perio", (0, 0)),
        ("subsub", (0, 0)),
        ("iu", (0, 0)),
        ("gtreqqles", (0, 0)),
        ("Leftrighta", (0, 0)),
        ("caret;", (8257, 0)),
        ("smallsetm", (0, 0)),
        ("coprod", (0, 0)),
        ("ipr", (0, 0)),
        ("ta", (0, 0)),
        ("Hcirc;", (292, 0)),
        ("rfr", (0, 0)),
        ("downharpoo", (0, 0)),
        ("Uc", (0, 0)),
        ("NotSupers", (0, 0)),
        ("xfr", (0, 0)),
        ("xfr;", (120117, 0)),
        ("downharpoonrig", (0, 0)),
        ("tced", (0, 0)),
        ("barwed;", (8965, 0)),
        ("lfis", (0, 0)),
        ("comple", (0, 0)),
        ("CounterClockwiseContourIn", (0, 0)),
        ("gsiml", (0, 0)),
        ("scpolint", (0, 0)),
        ("NotGreaterSlantEqua", (0, 0)),
        ("OpenCurlyDou", (0, 0)),
        ("Nti", (0, 0)),
        ("lstrok", (0, 0)),
        ("NotTildeEqual;", (8772, 0)),
        ("rightleftarro", (0, 0)),
        ("plussim", (0, 0)),
        ("subsi", (0, 0)),
        ("SupersetEq", (0, 0)),
        ("egrave;", (232, 0)),
        ("risingdots", (0, 0)),
        ("DifferentialD;", (8518, 0)),
        ("omin", (0, 0)),
        ("righ", (0, 0)),
        ("succne", (0, 0)),
        ("DoubleR", (0, 0)),
        ("ratio;", (8758, 0)),
        ("sca", (0, 0)),
        ("NotLessEqu", (0, 0)),
        ("NestedLessLes", (0, 0)),
        ("LeftRightVector;", (10574, 0)),
        ("ldq", (0, 0)),
        ("gsime", (0, 0)),
        ("f", (0, 0)),
        ("zca", (0, 0)),
        ("downdown", (0, 0)),
        ("DownLeftV", (0, 0)),
        ("supne", (0, 0)),
        ("ni;", (8715, 0)),
        ("Jukc", (0, 0)),
        ("Acirc;", (194, 0)),
        ("bnequ", (0, 0)),
        ("LeftTriangleEqua", (0, 0)),
        ("LongR", (0, 0)),
        ("Racute", (0, 0)),
        ("triplus;", (10809, 0)),
        ("NonBreakingSpa", (0, 0)),
        ("sq", (0, 0)),
        ("triangleri", (0, 0)),
        ("nis", (0, 0)),
        ("Doubl", (0, 0)),
        ("nsqsub", (0, 0)),
        ("longle", (0, 0)),
        ("nlsim;", (8820, 0)),
        ("cfr", (0, 0)),
        ("AElig;", (198, 0)),
        ("LeftVectorBar;", (10578, 0)),
        ("tprime;", (8244, 0)),
        ("nmid", (0, 0)),
        ("LeftFl", (0, 0)),
        ("NotGreaterEqual", (0, 0)),
        ("DownTe", (0, 0)),
        ("Ubr", (0, 0)),
        ("ascr", (0, 0)),
        ("esdot", (0, 0)),
        ("ropar;", (10630, 0)),
        ("LowerRightArrow;", (8600, 0)),
        ("sube;", (8838, 0)),
        ("mumap", (0, 0)),
        ("nca", (0, 0)),
        ("ShortUpA", (0, 0)),
        ("RightUpTeeVecto", (0, 0)),
        ("dow", (0, 0)),
        ("vartria", (0, 0)),
        ("NotDoubleVerticalB", (0, 0)),
        ("LeftDo", (0, 0)),
        ("xn", (0, 0)),
        ("ws", (0, 0)),
        ("yi", (0, 0)),
        ("luruh", (0, 0)),
        ("heartsuit;", (9829, 0)),
        ("olt", (0, 0)),
        ("NestedLessL", (0, 0)),
        ("NotReverseE", (0, 0)),
        ("REG;", (174, 0)),
        ("sqs", (0, 0)),
        ("geqslan", (0, 0)),
        ("precnapp", (0, 0)),
        ("FilledVery", (0, 0)),
        ("bigot", (0, 0)),
        ("rAt", (0, 0)),
        ("leftrighthar", (0, 0)),
        ("gesdoto;", (10882, 0)),
        ("LeftUpVectorB", (0, 0)),
        ("Pscr", (0, 0)),
        ("umacr", (0, 0)),
        ("rati", (0, 0)),
        ("Pfr", (0, 0)),
        ("capand;", (10820, 0)),
        ("thicks", (0, 0)),
        ("varka", (0, 0)),
        ("Mf", (0, 0)),
        ("subplu", (0, 0)),
        ("gtreqql", (0, 0)),
        ("dd", (0, 0)),
        ("complexes", (0, 0)),
        ("ubr", (0, 0)),
        ("xrarr", (0, 0)),
        ("phmmat;", (8499, 0)),
        ("UpArrowBar", (0, 0)),
        ("subsetneqq", (0, 0)),
        ("nparsl", (0, 0)),
        ("raquo;", (187, 0)),
        ("Uarr;", (8607, 0)),
        ("homth", (0, 0)),
        ("uuarr", (0, 0)),
        ("Supset", (0, 0)),
        ("triangleq;", (8796, 0)),
        ("tscr", (0, 0)),
        ("DownBr", (0, 0)),
        ("LessGre", (0, 0)),
        ("NotDo", (0, 0)),
        ("OpenCurlyDoubleQuo", (0, 0)),
        ("Fcy", (0, 0)),
        ("eparsl", (0, 0)),
        ("Escr;", (8496, 0)),
        ("bottom;", (8869, 0)),
        ("Fsc", (0, 0)),
        ("curlyw", (0, 0)),
        ("ShortU", (0, 0)),
        ("NestedL", (0, 0)),
        ("hookrigh", (0, 0)),
        ("sdot;", (8901, 0)),
        ("gtrdo", (0, 0)),
        ("DoubleLeftTe", (0, 0)),
        ("iacute;", (237, 0)),
        ("leftright", (0, 0)),
        ("NotPrecedesSlan", (0, 0)),
        ("Omega", (0, 0)),
        ("ovba", (0, 0)),
        ("sstar", (0, 0)),
        ("ohm;", (937, 0)),
        ("Lambda", (0, 0)),
        ("nleq", (0, 0)),
        ("ges", (0, 0)),
        ("LeftAngleBrack", (0, 0)),
        ("gsim", (0, 0)),
        ("NotTilde", (0, 0)),
        ("nvDash;", (8877, 0)),
        ("CounterClockwiseC", (0, 0)),
        ("lne;", (10887, 0)),
        ("NotCongruen", (0, 0)),
        ("twoheadrightarro", (0, 0)),
        ("re", (0, 0)),
        ("curv", (0, 0)),
        ("reals", (0, 0)),
        ("epsi;", (949, 0)),
        ("Dopf", (0, 0)),
        ("mu", (0, 0)),
        ("NotSquareSubse", (0, 0)),
        ("boxmin", (0, 0)),
        ("succeq;", (10928, 0)),
        ("rcaron", (0, 0)),
        ("longright", (0, 0)),
        ("tdot;", (8411, 0)),
        ("be", (0, 0)),
        ("cupcup;", (10826, 0)),
        ("pitch", (0, 0)),
        ("jse", (0, 0)),
        ("backep", (0, 0)),
        ("yacu", (0, 0)),
        ("gjcy", (0, 0)),
        ("sqsubset;", (8847, 0)),
        ("risingdot", (0, 0)),
        ("NestedGrea", (0, 0)),
        ("mnpl", (0, 0)),
        ("LeftUpTeeVe", (0, 0)),
        ("ands", (0, 0)),
        ("topci", (0, 0)),
        ("nrarrc", (0, 0)),
        ("propto;", (8733, 0)),
        ("curlyeqprec", (0, 0)),
        ("ll;", (8810, 0)),
        ("xodot", (0, 0)),
        ("maltese;", (10016, 0)),
        ("diamond;", (8900, 0)),
        ("nRightar", (0, 0)),
        ("C", (0, 0)),
        ("xm", (0, 0)),
        ("uuml", (252, 0)),
        ("risi", (0, 0)),
        ("Da", (0, 0)),
        ("gnapp", (0, 0)),
        ("npar", (0, 0)),
        ("NotElement;", (8713, 0)),
        ("IEcy;", (1045, 0)),
        ("curvearr", (0, 0)),
        ("FilledVeryS", (0, 0)),
        ("hard", (0, 0)),
        ("diam", (0, 0)),
        ("Yum", (0, 0)),
        ("Tcaron;", (356, 0)),
        ("Igrav", (0, 0)),
        ("nexis", (0, 0)),
        ("NegativeMediu", (0, 0)),
        ("otimes", (0, 0)),
        ("wop", (0, 0)),
        ("trianglel", (0, 0)),
        ("bscr;", (119991, 0)),
        ("oline;", (8254, 0)),
        ("Coprodu", (0, 0)),
        ("plusaci", (0, 0)),
        ("lrar", (0, 0)),
        ("Center", (0, 0)),
        ("fno", (0, 0)),
        ("nsh", (0, 0)),
        ("Ouml;", (214, 0)),
        ("nced", (0, 0)),
        ("NotSuper", (0, 0)),
        ("cd", (0, 0)),
        ("longrightarr", (0, 0)),
        ("supdsu", (0, 0)),
        ("nvlt;", (60, 8402)),
        ("Eacu", (0, 0)),
        ("expect", (0, 0)),
        ("Am", (0, 0)),
        ("InvisibleTim", (0, 0)),
        ("midcir", (0, 0)),
        ("setminu", (0, 0)),
        ("larrpl", (0, 0)),
        ("larrpl;", (10553, 0)),
        ("roang", (0, 0)),
        ("App", (0, 0)),
        ("NotGreaterTil", (0, 0)),
        ("rbrks", (0, 0)),
        ("VeryThinSpa", (0, 0)),
        ("lthre", (0, 0)),
        ("rla", (0, 0)),
        ("lsaqu", (0, 0)),
        ("NotGreaterSl", (0, 0)),
        ("angmsdaa;", (10664, 0)),
        ("NotSquar", (0, 0)),
        ("LeftAr", (0, 0)),
        ("us", (0, 0)),
        ("ecar", (0, 0)),
        ("Diffe", (0, 0)),
        ("gneq;", (10888, 0)),
        ("esd", (0, 0)),
        ("DoubleUpDow", (0, 0)),
        ("varphi;", (981, 0)),
        ("ntgl", (0, 0)),
        ("Imac", (0, 0)),
        ("til", (0, 0)),
        ("frasl;", (8260, 0)),
        ("caps;", (8745, 65024)),
        ("Uacu", (0, 0)),
        ("models;", (8871, 0)),
        ("RoundIm", (0, 0)),
        ("Otil", (0, 0)),
        ("Barv;", (10983, 0)),
        ("lBarr;", (10510, 0)),
        ("Rightar", (0, 0)),
        ("clubsuit;", (9827, 0)),
        ("minusd;", (8760, 0)),
        ("rmousta", (0, 0)),
        ("bsime;", (8909, 0)),
        ("TildeTilde;", (8776, 0)),
        ("acut", (0, 0)),
        ("NotGreaterEqual;", (8817, 0)),
        ("gcir", (0, 0)),
        ("subedot;", (10947, 0)),
        ("Epsi", (0, 0)),
        ("rightarrowtail;", (8611, 0)),
        ("quatin", (0, 0)),
        ("boxDl", (0, 0)),
        ("hookri", (0, 0)),
        ("circledci", (0, 0)),
        ("HumpDownHum", (0, 0)),
        ("nvle;", (8804, 8402)),
        ("longleftrighta", (0, 0)),
        ("measuredangl", (0, 0)),
        ("dA", (0, 0)),
        ("circledS", (0, 0)),
        ("iecy;", (1077, 0)),
        ("qint", (0, 0)),
        ("SmallCi", (0, 0)),
        ("rcy;", (1088, 0)),
        ("period", (0, 0)),
        ("Ug", (0, 0)),
        ("measur", (0, 0)),
        ("umacr;", (363, 0)),
        ("is", (0, 0)),
        ("backsi", (0, 0)),
        ("SmallC", (0, 0)),
        ("auml", (228, 0)),
        ("bigoplus;", (10753, 0)),
        ("drcorn", (0, 0)),
        ("ShortUpArrow;", (8593, 0)),
        ("rppolin", (0, 0)),
        ("Counte", (0, 0)),
        ("DownRightTe", (0, 0)),
        ("eplu", (0, 0)),
        ("boxVh", (0, 0)),
        ("vBar", (0, 0)),
        ("perp;", (8869, 0)),
        ("Lcedil", (0, 0)),
        ("downharpoonri", (0, 0)),
        ("rdqu", (0, 0)),
        ("ContourInteg", (0, 0)),
        ("ecirc", (234, 0)),
        ("dotsqu", (0, 0)),
        ("mstpos", (0, 0)),
        ("CloseCurlyDouble", (0, 0)),
        ("Delta", (0, 0)),
        ("llc", (0, 0)),
        ("NotSquareSubsetEqual", (0, 0)),
        ("Umacr", (0, 0)),
        ("LeftDownVectorB", (0, 0)),
        ("Ncedil", (0, 0)),
        ("trimi", (0, 0)),
        ("andsl", (0, 0)),
        ("rAarr;", (8667, 0)),
        ("seA", (0, 0)),
        ("Small", (0, 0)),
        ("Vcy", (0, 0)),
        ("coloneq;", (8788, 0)),
        ("Verbar", (0, 0)),
        ("rdca", (0, 0)),
        ("SmallCircle", (0, 0)),
        ("ye", (0, 0)),
        ("bl", (0, 0)),
        ("pho", (0, 0)),
        ("amacr;", (257, 0)),
        ("MinusPlus", (0, 0)),
        ("lbrace", (0, 0)),
        ("Sub;", (8912, 0)),
        ("nlArr", (0, 0)),
        ("zigr", (0, 0)),
        ("Lap", (0, 0)),
        ("Poinc", (0, 0)),
        ("longr", (0, 0)),
        ("DiacriticalDou", (0, 0)),
        ("Bf", (0, 0)),
        ("ltrif;", (9666, 0)),
        ("varthet", (0, 0)),
        ("thin", (0, 0)),
        ("NotLeftTriangleBar", (0, 0)),
        ("ogr", (0, 0)),
        ("LessGr", (0, 0)),
        ("complex", (0, 0)),
        ("VerticalTilde;", (8768, 0)),
        ("half;", (189, 0)),
        ("NotSubsetEqual", (0, 0)),
        ("frac58;", (8541, 0)),
        ("napid;", (8779, 824)),
        ("Gb", (0, 0)),
        ("rbrksld;", (10638, 0)),
        ("nL", (0, 0)),
        ("rrarr", (0, 0)),
        ("vBa", (0, 0)),
        ("NotGreaterFu", (0, 0)),
        ("DownLeftRightVect", (0, 0)),
        ("circl", (0, 0)),
        ("xsqc", (0, 0)),
        ("harrw;", (8621, 0)),
        ("PrecedesSlantEqu", (0, 0)),
        ("ar", (0, 0)),
        ("TildeFu", (0, 0)),
        ("triangleleft;", (9667, 0)),
        ("Superse", (0, 0)),
        ("ltri", (0, 0)),
        ("Ugrav", (0, 0)),
        ("mopf;", (120158, 0)),
        ("fallingdo", (0, 0)),
        ("Gcir", (0, 0)),
        ("zscr", (0, 0)),
        ("iopf;", (120154, 0)),
        ("ltrPar", (0, 0)),
        ("bigtriangleup;", (9651, 0)),
        ("setminus", (0, 0)),
        ("yum", (0, 0)),
        ("ration", (0, 0)),
        ("rhar", (0, 0)),
        ("Partial", (0, 0)),
        ("updowna", (0, 0)),
        ("UnderBracket;", (9141, 0)),
        ("topb", (0, 0)),
        ("Iukcy;", (1030, 0)),
        ("asympe", (0, 0)),
        ("notin;", (8713, 0)),
        ("scsi", (0, 0)),
        ("UnderParenthesis", (0, 0)),
        ("cudarrr;", (10549, 0)),
        ("bo", (0, 0)),
        ("tc", (0, 0)),
        ("urcr", (0, 0)),
        ("gfr", (0, 0)),
        ("NotNestedLess", (0, 0)),
        ("xrA", (0, 0)),
        ("wr", (0, 0)),
        ("nequiv;", (8802, 0)),
        ("NonBreakingSpac", (0, 0)),
        ("bempty", (0, 0)),
        ("ogo", (0, 0)),
        ("Assig", (0, 0)),
        ("Sce", (0, 0)),
        ("plustw", (0, 0)),
        ("lrco", (0, 0)),
        ("NotLeftTr", (0, 0)),
        ("tfr;", (120113, 0)),
        ("subsup", (0, 0)),
        ("kop", (0, 0)),
        ("phon", (0, 0)),
        ("doublebar", (0, 0)),
        ("oror;", (10838, 0)),
        ("veeba", (0, 0)),
        ("Itilde", (0, 0)),
        ("xlAr", (0, 0)),
        ("kced", (0, 0)),
        ("ShortDown", (0, 0)),
        ("rd", (0, 0)),
        ("NoBreak", (0, 0)),
        ("NotTildeEqu", (0, 0)),
        ("caron", (0, 0)),
        ("expectatio", (0, 0)),
        ("UnderParenthes", (0, 0)),
        ("Backslas", (0, 0)),
        ("Ccon", (0, 0)),
        ("varepsilon", (0, 0)),
        ("YUcy;", (1070, 0)),
        ("urcor", (0, 0)),
        ("MinusPl", (0, 0)),
        ("CircleTime", (0, 0)),
        ("SquareSub", (0, 0)),
        ("Ucy;", (1059, 0)),
        ("hopf", (0, 0)),
        ("NotDouble", (0, 0)),
        ("Dagger;", (8225, 0)),
        ("leqq", (0, 0)),
        ("Fo", (0, 0)),
        ("srarr;", (8594, 0)),
        ("NegativeVe", (0, 0)),
        ("operp", (0, 0)),
        ("RightTriangleE", (0, 0)),
        ("ccaro", (0, 0)),
        ("NotVerticalBa", (0, 0)),
        ("profs", (0, 0)),
        ("PrecedesSlantE", (0, 0)),
        ("UnderParenthe", (0, 0)),
        ("tstr", (0, 0)),
        ("NotRightTriangleBa", (0, 0)),
        ("ccupssm;", (10832, 0)),
        ("vartriangleleft;", (8882, 0)),
        ("eqslant", (0, 0)),
        ("flat", (0, 0)),
        ("eqslantless", (0, 0)),
        ("nvrtrie", (0, 0)),
        ("mea", (0, 0)),
        ("DoubleCo", (0, 0)),
        ("twoheadleftar", (0, 0)),
        ("uw", (0, 0)),
        ("RightDoubleBracke", (0, 0)),
        ("ShortUpArrow", (0, 0)),
        ("rbrack", (0, 0)),
        ("lrm", (0, 0)),
        ("LeftTeeV", (0, 0)),
        ("GreaterGreat", (0, 0)),
        ("nvi", (0, 0)),
        ("RightUpVecto", (0, 0)),
        ("ntrianglerighte", (0, 0)),
        ("Osla", (0, 0)),
        ("Rang;", (10219, 0)),
        ("thicksi", (0, 0)),
        ("Squ", (0, 0)),
        ("nwnea", (0, 0)),
        ("Sqrt;", (8730, 0)),
        ("nvDash", (0, 0)),
        ("Differen", (0, 0)),
        ("harrci", (0, 0)),
        ("oa", (0, 0)),
        ("aacute;", (225, 0)),
        ("rdl", (0, 0)),
        ("qfr;", (120110, 0)),
        ("tint;", (8749, 0)),
        ("NotRightTriangleB", (0, 0)),
        ("bN", (0, 0)),
        ("napos;", (329, 0)),
        ("lfr", (0, 0)),
        ("Zcy;", (1047, 0)),
        ("smallsetminus", (0, 0)),
        ("supm", (0, 0)),
        ("plusm", (0, 0)),
        ("downharp", (0, 0)),
        ("LeftDownVect", (0, 0)),
        ("nap;", (8777, 0)),
        ("Diacritical", (0, 0)),
        ("leftrightharpoon", (0, 0)),
        ("Coproduct", (0, 0)),
        ("duha", (0, 0)),
        ("Hump", (0, 0)),
        ("isinsv;", (8947, 0)),
        ("hardcy;", (1098, 0)),
        ("oacut", (0, 0)),
        ("blackloze", (0, 0)),
        ("xwe", (0, 0)),
        ("sup1;", (185, 0)),
        ("DiacriticalAc", (0, 0)),
        ("left", (0, 0)),
        ("Oa", (0, 0)),
        ("udha", (0, 0)),
        ("nge", (0, 0)),
        ("Coproduc", (0, 0)),
        ("hyphen;", (8208, 0)),
        ("RightTriangleBa", (0, 0)),
        ("intlar", (0, 0)),
        ("Zdot;", (379, 0)),
        ("DiacriticalDoubleAcut", (0, 0)),
        ("ShortR", (0, 0)),
        ("bkarow", (0, 0)),
        ("th", (0, 0)),
        ("lar", (0, 0)),
        ("cud", (0, 0)),
        ("ne;", (8800, 0)),
        ("RightAn", (0, 0)),
        ("vD", (0, 0)),
        ("downharpoonleft", (0, 0)),
        ("pitchfo", (0, 0)),
        ("lozenge", (0, 0)),
        ("mn", (0, 0)),
        ("Z", (0, 0)),
        ("expectat", (0, 0)),
        ("Horizon", (0, 0)),
        ("Tcedil;", (354, 0)),
        ("trid", (0, 0)),
        ("NotLessLe", (0, 0)),
        ("prim", (0, 0)),
        ("ReverseEqu", (0, 0)),
        ("Utilde;", (360, 0)),
        ("omacr", (0, 0)),
        ("mapstodow", (0, 0)),
        ("cemptyv", (0, 0)),
        ("UpArrow", (0, 0)),
        ("qf", (0, 0)),
        ("J", (0, 0)),
        ("diamon", (0, 0)),
        ("HorizontalLi", (0, 0)),
        ("Sqrt", (0, 0)),
        ("kap", (0, 0)),
        ("CloseCurlyQ", (0, 0)),
        ("EqualTil", (0, 0)),
        ("fallingdotse", (0, 0)),
        ("gE", (0, 0)),
        ("sqsubset", (0, 0)),
        ("FilledVerySmallSqua", (0, 0)),
        ("UpperRightA", (0, 0)),
        ("NotVerti", (0, 0)),
        ("ntlg", (0, 0)),
        ("pm", (0, 0)),
        ("DiacriticalDouble", (0, 0)),
        ("NotTildeFu", (0, 0)),
        ("xharr;", (10231, 0)),
        ("leqslant", (0, 0)),
        ("DZ", (0, 0)),
        ("twoheadleftarrow;", (8606, 0)),
        ("Ic", (0, 0)),
        ("DownR", (0, 0)),
        ("realpar", (0, 0)),
        ("Neste", (0, 0)),
        ("nvltrie;", (8884, 8402)),
        ("lcedil;", (316, 0)),
        ("lur", (0, 0)),
        ("NotSucceedsEqua", (0, 0)),
        ("gtrle", (0, 0)),
        ("qu", (0, 0)),
        ("RightTriangleEqu", (0, 0)),
        ("ReverseEqui", (0, 0)),
        ("Gcedi", (0, 0)),
        ("chi;", (967, 0)),
        ("thorn", (254, 0)),
        ("iinfi", (0, 0)),
        ("midc", (0, 0)),
        ("HilbertSpace", (0, 0)),
        ("lnappro", (0, 0)),
        ("cy", (0, 0)),
        ("marke", (0, 0)),
        ("lrtri;", (8895, 0)),
        ("smashp", (0, 0)),
        ("RightUpVectorBar", (0, 0)),
        ("ngt", (0, 0)),
        ("jcirc", (0, 0)),
        ("Epsilo", (0, 0)),
        ("GreaterTi", (0, 0)),
        ("oacute;", (243, 0)),
        ("tritime", (0, 0)),
        ("iogo", (0, 0)),
        ("looparrowr", (0, 0)),
        ("Lac", (0, 0)),
        ("vsupnE", (0, 0)),
        ("cura", (0, 0)),
        ("circ;", (710, 0)),
        ("dzcy", (0, 0)),
        ("napprox;", (8777, 0)),
        ("ncongdot;", (10861, 824)),
        ("DoubleLeftRightAr", (0, 0)),
        ("Vertic", (0, 0)),
        ("npart", (0, 0)),
        ("triminus", (0, 0)),
        ("egsdot;", (10904, 0)),
        ("hfr", (0, 0)),
        ("longleftri", (0, 0)),
        ("rsquor", (0, 0)),
        ("DownLeftTeeVec", (0, 0)),
        ("zdot", (0, 0)),
        ("SHCHcy", (0, 0)),
        ("Intersec", (0, 0)),
        ("An", (0, 0)),
        ("ccirc;", (265, 0)),
        ("swarro", (0, 0)),
        ("quest", (0, 0)),
        ("frac35", (0, 0)),
        ("PrecedesSlantEqual", (0, 0)),
        ("sbquo;", (8218, 0)),
        ("imacr;", (299, 0)),
        ("twoheadleftarr", (0, 0)),
        ("leftthreetimes", (0, 0)),
        ("sigma;", (963, 0)),
        ("boxh", (0, 0)),
        ("SquareSuperset;", (8848, 0)),
        ("eqsl", (0, 0)),
        ("circeq", (0, 0)),
        ("lesseqgt", (0, 0)),
        ("ufis", (0, 0)),
        ("nsubseteqq", (0, 0)),
        ("veebar", (0, 0)),
        ("bigotimes;", (10754, 0)),
        ("brvb", (0, 0)),
        ("LeftTeeArrow;", (8612, 0)),
        ("barw", (0, 0)),
        ("rAtail", (0, 0)),
        ("DownBrev", (0, 0)),
        ("fo", (0, 0)),
        ("Exists;", (8707, 0)),
        ("equals;", (61, 0)),
        ("upha", (0, 0)),
        ("Proport", (0, 0)),
        ("vel", (0, 0)),
        ("subp", (0, 0)),
        ("Gf", (0, 0)),
        ("hoarr", (0, 0)),
        ("thksim", (0, 0)),
        ("atil", (0, 0)),
        ("nleqslant", (0, 0)),
        ("mscr;", (120002, 0)),
        ("lsime;", (10893, 0)),
        ("Hstr", (0, 0)),
        ("yfr;", (120118, 0)),
        ("UpArrowBa", (0, 0)),
        ("filig", (0, 0)),
        ("DoubleLeftRight", (0, 0)),
        ("gci", (0, 0)),
        ("dbkarow", (0, 0)),
        ("lEg", (0, 0)),
        ("bprime", (0, 0)),
        ("nearrow", (0, 0)),
        ("HumpEq", (0, 0)),
        ("forkv;", (10969, 0)),
        ("Igra", (0, 0)),
        ("Bet", (0, 0)),
        ("RightArrowLeft", (0, 0)),
        ("RightDownTeeVector;", (10589, 0)),
        ("CO", (0, 0)),
        ("rarrtl", (0, 0)),
        ("CapitalDi", (0, 0)),
        ("TripleD", (0, 0)),
        ("xcup;", (8899, 0)),
        ("sci", (0, 0)),
        ("Short", (0, 0)),
        ("bdquo", (0, 0)),
        ("zcar", (0, 0)),
        ("RightCeiling;", (8969, 0)),
        ("lcei", (0, 0)),
        ("ijl", (0, 0)),
        ("dlcorn;", (8990, 0)),
        ("circlea", (0, 0)),
        ("risingd", (0, 0)),
        ("Cd", (0, 0)),
        ("Rsc", (0, 0)),
        ("NotPrecedesSlantEqu", (0, 0)),
        ("thi", (0, 0)),
        ("efD", (0, 0)),
        ("LessEqualGr", (0, 0)),
        ("Dstrok;", (272, 0)),
        ("Iogo", (0, 0)),
        ("Hacek", (0, 0)),
        ("blacktri", (0, 0)),
        ("Integral;", (8747, 0)),
        ("lessgtr;", (8822, 0)),
        ("notinE;", (8953, 824)),
        ("xci", (0, 0)),
        ("LeftT", (0, 0)),
        ("esdo", (0, 0)),
        ("GreaterSlant", (0, 0)),
        ("NotGreaterTild", (0, 0)),
        ("DoubleLeftRightArrow", (0, 0)),
        ("nLeftarrow", (0, 0)),
        ("supsub", (0, 0)),
        ("Uti", (0, 0)),
        ("Longleftarr", (0, 0)),
        ("hear", (0, 0)),
        ("bigw", (0, 0)),
        ("Sm", (0, 0)),
        ("Hscr", (0, 0)),
        ("iexc", (0, 0)),
        ("Tfr;", (120087, 0)),
        ("rac", (0, 0)),
        ("supsi", (0, 0)),
        ("cirfnint;", (10768, 0)),
        ("lefta", (0, 0)),
        ("lla", (0, 0)),
        ("qopf;", (120162, 0)),
        ("les;", (10877, 0)),
        ("EmptyVerySmallSqua", (0, 0)),
        ("NotDoubleVerticalBar;", (8742, 0)),
        ("barvee", (0, 0)),
        ("ccups;", (10828, 0)),
        ("PartialD", (0, 0)),
        ("telrec", (0, 0)),
        ("boxVh;", (9579, 0)),
        ("VeryTh", (0, 0)),
        ("NotVertical", (0, 0)),
        ("ShortDownAr", (0, 0)),
        ("supsub;", (10964, 0)),
        ("blacklozeng", (0, 0)),
        ("nRightarrow", (0, 0)),
        ("Osc", (0, 0)),
        ("rbbrk", (0, 0)),
        ("iii", (0, 0)),
        ("Uscr;", (119984, 0)),
        ("RightVector;", (8640, 0)),
        ("geqs", (0, 0)),
        ("Agra", (0, 0)),
        ("FilledSm", (0, 0)),
        ("rArr", (0, 0)),
        ("Equal;", (10869, 0)),
        ("NotSupersetEq", (0, 0)),
        ("capcap", (0, 0)),
        ("ntild", (0, 0)),
        ("rtim", (0, 0)),
        ("nvda", (0, 0)),
        ("NestedLess", (0, 0)),
        ("lnsim;", (8934, 0)),
        ("ulcro", (0, 0)),
        ("oscr;", (8500, 0)),
        ("tcaron", (0, 0)),
        ("CloseCurlyDoubl", (0, 0)),
        ("sqcu", (0, 0)),
        ("olcir;", (10686, 0)),
        ("And", (0, 0)),
        ("or;", (8744, 0)),
        ("Scir", (0, 0)),
        ("Leftrightarr", (0, 0)),
        ("NotLessSlantEqu", (0, 0)),
        ("Ecy;", (1069, 0)),
        ("rti", (0, 0)),
        ("gt", (62, 0)),
        ("boxHu;", (9575, 0)),
        ("napp", (0, 0)),
        ("ocy", (0, 0)),
        ("DDot", (0, 0)),
        ("ques", (0, 0)),
        ("ltdot", (0, 0)),
        ("EqualTild", (0, 0)),
        ("nleftrightarrow", (0, 0)),
        ("twohead", (0, 0)),
        ("Scirc;", (348, 0)),
        ("fpar", (0, 0)),
        ("UpArrowDownArrow;", (8645, 0)),
        ("Aog", (0, 0)),
        ("point", (0, 0)),
        ("leftar", (0, 0)),
        ("lbbrk", (0, 0)),
        ("DoubleRightArr", (0, 0)),
        ("CounterClockwiseContour", (0, 0)),
        ("ldrdh", (0, 0)),
        ("NotEqualTil", (0, 0)),
        ("Sf", (0, 0)),
        ("uci", (0, 0)),
        ("rsc", (0, 0)),
        ("Popf", (0, 0)),
        ("NotSubsetEqual;", (8840, 0)),
        ("swnwa", (0, 0)),
        ("fop", (0, 0)),
        ("ThickSpa", (0, 0)),
        ("luruhar;", (10598, 0)),
        ("nsim", (0, 0)),
        ("po", (0, 0)),
        ("Bumpeq;", (8782, 0)),
        ("vare", (0, 0)),
        ("gtre", (0, 0)),
        ("downhar", (0, 0)),
        ("Oopf", (0, 0)),
        ("blacktriangled", (0, 0)),
        ("UpTeeArrow", (0, 0)),
        ("cuwe", (0, 0)),
        ("oror", (0, 0)),
        ("Exponentia", (0, 0)),
        ("zacute", (0, 0)),
        ("RightDo", (0, 0)),
        ("gtreqless", (0, 0)),
        ("Dif", (0, 0)),
        ("Qo", (0, 0)),
        ("vc", (0, 0)),
        ("trp", (0, 0)),
        ("nsqsube;", (8930, 0)),
        ("downdownarrows;", (8650, 0)),
        ("rt", (0, 0)),
        ("CircleMin", (0, 0)),
        ("Invi", (0, 0)),
        ("so", (0, 0)),
        ("rightl", (0, 0)),
        ("boxHD", (0, 0)),
        ("Upsilon;", (933, 0)),
        ("omega", (0, 0)),
        ("beth", (0, 0)),
        ("mcomma;", (10793, 0)),
        ("lac", (0, 0)),
        ("rArr;", (8658, 0)),
        ("alph", (0, 0)),
        ("cupdot", (0, 0)),
        ("bloc", (0, 0)),
        ("mc", (0, 0)),
        ("Lsc", (0, 0)),
        ("ce", (0, 0)),
        ("LessEqualGreate", (0, 0)),
        ("imagpa", (0, 0)),
        ("dotminus", (0, 0)),
        ("tria", (0, 0)),
        ("LeftCeiling;", (8968, 0)),
        ("ccu", (0, 0)),
        ("shortmid;", (8739, 0)),
        ("mark", (0, 0)),
        ("trianglelefte", (0, 0)),
        ("qs", (0, 0)),
        ("trianglelef", (0, 0)),
        ("mal", (0, 0)),
        ("Righta", (0, 0)),
        ("checkmark;", (10003, 0)),
        ("xsc", (0, 0)),
        ("ksc", (0, 0)),
        ("leftharp", (0, 0)),
        ("ReverseEleme", (0, 0)),
        ("fil", (0, 0)),
        ("incare", (0, 0)),
        ("angst;", (197, 0)),
        ("NotGreaterGreate", (0, 0)),
        ("varph", (0, 0)),
        ("GreaterFullEq", (0, 0)),
        ("NotSubsetE", (0, 0)),
        ("tild", (0, 0)),
        ("ClockwiseContourInte", (0, 0)),
        ("LowerRigh", (0, 0)),
        ("Greater", (0, 0)),
        ("dfr", (0, 0)),
        ("subE;", (10949, 0)),
        ("rx", (0, 0)),
        ("RightDoubleB", (0, 0)),
        ("Alpha", (0, 0)),
        ("blk12;", (9618, 0)),
        ("DotEqual;", (8784, 0)),
        ("LeftUp", (0, 0)),
        ("NotPrecedesEqua", (0, 0)),
        ("Nu", (0, 0)),
        ("UnderPa", (0, 0)),
        ("vzigza", (0, 0)),
        ("nexists;", (8708, 0)),
        ("leftarr", (0, 0)),
        ("lesg", (0, 0)),
        ("DoubleD", (0, 0)),
        ("beta", (0, 0)),
        ("LeftDownVectorBar;", (10585, 0)),
        ("va", (0, 0)),
        ("Longrightar", (0, 0)),
        ("cuve", (0, 0)),
        ("bot", (0, 0)),
        ("bottom", (0, 0)),
        ("Fou", (0, 0)),
        ("blacksquare", (0, 0)),
        ("SucceedsT", (0, 0)),
        ("Nced", (0, 0)),
        ("uscr;", (120010, 0)),
        ("ddarr;", (8650, 0)),
        ("gtreqless;", (8923, 0)),
        ("congdot;", (10861, 0)),
        ("deg;", (176, 0)),
        ("ffil", (0, 0)),
        ("gcy;", (1075, 0)),
        ("xl", (0, 0)),
        ("lan", (0, 0)),
        ("le", (0, 0)),
        ("NotTildeEq", (0, 0)),
        ("ium", (0, 0)),
        ("utrif;", (9652, 0)),
        ("gj", (0, 0)),
        ("CenterD", (0, 0)),
        ("precneqq;", (10933, 0)),
        ("Imagin", (0, 0)),
        ("mult", (0, 0)),
        ("rightth", (0, 0)),
        ("NotPrecedes", (0, 0)),
        ("boxur", (0, 0)),
        ("Uparrow", (0, 0)),
        ("pfr", (0, 0)),
        ("ubrc", (0, 0)),
        ("ang", (0, 0)),
        ("NotEqua", (0, 0)),
        ("UpDownArr", (0, 0)),
        ("TildeFullEq", (0, 0)),
        ("Hcirc", (0, 0)),
        ("CloseCurlyDoubleQuote;", (8221, 0)),
        ("Poincar", (0, 0)),
        ("smallse", (0, 0)),
        ("simp", (0, 0)),
        ("leqsla", (0, 0)),
        ("Rrig", (0, 0)),
        ("uo", (0, 0)),
        ("east", (0, 0)),
        ("IOcy;", (1025, 0)),
        ("LeftTeeAr", (0, 0)),
        ("rarrhk", (0, 0)),
        ("UpperLef", (0, 0)),
        ("mum", (0, 0)),
        ("gtque", (0, 0)),
        ("DownLeftTeeVecto", (0, 0)),
        ("Supset;", (8913, 0)),
        ("NotHumpEqua", (0, 0)),
        ("rightrighta", (0, 0)),
        ("abreve", (0, 0)),
        ("Lapla", (0, 0)),
        ("bigsta", (0, 0)),
        ("ell", (0, 0)),
        ("RightCeilin", (0, 0)),
        ("Longleftrightarr", (0, 0)),
        ("rightsquigarr", (0, 0)),
        ("iquest", (191, 0)),
        ("UpT", (0, 0)),
        ("supsup", (0, 0)),
        ("ZeroW", (0, 0)),
        ("shcy", (0, 0)),
        ("mda", (0, 0)),
        ("SucceedsSlantEqua", (0, 0)),
        ("ncy;", (1085, 0)),
        ("scs", (0, 0)),
        ("SquareInters", (0, 0)),
        ("rharul", (0, 0)),
        ("Verti", (0, 0)),
        ("updown", (0, 0)),
        ("lstrok;", (322, 0)),
        ("vartheta", (0, 0)),
        ("nls", (0, 0)),
        ("setmn", (0, 0)),
        ("supE", (0, 0)),
        ("bigop", (0, 0)),
        ("DoubleLongLeftRightArrow;", (10234, 0)),
        ("Vdash", (0, 0)),
        ("dtrif", (0, 0)),
        ("lozf;", (10731, 0)),
        ("odash;", (8861, 0)),
        ("larrtl;", (8610, 0)),
        ("ClockwiseCont", (0, 0)),
        ("Assi", (0, 0)),
        ("udbl", (0, 0)),
        ("Imagina", (0, 0)),
        ("nleftrig", (0, 0)),
        ("acE;", (8766, 819)),
        ("gneqq;", (8809, 0)),
        ("and;", (8743, 0)),
        ("downharpoonright;", (8642, 0)),
        ("diamondsuit", (0, 0)),
        ("bigoplus", (0, 0)),
        ("oint;", (8750, 0)),
        ("cirfn", (0, 0)),
        ("nleftrightarro", (0, 0)),
        ("vellip;", (8942, 0)),
        ("LessFullEq", (0, 0)),
        ("blacktriangler", (0, 0)),
        ("DiacriticalA", (0, 0)),
        ("VDa", (0, 0)),
        ("leftharpoonup;", (8636, 0)),
        ("Na", (0, 0)),
        ("VerticalLin", (0, 0)),
        ("iscr;", (119998, 0)),
        ("ndas", (0, 0)),
        ("lnsi", (0, 0)),
        ("langl", (0, 0)),
        ("sext;", (10038, 0)),
        ("DownRightVectorBar;", (10583, 0)),
        ("ldrd", (0, 0)),
        ("Zcaro", (0, 0)),
        ("Cedilla;", (184, 0)),
        ("NotGreaterE", (0, 0)),
        ("Zacute", (0, 0)),
        ("telr", (0, 0)),
        ("xhAr", (0, 0)),
        ("NotTildeFul", (0, 0)),
        ("ContourI", (0, 0)),
        ("varkappa", (0, 0)),
        ("uArr;", (8657, 0)),
        ("OpenCurlyDoubleQ", (0, 0)),
        ("triangledown;", (9663, 0)),
        ("ucy;", (1091, 0)),
        ("scy;", (1089, 0)),
        ("NotLessLess", (0, 0)),
        ("tst", (0, 0)),
        ("eDo", (0, 0)),
        ("Ov", (0, 0)),
        ("twixt", (0, 0)),
        ("Poincareplane", (0, 0)),
        ("frac7", (0, 0)),
        ("xutri", (0, 0)),
        ("boxh;", (9472, 0)),
        ("lessapprox", (0, 0)),
        ("eqslantgtr;", (10902, 0)),
        ("ovb", (0, 0)),
        ("intp", (0, 0)),
        ("fc", (0, 0)),
        ("topfork;", (10970, 0)),
        ("nLeftrightarrow", (0, 0)),
        ("hfr;", (120101, 0)),
        ("lfloo", (0, 0)),
        ("nsq", (0, 0)),
        ("arin", (0, 0)),
        ("plusc", (0, 0)),
        ("nvH", (0, 0)),
        ("preca", (0, 0)),
        ("wedge", (0, 0)),
        ("LeftDownTeeVector", (0, 0)),
        ("GreaterGreate", (0, 0)),
        ("quaternions", (0, 0)),
        ("multim", (0, 0)),
        ("lesse", (0, 0)),
        ("r", (0, 0)),
        ("thicka", (0, 0)),
        ("LongLeftR", (0, 0)),
        ("NotH", (0, 0)),
        ("qscr", (0, 0)),
        ("NotNestedL", (0, 0)),
        ("Vv", (0, 0)),
        ("dwangle", (0, 0)),
        ("supmu", (0, 0)),
        ("NotNestedLes", (0, 0)),
        ("Ubrcy", (0, 0)),
        ("percn", (0, 0)),
        ("Uopf;", (120140, 0)),
        ("cuwed", (0, 0)),
        ("submult;", (10945, 0)),
        ("nleftrightarr", (0, 0)),
        ("vsubnE;", (10955, 65024)),
        ("nlt", (0, 0)),
        ("NegativeThinSpace;", (8203, 0)),
        ("hk", (0, 0)),
        ("DoubleDownA", (0, 0)),
        ("Gd", (0, 0)),
        ("blac", (0, 0)),
        ("ng", (0, 0)),
        ("LeftRightVecto", (0, 0)),
        ("gamma;", (947, 0)),
        ("bern", (0, 0)),
        ("zcaron", (0, 0)),
        ("gtrsi", (0, 0)),
        ("Differ", (0, 0)),
        ("swnw", (0, 0)),
        ("divideon", (0, 0)),
        ("SucceedsSlantEq", (0, 0)),
        ("leftarrowt", (0, 0)),
        ("Ver", (0, 0)),
        ("Vee;", (8897, 0)),
        ("Edot", (0, 0)),
        ("ioc", (0, 0)),
        ("nvgt", (0, 0)),
        ("small", (0, 0)),
        ("Melli", (0, 0)),
        ("timesbar", (0, 0)),
        ("curvearro", (0, 0)),
        ("OpenCurlyQuote", (0, 0)),
        ("jscr;", (119999, 0)),
        ("equivDD;", (10872, 0)),
        ("sbqu", (0, 0)),
        ("longleftrightar", (0, 0)),
        ("vark", (0, 0)),
        ("vnsu", (0, 0)),
        ("tprim", (0, 0)),
        ("LessTi", (0, 0)),
        ("Vfr", (0, 0)),
        ("lrcorner;", (8991, 0)),
        ("Scedil", (0, 0)),
        ("hookrightar", (0, 0)),
        ("circledcirc;", (8858, 0)),
        ("hstrok", (0, 0)),
        ("gtcc", (0, 0)),
        ("blacksqu", (0, 0)),
        ("FilledSmallSqu", (0, 0)),
        ("pertenk", (0, 0)),
        ("nsmi", (0, 0)),
        ("omega;", (969, 0)),
        ("Product;", (8719, 0)),
        ("Omac", (0, 0)),
        ("LeftDownTe", (0, 0)),
        ("EqualTi", (0, 0)),
        ("ohm", (0, 0)),
        ("midast", (0, 0)),
        ("curlywedge;", (8911, 0)),
        ("complement;", (8705, 0)),
        ("rtimes", (0, 0)),
        ("Lcy", (0, 0)),
        ("nldr", (0, 0)),
        ("bigtriangledown;", (9661, 0)),
        ("RightTriangleEqual", (0, 0)),
        ("nedo", (0, 0)),
        ("otimesas", (0, 0)),
        ("Gfr;", (120074, 0)),
        ("db", (0, 0)),
        ("Odb", (0, 0)),
        ("Copro", (0, 0)),
        ("Cfr", (0, 0)),
        ("leftrightsqui", (0, 0)),
        ("ys", (0, 0)),
        ("betw", (0, 0)),
        ("DiacriticalGrave;", (96, 0)),
        ("LeftRightVect", (0, 0)),
        ("St", (0, 0)),
        ("TildeFullEqu", (0, 0)),
        ("rightharpoonup", (0, 0)),
        ("Ifr", (0, 0)),
        ("zhcy", (0, 0)),
        ("rangle", (0, 0)),
        ("Zsc", (0, 0)),
        ("pluse;", (10866, 0)),
        ("Subs", (0, 0)),
        ("LeftUpDownVecto", (0, 0)),
        ("CircleDot", (0, 0)),
        ("Yc", (0, 0)),
        ("backepsilon", (0, 0)),
        ("vz", (0, 0)),
        ("ReverseEquilib", (0, 0)),
        ("CapitalDiff", (0, 0)),
        ("varrho;", (1009, 0)),
        ("lurdsha", (0, 0)),
        ("gnap;", (10890, 0)),
        ("if", (0, 0)),
        ("rarrlp", (0, 0)),
        ("gac", (0, 0)),
        ("xodo", (0, 0)),
        ("righth", (0, 0)),
        ("NotSquareSupersetEqual;", (8931, 0)),
        ("RightDownTeeVecto", (0, 0)),
        ("Ove", (0, 0)),
        ("dHar", (0, 0)),
        ("New", (0, 0)),
        ("Open", (0, 0)),
        ("precnsim;", (8936, 0)),
        ("CounterClockwiseContourIntegra", (0, 0)),
        ("rightleftharpoo", (0, 0)),
        ("Odbl", (0, 0)),
        ("gtlPa", (0, 0)),
        ("backcon", (0, 0)),
        ("sqsup;", (8848, 0)),
        ("mopf", (0, 0)),
        ("kscr;", (120000, 0)),
        ("nharr;", (8622, 0)),
        ("Ima", (0, 0)),
        ("TildeTilde", (0, 0)),
        ("Conint", (0, 0)),
        ("DownArrowUpArro", (0, 0)),
        ("circlear", (0, 0)),
        ("UpDownArrow", (0, 0)),
        ("because", (0, 0)),
        ("udarr;", (8645, 0)),
        ("tcy;", (1090, 0)),
        ("Fs", (0, 0)),
        ("boxuL;", (9563, 0)),
        ("Ls", (0, 0)),
        ("af", (0, 0)),
        ("Ko", (0, 0)),
        ("jscr", (0, 0)),
        ("Dopf;", (120123, 0)),
        ("cscr", (0, 0)),
        ("ldca", (0, 0)),
        ("Poincareplan", (0, 0)),
        ("igrav", (0, 0)),
        ("nrightarro", (0, 0)),
        ("sho", (0, 0)),
        ("kappa", (0, 0)),
        ("bemptyv;", (10672, 0)),
        ("sigmaf;", (962, 0)),
        ("era", (0, 0)),
        ("pitchfor", (0, 0)),
        ("Ubrcy;", (1038, 0)),
        ("expectation", (0, 0)),
        ("Arin", (0, 0)),
        ("mac", (0, 0)),
        ("Longleftright", (0, 0)),
        ("lurdshar", (0, 0)),
        ("cedil;", (184, 0)),
        ("lsh;", (8624, 0)),
        ("nrArr", (0, 0)),
        ("rightlefth", (0, 0)),
        ("supdot", (0, 0)),
        ("NotGreaterLess;", (8825, 0)),
        ("NegativeMedi", (0, 0)),
        ("LeftAngleBrac", (0, 0)),
        ("Ncedi", (0, 0)),
        ("amac", (0, 0)),
        ("diamondsui", (0, 0)),
        ("Emacr;", (274, 0)),
        ("NegativeThic", (0, 0)),
        ("ggg", (0, 0)),
        ("imacr", (0, 0)),
        ("lesges", (0, 0)),
        ("Agrave", (192, 0)),
        ("Vop", (0, 0)),
        ("forkv", (0, 0)),
        ("uharl;", (8639, 0)),
        ("rightarrowtail", (0, 0)),
        ("hamilt;", (8459, 0)),
        ("rmou", (0, 0)),
        ("leqslan", (0, 0)),
        ("rds", (0, 0)),
        ("ccir", (0, 0)),
        ("cwint", (0, 0)),
        ("shortp", (0, 0)),
        ("sung", (0, 0)),
        ("TRAD", (0, 0)),
        ("NotSqua", (0, 0)),
        ("DownLef", (0, 0)),
        ("aog", (0, 0)),
        ("VeryThinSp", (0, 0)),
        ("ShortLeftAr", (0, 0)),
        ("divid", (0, 0)),
        ("integ", (0, 0)),
        ("UpperLeftArro", (0, 0)),
        ("frac45;", (8536, 0)),
        ("UpperRightArrow;", (8599, 0)),
        ("hksea", (0, 0)),
        ("notinva", (0, 0)),
        ("NotEqu", (0, 0)),
        ("fnof;", (402, 0)),
        ("blk34;", (9619, 0)),
        ("infint", (0, 0)),
        ("nltri", (0, 0)),
        ("RightAngleBra", (0, 0)),
        ("HilbertSpace;", (8459, 0)),
        ("Upsi", (0, 0)),
        ("rightharpoon", (0, 0)),
        ("curl", (0, 0)),
        ("DownTeeArro", (0, 0)),
        ("rho;", (961, 0)),
        ("suphsol", (0, 0)),
        ("alp", (0, 0)),
        ("nleqslan", (0, 0)),
        ("ring", (0, 0)),
        ("bumpeq", (0, 0)),
        ("Vs", (0, 0)),
        ("LeftTrian", (0, 0)),
        ("curren;", (164, 0)),
        ("rarrpl", (0, 0)),
        ("longma", (0, 0)),
        ("DJcy", (0, 0)),
        ("Ncaron;", (327, 0)),
        ("Zopf", (0, 0)),
        ("UnderBrace;", (9183, 0)),
        ("Poincare", (0, 0)),
        ("gsim;", (8819, 0)),
        ("ENG", (0, 0)),
        ("uuml;", (252, 0)),
        ("NotHumpDo", (0, 0)),
        ("NotLeftTriangleEqual;", (8940, 0)),
        ("phone;", (9742, 0)),
        ("Breve;", (728, 0)),
        ("leqslant;", (10877, 0)),
        ("LeftTriang", (0, 0)),
        ("leftrigh", (0, 0)),
        ("Rou", (0, 0)),
        ("el", (0, 0)),
        ("NotLeftTriangleEqual", (0, 0)),
        ("et", (0, 0)),
        ("Leftrigh", (0, 0)),
        ("igr", (0, 0)),
        ("eqslantgt", (0, 0)),
        ("bigoti", (0, 0)),
        ("tcedi", (0, 0)),
        ("ReverseEquilibrium;", (8651, 0)),
        ("minus", (0, 0)),
        ("profalar", (0, 0)),
        ("F", (0, 0)),
        ("rsaq", (0, 0)),
        ("ngeq;", (8817, 0)),
        ("nwAr", (0, 0)),
        ("nsmid;", (8740, 0)),
        ("esc", (0, 0)),
        ("angmsdag;", (10670, 0)),
        ("s", (0, 0)),
        ("ds", (0, 0)),
        ("rmoustache", (0, 0)),
        ("LongLeftAr", (0, 0)),
        ("zopf;", (120171, 0)),
        ("RightDoubleBrac", (0, 0)),
        ("ddot", (0, 0)),
        ("cupbr", (0, 0)),
        ("DoubleLongLeftRig", (0, 0)),
        ("NotGreaterEqu", (0, 0)),
        ("ms", (0, 0)),
        ("NotSupe", (0, 0)),
        ("ForAll;", (8704, 0)),
        ("Gci", (0, 0)),
        ("Filled", (0, 0)),
        ("fili", (0, 0)),
        ("ofcir", (0, 0)),
        ("DownArrowBar", (0, 0)),
        ("ShortUp", (0, 0)),
        ("Lleftarrow", (0, 0)),
        ("lHar;", (10594, 0)),
        ("upuparrows", (0, 0)),
        ("LeftRig", (0, 0)),
        ("conin", (0, 0)),
        ("nume", (0, 0)),
        ("rsquo;", (8217, 0)),
        ("ucirc;", (251, 0)),
        ("urcorn", (0, 0)),
        ("Ran", (0, 0)),
        ("nequi", (0, 0)),
        ("radic;", (8730, 0)),
        ("DiacriticalGr", (0, 0)),
        ("tshcy;", (1115, 0)),
        ("ber", (0, 0)),
        ("Xopf", (0, 0)),
        ("Horizont", (0, 0)),
        ("coni", (0, 0)),
        ("smte;", (10924, 0)),
        ("DiacriticalDot;", (729, 0)),
        ("rarrtl;", (8611, 0)),
        ("scaron;", (353, 0)),
        ("pit", (0, 0)),
        ("DifferentialD", (0, 0)),
        ("RightFlo", (0, 0)),
        ("In", (0, 0)),
        ("GreaterT", (0, 0)),
        ("vrtri", (0, 0)),
        ("swarrow;", (8601, 0)),
        ("subplus;", (10943, 0)),
        ("rotime", (0, 0)),
        ("downdownarro", (0, 0)),
        ("dcy", (0, 0)),
        ("plusac", (0, 0)),
        ("UpArrowDownArro", (0, 0)),
        ("propt", (0, 0)),
        ("kappa;", (954, 0)),
        ("shor", (0, 0)),
        ("NotTildeTilde", (0, 0)),
        ("kgre", (0, 0)),
        ("lbrks", (0, 0)),
        ("LeftDoubleBracket;", (10214, 0)),
        ("dlc", (0, 0)),
        ("boxvl;", (9508, 0)),
        ("starf;", (9733, 0)),
        ("Tab;", (9, 0)),
        ("efDo", (0, 0)),
        ("nmid;", (8740, 0)),
        ("cacu", (0, 0)),
        ("CloseCurlyDo", (0, 0)),
        ("ltque", (0, 0)),
        ("Cs", (0, 0)),
        ("NotEq", (0, 0)),
        ("Epsil", (0, 0)),
        ("nsqs", (0, 0)),
        ("Abreve", (0, 0)),
        ("ldrusha", (0, 0)),
        ("oline", (0, 0)),
        ("SquareSubsetEq", (0, 0)),
        ("apos;", (39, 0)),
        ("longm", (0, 0)),
        ("notinvc;", (8950, 0)),
        ("NotRightTriangleEq", (0, 0)),
        ("vartriangleleft", (0, 0)),
        ("leqs", (0, 0)),
        ("Upp", (0, 0)),
        ("tim", (0, 0)),
        ("prns", (0, 0)),
        ("NestedGreat", (0, 0)),
        ("Lmi", (0, 0)),
        ("OpenCurl", (0, 0)),
        ("UnderParenthesi", (0, 0)),
        ("bi", (0, 0)),
        ("gtrapp", (0, 0)),
        ("Cdot", (0, 0)),
        ("gEl;", (10892, 0)),
        ("Upa", (0, 0)),
        ("dash", (0, 0)),
        ("DoubleLongLeftArrow", (0, 0)),
        ("rppolint;", (10770, 0)),
        ("blacktr", (0, 0)),
        ("bcon", (0, 0)),
        ("sun", (0, 0)),
        ("RightUpTee", (0, 0)),
        ("KJcy", (0, 0)),
        ("hybull;", (8259, 0)),
        ("simdo", (0, 0)),
        ("bigsqcup", (0, 0)),
        ("leftharpoondown", (0, 0)),
        ("NotSq", (0, 0)),
        ("longleftarrow", (0, 0)),
        ("supseteqq;", (10950, 0)),
        ("NotNestedGreat", (0, 0)),
        ("nsub;", (8836, 0)),
        ("dstrok;", (273, 0)),
        ("mh", (0, 0)),
        ("eDot", (0, 0)),
        ("Jopf", (0, 0)),
        ("jci", (0, 0)),
        ("nspar", (0, 0)),
        ("notnivb", (0, 0)),
        ("subrarr", (0, 0)),
        ("lsaq", (0, 0)),
        ("rfloor", (0, 0)),
        ("ntria", (0, 0)),
        ("Cdo", (0, 0)),
        ("not", (172, 0)),
        ("Omicron", (0, 0)),
        ("Ugrave", (217, 0)),
        ("NotSquareSupers", (0, 0)),
        ("top;", (8868, 0)),
        ("Rrigh", (0, 0)),
        ("DoubleConto", (0, 0)),
        ("lcy", (0, 0)),
        ("SquareSubsetE", (0, 0)),
        ("scirc;", (349, 0)),
        ("Differenti", (0, 0)),
        ("beta;", (946, 0)),
        ("NotHumpDow", (0, 0)),
        ("yopf;", (120170, 0)),
        ("RightUp", (0, 0)),
        ("urcrop", (0, 0)),
        ("lozenge;", (9674, 0)),
        ("interc", (0, 0)),
        ("fflig;", (64256, 0)),
        ("trie;", (8796, 0)),
        ("lhard;", (8637, 0)),
        ("imped", (0, 0)),
        ("prime;", (8242, 0)),
        ("Uacut", (0, 0)),
        ("NotE", (0, 0)),
        ("Hum", (0, 0)),
        ("NegativeT", (0, 0)),
        ("boxUr;", (9561, 0)),
        ("Impl", (0, 0)),
        ("gd", (0, 0)),
        ("nappro", (0, 0)),
        ("Nsc", (0, 0)),
        ("Subset;", (8912, 0)),
        ("nleftarro", (0, 0)),
        ("leftleftar", (0, 0)),
        ("Ccar", (0, 0)),
        ("Laplace", (0, 0)),
        ("DoubleUpAr", (0, 0)),
        ("lmid", (0, 0)),
        ("Barv", (0, 0)),
        ("THORN", (222, 0)),
        ("infintie;", (10717, 0)),
        ("mlc", (0, 0)),
        ("Abr", (0, 0)),
        ("Lef", (0, 0)),
        ("downharpoonrigh", (0, 0)),
        ("bigvee", (0, 0)),
        ("fjlig;", (102, 106)),
        ("Iscr", (0, 0)),
        ("NotLeft", (0, 0)),
        ("GreaterFullEqual", (0, 0)),
        ("YI", (0, 0)),
        ("NotPrecedesSlantEqual", (0, 0)),
        ("seswar", (0, 0)),
        ("Eps", (0, 0)),
        ("FilledV", (0, 0)),
        ("Integr", (0, 0)),
        ("zeetr", (0, 0)),
        ("subsim", (0, 0)),
        ("ntriangleright", (0, 0)),
        ("doteqdo", (0, 0)),
        ("RightVectorBa", (0, 0)),
        ("jmath", (0, 0)),
        ("an", (0, 0)),
        ("NotSquareSubset", (0, 0)),
        ("nhpar;", (10994, 0)),
        ("nLeftarr", (0, 0)),
        ("vareps", (0, 0)),
        ("frasl", (0, 0)),
        ("CircleP", (0, 0)),
        ("Empt", (0, 0)),
        ("LeftAngleBracket", (0, 0)),
        ("ec", (0, 0)),
        ("perc", (0, 0)),
        ("cent", (162, 0)),
        ("rbrksld", (0, 0)),
        ("Uarr", (0, 0)),
        ("Integ", (0, 0)),
        ("Ffr", (0, 0)),
        ("Dow", (0, 0)),
        ("Ncy;", (1053, 0)),
        ("oac", (0, 0)),
        ("Jci", (0, 0)),
        ("z", (0, 0)),
        ("intlarhk", (0, 0)),
        ("psc", (0, 0)),
        ("Sca", (0, 0)),
        ("THOR", (0, 0)),
        ("ogon;", (731, 0)),
        ("EmptySma", (0, 0)),
        ("ratail;", (10522, 0)),
        ("roplu", (0, 0)),
        ("loplus", (0, 0)),
        ("subseteq;", (8838, 0)),
        ("bsolb", (0, 0)),
        ("subsete", (0, 0)),
        ("notnivb;", (8958, 0)),
        ("lopl", (0, 0)),
        ("Inv", (0, 0)),
        ("gop", (0, 0)),
        ("NotC", (0, 0)),
        ("Es", (0, 0)),
        ("Over", (0, 0)),
        ("Downarrow;", (8659, 0)),
        ("bigtriangle", (0, 0)),
        ("varpropto", (0, 0)),
        ("nltrie;", (8940, 0)),
        ("vellip", (0, 0)),
        ("supsetneq;", (8843, 0)),
        ("LJcy", (0, 0)),
        ("homtht;", (8763, 0)),
        ("Equilibr", (0, 0)),
        ("NotNestedGreaterGrea", (0, 0)),
        ("en", (0, 0)),
        ("LeftFloo", (0, 0)),
        ("cross;", (10007, 0)),
        ("Vertica", (0, 0)),
        ("biguplus;", (10756, 0)),
        ("plu", (0, 0)),
        ("RightUpTeeVe", (0, 0)),
        ("rbb", (0, 0)),
        ("nLeftr", (0, 0)),
        ("Ele", (0, 0)),
        ("gtreqqle", (0, 0)),
        ("straightphi", (0, 0)),
        ("longleftarro", (0, 0)),
        ("lrcorne", (0, 0)),
        ("ropar", (0, 0)),
        ("kcedi", (0, 0)),
        ("Wedg", (0, 0)),
        ("hardcy", (0, 0)),
        ("Rright", (0, 0)),
        ("maps", (0, 0)),
        ("nLl", (0, 0)),
        ("realine;", (8475, 0)),
        ("dcar", (0, 0)),
        ("ddagger;", (8225, 0)),
        ("lhblk", (0, 0)),
        ("LeftTeeVec", (0, 0)),
        ("Qopf", (0, 0)),
        ("Mell", (0, 0)),
        ("lr", (0, 0)),
        ("twixt;", (8812, 0)),
        ("lbrace;", (123, 0)),
        ("csube", (0, 0)),
        ("oacu", (0, 0)),
        ("rarrpl;", (10565, 0)),
        ("Aop", (0, 0)),
        ("frow", (0, 0)),
        ("simlE", (0, 0)),
        ("DownRightVe", (0, 0)),
        ("ClockwiseContourIntegr", (0, 0)),
        ("LeftArrowRightA", (0, 0)),
        ("supsim;", (10952, 0)),
        ("boxHD;", (9574, 0)),
        ("DownLeftTeeV", (0, 0)),
        ("Proportional;", (8733, 0)),
        ("CloseCurlyQuot", (0, 0)),
        ("iuk", (0, 0)),
        ("isindo", (0, 0)),
        ("boxplus;", (8862, 0)),
        ("Dou", (0, 0)),
        ("diams;", (9830, 0)),
        ("swa", (0, 0)),
        ("compfn;", (8728, 0)),
        ("eogo", (0, 0)),
        ("LessEqu", (0, 0)),
        ("UpArrowDown", (0, 0)),
        ("rm", (0, 0)),
        ("lnap;", (10889, 0)),
        ("SOFTcy", (0, 0)),
        ("curvearrowright", (0, 0)),
        ("nld", (0, 0)),
        ("dhar", (0, 0)),
        ("GreaterFullEqua", (0, 0)),
        ("long", (0, 0)),
        ("integer", (0, 0)),
        ("succneqq", (0, 0)),
        ("SOFTcy;", (1068, 0)),
        ("fallingdot", (0, 0)),
        ("Such", (0, 0)),
        ("eca", (0, 0)),
        ("Produ", (0, 0)),
        ("wr;", (8768, 0)),
        ("SHCHcy;", (1065, 0)),
        ("subsim;", (10951, 0)),
        ("tris", (0, 0)),
        ("UpTee", (0, 0)),
        ("DownRightTeeVect", (0, 0)),
        ("No", (0, 0)),
        ("compl", (0, 0)),
        ("NotDoubleVertic", (0, 0)),
        ("nvge;", (8805, 8402)),
        ("psi;", (968, 0)),
        ("cci", (0, 0)),
        ("x", (0, 0)),
        ("rtrie", (0, 0)),
        ("Udb", (0, 0)),
        ("ZeroWidthSpace;", (8203, 0)),
        ("nRight", (0, 0)),
        ("hookrig", (0, 0)),
        ("esim;", (8770, 0)),
        ("Ugra", (0, 0)),
        ("target", (0, 0)),
        ("Gs", (0, 0)),
        ("emsp;", (8195, 0)),
        ("llarr;", (8647, 0)),
        ("che", (0, 0)),
        ("Implie", (0, 0)),
        ("NotLessLes", (0, 0)),
        ("RightUpDownVecto", (0, 0)),
        ("nwarh", (0, 0)),
        ("vs", (0, 0)),
        ("lap", (0, 0)),
        ("awint", (0, 0)),
        ("omacr;", (333, 0)),
        ("si", (0, 0)),
        ("jfr", (0, 0)),
        ("leftrightsquigarrow;", (8621, 0)),
        ("RightDownTeeVec", (0, 0)),
        ("sung;", (9834, 0)),
        ("mstpo", (0, 0)),
        ("dlcr", (0, 0)),
        ("LowerRightAr", (0, 0)),
        ("bb", (0, 0)),
        ("ham", (0, 0)),
        ("smas", (0, 0)),
        ("nwA", (0, 0)),
        ("cemp", (0, 0)),
        ("LongLeftArrow", (0, 0)),
        ("lesseqgtr", (0, 0)),
        ("imof", (0, 0)),
        ("between;", (8812, 0)),
        ("NotPrecedesEq", (0, 0)),
        ("nrA", (0, 0)),
        ("UpperRig", (0, 0)),
        ("CircleMinus", (0, 0)),
        ("boxUl;", (9564, 0)),
        ("mapst", (0, 0)),
        ("KHc", (0, 0)),
        ("SquareSupersetEqu", (0, 0)),
        ("precnapprox", (0, 0)),
        ("Eacute;", (201, 0)),
        ("zacute;", (378, 0)),
        ("Longrighta", (0, 0)),
        ("nsccue;", (8929, 0)),
        ("caro", (0, 0)),
        ("NotGreaterTilde", (0, 0)),
        ("jser", (0, 0)),
        ("beps", (0, 0)),
        ("Negati", (0, 0)),
        ("ShortRightArrow;", (8594, 0)),
        ("nLeftar", (0, 0)),
        ("GreaterE", (0, 0)),
        ("leftrig", (0, 0)),
        ("LessSlantEq", (0, 0)),
        ("ldrus", (0, 0)),
        ("chcy", (0, 0)),
        ("xdt", (0, 0)),
        ("Gdot", (0, 0)),
        ("blackloz", (0, 0)),
        ("DoubleLeftRi", (0, 0)),
        ("Sup;", (8913, 0)),
        ("NotSu", (0, 0)),
        ("Zeta", (0, 0)),
        ("dcaron;", (271, 0)),
        ("udb", (0, 0)),
        ("leftleftarrow", (0, 0)),
        ("Copf", (0, 0)),
        ("NotSucceed", (0, 0)),
        ("DotDot", (0, 0)),
        ("NotVerticalB", (0, 0)),
        ("Vvdash;", (8874, 0)),
        ("trianglerighteq;", (8885, 0)),
        ("szli", (0, 0)),
        ("ntriangleright;", (8939, 0)),
        ("short", (0, 0)),
        ("mop", (0, 0)),
        ("leftrightarrows;", (8646, 0)),
        ("ufi", (0, 0)),
        ("ReverseUpEquilibr", (0, 0)),
        ("Gamma", (0, 0)),
        ("NotLeftT", (0, 0)),
        ("flat;", (9837, 0)),
        ("Pre", (0, 0)),
        ("looparrowright", (0, 0)),
        ("glj", (0, 0)),
        ("boxdL;", (9557, 0)),
        ("ThickSp", (0, 0)),
        ("shortparallel;", (8741, 0)),
        ("dtdot", (0, 0)),
        ("sfrow", (0, 0)),
        ("UpperRightArr", (0, 0)),
        ("suphsub", (0, 0)),
        ("Gcy", (0, 0)),
        ("otilde", (245, 0)),
        ("ldrushar;", (10571, 0)),
        ("nsmid", (0, 0)),
        ("HumpEqual;", (8783, 0)),
        ("THO", (0, 0)),
        ("ReverseUpEquilibrium", (0, 0)),
        ("nVD", (0, 0)),
        ("CircleT", (0, 0)),
        ("Tcaro", (0, 0)),
        ("VerticalBar;", (8739, 0)),
        ("LeftTriangle;", (8882, 0)),
        ("divideont", (0, 0)),
        ("Updown", (0, 0)),
        ("Rcar", (0, 0)),
        ("Dop", (0, 0)),
        ("cues", (0, 0)),
        ("odas", (0, 0)),
        ("Rul", (0, 0)),
        ("UnderParenthesis;", (9181, 0)),
        ("checkm", (0, 0)),
        ("OpenCurlyQuo", (0, 0)),
        ("RoundImpl", (0, 0)),
        ("Bar", (0, 0)),
        ("OpenCurlyDoubleQuote", (0, 0)),
        ("Cscr;", (119966, 0)),
        ("sqsub;", (8847, 0)),
        ("hsla", (0, 0)),
        ("orderof;", (8500, 0)),
        ("mfr", (0, 0)),
        ("ldca;", (10550, 0)),
        ("Jcir", (0, 0)),
        ("yop", (0, 0)),
        ("DoubleContourI", (0, 0)),
        ("Uacute", (218, 0)),
        ("ll", (0, 0)),
        ("DoubleV", (0, 0)),
        ("diams", (0, 0)),
        ("vsu", (0, 0)),
        ("csc", (0, 0)),
        ("backco", (0, 0)),
        ("orarr;", (8635, 0)),
        ("dig", (0, 0)),
        ("hksearow;", (10533, 0)),
        ("nvlArr", (0, 0)),
        ("NotNestedGreate", (0, 0)),
        ("demptyv", (0, 0)),
        ("Vvdas", (0, 0)),
        ("boxUr", (0, 0)),
        ("Th", (0, 0)),
        ("circlearrowrigh", (0, 0)),
        ("Hor", (0, 0)),
        ("CirclePlu", (0, 0)),
        ("jopf", (0, 0)),
        ("DDotr", (0, 0)),
        ("rAa", (0, 0)),
        ("circled", (0, 0)),
        ("pointin", (0, 0)),
        ("varsubsetn", (0, 0)),
        ("lfish", (0, 0)),
        ("DownRightV", (0, 0)),
        ("varsupsetneqq;", (10956, 65024)),
        ("drcorn;", (8991, 0)),
        ("integers;", (8484, 0)),
        ("NotSquareSubsetE", (0, 0)),
        ("UpArrow;", (8593, 0)),
        ("lver", (0, 0)),
        ("iiin", (0, 0)),
        ("NotGreaterGr", (0, 0)),
        ("gnap", (0, 0)),
        ("measuredang", (0, 0)),
        ("bott", (0, 0)),
        ("aacu", (0, 0)),
        ("xnis;", (8955, 0)),
        ("planck", (0, 0)),
        ("Uogon", (0, 0)),
        ("Xi", (0, 0)),
        ("dlco", (0, 0)),
        ("Longleftrightar", (0, 0)),
        ("Equilibri", (0, 0)),
        ("Iog", (0, 0)),
        ("cce", (0, 0)),
        ("smt", (0, 0)),
        ("DownRightTeeVector", (0, 0)),
        ("zwnj", (0, 0)),
        ("iacut", (0, 0)),
        ("Longr", (0, 0)),
        ("DownLeftRightVecto", (0, 0)),
        ("Bcy", (0, 0)),
        ("wedge;", (8743, 0)),
        ("wci", (0, 0)),
        ("dzig", (0, 0)),
        ("xvee", (0, 0)),
        ("UnderBra", (0, 0)),
        ("ccirc", (0, 0)),
        ("Elemen", (0, 0)),
        ("ltd", (0, 0)),
        ("lesssi", (0, 0)),
        ("ngE;", (8807, 824)),
        ("lbrksl", (0, 0)),
        ("CounterClockwis", (0, 0)),
        ("dotm", (0, 0)),
        ("NegativeThickSpace;", (8203, 0)),
        ("Bernoullis", (0, 0)),
        ("CounterClock", (0, 0)),
        ("CounterCl", (0, 0)),
        ("Und", (0, 0)),
        ("eu", (0, 0)),
        ("wed", (0, 0)),
        ("bsc", (0, 0)),
        ("Uarroc", (0, 0)),
        ("dwan", (0, 0)),
        ("hooklef", (0, 0)),
        ("InvisibleTi", (0, 0)),
        ("kcy;", (1082, 0)),
        ("Star", (0, 0)),
        ("kappav", (0, 0)),
        ("eth;", (240, 0)),
        ("DiacriticalAcute;", (180, 0)),
        ("acirc", (226, 0)),
        ("HARDcy", (0, 0)),
        ("NotPrecedesE", (0, 0)),
        ("bowt", (0, 0)),
        ("abr", (0, 0)),
        ("loop", (0, 0)),
        ("upharpoonle", (0, 0)),
        ("bigsqcu", (0, 0)),
        ("pi", (0, 0)),
        ("rn", (0, 0)),
        ("boxvr;", (9500, 0)),
        ("angms", (0, 0)),
        ("blacktria", (0, 0)),
        ("LongRi", (0, 0)),
        ("LeftF", (0, 0)),
        ("Ch", (0, 0)),
        ("UpDo", (0, 0)),
        ("Aacute", (193, 0)),
        ("eqslantle", (0, 0)),
        ("dlcrop", (0, 0)),
        ("emptyse", (0, 0)),
        ("al", (0, 0)),
        ("EmptyVerySmall", (0, 0)),
        ("DownLeftRightV", (0, 0)),
        ("sa", (0, 0)),
        ("ver", (0, 0)),
        ("preccurlyeq;", (8828, 0)),
        ("Congrue", (0, 0)),
        ("Hat;", (94, 0)),
        ("nesim;", (8770, 824)),
        ("coloneq", (0, 0)),
        ("Alp", (0, 0)),
        ("boxuL", (0, 0)),
        ("smep", (0, 0)),
        ("LowerLeftArr", (0, 0)),
        ("nges;", (10878, 824)),
        ("primes", (0, 0)),
        ("hookleftar", (0, 0)),
        ("ClockwiseContourIntegral;", (8754, 0)),
        ("roplus;", (10798, 0)),
        ("ntriangle", (0, 0)),
        ("xwedge", (0, 0)),
        ("rights", (0, 0)),
        ("lmousta", (0, 0)),
        ("Auml", (196, 0)),
        ("RightDoubleBr", (0, 0)),
        ("circledda", (0, 0)),
        ("Cacu", (0, 0)),
        ("uacu", (0, 0)),
        ("precappro", (0, 0)),
        ("targ", (0, 0)),
        ("sqcup;", (8852, 0)),
        ("Aacut", (0, 0)),
        ("vzigzag", (0, 0)),
        ("strai", (0, 0)),
        ("LeftTriangleB", (0, 0)),
        ("NotLessGreate", (0, 0)),
        ("Po", (0, 0)),
        ("cirfnin", (0, 0)),
        ("nshor", (0, 0)),
        ("bp", (0, 0)),
        ("ograve;", (242, 0)),
        ("ugrav", (0, 0)),
        ("hkswar", (0, 0)),
        ("tf", (0, 0)),
        ("dstrok", (0, 0)),
        ("rarrf", (0, 0)),
        ("oelig", (0, 0)),
        ("Dstrok", (0, 0)),
        ("gam", (0, 0)),
        ("sqsubse", (0, 0)),
        ("Jsc", (0, 0)),
        ("Supers", (0, 0)),
        ("kappav;", (1008, 0)),
        ("curar", (0, 0)),
        ("ino", (0, 0)),
        ("angrtvb", (0, 0)),
        ("os", (0, 0)),
        ("lbb", (0, 0)),
        ("frac16", (0, 0)),
        ("gtdot;", (8919, 0)),
        ("Kopf;", (120130, 0)),
        ("prurel;", (8880, 0)),
        ("THORN;", (222, 0)),
        ("TildeTi", (0, 0)),
        ("lBar", (0, 0)),
        ("blacktriangleleft", (0, 0)),
        ("dlcrop;", (8973, 0)),
        ("CounterClockwise", (0, 0)),
        ("isinv", (0, 0)),
        ("Proportion", (0, 0)),
        ("DownRightVectorBa", (0, 0)),
        ("mp;", (8723, 0)),
        ("varn", (0, 0)),
        ("LessSlantEqua", (0, 0)),
        ("mco", (0, 0)),
        ("Jse", (0, 0)),
        ("rthre", (0, 0)),
        ("Udbla", (0, 0)),
        ("NegativeThickSp", (0, 0)),
        ("LongLeftRigh", (0, 0)),
        ("Theta;", (920, 0)),
        ("wscr", (0, 0)),
        ("SquareSuper", (0, 0)),
        ("nap", (0, 0)),
        ("gtqu", (0, 0)),
        ("doublebarwe", (0, 0)),
        ("ssetm", (0, 0)),
        ("longrighta", (0, 0)),
        ("PrecedesT", (0, 0)),
        ("lop", (0, 0)),
        ("Itilde;", (296, 0)),
        ("sup2", (178, 0)),
        ("RuleD", (0, 0)),
        ("Fouriertrf", (0, 0)),
        ("flli", (0, 0)),
        ("multima", (0, 0)),
        ("Delta;", (916, 0)),
        ("DoubleLon", (0, 0)),
        ("rBarr", (0, 0)),
        ("smal", (0, 0)),
        ("DownLeftVect", (0, 0)),
        ("Rfr;", (8476, 0)),
        ("LeftU", (0, 0)),
        ("helli", (0, 0)),
        ("Gammad;", (988, 0)),
        ("righthar", (0, 0)),
        ("nsce", (0, 0)),
        ("spar", (0, 0)),
        ("nGt;", (8811, 8402)),
        ("ud", (0, 0)),
        ("varpi", (0, 0)),
        ("Og", (0, 0)),
        ("Leftarr", (0, 0)),
        ("Mediu", (0, 0)),
        ("plusmn;", (177, 0)),
        ("RightTriangle", (0, 0)),
        ("ncon", (0, 0)),
        ("Nca", (0, 0)),
        ("napE", (0, 0)),
        ("succneqq;", (10934, 0)),
        ("Ubrc", (0, 0)),
        ("divide;", (247, 0)),
        ("kg", (0, 0)),
        ("topcir;", (10993, 0)),
        ("LeftDownTee", (0, 0)),
        ("NegativeVeryTh", (0, 0)),
        ("fema", (0, 0)),
        ("frac15", (0, 0)),
        ("scn", (0, 0)),
        ("RightTeeVe", (0, 0)),
        ("blackl", (0, 0)),
        ("Shor", (0, 0)),
        ("DoubleLeftArrow;", (8656, 0)),
        ("Rr", (0, 0)),
        ("ngeqq", (0, 0)),
        ("Uarrocir;", (10569, 0)),
        ("NotRev", (0, 0)),
        ("NotDoubleVerti", (0, 0)),
        ("Lcar", (0, 0)),
        ("LeftTeeArr", (0, 0)),
        ("supsu", (0, 0)),
        ("PrecedesTi", (0, 0)),
        ("oci", (0, 0)),
        ("angm", (0, 0)),
        ("fsc", (0, 0)),
        ("complem", (0, 0)),
        ("straightphi;", (981, 0)),
        ("sqsupe;", (8850, 0)),
        ("LongRightArrow;", (10230, 0)),
        ("dbla", (0, 0)),
        ("Psi;", (936, 0)),
        ("drbk", (0, 0)),
        ("ring;", (730, 0)),
        ("leftleftarrows;", (8647, 0)),
        ("ntrianglerighteq;", (8941, 0)),
        ("updownarro", (0, 0)),
        ("hsl", (0, 0)),
        ("ClockwiseCo", (0, 0)),
        ("imath", (0, 0)),
        ("LeftTeeA", (0, 0)),
        ("hstro", (0, 0)),
        ("NotHum", (0, 0)),
        ("Re;", (8476, 0)),
        ("ycir", (0, 0)),
        ("subsu", (0, 0)),
        ("wre", (0, 0)),
        ("cwconint", (0, 0)),
        ("NotSucceedsTild", (0, 0)),
        ("ntrian", (0, 0)),
        ("lHar", (0, 0)),
        ("Prime", (0, 0)),
        ("SmallCircl", (0, 0)),
        ("Backsl", (0, 0)),
        ("euml;", (235, 0)),
        ("ntl", (0, 0)),
        ("Fop", (0, 0)),
        ("lcy;", (1083, 0)),
        ("doublebarw", (0, 0)),
        ("Oac", (0, 0)),
        ("zdot;", (380, 0)),
        ("ReverseUpEqu", (0, 0)),
        ("ucir", (0, 0)),
        ("Interse", (0, 0)),
        ("Otilde;", (213, 0)),
        ("rflo", (0, 0)),
        ("LeftVectorBar", (0, 0)),
        ("gtrsim", (0, 0)),
        ("subn", (0, 0)),
        ("spar;", (8741, 0)),
        ("Tc", (0, 0)),
        ("UpArr", (0, 0)),
        ("Ccir", (0, 0)),
        ("RightAngleBracket", (0, 0)),
        ("udarr", (0, 0)),
        ("uwan", (0, 0)),
        ("club", (0, 0)),
        ("lopf;", (120157, 0)),
        ("xharr", (0, 0)),
        ("xma", (0, 0)),
        ("ltci", (0, 0)),
        ("nw", (0, 0)),
        ("FilledVerySm", (0, 0)),
        ("hs", (0, 0)),
        ("UpperLeftArrow", (0, 0)),
        ("NotSuperset", (0, 0)),
        ("SHCHc", (0, 0)),
        ("Rscr", (0, 0)),
        ("FilledVerySma", (0, 0)),
        ("trimin", (0, 0)),
        ("uring;", (367, 0)),
        ("lAtai", (0, 0)),
        ("Equilib", (0, 0)),
        ("varpropt", (0, 0)),
        ("inod", (0, 0)),
        ("Longrightarrow;", (10233, 0)),
        ("Yf", (0, 0)),
        ("Bo", (0, 0)),
        ("Round", (0, 0)),
        ("capb", (0, 0)),
        ("ecir", (0, 0)),
        ("NotHumpDownHump", (0, 0)),
        ("curarrm", (0, 0)),
        ("Aac", (0, 0)),
        ("lthree;", (8907, 0)),
        ("olcir", (0, 0)),
        ("Intersection;", (8898, 0)),
    ],
};
