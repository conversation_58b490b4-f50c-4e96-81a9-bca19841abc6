import { useState, useEffect } from "react";
import { Plus, TestTube, Trash2, RefreshCw } from "lucide-react";
import { TauriAPI, ProxyPoolStatus } from "../lib/tauri";

export function ProxyManager() {
  const [poolStatus, setPoolStatus] = useState<ProxyPoolStatus | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProxyData();
    const interval = setInterval(loadProxyData, 10000);
    return () => clearInterval(interval);
  }, []);

  const loadProxyData = async () => {
    try {
      const status = await TauriAPI.getProxyPoolStatus();
      setPoolStatus(status);
    } catch (error) {
      console.error("加载代理数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和操作按钮 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">代理池管理</h1>
          <p className="text-muted-foreground mt-2">
            管理代理服务器池，确保爬虫任务的稳定执行
          </p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowAddDialog(true)}
            className="btn btn-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            添加代理
          </button>
          <button
            onClick={loadProxyData}
            className="btn btn-outline"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            刷新
          </button>
        </div>
      </div>

      {/* 代理池状态概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <div className="text-2xl font-bold">{poolStatus?.total_proxies || 0}</div>
              <div className="text-sm text-muted-foreground">总代理数</div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{poolStatus?.active_proxies || 0}</div>
              <div className="text-sm text-muted-foreground">可用代理</div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{poolStatus?.healthy_proxies || 0}</div>
              <div className="text-sm text-muted-foreground">健康代理</div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="text-center">
              <div className="text-2xl font-bold">{poolStatus?.success_rate?.toFixed(1) || 0}%</div>
              <div className="text-sm text-muted-foreground">成功率</div>
            </div>
          </div>
        </div>
      </div>

      {/* 代理列表 */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">代理列表</h3>
          <p className="card-description">当前代理池中的所有代理服务器</p>
        </div>
        <div className="card-content">
          <div className="overflow-x-auto">
            <table className="table">
              <thead className="table-header">
                <tr className="table-row">
                  <th className="table-head">状态</th>
                  <th className="table-head">地址</th>
                  <th className="table-head">协议</th>
                  <th className="table-head">地区</th>
                  <th className="table-head">响应时间</th>
                  <th className="table-head">成功率</th>
                  <th className="table-head">操作</th>
                </tr>
              </thead>
              <tbody className="table-body">
                {/* 模拟数据 */}
                {[1, 2, 3, 4, 5].map((i) => (
                  <tr key={i} className="table-row">
                    <td className="table-cell">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm">正常</span>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div>
                        <div className="font-medium">192.168.1.{i}</div>
                        <div className="text-sm text-muted-foreground">8080</div>
                      </div>
                    </td>
                    <td className="table-cell">
                      <span className="badge badge-secondary">HTTP</span>
                    </td>
                    <td className="table-cell">中国</td>
                    <td className="table-cell">{(Math.random() * 2000 + 500).toFixed(0)}ms</td>
                    <td className="table-cell">{(Math.random() * 20 + 80).toFixed(1)}%</td>
                    <td className="table-cell">
                      <div className="flex space-x-2">
                        <button className="btn btn-sm btn-outline">
                          <TestTube className="h-3 w-3" />
                        </button>
                        <button className="btn btn-sm btn-destructive">
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* 添加代理对话框 */}
      {showAddDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">添加代理</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">主机地址</label>
                <input type="text" className="input" placeholder="192.168.1.1" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">端口</label>
                <input type="number" className="input" placeholder="8080" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">协议</label>
                <select className="input">
                  <option value="http">HTTP</option>
                  <option value="https">HTTPS</option>
                  <option value="socks5">SOCKS5</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">用户名（可选）</label>
                <input type="text" className="input" />
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">密码（可选）</label>
                <input type="password" className="input" />
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setShowAddDialog(false)}
                className="btn btn-outline"
              >
                取消
              </button>
              <button className="btn btn-primary">
                添加
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// 默认导出
export default ProxyManager;
