<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\bin\test_db.rs`."><title>test_db.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="test_db" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">test_db/</div>test_db.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>sqlx::SqlitePool;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>std::path::Path;
<a href=#3 id=3 data-nosnippet>3</a>
<a href=#4 id=4 data-nosnippet>4</a><span class="attr">#[tokio::main]
<a href=#5 id=5 data-nosnippet>5</a></span><span class="kw">async fn </span>main() -&gt; <span class="prelude-ty">Result</span>&lt;(), Box&lt;<span class="kw">dyn </span>std::error::Error&gt;&gt; {
<a href=#6 id=6 data-nosnippet>6</a>    <span class="kw">let </span>database_url = <span class="string">"sqlite:./data/crawler_node.db"</span>;
<a href=#7 id=7 data-nosnippet>7</a>    
<a href=#8 id=8 data-nosnippet>8</a>    <span class="macro">println!</span>(<span class="string">"测试数据库连接: {}"</span>, database_url);
<a href=#9 id=9 data-nosnippet>9</a>    
<a href=#10 id=10 data-nosnippet>10</a>    <span class="comment">// 检查数据库文件是否存在
<a href=#11 id=11 data-nosnippet>11</a>    </span><span class="kw">let </span>db_path = database_url.trim_start_matches(<span class="string">"sqlite:"</span>);
<a href=#12 id=12 data-nosnippet>12</a>    <span class="kw">if </span>Path::new(db_path).exists() {
<a href=#13 id=13 data-nosnippet>13</a>        <span class="macro">println!</span>(<span class="string">"✓ 数据库文件存在: {}"</span>, db_path);
<a href=#14 id=14 data-nosnippet>14</a>    } <span class="kw">else </span>{
<a href=#15 id=15 data-nosnippet>15</a>        <span class="macro">println!</span>(<span class="string">"✗ 数据库文件不存在: {}"</span>, db_path);
<a href=#16 id=16 data-nosnippet>16</a>        <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#17 id=17 data-nosnippet>17</a>    }
<a href=#18 id=18 data-nosnippet>18</a>    
<a href=#19 id=19 data-nosnippet>19</a>    <span class="comment">// 尝试连接数据库
<a href=#20 id=20 data-nosnippet>20</a>    </span><span class="kw">match </span>SqlitePool::connect(database_url).<span class="kw">await </span>{
<a href=#21 id=21 data-nosnippet>21</a>        <span class="prelude-val">Ok</span>(pool) =&gt; {
<a href=#22 id=22 data-nosnippet>22</a>            <span class="macro">println!</span>(<span class="string">"✓ 数据库连接成功"</span>);
<a href=#23 id=23 data-nosnippet>23</a>            
<a href=#24 id=24 data-nosnippet>24</a>            <span class="comment">// 测试查询
<a href=#25 id=25 data-nosnippet>25</a>            </span><span class="kw">match </span><span class="macro">sqlx::query!</span>(<span class="string">"SELECT name FROM sqlite_master WHERE type='table'"</span>)
<a href=#26 id=26 data-nosnippet>26</a>                .fetch_all(<span class="kw-2">&amp;</span>pool)
<a href=#27 id=27 data-nosnippet>27</a>                .<span class="kw">await 
<a href=#28 id=28 data-nosnippet>28</a>            </span>{
<a href=#29 id=29 data-nosnippet>29</a>                <span class="prelude-val">Ok</span>(rows) =&gt; {
<a href=#30 id=30 data-nosnippet>30</a>                    <span class="macro">println!</span>(<span class="string">"✓ 数据库表列表:"</span>);
<a href=#31 id=31 data-nosnippet>31</a>                    <span class="kw">for </span>row <span class="kw">in </span>rows {
<a href=#32 id=32 data-nosnippet>32</a>                        <span class="macro">println!</span>(<span class="string">"  - {:?}"</span>, row.name);
<a href=#33 id=33 data-nosnippet>33</a>                    }
<a href=#34 id=34 data-nosnippet>34</a>                }
<a href=#35 id=35 data-nosnippet>35</a>                <span class="prelude-val">Err</span>(e) =&gt; {
<a href=#36 id=36 data-nosnippet>36</a>                    <span class="macro">println!</span>(<span class="string">"✗ 查询表失败: {}"</span>, e);
<a href=#37 id=37 data-nosnippet>37</a>                }
<a href=#38 id=38 data-nosnippet>38</a>            }
<a href=#39 id=39 data-nosnippet>39</a>            
<a href=#40 id=40 data-nosnippet>40</a>            pool.close().<span class="kw">await</span>;
<a href=#41 id=41 data-nosnippet>41</a>        }
<a href=#42 id=42 data-nosnippet>42</a>        <span class="prelude-val">Err</span>(e) =&gt; {
<a href=#43 id=43 data-nosnippet>43</a>            <span class="macro">println!</span>(<span class="string">"✗ 数据库连接失败: {}"</span>, e);
<a href=#44 id=44 data-nosnippet>44</a>        }
<a href=#45 id=45 data-nosnippet>45</a>    }
<a href=#46 id=46 data-nosnippet>46</a>    
<a href=#47 id=47 data-nosnippet>47</a>    <span class="prelude-val">Ok</span>(())
<a href=#48 id=48 data-nosnippet>48</a>}</code></pre></div></section></main></body></html>