{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 8181899942129726463, "deps": [[4450062412064442726, "dirs_next", false, 1456508782415538895], [4899080583175475170, "semver", false, 2022204265236511886], [7468248713591957673, "cargo_toml", false, 9892169153578698918], [8292277814562636972, "tauri_utils", false, 15585448512050629145], [8569119365930580996, "serde_json", false, 6464436191210645041], [9689903380558560274, "serde", false, 4389908846715814937], [10301936376833819828, "json_patch", false, 7788522536251391700], [13077543566650298139, "heck", false, 723539331256257772], [13625485746686963219, "anyhow", false, 11319210697648472172], [14189313126492979171, "tauri_winres", false, 8029028713758008491], [15622660310229662834, "walkdir", false, 6522704883177604130]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-0bc3563967d29821\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}