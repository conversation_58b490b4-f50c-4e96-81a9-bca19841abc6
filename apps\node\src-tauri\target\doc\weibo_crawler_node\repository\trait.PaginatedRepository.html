<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="分页查询特征"><title>PaginatedRepository in weibo_crawler_node::repository - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc trait"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Paginated<wbr>Repository</a></h2><h3><a href="#required-methods">Required Methods</a></h3><ul class="block"><li><a href="#tymethod.find_paginated" title="find_paginated">find_paginated</a></li></ul><h3><a href="#implementors">Implementors</a></h3></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>repository</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">repository</a></div><h1>Trait <span class="trait">PaginatedRepository</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/repository/mod.rs.html#27-29">Source</a> </span></div><pre class="rust item-decl"><code>pub trait PaginatedRepository&lt;T&gt; {
    // Required method
    fn <a href="#tymethod.find_paginated" class="fn">find_paginated</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
        page: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>,
        limit: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="struct.PaginatedResult.html" title="struct weibo_crawler_node::repository::PaginatedResult">PaginatedResult</a>&lt;T&gt;, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>分页查询特征</p>
</div></details><h2 id="required-methods" class="section-header">Required Methods<a href="#required-methods" class="anchor">§</a></h2><div class="methods"><section id="tymethod.find_paginated" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/repository/mod.rs.html#28">Source</a><h4 class="code-header">fn <a href="#tymethod.find_paginated" class="fn">find_paginated</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
    page: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>,
    limit: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u32.html">u32</a>,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="struct.PaginatedResult.html" title="struct weibo_crawler_node::repository::PaginatedResult">PaginatedResult</a>&lt;T&gt;, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section></div><h2 id="implementors" class="section-header">Implementors<a href="#implementors" class="anchor">§</a></h2><div id="implementors-list"><section id="impl-PaginatedRepository%3CAccountRecord%3E-for-AccountRepository" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/repository/account_repository.rs.html#78-82">Source</a><a href="#impl-PaginatedRepository%3CAccountRecord%3E-for-AccountRepository" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.PaginatedRepository.html" title="trait weibo_crawler_node::repository::PaginatedRepository">PaginatedRepository</a>&lt;<a class="struct" href="../storage/struct.AccountRecord.html" title="struct weibo_crawler_node::storage::AccountRecord">AccountRecord</a>&gt; for <a class="struct" href="account_repository/struct.AccountRepository.html" title="struct weibo_crawler_node::repository::account_repository::AccountRepository">AccountRepository</a></h3></section><section id="impl-PaginatedRepository%3CProxyRecord%3E-for-ProxyRepository" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/repository/proxy_repository.rs.html#72-76">Source</a><a href="#impl-PaginatedRepository%3CProxyRecord%3E-for-ProxyRepository" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.PaginatedRepository.html" title="trait weibo_crawler_node::repository::PaginatedRepository">PaginatedRepository</a>&lt;<a class="struct" href="../storage/struct.ProxyRecord.html" title="struct weibo_crawler_node::storage::ProxyRecord">ProxyRecord</a>&gt; for <a class="struct" href="proxy_repository/struct.ProxyRepository.html" title="struct weibo_crawler_node::repository::proxy_repository::ProxyRepository">ProxyRepository</a></h3></section><section id="impl-PaginatedRepository%3CTaskRecord%3E-for-TaskRepository" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/repository/task_repository.rs.html#109-113">Source</a><a href="#impl-PaginatedRepository%3CTaskRecord%3E-for-TaskRepository" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.PaginatedRepository.html" title="trait weibo_crawler_node::repository::PaginatedRepository">PaginatedRepository</a>&lt;<a class="struct" href="../storage/struct.TaskRecord.html" title="struct weibo_crawler_node::storage::TaskRecord">TaskRecord</a>&gt; for <a class="struct" href="task_repository/struct.TaskRepository.html" title="struct weibo_crawler_node::repository::task_repository::TaskRepository">TaskRepository</a></h3></section></div><script src="../../trait.impl/weibo_crawler_node/repository/trait.PaginatedRepository.js" async></script></section></div></main></body></html>