{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 9832628878587282587, "deps": [[1988483478007900009, "unicode_ident", false, 4998480155755173505], [3060637413840920116, "proc_macro2", false, 10993881616374897205], [17990358020177143287, "quote", false, 8157755205024365785]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-ca4600fd545d9023\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}