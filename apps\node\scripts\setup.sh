#!/bin/bash

# 微博爬虫节点设置脚本

set -e

echo "🚀 开始设置微博爬虫节点..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+"
    exit 1
fi

# 检查Rust
if ! command -v cargo &> /dev/null; then
    echo "❌ Rust 未安装，请先安装 Rust 1.70+"
    exit 1
fi

# 检查Tauri CLI
if ! command -v cargo-tauri &> /dev/null; then
    echo "📦 安装 Tauri CLI..."
    cargo install tauri-cli
fi

# 安装前端依赖
echo "📦 安装前端依赖..."
npm install

# 创建必要的目录
echo "📁 创建目录结构..."
mkdir -p data logs

# 复制环境配置文件
if [ ! -f .env ]; then
    echo "📝 创建环境配置文件..."
    cp .env.example .env
    echo "✅ 请编辑 .env 文件配置您的环境变量"
fi

# 运行数据库迁移
echo "🗄️ 初始化数据库..."
cd src-tauri
cargo run --bin migrate
cargo run --bin seed
cd ..

echo "✅ 设置完成！"
echo ""
echo "🎯 下一步："
echo "1. 编辑 .env 文件配置环境变量"
echo "2. 运行 'npm run tauri:dev' 启动开发服务器"
echo "3. 或运行 'npm run tauri:build' 构建生产版本"
