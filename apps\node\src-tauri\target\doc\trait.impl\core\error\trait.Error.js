(function() {
    var implementors = Object.fromEntries([["weibo_crawler_node",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/error/trait.Error.html\" title=\"trait core::error::Error\">Error</a> for <a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[306]}