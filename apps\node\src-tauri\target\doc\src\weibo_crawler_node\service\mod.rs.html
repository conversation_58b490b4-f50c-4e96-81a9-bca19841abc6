<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\service\mod.rs`."><title>mod.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node\service/</div>mod.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::Result;
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span><span class="kw">crate</span>::repository::RepositoryManager;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::sync::Arc;
<a href=#4 id=4 data-nosnippet>4</a>
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">pub mod </span>task_service;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">pub mod </span>proxy_service;
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">pub mod </span>account_service;
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">pub mod </span>crawler_service;
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">pub mod </span>monitor_service;
<a href=#10 id=10 data-nosnippet>10</a>
<a href=#11 id=11 data-nosnippet>11</a><span class="kw">pub use </span>task_service::TaskService;
<a href=#12 id=12 data-nosnippet>12</a><span class="kw">pub use </span>proxy_service::ProxyService;
<a href=#13 id=13 data-nosnippet>13</a><span class="kw">pub use </span>account_service::AccountService;
<a href=#14 id=14 data-nosnippet>14</a><span class="kw">pub use </span>crawler_service::CrawlerService;
<a href=#15 id=15 data-nosnippet>15</a><span class="kw">pub use </span>monitor_service::MonitorService;
<a href=#16 id=16 data-nosnippet>16</a>
<a href=#17 id=17 data-nosnippet>17</a><span class="doccomment">/// 服务管理器 - 统一管理所有服务
<a href=#18 id=18 data-nosnippet>18</a></span><span class="kw">pub struct </span>ServiceManager {
<a href=#19 id=19 data-nosnippet>19</a>    <span class="kw">pub </span>task: TaskService,
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">pub </span>proxy: ProxyService,
<a href=#21 id=21 data-nosnippet>21</a>    <span class="kw">pub </span>account: AccountService,
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">pub </span>crawler: CrawlerService,
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">pub </span>monitor: MonitorService,
<a href=#24 id=24 data-nosnippet>24</a>}
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a><span class="kw">impl </span>ServiceManager {
<a href=#27 id=27 data-nosnippet>27</a>    <span class="kw">pub fn </span>new(repository_manager: Arc&lt;RepositoryManager&gt;) -&gt; <span class="self">Self </span>{
<a href=#28 id=28 data-nosnippet>28</a>        <span class="self">Self </span>{
<a href=#29 id=29 data-nosnippet>29</a>            task: TaskService::new(repository_manager.clone()),
<a href=#30 id=30 data-nosnippet>30</a>            proxy: ProxyService::new(repository_manager.clone()),
<a href=#31 id=31 data-nosnippet>31</a>            account: AccountService::new(repository_manager.clone()),
<a href=#32 id=32 data-nosnippet>32</a>            crawler: CrawlerService::new(repository_manager.clone()),
<a href=#33 id=33 data-nosnippet>33</a>            monitor: MonitorService::new(repository_manager),
<a href=#34 id=34 data-nosnippet>34</a>        }
<a href=#35 id=35 data-nosnippet>35</a>    }
<a href=#36 id=36 data-nosnippet>36</a>}
<a href=#37 id=37 data-nosnippet>37</a>
<a href=#38 id=38 data-nosnippet>38</a><span class="doccomment">/// 基础服务特征
<a href=#39 id=39 data-nosnippet>39</a></span><span class="kw">pub trait </span>Service {
<a href=#40 id=40 data-nosnippet>40</a>    <span class="kw">fn </span>name(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span><span class="lifetime">'static </span>str;
<a href=#41 id=41 data-nosnippet>41</a>}
<a href=#42 id=42 data-nosnippet>42</a>
<a href=#43 id=43 data-nosnippet>43</a><span class="doccomment">/// 可启动的服务特征
<a href=#44 id=44 data-nosnippet>44</a></span><span class="attr">#[async_trait::async_trait]
<a href=#45 id=45 data-nosnippet>45</a></span><span class="kw">pub trait </span>StartableService: Service {
<a href=#46 id=46 data-nosnippet>46</a>    <span class="kw">async fn </span>start(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#47 id=47 data-nosnippet>47</a>    <span class="kw">async fn </span>stop(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#48 id=48 data-nosnippet>48</a>    <span class="kw">async fn </span>restart(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#49 id=49 data-nosnippet>49</a>        <span class="self">self</span>.stop().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#50 id=50 data-nosnippet>50</a>        <span class="self">self</span>.start().<span class="kw">await
<a href=#51 id=51 data-nosnippet>51</a>    </span>}
<a href=#52 id=52 data-nosnippet>52</a>    <span class="kw">fn </span>is_running(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool;
<a href=#53 id=53 data-nosnippet>53</a>}
<a href=#54 id=54 data-nosnippet>54</a>
<a href=#55 id=55 data-nosnippet>55</a><span class="doccomment">/// 可配置的服务特征
<a href=#56 id=56 data-nosnippet>56</a></span><span class="kw">pub trait </span>ConfigurableService: Service {
<a href=#57 id=57 data-nosnippet>57</a>    <span class="kw">type </span>Config;
<a href=#58 id=58 data-nosnippet>58</a>    
<a href=#59 id=59 data-nosnippet>59</a>    <span class="kw">fn </span>get_config(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span><span class="self">Self</span>::Config;
<a href=#60 id=60 data-nosnippet>60</a>    <span class="kw">fn </span>update_config(<span class="kw-2">&amp;mut </span><span class="self">self</span>, config: <span class="self">Self</span>::Config) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt;;
<a href=#61 id=61 data-nosnippet>61</a>}
<a href=#62 id=62 data-nosnippet>62</a>
<a href=#63 id=63 data-nosnippet>63</a><span class="doccomment">/// 可监控的服务特征
<a href=#64 id=64 data-nosnippet>64</a></span><span class="attr">#[async_trait::async_trait]
<a href=#65 id=65 data-nosnippet>65</a></span><span class="kw">pub trait </span>MonitorableService: Service {
<a href=#66 id=66 data-nosnippet>66</a>    <span class="kw">type </span>Metrics;
<a href=#67 id=67 data-nosnippet>67</a>    
<a href=#68 id=68 data-nosnippet>68</a>    <span class="kw">async fn </span>get_metrics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>::Metrics&gt;;
<a href=#69 id=69 data-nosnippet>69</a>    <span class="kw">async fn </span>get_health_status(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;HealthStatus&gt;;
<a href=#70 id=70 data-nosnippet>70</a>}
<a href=#71 id=71 data-nosnippet>71</a>
<a href=#72 id=72 data-nosnippet>72</a><span class="doccomment">/// 健康状态
<a href=#73 id=73 data-nosnippet>73</a></span><span class="attr">#[derive(Debug, Clone, PartialEq)]
<a href=#74 id=74 data-nosnippet>74</a></span><span class="kw">pub enum </span>HealthStatus {
<a href=#75 id=75 data-nosnippet>75</a>    Healthy,
<a href=#76 id=76 data-nosnippet>76</a>    Degraded(String),
<a href=#77 id=77 data-nosnippet>77</a>    Unhealthy(String),
<a href=#78 id=78 data-nosnippet>78</a>}
<a href=#79 id=79 data-nosnippet>79</a>
<a href=#80 id=80 data-nosnippet>80</a><span class="kw">impl </span>HealthStatus {
<a href=#81 id=81 data-nosnippet>81</a>    <span class="kw">pub fn </span>is_healthy(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#82 id=82 data-nosnippet>82</a>        <span class="macro">matches!</span>(<span class="self">self</span>, HealthStatus::Healthy)
<a href=#83 id=83 data-nosnippet>83</a>    }
<a href=#84 id=84 data-nosnippet>84</a>
<a href=#85 id=85 data-nosnippet>85</a>    <span class="kw">pub fn </span>is_degraded(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#86 id=86 data-nosnippet>86</a>        <span class="macro">matches!</span>(<span class="self">self</span>, HealthStatus::Degraded(<span class="kw">_</span>))
<a href=#87 id=87 data-nosnippet>87</a>    }
<a href=#88 id=88 data-nosnippet>88</a>
<a href=#89 id=89 data-nosnippet>89</a>    <span class="kw">pub fn </span>is_unhealthy(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#90 id=90 data-nosnippet>90</a>        <span class="macro">matches!</span>(<span class="self">self</span>, HealthStatus::Unhealthy(<span class="kw">_</span>))
<a href=#91 id=91 data-nosnippet>91</a>    }
<a href=#92 id=92 data-nosnippet>92</a>
<a href=#93 id=93 data-nosnippet>93</a>    <span class="kw">pub fn </span>message(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>str&gt; {
<a href=#94 id=94 data-nosnippet>94</a>        <span class="kw">match </span><span class="self">self </span>{
<a href=#95 id=95 data-nosnippet>95</a>            HealthStatus::Healthy =&gt; <span class="prelude-val">None</span>,
<a href=#96 id=96 data-nosnippet>96</a>            HealthStatus::Degraded(msg) | HealthStatus::Unhealthy(msg) =&gt; <span class="prelude-val">Some</span>(msg),
<a href=#97 id=97 data-nosnippet>97</a>        }
<a href=#98 id=98 data-nosnippet>98</a>    }
<a href=#99 id=99 data-nosnippet>99</a>}
<a href=#100 id=100 data-nosnippet>100</a>
<a href=#101 id=101 data-nosnippet>101</a><span class="doccomment">/// 服务事件
<a href=#102 id=102 data-nosnippet>102</a></span><span class="attr">#[derive(Debug, Clone)]
<a href=#103 id=103 data-nosnippet>103</a></span><span class="kw">pub enum </span>ServiceEvent {
<a href=#104 id=104 data-nosnippet>104</a>    Started(String),
<a href=#105 id=105 data-nosnippet>105</a>    Stopped(String),
<a href=#106 id=106 data-nosnippet>106</a>    Error(String, String),
<a href=#107 id=107 data-nosnippet>107</a>    ConfigUpdated(String),
<a href=#108 id=108 data-nosnippet>108</a>    HealthChanged(String, HealthStatus),
<a href=#109 id=109 data-nosnippet>109</a>}
<a href=#110 id=110 data-nosnippet>110</a>
<a href=#111 id=111 data-nosnippet>111</a><span class="doccomment">/// 事件监听器特征
<a href=#112 id=112 data-nosnippet>112</a></span><span class="attr">#[async_trait::async_trait]
<a href=#113 id=113 data-nosnippet>113</a></span><span class="kw">pub trait </span>EventListener {
<a href=#114 id=114 data-nosnippet>114</a>    <span class="kw">async fn </span>on_event(<span class="kw-2">&amp;</span><span class="self">self</span>, event: ServiceEvent);
<a href=#115 id=115 data-nosnippet>115</a>}
<a href=#116 id=116 data-nosnippet>116</a>
<a href=#117 id=117 data-nosnippet>117</a><span class="doccomment">/// 服务注册表
<a href=#118 id=118 data-nosnippet>118</a></span><span class="kw">pub struct </span>ServiceRegistry {
<a href=#119 id=119 data-nosnippet>119</a>    services: std::collections::HashMap&lt;String, Box&lt;<span class="kw">dyn </span>Service + Send + Sync&gt;&gt;,
<a href=#120 id=120 data-nosnippet>120</a>    event_listeners: Vec&lt;Box&lt;<span class="kw">dyn </span>EventListener + Send + Sync&gt;&gt;,
<a href=#121 id=121 data-nosnippet>121</a>}
<a href=#122 id=122 data-nosnippet>122</a>
<a href=#123 id=123 data-nosnippet>123</a><span class="kw">impl </span>ServiceRegistry {
<a href=#124 id=124 data-nosnippet>124</a>    <span class="kw">pub fn </span>new() -&gt; <span class="self">Self </span>{
<a href=#125 id=125 data-nosnippet>125</a>        <span class="self">Self </span>{
<a href=#126 id=126 data-nosnippet>126</a>            services: std::collections::HashMap::new(),
<a href=#127 id=127 data-nosnippet>127</a>            event_listeners: Vec::new(),
<a href=#128 id=128 data-nosnippet>128</a>        }
<a href=#129 id=129 data-nosnippet>129</a>    }
<a href=#130 id=130 data-nosnippet>130</a>
<a href=#131 id=131 data-nosnippet>131</a>    <span class="kw">pub fn </span>register&lt;S&gt;(<span class="kw-2">&amp;mut </span><span class="self">self</span>, service: S) 
<a href=#132 id=132 data-nosnippet>132</a>    <span class="kw">where 
<a href=#133 id=133 data-nosnippet>133</a>        </span>S: Service + Send + Sync + <span class="lifetime">'static 
<a href=#134 id=134 data-nosnippet>134</a>    </span>{
<a href=#135 id=135 data-nosnippet>135</a>        <span class="kw">let </span>name = service.name().to_string();
<a href=#136 id=136 data-nosnippet>136</a>        <span class="self">self</span>.services.insert(name, Box::new(service));
<a href=#137 id=137 data-nosnippet>137</a>    }
<a href=#138 id=138 data-nosnippet>138</a>
<a href=#139 id=139 data-nosnippet>139</a>    <span class="kw">pub fn </span>get_service(<span class="kw-2">&amp;</span><span class="self">self</span>, name: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Option</span>&lt;<span class="kw-2">&amp;</span>(<span class="kw">dyn </span>Service + Send + Sync)&gt; {
<a href=#140 id=140 data-nosnippet>140</a>        <span class="self">self</span>.services.get(name).map(|s| s.as_ref())
<a href=#141 id=141 data-nosnippet>141</a>    }
<a href=#142 id=142 data-nosnippet>142</a>
<a href=#143 id=143 data-nosnippet>143</a>    <span class="kw">pub fn </span>add_event_listener&lt;L&gt;(<span class="kw-2">&amp;mut </span><span class="self">self</span>, listener: L)
<a href=#144 id=144 data-nosnippet>144</a>    <span class="kw">where
<a href=#145 id=145 data-nosnippet>145</a>        </span>L: EventListener + Send + Sync + <span class="lifetime">'static
<a href=#146 id=146 data-nosnippet>146</a>    </span>{
<a href=#147 id=147 data-nosnippet>147</a>        <span class="self">self</span>.event_listeners.push(Box::new(listener));
<a href=#148 id=148 data-nosnippet>148</a>    }
<a href=#149 id=149 data-nosnippet>149</a>
<a href=#150 id=150 data-nosnippet>150</a>    <span class="kw">pub async fn </span>emit_event(<span class="kw-2">&amp;</span><span class="self">self</span>, event: ServiceEvent) {
<a href=#151 id=151 data-nosnippet>151</a>        <span class="kw">for </span>listener <span class="kw">in </span><span class="kw-2">&amp;</span><span class="self">self</span>.event_listeners {
<a href=#152 id=152 data-nosnippet>152</a>            listener.on_event(event.clone()).<span class="kw">await</span>;
<a href=#153 id=153 data-nosnippet>153</a>        }
<a href=#154 id=154 data-nosnippet>154</a>    }
<a href=#155 id=155 data-nosnippet>155</a>
<a href=#156 id=156 data-nosnippet>156</a>    <span class="kw">pub fn </span>list_services(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; Vec&lt;<span class="kw-2">&amp;</span>str&gt; {
<a href=#157 id=157 data-nosnippet>157</a>        <span class="self">self</span>.services.keys().map(|s| s.as_str()).collect()
<a href=#158 id=158 data-nosnippet>158</a>    }
<a href=#159 id=159 data-nosnippet>159</a>}
<a href=#160 id=160 data-nosnippet>160</a>
<a href=#161 id=161 data-nosnippet>161</a><span class="kw">impl </span>Default <span class="kw">for </span>ServiceRegistry {
<a href=#162 id=162 data-nosnippet>162</a>    <span class="kw">fn </span>default() -&gt; <span class="self">Self </span>{
<a href=#163 id=163 data-nosnippet>163</a>        <span class="self">Self</span>::new()
<a href=#164 id=164 data-nosnippet>164</a>    }
<a href=#165 id=165 data-nosnippet>165</a>}
<a href=#166 id=166 data-nosnippet>166</a>
<a href=#167 id=167 data-nosnippet>167</a><span class="doccomment">/// 服务生命周期管理器
<a href=#168 id=168 data-nosnippet>168</a></span><span class="kw">pub struct </span>ServiceLifecycleManager {
<a href=#169 id=169 data-nosnippet>169</a>    registry: ServiceRegistry,
<a href=#170 id=170 data-nosnippet>170</a>}
<a href=#171 id=171 data-nosnippet>171</a>
<a href=#172 id=172 data-nosnippet>172</a><span class="kw">impl </span>ServiceLifecycleManager {
<a href=#173 id=173 data-nosnippet>173</a>    <span class="kw">pub fn </span>new() -&gt; <span class="self">Self </span>{
<a href=#174 id=174 data-nosnippet>174</a>        <span class="self">Self </span>{
<a href=#175 id=175 data-nosnippet>175</a>            registry: ServiceRegistry::new(),
<a href=#176 id=176 data-nosnippet>176</a>        }
<a href=#177 id=177 data-nosnippet>177</a>    }
<a href=#178 id=178 data-nosnippet>178</a>
<a href=#179 id=179 data-nosnippet>179</a>    <span class="kw">pub fn </span>with_registry(registry: ServiceRegistry) -&gt; <span class="self">Self </span>{
<a href=#180 id=180 data-nosnippet>180</a>        <span class="self">Self </span>{ registry }
<a href=#181 id=181 data-nosnippet>181</a>    }
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a>    <span class="kw">pub async fn </span>start_all_services(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#184 id=184 data-nosnippet>184</a>        <span class="kw">for </span>(name, _service) <span class="kw">in </span><span class="kw-2">&amp;</span><span class="self">self</span>.registry.services {
<a href=#185 id=185 data-nosnippet>185</a>            <span class="comment">// 这里需要类型转换，实际实现中需要更复杂的处理
<a href=#186 id=186 data-nosnippet>186</a>            </span><span class="macro">tracing::info!</span>(<span class="string">"Starting service: {}"</span>, name);
<a href=#187 id=187 data-nosnippet>187</a>            <span class="self">self</span>.registry.emit_event(ServiceEvent::Started(name.clone())).<span class="kw">await</span>;
<a href=#188 id=188 data-nosnippet>188</a>        }
<a href=#189 id=189 data-nosnippet>189</a>        <span class="prelude-val">Ok</span>(())
<a href=#190 id=190 data-nosnippet>190</a>    }
<a href=#191 id=191 data-nosnippet>191</a>
<a href=#192 id=192 data-nosnippet>192</a>    <span class="kw">pub async fn </span>stop_all_services(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#193 id=193 data-nosnippet>193</a>        <span class="kw">for </span>(name, _service) <span class="kw">in </span><span class="kw-2">&amp;</span><span class="self">self</span>.registry.services {
<a href=#194 id=194 data-nosnippet>194</a>            <span class="macro">tracing::info!</span>(<span class="string">"Stopping service: {}"</span>, name);
<a href=#195 id=195 data-nosnippet>195</a>            <span class="self">self</span>.registry.emit_event(ServiceEvent::Stopped(name.clone())).<span class="kw">await</span>;
<a href=#196 id=196 data-nosnippet>196</a>        }
<a href=#197 id=197 data-nosnippet>197</a>        <span class="prelude-val">Ok</span>(())
<a href=#198 id=198 data-nosnippet>198</a>    }
<a href=#199 id=199 data-nosnippet>199</a>
<a href=#200 id=200 data-nosnippet>200</a>    <span class="kw">pub fn </span>get_registry(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>ServiceRegistry {
<a href=#201 id=201 data-nosnippet>201</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.registry
<a href=#202 id=202 data-nosnippet>202</a>    }
<a href=#203 id=203 data-nosnippet>203</a>
<a href=#204 id=204 data-nosnippet>204</a>    <span class="kw">pub fn </span>get_registry_mut(<span class="kw-2">&amp;mut </span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;mut </span>ServiceRegistry {
<a href=#205 id=205 data-nosnippet>205</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>.registry
<a href=#206 id=206 data-nosnippet>206</a>    }
<a href=#207 id=207 data-nosnippet>207</a>}
<a href=#208 id=208 data-nosnippet>208</a>
<a href=#209 id=209 data-nosnippet>209</a><span class="kw">impl </span>Default <span class="kw">for </span>ServiceLifecycleManager {
<a href=#210 id=210 data-nosnippet>210</a>    <span class="kw">fn </span>default() -&gt; <span class="self">Self </span>{
<a href=#211 id=211 data-nosnippet>211</a>        <span class="self">Self</span>::new()
<a href=#212 id=212 data-nosnippet>212</a>    }
<a href=#213 id=213 data-nosnippet>213</a>}
<a href=#214 id=214 data-nosnippet>214</a>
<a href=#215 id=215 data-nosnippet>215</a><span class="doccomment">/// 服务依赖注入容器
<a href=#216 id=216 data-nosnippet>216</a></span><span class="kw">pub struct </span>ServiceContainer {
<a href=#217 id=217 data-nosnippet>217</a>    repository_manager: Arc&lt;RepositoryManager&gt;,
<a href=#218 id=218 data-nosnippet>218</a>    service_manager: Arc&lt;ServiceManager&gt;,
<a href=#219 id=219 data-nosnippet>219</a>    lifecycle_manager: ServiceLifecycleManager,
<a href=#220 id=220 data-nosnippet>220</a>}
<a href=#221 id=221 data-nosnippet>221</a>
<a href=#222 id=222 data-nosnippet>222</a><span class="kw">impl </span>ServiceContainer {
<a href=#223 id=223 data-nosnippet>223</a>    <span class="kw">pub fn </span>new(repository_manager: Arc&lt;RepositoryManager&gt;) -&gt; <span class="self">Self </span>{
<a href=#224 id=224 data-nosnippet>224</a>        <span class="kw">let </span>service_manager = Arc::new(ServiceManager::new(repository_manager.clone()));
<a href=#225 id=225 data-nosnippet>225</a>        <span class="kw">let </span>lifecycle_manager = ServiceLifecycleManager::new();
<a href=#226 id=226 data-nosnippet>226</a>
<a href=#227 id=227 data-nosnippet>227</a>        <span class="self">Self </span>{
<a href=#228 id=228 data-nosnippet>228</a>            repository_manager,
<a href=#229 id=229 data-nosnippet>229</a>            service_manager,
<a href=#230 id=230 data-nosnippet>230</a>            lifecycle_manager,
<a href=#231 id=231 data-nosnippet>231</a>        }
<a href=#232 id=232 data-nosnippet>232</a>    }
<a href=#233 id=233 data-nosnippet>233</a>
<a href=#234 id=234 data-nosnippet>234</a>    <span class="kw">pub fn </span>get_repository_manager(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>Arc&lt;RepositoryManager&gt; {
<a href=#235 id=235 data-nosnippet>235</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.repository_manager
<a href=#236 id=236 data-nosnippet>236</a>    }
<a href=#237 id=237 data-nosnippet>237</a>
<a href=#238 id=238 data-nosnippet>238</a>    <span class="kw">pub fn </span>get_service_manager(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>Arc&lt;ServiceManager&gt; {
<a href=#239 id=239 data-nosnippet>239</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.service_manager
<a href=#240 id=240 data-nosnippet>240</a>    }
<a href=#241 id=241 data-nosnippet>241</a>
<a href=#242 id=242 data-nosnippet>242</a>    <span class="kw">pub fn </span>get_lifecycle_manager(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>ServiceLifecycleManager {
<a href=#243 id=243 data-nosnippet>243</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.lifecycle_manager
<a href=#244 id=244 data-nosnippet>244</a>    }
<a href=#245 id=245 data-nosnippet>245</a>
<a href=#246 id=246 data-nosnippet>246</a>    <span class="kw">pub fn </span>get_lifecycle_manager_mut(<span class="kw-2">&amp;mut </span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;mut </span>ServiceLifecycleManager {
<a href=#247 id=247 data-nosnippet>247</a>        <span class="kw-2">&amp;mut </span><span class="self">self</span>.lifecycle_manager
<a href=#248 id=248 data-nosnippet>248</a>    }
<a href=#249 id=249 data-nosnippet>249</a>
<a href=#250 id=250 data-nosnippet>250</a>    <span class="kw">pub async fn </span>initialize(<span class="kw-2">&amp;mut </span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#251 id=251 data-nosnippet>251</a>        <span class="comment">// 注册所有服务
<a href=#252 id=252 data-nosnippet>252</a>        // 这里需要实际的服务注册逻辑
<a href=#253 id=253 data-nosnippet>253</a>        </span><span class="macro">tracing::info!</span>(<span class="string">"Initializing service container"</span>);
<a href=#254 id=254 data-nosnippet>254</a>        
<a href=#255 id=255 data-nosnippet>255</a>        <span class="comment">// 启动所有服务
<a href=#256 id=256 data-nosnippet>256</a>        </span><span class="self">self</span>.lifecycle_manager.start_all_services().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#257 id=257 data-nosnippet>257</a>        
<a href=#258 id=258 data-nosnippet>258</a>        <span class="prelude-val">Ok</span>(())
<a href=#259 id=259 data-nosnippet>259</a>    }
<a href=#260 id=260 data-nosnippet>260</a>
<a href=#261 id=261 data-nosnippet>261</a>    <span class="kw">pub async fn </span>shutdown(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#262 id=262 data-nosnippet>262</a>        <span class="macro">tracing::info!</span>(<span class="string">"Shutting down service container"</span>);
<a href=#263 id=263 data-nosnippet>263</a>        <span class="self">self</span>.lifecycle_manager.stop_all_services().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#264 id=264 data-nosnippet>264</a>        <span class="prelude-val">Ok</span>(())
<a href=#265 id=265 data-nosnippet>265</a>    }
<a href=#266 id=266 data-nosnippet>266</a>}
<a href=#267 id=267 data-nosnippet>267</a>
<a href=#268 id=268 data-nosnippet>268</a><span class="doccomment">/// 服务配置
<a href=#269 id=269 data-nosnippet>269</a></span><span class="attr">#[derive(Debug, Clone)]
<a href=#270 id=270 data-nosnippet>270</a></span><span class="kw">pub struct </span>ServiceConfig {
<a href=#271 id=271 data-nosnippet>271</a>    <span class="kw">pub </span>name: String,
<a href=#272 id=272 data-nosnippet>272</a>    <span class="kw">pub </span>enabled: bool,
<a href=#273 id=273 data-nosnippet>273</a>    <span class="kw">pub </span>auto_start: bool,
<a href=#274 id=274 data-nosnippet>274</a>    <span class="kw">pub </span>restart_on_failure: bool,
<a href=#275 id=275 data-nosnippet>275</a>    <span class="kw">pub </span>max_restart_attempts: u32,
<a href=#276 id=276 data-nosnippet>276</a>    <span class="kw">pub </span>health_check_interval: std::time::Duration,
<a href=#277 id=277 data-nosnippet>277</a>    <span class="kw">pub </span>config: serde_json::Value,
<a href=#278 id=278 data-nosnippet>278</a>}
<a href=#279 id=279 data-nosnippet>279</a>
<a href=#280 id=280 data-nosnippet>280</a><span class="kw">impl </span>Default <span class="kw">for </span>ServiceConfig {
<a href=#281 id=281 data-nosnippet>281</a>    <span class="kw">fn </span>default() -&gt; <span class="self">Self </span>{
<a href=#282 id=282 data-nosnippet>282</a>        <span class="self">Self </span>{
<a href=#283 id=283 data-nosnippet>283</a>            name: String::new(),
<a href=#284 id=284 data-nosnippet>284</a>            enabled: <span class="bool-val">true</span>,
<a href=#285 id=285 data-nosnippet>285</a>            auto_start: <span class="bool-val">true</span>,
<a href=#286 id=286 data-nosnippet>286</a>            restart_on_failure: <span class="bool-val">true</span>,
<a href=#287 id=287 data-nosnippet>287</a>            max_restart_attempts: <span class="number">3</span>,
<a href=#288 id=288 data-nosnippet>288</a>            health_check_interval: std::time::Duration::from_secs(<span class="number">30</span>),
<a href=#289 id=289 data-nosnippet>289</a>            config: serde_json::Value::Null,
<a href=#290 id=290 data-nosnippet>290</a>        }
<a href=#291 id=291 data-nosnippet>291</a>    }
<a href=#292 id=292 data-nosnippet>292</a>}</code></pre></div></section></main></body></html>