[package]
name = "weibo-crawler-node"
version = "0.1.0"
description = "微博舆情分析系统的分布式爬虫节点"
authors = ["Weibo Sentiment Analysis Team"]
license = "MIT"
repository = "https://github.com/your-org/weibo-sentiment-analysis"
edition = "2021"

[lib]
name = "weibo_crawler_node"
path = "src/lib.rs"

[[bin]]
name = "weibo-crawler-node"
path = "src/main.rs"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[dependencies]
tauri = { version = "1.5", features = [ "window-hide", "window-close", "window-start-dragging", "shell-open", "os-all", "fs-all", "window-unminimize", "window-unmaximize", "window-show", "window-minimize", "window-maximize", "path-all", "global-shortcut"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid", "macros", "migrate"] }
reqwest = { version = "0.11", features = ["json", "cookies", "gzip", "brotli"] }
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
thiserror = "1.0"
redis = { version = "0.24", features = ["tokio-comp"] }
lapin = "2.3"
futures = "0.3"
rand = "0.8"
base64 = "0.21"
url = "2.4"
scraper = "0.18"
regex = "1.10"
image = "0.24"
parking_lot = "0.12"
dashmap = "5.5"
once_cell = "1.19"
async-trait = "0.1"
open = "5.0"
playwright = "0.0.20"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[[bin]]
name = "migrate"
path = "src/bin/migrate.rs"

[[bin]]
name = "seed"
path = "src/bin/seed.rs"
