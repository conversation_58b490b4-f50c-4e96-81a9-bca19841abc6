<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `service` mod in crate `weibo_crawler_node`."><title>weibo_crawler_node::service - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc mod"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Module service</a></h2><h3><a href="#reexports">Module Items</a></h3><ul class="block"><li><a href="#reexports" title="Re-exports">Re-exports</a></li><li><a href="#modules" title="Modules">Modules</a></li><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#enums" title="Enums">Enums</a></li><li><a href="#traits" title="Traits">Traits</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="../index.html">In crate weibo_<wbr>crawler_<wbr>node</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a></div><h1>Module <span>service</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/service/mod.rs.html#1-292">Source</a> </span></div><h2 id="reexports" class="section-header">Re-exports<a href="#reexports" class="anchor">§</a></h2><dl class="item-table reexports"><dt id="reexport.TaskService"><code>pub use task_service::<a class="struct" href="task_service/struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a>;</code></dt><dt id="reexport.ProxyService"><code>pub use proxy_service::<a class="struct" href="proxy_service/struct.ProxyService.html" title="struct weibo_crawler_node::service::proxy_service::ProxyService">ProxyService</a>;</code></dt><dt id="reexport.AccountService"><code>pub use account_service::<a class="struct" href="account_service/struct.AccountService.html" title="struct weibo_crawler_node::service::account_service::AccountService">AccountService</a>;</code></dt><dt id="reexport.CrawlerService"><code>pub use crawler_service::<a class="struct" href="crawler_service/struct.CrawlerService.html" title="struct weibo_crawler_node::service::crawler_service::CrawlerService">CrawlerService</a>;</code></dt><dt id="reexport.MonitorService"><code>pub use monitor_service::<a class="struct" href="monitor_service/struct.MonitorService.html" title="struct weibo_crawler_node::service::monitor_service::MonitorService">MonitorService</a>;</code></dt></dl><h2 id="modules" class="section-header">Modules<a href="#modules" class="anchor">§</a></h2><dl class="item-table"><dt><a class="mod" href="account_service/index.html" title="mod weibo_crawler_node::service::account_service">account_<wbr>service</a></dt><dt><a class="mod" href="crawler_service/index.html" title="mod weibo_crawler_node::service::crawler_service">crawler_<wbr>service</a></dt><dt><a class="mod" href="monitor_service/index.html" title="mod weibo_crawler_node::service::monitor_service">monitor_<wbr>service</a></dt><dt><a class="mod" href="proxy_service/index.html" title="mod weibo_crawler_node::service::proxy_service">proxy_<wbr>service</a></dt><dt><a class="mod" href="task_service/index.html" title="mod weibo_crawler_node::service::task_service">task_<wbr>service</a></dt></dl><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.ServiceConfig.html" title="struct weibo_crawler_node::service::ServiceConfig">Service<wbr>Config</a></dt><dd>服务配置</dd><dt><a class="struct" href="struct.ServiceContainer.html" title="struct weibo_crawler_node::service::ServiceContainer">Service<wbr>Container</a></dt><dd>服务依赖注入容器</dd><dt><a class="struct" href="struct.ServiceLifecycleManager.html" title="struct weibo_crawler_node::service::ServiceLifecycleManager">Service<wbr>Lifecycle<wbr>Manager</a></dt><dd>服务生命周期管理器</dd><dt><a class="struct" href="struct.ServiceManager.html" title="struct weibo_crawler_node::service::ServiceManager">Service<wbr>Manager</a></dt><dd>服务管理器 - 统一管理所有服务</dd><dt><a class="struct" href="struct.ServiceRegistry.html" title="struct weibo_crawler_node::service::ServiceRegistry">Service<wbr>Registry</a></dt><dd>服务注册表</dd></dl><h2 id="enums" class="section-header">Enums<a href="#enums" class="anchor">§</a></h2><dl class="item-table"><dt><a class="enum" href="enum.HealthStatus.html" title="enum weibo_crawler_node::service::HealthStatus">Health<wbr>Status</a></dt><dd>健康状态</dd><dt><a class="enum" href="enum.ServiceEvent.html" title="enum weibo_crawler_node::service::ServiceEvent">Service<wbr>Event</a></dt><dd>服务事件</dd></dl><h2 id="traits" class="section-header">Traits<a href="#traits" class="anchor">§</a></h2><dl class="item-table"><dt><a class="trait" href="trait.ConfigurableService.html" title="trait weibo_crawler_node::service::ConfigurableService">Configurable<wbr>Service</a></dt><dd>可配置的服务特征</dd><dt><a class="trait" href="trait.EventListener.html" title="trait weibo_crawler_node::service::EventListener">Event<wbr>Listener</a></dt><dd>事件监听器特征</dd><dt><a class="trait" href="trait.MonitorableService.html" title="trait weibo_crawler_node::service::MonitorableService">Monitorable<wbr>Service</a></dt><dd>可监控的服务特征</dd><dt><a class="trait" href="trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a></dt><dd>基础服务特征</dd><dt><a class="trait" href="trait.StartableService.html" title="trait weibo_crawler_node::service::StartableService">Startable<wbr>Service</a></dt><dd>可启动的服务特征</dd></dl></section></div></main></body></html>