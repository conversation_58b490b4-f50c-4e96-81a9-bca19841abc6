{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 8742123966112767284, "deps": [[2883436298747778685, "pki_types", false, 17984470032282637163], [3722963349756955755, "once_cell", false, 3092077161891181902], [5491919304041016563, "ring", false, 16503626731405694287], [6528079939221783635, "zeroize", false, 2107467412134038271], [8151164558401866693, "<PERSON><PERSON><PERSON>", false, 8517344051108291926], [17003143334332120809, "subtle", false, 6221056946787749379], [17956658536657219733, "build_script_build", false, 18006552977241695110]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-985ad3750e401728\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}