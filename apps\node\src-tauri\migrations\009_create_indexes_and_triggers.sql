-- 为现有表添加额外的复合索引以优化查询性能

-- 爬取任务表的复合索引
CREATE INDEX idx_crawl_tasks_status_priority ON crawl_tasks(status, priority DESC);
CREATE INDEX idx_crawl_tasks_task_type_status ON crawl_tasks(task_type, status);
CREATE INDEX idx_crawl_tasks_created_at_status ON crawl_tasks(created_at, status);

-- 代理表的复合索引
CREATE INDEX idx_proxies_status_response_time ON proxies(status, response_time);
CREATE INDEX idx_proxies_country_status ON proxies(country, status);
CREATE INDEX idx_proxies_provider_status ON proxies(provider, status);

-- 账号表的复合索引
CREATE INDEX idx_accounts_status_risk_score ON accounts(status, risk_score);
CREATE INDEX idx_accounts_last_login_status ON accounts(last_login, status);

-- 任务结果表的复合索引
CREATE INDEX idx_task_results_result_type_created_at ON task_results(result_type, created_at);
CREATE INDEX idx_task_results_proxy_used_created_at ON task_results(proxy_used, created_at);
CREATE INDEX idx_task_results_account_used_created_at ON task_results(account_used, created_at);

-- 缓存表的复合索引
CREATE INDEX idx_user_cache_expires_at_last_updated ON user_cache(expires_at, last_updated);
CREATE INDEX idx_content_cache_content_type_expires_at ON content_cache(content_type, expires_at);
CREATE INDEX idx_content_cache_author_id_publish_time ON content_cache(author_id, publish_time);

-- 创建数据清理触发器

-- 自动清理过期的请求缓存
CREATE TRIGGER cleanup_expired_request_cache
    AFTER INSERT ON request_cache
    FOR EACH ROW
    WHEN (SELECT COUNT(*) FROM request_cache WHERE expires_at < datetime('now')) > 1000
BEGIN
    DELETE FROM request_cache WHERE expires_at < datetime('now', '-1 hour');
END;

-- 自动清理过期的用户缓存
CREATE TRIGGER cleanup_expired_user_cache
    AFTER INSERT ON user_cache
    FOR EACH ROW
    WHEN (SELECT COUNT(*) FROM user_cache WHERE expires_at < datetime('now')) > 500
BEGIN
    DELETE FROM user_cache WHERE expires_at < datetime('now', '-1 hour');
END;

-- 自动清理过期的内容缓存
CREATE TRIGGER cleanup_expired_content_cache
    AFTER INSERT ON content_cache
    FOR EACH ROW
    WHEN (SELECT COUNT(*) FROM content_cache WHERE expires_at < datetime('now')) > 1000
BEGIN
    DELETE FROM content_cache WHERE expires_at < datetime('now', '-1 hour');
END;

-- 自动清理过期的话题缓存
CREATE TRIGGER cleanup_expired_topic_cache
    AFTER INSERT ON topic_cache
    FOR EACH ROW
    WHEN (SELECT COUNT(*) FROM topic_cache WHERE expires_at < datetime('now')) > 200
BEGIN
    DELETE FROM topic_cache WHERE expires_at < datetime('now', '-1 hour');
END;

-- 自动清理过期的系统缓存
CREATE TRIGGER cleanup_expired_system_cache
    AFTER INSERT ON system_cache
    FOR EACH ROW
    WHEN NEW.expires_at IS NOT NULL
BEGIN
    DELETE FROM system_cache WHERE expires_at IS NOT NULL AND expires_at < datetime('now', '-1 hour');
END;

-- 自动清理旧的系统监控数据 (保留30天)
CREATE TRIGGER cleanup_old_system_metrics
    AFTER INSERT ON system_metrics
    FOR EACH ROW
    WHEN (SELECT COUNT(*) FROM system_metrics WHERE recorded_at < datetime('now', '-30 days')) > 0
BEGIN
    DELETE FROM system_metrics WHERE recorded_at < datetime('now', '-30 days');
END;

-- 自动清理旧的错误日志 (保留7天的Debug/Info，30天的Warn/Error/Fatal)
CREATE TRIGGER cleanup_old_error_logs
    AFTER INSERT ON error_logs
    FOR EACH ROW
BEGIN
    DELETE FROM error_logs WHERE error_level <= 2 AND created_at < datetime('now', '-7 days');
    DELETE FROM error_logs WHERE error_level > 2 AND created_at < datetime('now', '-30 days');
END;

-- 自动清理旧的任务日志 (保留7天)
CREATE TRIGGER cleanup_old_task_logs
    AFTER INSERT ON task_logs
    FOR EACH ROW
    WHEN (SELECT COUNT(*) FROM task_logs WHERE created_at < datetime('now', '-7 days')) > 10000
BEGIN
    DELETE FROM task_logs WHERE created_at < datetime('now', '-7 days');
END;
