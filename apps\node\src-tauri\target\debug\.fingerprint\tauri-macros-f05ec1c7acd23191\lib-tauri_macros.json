{"rustc": 1842507548689473721, "features": "[\"compression\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 11209818962078086765, "deps": [[2713742371683562785, "syn", false, 9891413255480890497], [3060637413840920116, "proc_macro2", false, 10993881616374897205], [8292277814562636972, "tauri_utils", false, 10667560426277437857], [13077543566650298139, "heck", false, 723539331256257772], [17492769205600034078, "tauri_codegen", false, 3537994757193276251], [17990358020177143287, "quote", false, 8157755205024365785]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-f05ec1c7acd23191\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}