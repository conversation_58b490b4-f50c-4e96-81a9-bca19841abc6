import { Routes, Route } from "react-router-dom";
import { Suspense, lazy } from "react";
import { Layout, ErrorBoundary, LoadingSkeleton } from "./components/Layout";

// 懒加载页面组件
const Dashboard = lazy(() => import("./pages/Dashboard"));
const TaskManager = lazy(() => import("./pages/TaskManager"));
const ProxyManager = lazy(() => import("./pages/ProxyManager"));
const AccountManager = lazy(() => import("./pages/AccountManager"));
const SystemMonitor = lazy(() => import("./pages/SystemMonitor"));
const Settings = lazy(() => import("./pages/Settings"));
const BrowserTest = lazy(() => import("./pages/BrowserTest"));

function App() {
  return (
    <ErrorBoundary>
      <Layout>
        <Suspense fallback={<LoadingSkeleton />}>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/tasks" element={<TaskManager />} />
            <Route path="/proxies" element={<ProxyManager />} />
            <Route path="/accounts" element={<AccountManager />} />
            <Route path="/monitor" element={<SystemMonitor />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/browser-test" element={<BrowserTest />} />
          </Routes>
        </Suspense>
      </Layout>
    </ErrorBoundary>
  );
}

export default App;
