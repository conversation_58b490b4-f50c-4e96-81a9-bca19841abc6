# 卡片组件布局最佳实践指南

## 📋 概述

本指南介绍了项目中卡片组件的最佳实践，包括布局原则、组件使用和响应式设计。

## 🎨 卡片组件类型

### 1. 基础卡片 (Card)
```tsx
<Card variant="default" size="default">
  <CardHeader>
    <CardTitle>标题</CardTitle>
    <CardDescription>描述信息</CardDescription>
  </CardHeader>
  <CardContent>
    内容区域
  </CardContent>
  <CardFooter>
    操作按钮
  </CardFooter>
</Card>
```

**适用场景**：通用内容展示、表单容器、详细信息展示

### 2. 统计卡片 (StatCard)
```tsx
<StatCard
  title="活跃用户"
  value="1,234"
  change="+12% 较上月"
  trend="up"
  icon={<Users className="w-6 h-6" />}
/>
```

**适用场景**：数据统计、KPI展示、快速概览

### 3. 指标卡片 (MetricCard)
```tsx
<MetricCard
  title="CPU使用率"
  value={75.5}
  unit="%"
  progress={75.5}
  color="hsl(var(--info))"
  description="当前处理器使用情况"
/>
```

**适用场景**：系统监控、性能指标、进度展示

### 4. 信息卡片 (InfoCard)
```tsx
<InfoCard
  title="系统状态"
  subtitle="节点运行信息"
  status="success"
  content={<div>详细内容</div>}
  actions={<Button>操作</Button>}
/>
```

**适用场景**：复杂信息展示、状态面板、操作中心

## 📐 布局原则

### 1. 间距规范
- **卡片间距**：4-6个单位 (`gap-4 xl:gap-6`)
- **内边距**：移动端4个单位，桌面端6个单位 (`p-4 sm:p-6`)
- **元素间距**：2-4个单位 (`space-y-2` 到 `space-y-4`)

### 2. 响应式设计
```tsx
// 推荐的网格布局
<div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 xl:gap-6">
  {/* 卡片内容 */}
</div>
```

**断点说明**：
- `grid-cols-1`：移动端单列
- `sm:grid-cols-2`：小屏幕双列
- `xl:grid-cols-4`：大屏幕四列

### 3. 文字大小层级
```css
/* 标题 */
text-lg sm:text-xl        /* 卡片标题 */
text-sm                   /* 描述文字 */

/* 数值 */
text-xl sm:text-2xl lg:text-3xl  /* 统计数值 */
text-2xl sm:text-3xl lg:text-4xl /* 重要指标 */

/* 辅助信息 */
text-xs sm:text-sm        /* 变化趋势、单位等 */
```

## 🎯 使用建议

### 1. 选择合适的卡片类型
- **简单数据展示** → StatCard
- **带进度条的指标** → MetricCard  
- **复杂内容布局** → InfoCard
- **通用内容容器** → Card

### 2. 图标使用规范
```tsx
// 统计卡片图标
<Icon className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />

// 指标卡片图标容器
<div className="w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl">
  <Icon />
</div>
```

### 3. 颜色系统
```tsx
// 状态颜色
color="hsl(var(--primary))"    // 主要信息
color="hsl(var(--success))"    // 成功/正常
color="hsl(var(--warning))"    // 警告/注意
color="hsl(var(--destructive))" // 错误/危险
color="hsl(var(--info))"       // 信息/提示
```

## 🚀 性能优化

### 1. 动画效果
```tsx
// 入场动画
className="animate-scale-in"
className="animate-fade-in"

// 延迟动画
style={{ animationDelay: `${index * 100}ms` }}
```

### 2. 交互反馈
```tsx
// 交互式卡片
<Card variant="interactive">
  {/* 自动添加 hover 效果和点击反馈 */}
</Card>
```

## ❌ 常见问题

### 1. 避免混用组件
```tsx
// ❌ 错误：混用旧的CSS类和新组件
<div className="card">
  <Card>...</Card>
</div>

// ✅ 正确：统一使用新组件
<Card variant="elevated">
  <CardContent>...</CardContent>
</Card>
```

### 2. 避免固定尺寸
```tsx
// ❌ 错误：固定宽高
<Card style={{ width: '300px', height: '200px' }}>

// ✅ 正确：响应式布局
<Card className="w-full">
```

### 3. 避免内容溢出
```tsx
// ✅ 正确：使用 truncate 处理长文本
<p className="text-sm font-medium text-muted-foreground truncate">
  {longTitle}
</p>
```

## 📱 移动端适配

### 1. 触摸友好
- 最小点击区域：44px × 44px
- 卡片间距足够，避免误触
- 重要操作使用较大的按钮

### 2. 内容优先级
```tsx
// 移动端隐藏次要信息
<div className="hidden sm:block">
  <CardDescription>详细描述</CardDescription>
</div>
```

### 3. 滚动优化
```tsx
// 水平滚动卡片列表
<div className="flex space-x-4 overflow-x-auto pb-4">
  {cards.map(card => <Card key={card.id} className="flex-shrink-0 w-64" />)}
</div>
```

## 🔧 自定义扩展

### 1. 创建自定义卡片变体
```tsx
// 扩展现有的变体样式函数
const getCustomCardVariantStyles = (variant: CardVariant | 'premium' = 'default') => {
  const baseVariants = getCardVariantStyles(variant as CardVariant);
  const customVariants = {
    premium: "bg-gradient-to-br from-purple-500/10 to-pink-500/10 border-purple-200",
  };

  return variant === 'premium' ? customVariants.premium : baseVariants;
};
```

### 2. 扩展现有组件
```tsx
interface CustomStatCardProps extends StatCardProps {
  badge?: string;
  onClick?: () => void;
}

const CustomStatCard = ({ badge, onClick, ...props }: CustomStatCardProps) => (
  <div className="relative">
    <StatCard {...props} onClick={onClick} />
    {badge && (
      <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
        {badge}
      </div>
    )}
  </div>
);
```

## 📊 布局示例

### 仪表板布局
```tsx
<div className="space-y-6">
  {/* 主要状态卡片 */}
  <InfoCard title="系统概览" content={...} />
  
  {/* 统计指标网格 */}
  <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 xl:gap-6">
    <StatCard title="用户数" value="1,234" />
    <StatCard title="订单数" value="5,678" />
    <StatCard title="收入" value="¥12,345" />
    <StatCard title="转化率" value="3.2%" />
  </div>
  
  {/* 详细指标 */}
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <Card>
      <CardHeader>
        <CardTitle>性能监控</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <MetricCard title="CPU" value={75} unit="%" progress={75} />
          <MetricCard title="内存" value={60} unit="%" progress={60} />
          <MetricCard title="磁盘" value={45} unit="%" progress={45} />
        </div>
      </CardContent>
    </Card>
  </div>
</div>
```

这个指南确保了卡片组件的一致性使用和最佳的用户体验。
