<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\repository\task_repository.rs`."><title>task_repository.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node\repository/</div>task_repository.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use super</span>::{Repository, PaginatedRepository, PaginatedResult};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span><span class="kw">crate</span>::error::{<span class="prelude-ty">Result</span>};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span><span class="kw">crate</span>::storage::TaskRecord;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>async_trait::async_trait;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>sqlx::SqlitePool;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>std::sync::Arc;
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="attr">#[derive(Debug, Clone)]
<a href=#9 id=9 data-nosnippet>9</a></span><span class="kw">pub struct </span>TaskRepository {
<a href=#10 id=10 data-nosnippet>10</a>    pool: Arc&lt;SqlitePool&gt;,
<a href=#11 id=11 data-nosnippet>11</a>}
<a href=#12 id=12 data-nosnippet>12</a>
<a href=#13 id=13 data-nosnippet>13</a><span class="kw">impl </span>TaskRepository {
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub fn </span>new(pool: Arc&lt;SqlitePool&gt;) -&gt; <span class="self">Self </span>{
<a href=#15 id=15 data-nosnippet>15</a>        <span class="self">Self </span>{ pool }
<a href=#16 id=16 data-nosnippet>16</a>    }
<a href=#17 id=17 data-nosnippet>17</a>
<a href=#18 id=18 data-nosnippet>18</a>    <span class="doccomment">/// 根据状态查找任务
<a href=#19 id=19 data-nosnippet>19</a>    </span><span class="kw">pub async fn </span>find_by_status(<span class="kw-2">&amp;</span><span class="self">self</span>, status: i32) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;TaskRecord&gt;&gt; {
<a href=#20 id=20 data-nosnippet>20</a>        <span class="comment">// 暂时返回空列表，避免编译错误
<a href=#21 id=21 data-nosnippet>21</a>        // 实际实现需要数据库表存在
<a href=#22 id=22 data-nosnippet>22</a>        </span><span class="kw">let _ </span>= status; <span class="comment">// 避免未使用参数警告
<a href=#23 id=23 data-nosnippet>23</a>        </span><span class="prelude-val">Ok</span>(Vec::new())
<a href=#24 id=24 data-nosnippet>24</a>    }
<a href=#25 id=25 data-nosnippet>25</a>
<a href=#26 id=26 data-nosnippet>26</a>    <span class="doccomment">/// 根据任务类型查找任务
<a href=#27 id=27 data-nosnippet>27</a>    </span><span class="kw">pub async fn </span>find_by_type(<span class="kw-2">&amp;</span><span class="self">self</span>, task_type: i32) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;TaskRecord&gt;&gt; {
<a href=#28 id=28 data-nosnippet>28</a>        <span class="comment">// 暂时返回空列表，避免编译错误
<a href=#29 id=29 data-nosnippet>29</a>        </span><span class="kw">let _ </span>= task_type;
<a href=#30 id=30 data-nosnippet>30</a>        <span class="prelude-val">Ok</span>(Vec::new())
<a href=#31 id=31 data-nosnippet>31</a>    }
<a href=#32 id=32 data-nosnippet>32</a>
<a href=#33 id=33 data-nosnippet>33</a>    <span class="doccomment">/// 获取待处理任务（按优先级排序）
<a href=#34 id=34 data-nosnippet>34</a>    </span><span class="kw">pub async fn </span>get_pending_tasks(<span class="kw-2">&amp;</span><span class="self">self</span>, limit: u32) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;TaskRecord&gt;&gt; {
<a href=#35 id=35 data-nosnippet>35</a>        <span class="kw">let _ </span>= limit;
<a href=#36 id=36 data-nosnippet>36</a>        <span class="prelude-val">Ok</span>(Vec::new())
<a href=#37 id=37 data-nosnippet>37</a>    }
<a href=#38 id=38 data-nosnippet>38</a>
<a href=#39 id=39 data-nosnippet>39</a>    <span class="doccomment">/// 更新任务状态
<a href=#40 id=40 data-nosnippet>40</a>    </span><span class="kw">pub async fn </span>update_status(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str, status: i32) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#41 id=41 data-nosnippet>41</a>        <span class="kw">let _ </span>= (task_id, status);
<a href=#42 id=42 data-nosnippet>42</a>        <span class="prelude-val">Ok</span>(())
<a href=#43 id=43 data-nosnippet>43</a>    }
<a href=#44 id=44 data-nosnippet>44</a>
<a href=#45 id=45 data-nosnippet>45</a>    <span class="doccomment">/// 开始任务（设置开始时间）
<a href=#46 id=46 data-nosnippet>46</a>    </span><span class="kw">pub async fn </span>start_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#47 id=47 data-nosnippet>47</a>        <span class="kw">let _ </span>= task_id;
<a href=#48 id=48 data-nosnippet>48</a>        <span class="prelude-val">Ok</span>(())
<a href=#49 id=49 data-nosnippet>49</a>    }
<a href=#50 id=50 data-nosnippet>50</a>
<a href=#51 id=51 data-nosnippet>51</a>    <span class="doccomment">/// 完成任务
<a href=#52 id=52 data-nosnippet>52</a>    </span><span class="kw">pub async fn </span>complete_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#53 id=53 data-nosnippet>53</a>        <span class="kw">let _ </span>= task_id;
<a href=#54 id=54 data-nosnippet>54</a>        <span class="prelude-val">Ok</span>(())
<a href=#55 id=55 data-nosnippet>55</a>    }
<a href=#56 id=56 data-nosnippet>56</a>
<a href=#57 id=57 data-nosnippet>57</a>    <span class="doccomment">/// 任务失败（增加重试次数）
<a href=#58 id=58 data-nosnippet>58</a>    </span><span class="kw">pub async fn </span>fail_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#59 id=59 data-nosnippet>59</a>        <span class="kw">let _ </span>= task_id;
<a href=#60 id=60 data-nosnippet>60</a>        <span class="prelude-val">Ok</span>(())
<a href=#61 id=61 data-nosnippet>61</a>    }
<a href=#62 id=62 data-nosnippet>62</a>
<a href=#63 id=63 data-nosnippet>63</a>    <span class="doccomment">/// 获取任务统计信息
<a href=#64 id=64 data-nosnippet>64</a>    </span><span class="kw">pub async fn </span>get_statistics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;TaskStatistics&gt; {
<a href=#65 id=65 data-nosnippet>65</a>        <span class="prelude-val">Ok</span>(TaskStatistics {
<a href=#66 id=66 data-nosnippet>66</a>            total: <span class="number">0</span>,
<a href=#67 id=67 data-nosnippet>67</a>            pending: <span class="number">0</span>,
<a href=#68 id=68 data-nosnippet>68</a>            running: <span class="number">0</span>,
<a href=#69 id=69 data-nosnippet>69</a>            completed: <span class="number">0</span>,
<a href=#70 id=70 data-nosnippet>70</a>            failed: <span class="number">0</span>,
<a href=#71 id=71 data-nosnippet>71</a>        })
<a href=#72 id=72 data-nosnippet>72</a>    }
<a href=#73 id=73 data-nosnippet>73</a>
<a href=#74 id=74 data-nosnippet>74</a>    <span class="doccomment">/// 清理旧任务
<a href=#75 id=75 data-nosnippet>75</a>    </span><span class="kw">pub async fn </span>cleanup_old_tasks(<span class="kw-2">&amp;</span><span class="self">self</span>, days: i32) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#76 id=76 data-nosnippet>76</a>        <span class="kw">let _ </span>= days;
<a href=#77 id=77 data-nosnippet>77</a>        <span class="prelude-val">Ok</span>(<span class="number">0</span>)
<a href=#78 id=78 data-nosnippet>78</a>    }
<a href=#79 id=79 data-nosnippet>79</a>}
<a href=#80 id=80 data-nosnippet>80</a>
<a href=#81 id=81 data-nosnippet>81</a><span class="attr">#[async_trait]
<a href=#82 id=82 data-nosnippet>82</a></span><span class="kw">impl </span>Repository&lt;TaskRecord, i64&gt; <span class="kw">for </span>TaskRepository {
<a href=#83 id=83 data-nosnippet>83</a>    <span class="kw">async fn </span>find_by_id(<span class="kw-2">&amp;</span><span class="self">self</span>, _id: i64) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;TaskRecord&gt;&gt; {
<a href=#84 id=84 data-nosnippet>84</a>        <span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>)
<a href=#85 id=85 data-nosnippet>85</a>    }
<a href=#86 id=86 data-nosnippet>86</a>
<a href=#87 id=87 data-nosnippet>87</a>    <span class="kw">async fn </span>find_all(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;TaskRecord&gt;&gt; {
<a href=#88 id=88 data-nosnippet>88</a>        <span class="prelude-val">Ok</span>(Vec::new())
<a href=#89 id=89 data-nosnippet>89</a>    }
<a href=#90 id=90 data-nosnippet>90</a>
<a href=#91 id=91 data-nosnippet>91</a>    <span class="kw">async fn </span>create(<span class="kw-2">&amp;</span><span class="self">self</span>, entity: TaskRecord) -&gt; <span class="prelude-ty">Result</span>&lt;TaskRecord&gt; {
<a href=#92 id=92 data-nosnippet>92</a>        <span class="prelude-val">Ok</span>(entity)
<a href=#93 id=93 data-nosnippet>93</a>    }
<a href=#94 id=94 data-nosnippet>94</a>
<a href=#95 id=95 data-nosnippet>95</a>    <span class="kw">async fn </span>update(<span class="kw-2">&amp;</span><span class="self">self</span>, _id: i64, entity: TaskRecord) -&gt; <span class="prelude-ty">Result</span>&lt;TaskRecord&gt; {
<a href=#96 id=96 data-nosnippet>96</a>        <span class="prelude-val">Ok</span>(entity)
<a href=#97 id=97 data-nosnippet>97</a>    }
<a href=#98 id=98 data-nosnippet>98</a>
<a href=#99 id=99 data-nosnippet>99</a>    <span class="kw">async fn </span>delete(<span class="kw-2">&amp;</span><span class="self">self</span>, _id: i64) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#100 id=100 data-nosnippet>100</a>        <span class="prelude-val">Ok</span>(())
<a href=#101 id=101 data-nosnippet>101</a>    }
<a href=#102 id=102 data-nosnippet>102</a>
<a href=#103 id=103 data-nosnippet>103</a>    <span class="kw">async fn </span>count(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;i64&gt; {
<a href=#104 id=104 data-nosnippet>104</a>        <span class="prelude-val">Ok</span>(<span class="number">0</span>)
<a href=#105 id=105 data-nosnippet>105</a>    }
<a href=#106 id=106 data-nosnippet>106</a>}
<a href=#107 id=107 data-nosnippet>107</a>
<a href=#108 id=108 data-nosnippet>108</a><span class="attr">#[async_trait]
<a href=#109 id=109 data-nosnippet>109</a></span><span class="kw">impl </span>PaginatedRepository&lt;TaskRecord&gt; <span class="kw">for </span>TaskRepository {
<a href=#110 id=110 data-nosnippet>110</a>    <span class="kw">async fn </span>find_paginated(<span class="kw-2">&amp;</span><span class="self">self</span>, page: u32, limit: u32) -&gt; <span class="prelude-ty">Result</span>&lt;PaginatedResult&lt;TaskRecord&gt;&gt; {
<a href=#111 id=111 data-nosnippet>111</a>        <span class="prelude-val">Ok</span>(PaginatedResult::new(Vec::new(), <span class="number">0</span>, page, limit))
<a href=#112 id=112 data-nosnippet>112</a>    }
<a href=#113 id=113 data-nosnippet>113</a>}
<a href=#114 id=114 data-nosnippet>114</a>
<a href=#115 id=115 data-nosnippet>115</a><span class="attr">#[derive(Debug, Clone)]
<a href=#116 id=116 data-nosnippet>116</a></span><span class="kw">pub struct </span>TaskStatistics {
<a href=#117 id=117 data-nosnippet>117</a>    <span class="kw">pub </span>total: u64,
<a href=#118 id=118 data-nosnippet>118</a>    <span class="kw">pub </span>pending: u32,
<a href=#119 id=119 data-nosnippet>119</a>    <span class="kw">pub </span>running: u32,
<a href=#120 id=120 data-nosnippet>120</a>    <span class="kw">pub </span>completed: u64,
<a href=#121 id=121 data-nosnippet>121</a>    <span class="kw">pub </span>failed: u64,
<a href=#122 id=122 data-nosnippet>122</a>}</code></pre></div></section></main></body></html>