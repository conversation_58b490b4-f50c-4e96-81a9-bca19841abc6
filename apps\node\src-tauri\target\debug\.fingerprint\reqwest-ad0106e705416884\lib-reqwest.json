{"rustc": 1842507548689473721, "features": "[\"__tls\", \"async-compression\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"gzip\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\", \"tokio-util\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 15657897354478470176, "path": 5920118276528983727, "deps": [[40386456601120721, "percent_encoding", false, 14003801311530373528], [95042085696191081, "ipnet", false, 3424573487449277788], [264090853244900308, "sync_wrapper", false, 16260191218869311692], [784494742817713399, "tower_service", false, 12359912582921736970], [1288403060204016458, "tokio_util", false, 11334306296847631233], [1906322745568073236, "pin_project_lite", false, 18131270670966278584], [2779053297469913730, "cookie_crate", false, 14361861861946070145], [3150220818285335163, "url", false, 1746054398145793718], [3722963349756955755, "once_cell", false, 3092077161891181902], [4405182208873388884, "http", false, 5891631273443115995], [5986029879202738730, "log", false, 13216658706447473378], [7414427314941361239, "hyper", false, 17237323595206262043], [7620660491849607393, "futures_core", false, 6897113057850168815], [8405603588346937335, "winreg", false, 4191094667233293947], [8569119365930580996, "serde_json", false, 3777298738341353814], [8915503303801890683, "http_body", false, 5424949408989136933], [9689903380558560274, "serde", false, 4389908846715814937], [10229185211513642314, "mime", false, 7894573325271278637], [10629569228670356391, "futures_util", false, 15644822958053016495], [12186126227181294540, "tokio_native_tls", false, 17023224673546874108], [12367227501898450486, "hyper_tls", false, 5688139258115020678], [12944427623413450645, "tokio", false, 13941386680418085679], [13763625454224483636, "h2", false, 13171098002944490536], [14564311161534545801, "encoding_rs", false, 17236660259466684603], [14721851354164625169, "async_compression", false, 10831249237352326529], [16066129441945555748, "bytes", false, 256859762049119040], [16311359161338405624, "rustls_pemfile", false, 17295842003052255707], [16542808166767769916, "serde_urlencoded", false, 10149130919485450018], [16785601910559813697, "native_tls_crate", false, 5149872407191215455], [17973378407174338648, "cookie_store", false, 8957808222715226604], [18066890886671768183, "base64", false, 3541723674890208800]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-ad0106e705416884\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}