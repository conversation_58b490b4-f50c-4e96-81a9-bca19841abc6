<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\scheduler.rs`."><title>scheduler.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>scheduler.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{AppError, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span><span class="kw">crate</span>::crawler::{CrawlTask, TaskType};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::collections::{BinaryHeap, HashMap};
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>std::cmp::Ordering;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>std::sync::Arc;
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span>tokio::sync::{RwLock, Semaphore};
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">use </span>tokio::time::{Duration, Instant};
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">use </span>tracing::{info, error};
<a href=#10 id=10 data-nosnippet>10</a><span class="kw">use </span>uuid::Uuid;
<a href=#11 id=11 data-nosnippet>11</a>
<a href=#12 id=12 data-nosnippet>12</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#13 id=13 data-nosnippet>13</a></span><span class="kw">pub struct </span>PriorityTask {
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>task: CrawlTask,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>priority: u8,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>scheduled_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#17 id=17 data-nosnippet>17</a>}
<a href=#18 id=18 data-nosnippet>18</a>
<a href=#19 id=19 data-nosnippet>19</a><span class="kw">impl </span>PartialEq <span class="kw">for </span>PriorityTask {
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">fn </span>eq(<span class="kw-2">&amp;</span><span class="self">self</span>, other: <span class="kw-2">&amp;</span><span class="self">Self</span>) -&gt; bool {
<a href=#21 id=21 data-nosnippet>21</a>        <span class="self">self</span>.priority == other.priority
<a href=#22 id=22 data-nosnippet>22</a>    }
<a href=#23 id=23 data-nosnippet>23</a>}
<a href=#24 id=24 data-nosnippet>24</a>
<a href=#25 id=25 data-nosnippet>25</a><span class="kw">impl </span>Eq <span class="kw">for </span>PriorityTask {}
<a href=#26 id=26 data-nosnippet>26</a>
<a href=#27 id=27 data-nosnippet>27</a><span class="kw">impl </span>PartialOrd <span class="kw">for </span>PriorityTask {
<a href=#28 id=28 data-nosnippet>28</a>    <span class="kw">fn </span>partial_cmp(<span class="kw-2">&amp;</span><span class="self">self</span>, other: <span class="kw-2">&amp;</span><span class="self">Self</span>) -&gt; <span class="prelude-ty">Option</span>&lt;Ordering&gt; {
<a href=#29 id=29 data-nosnippet>29</a>        <span class="prelude-val">Some</span>(<span class="self">self</span>.cmp(other))
<a href=#30 id=30 data-nosnippet>30</a>    }
<a href=#31 id=31 data-nosnippet>31</a>}
<a href=#32 id=32 data-nosnippet>32</a>
<a href=#33 id=33 data-nosnippet>33</a><span class="kw">impl </span>Ord <span class="kw">for </span>PriorityTask {
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">fn </span>cmp(<span class="kw-2">&amp;</span><span class="self">self</span>, other: <span class="kw-2">&amp;</span><span class="self">Self</span>) -&gt; Ordering {
<a href=#35 id=35 data-nosnippet>35</a>        <span class="comment">// 优先级高的任务排在前面（数字越大优先级越高）
<a href=#36 id=36 data-nosnippet>36</a>        </span>other.priority.cmp(<span class="kw-2">&amp;</span><span class="self">self</span>.priority)
<a href=#37 id=37 data-nosnippet>37</a>            .then_with(|| <span class="self">self</span>.scheduled_at.cmp(<span class="kw-2">&amp;</span>other.scheduled_at))
<a href=#38 id=38 data-nosnippet>38</a>    }
<a href=#39 id=39 data-nosnippet>39</a>}
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#42 id=42 data-nosnippet>42</a></span><span class="kw">pub struct </span>TaskStatistics {
<a href=#43 id=43 data-nosnippet>43</a>    <span class="kw">pub </span>total_received: u64,
<a href=#44 id=44 data-nosnippet>44</a>    <span class="kw">pub </span>total_processed: u64,
<a href=#45 id=45 data-nosnippet>45</a>    <span class="kw">pub </span>total_success: u64,
<a href=#46 id=46 data-nosnippet>46</a>    <span class="kw">pub </span>total_failed: u64,
<a href=#47 id=47 data-nosnippet>47</a>    <span class="kw">pub </span>average_processing_time: f64,
<a href=#48 id=48 data-nosnippet>48</a>    <span class="kw">pub </span>tasks_per_minute: f64,
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">pub </span>queue_length: usize,
<a href=#50 id=50 data-nosnippet>50</a>    <span class="kw">pub </span>active_tasks: usize,
<a href=#51 id=51 data-nosnippet>51</a>}
<a href=#52 id=52 data-nosnippet>52</a>
<a href=#53 id=53 data-nosnippet>53</a><span class="kw">pub struct </span>TaskScheduler {
<a href=#54 id=54 data-nosnippet>54</a>    task_queue: Arc&lt;RwLock&lt;BinaryHeap&lt;PriorityTask&gt;&gt;&gt;,
<a href=#55 id=55 data-nosnippet>55</a>    active_tasks: Arc&lt;RwLock&lt;HashMap&lt;String, PriorityTask&gt;&gt;&gt;,
<a href=#56 id=56 data-nosnippet>56</a>    completed_tasks: Arc&lt;RwLock&lt;Vec&lt;String&gt;&gt;&gt;,
<a href=#57 id=57 data-nosnippet>57</a>    failed_tasks: Arc&lt;RwLock&lt;Vec&lt;String&gt;&gt;&gt;,
<a href=#58 id=58 data-nosnippet>58</a>    semaphore: Arc&lt;Semaphore&gt;,
<a href=#59 id=59 data-nosnippet>59</a>    <span class="attr">#[allow(dead_code)]
<a href=#60 id=60 data-nosnippet>60</a>    </span>max_concurrent: usize,
<a href=#61 id=61 data-nosnippet>61</a>    is_processing: Arc&lt;RwLock&lt;bool&gt;&gt;,
<a href=#62 id=62 data-nosnippet>62</a>    statistics: Arc&lt;RwLock&lt;TaskStatistics&gt;&gt;,
<a href=#63 id=63 data-nosnippet>63</a>}
<a href=#64 id=64 data-nosnippet>64</a>
<a href=#65 id=65 data-nosnippet>65</a><span class="kw">impl </span>TaskScheduler {
<a href=#66 id=66 data-nosnippet>66</a>    <span class="kw">pub async fn </span>new(max_concurrent: usize) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#67 id=67 data-nosnippet>67</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{
<a href=#68 id=68 data-nosnippet>68</a>            task_queue: Arc::new(RwLock::new(BinaryHeap::new())),
<a href=#69 id=69 data-nosnippet>69</a>            active_tasks: Arc::new(RwLock::new(HashMap::new())),
<a href=#70 id=70 data-nosnippet>70</a>            completed_tasks: Arc::new(RwLock::new(Vec::new())),
<a href=#71 id=71 data-nosnippet>71</a>            failed_tasks: Arc::new(RwLock::new(Vec::new())),
<a href=#72 id=72 data-nosnippet>72</a>            semaphore: Arc::new(Semaphore::new(max_concurrent)),
<a href=#73 id=73 data-nosnippet>73</a>            max_concurrent,
<a href=#74 id=74 data-nosnippet>74</a>            is_processing: Arc::new(RwLock::new(<span class="bool-val">false</span>)),
<a href=#75 id=75 data-nosnippet>75</a>            statistics: Arc::new(RwLock::new(TaskStatistics {
<a href=#76 id=76 data-nosnippet>76</a>                total_received: <span class="number">0</span>,
<a href=#77 id=77 data-nosnippet>77</a>                total_processed: <span class="number">0</span>,
<a href=#78 id=78 data-nosnippet>78</a>                total_success: <span class="number">0</span>,
<a href=#79 id=79 data-nosnippet>79</a>                total_failed: <span class="number">0</span>,
<a href=#80 id=80 data-nosnippet>80</a>                average_processing_time: <span class="number">0.0</span>,
<a href=#81 id=81 data-nosnippet>81</a>                tasks_per_minute: <span class="number">0.0</span>,
<a href=#82 id=82 data-nosnippet>82</a>                queue_length: <span class="number">0</span>,
<a href=#83 id=83 data-nosnippet>83</a>                active_tasks: <span class="number">0</span>,
<a href=#84 id=84 data-nosnippet>84</a>            })),
<a href=#85 id=85 data-nosnippet>85</a>        })
<a href=#86 id=86 data-nosnippet>86</a>    }
<a href=#87 id=87 data-nosnippet>87</a>
<a href=#88 id=88 data-nosnippet>88</a>    <span class="kw">pub async fn </span>add_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_type: TaskType, target_url: String, priority: u8) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#89 id=89 data-nosnippet>89</a>        <span class="kw">let </span>task_id = Uuid::new_v4().to_string();
<a href=#90 id=90 data-nosnippet>90</a>        
<a href=#91 id=91 data-nosnippet>91</a>        <span class="kw">let </span>task = CrawlTask {
<a href=#92 id=92 data-nosnippet>92</a>            id: task_id.clone(),
<a href=#93 id=93 data-nosnippet>93</a>            task_type,
<a href=#94 id=94 data-nosnippet>94</a>            target_url,
<a href=#95 id=95 data-nosnippet>95</a>            priority,
<a href=#96 id=96 data-nosnippet>96</a>            retry_count: <span class="number">0</span>,
<a href=#97 id=97 data-nosnippet>97</a>            max_retries: <span class="number">3</span>,
<a href=#98 id=98 data-nosnippet>98</a>            metadata: HashMap::new(),
<a href=#99 id=99 data-nosnippet>99</a>            created_at: chrono::Utc::now(),
<a href=#100 id=100 data-nosnippet>100</a>        };
<a href=#101 id=101 data-nosnippet>101</a>
<a href=#102 id=102 data-nosnippet>102</a>        <span class="kw">let </span>priority_task = PriorityTask {
<a href=#103 id=103 data-nosnippet>103</a>            task,
<a href=#104 id=104 data-nosnippet>104</a>            priority,
<a href=#105 id=105 data-nosnippet>105</a>            scheduled_at: chrono::Utc::now(),
<a href=#106 id=106 data-nosnippet>106</a>        };
<a href=#107 id=107 data-nosnippet>107</a>
<a href=#108 id=108 data-nosnippet>108</a>        <span class="comment">// 添加到队列
<a href=#109 id=109 data-nosnippet>109</a>        </span>{
<a href=#110 id=110 data-nosnippet>110</a>            <span class="kw">let </span><span class="kw-2">mut </span>queue = <span class="self">self</span>.task_queue.write().<span class="kw">await</span>;
<a href=#111 id=111 data-nosnippet>111</a>            queue.push(priority_task);
<a href=#112 id=112 data-nosnippet>112</a>        }
<a href=#113 id=113 data-nosnippet>113</a>
<a href=#114 id=114 data-nosnippet>114</a>        <span class="comment">// 更新统计信息
<a href=#115 id=115 data-nosnippet>115</a>        </span>{
<a href=#116 id=116 data-nosnippet>116</a>            <span class="kw">let </span><span class="kw-2">mut </span>stats = <span class="self">self</span>.statistics.write().<span class="kw">await</span>;
<a href=#117 id=117 data-nosnippet>117</a>            stats.total_received += <span class="number">1</span>;
<a href=#118 id=118 data-nosnippet>118</a>            stats.queue_length = {
<a href=#119 id=119 data-nosnippet>119</a>                <span class="kw">let </span>queue = <span class="self">self</span>.task_queue.read().<span class="kw">await</span>;
<a href=#120 id=120 data-nosnippet>120</a>                queue.len()
<a href=#121 id=121 data-nosnippet>121</a>            };
<a href=#122 id=122 data-nosnippet>122</a>        }
<a href=#123 id=123 data-nosnippet>123</a>
<a href=#124 id=124 data-nosnippet>124</a>        <span class="macro">info!</span>(<span class="string">"任务 {} 已添加到队列，优先级: {}"</span>, task_id, priority);
<a href=#125 id=125 data-nosnippet>125</a>        <span class="prelude-val">Ok</span>(task_id)
<a href=#126 id=126 data-nosnippet>126</a>    }
<a href=#127 id=127 data-nosnippet>127</a>
<a href=#128 id=128 data-nosnippet>128</a>    <span class="kw">pub async fn </span>start_processing(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#129 id=129 data-nosnippet>129</a>        <span class="kw">let </span><span class="kw-2">mut </span>is_processing = <span class="self">self</span>.is_processing.write().<span class="kw">await</span>;
<a href=#130 id=130 data-nosnippet>130</a>        <span class="kw">if </span><span class="kw-2">*</span>is_processing {
<a href=#131 id=131 data-nosnippet>131</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Scheduler(<span class="string">"任务处理已在运行中"</span>.to_string()));
<a href=#132 id=132 data-nosnippet>132</a>        }
<a href=#133 id=133 data-nosnippet>133</a>        <span class="kw-2">*</span>is_processing = <span class="bool-val">true</span>;
<a href=#134 id=134 data-nosnippet>134</a>
<a href=#135 id=135 data-nosnippet>135</a>        <span class="macro">info!</span>(<span class="string">"启动任务处理循环"</span>);
<a href=#136 id=136 data-nosnippet>136</a>        <span class="self">self</span>.spawn_processing_loop().<span class="kw">await</span>;
<a href=#137 id=137 data-nosnippet>137</a>        <span class="prelude-val">Ok</span>(())
<a href=#138 id=138 data-nosnippet>138</a>    }
<a href=#139 id=139 data-nosnippet>139</a>
<a href=#140 id=140 data-nosnippet>140</a>    <span class="kw">pub async fn </span>stop_processing(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#141 id=141 data-nosnippet>141</a>        <span class="kw">let </span><span class="kw-2">mut </span>is_processing = <span class="self">self</span>.is_processing.write().<span class="kw">await</span>;
<a href=#142 id=142 data-nosnippet>142</a>        <span class="kw">if </span>!<span class="kw-2">*</span>is_processing {
<a href=#143 id=143 data-nosnippet>143</a>            <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Scheduler(<span class="string">"任务处理未在运行"</span>.to_string()));
<a href=#144 id=144 data-nosnippet>144</a>        }
<a href=#145 id=145 data-nosnippet>145</a>        <span class="kw-2">*</span>is_processing = <span class="bool-val">false</span>;
<a href=#146 id=146 data-nosnippet>146</a>
<a href=#147 id=147 data-nosnippet>147</a>        <span class="macro">info!</span>(<span class="string">"停止任务处理循环"</span>);
<a href=#148 id=148 data-nosnippet>148</a>        <span class="prelude-val">Ok</span>(())
<a href=#149 id=149 data-nosnippet>149</a>    }
<a href=#150 id=150 data-nosnippet>150</a>
<a href=#151 id=151 data-nosnippet>151</a>    <span class="kw">async fn </span>spawn_processing_loop(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#152 id=152 data-nosnippet>152</a>        <span class="kw">let </span>task_queue = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.task_queue);
<a href=#153 id=153 data-nosnippet>153</a>        <span class="kw">let </span>active_tasks = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.active_tasks);
<a href=#154 id=154 data-nosnippet>154</a>        <span class="kw">let </span>completed_tasks = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.completed_tasks);
<a href=#155 id=155 data-nosnippet>155</a>        <span class="kw">let </span>failed_tasks = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.failed_tasks);
<a href=#156 id=156 data-nosnippet>156</a>        <span class="kw">let </span>semaphore = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.semaphore);
<a href=#157 id=157 data-nosnippet>157</a>        <span class="kw">let </span>is_processing = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.is_processing);
<a href=#158 id=158 data-nosnippet>158</a>        <span class="kw">let </span>statistics = Arc::clone(<span class="kw-2">&amp;</span><span class="self">self</span>.statistics);
<a href=#159 id=159 data-nosnippet>159</a>
<a href=#160 id=160 data-nosnippet>160</a>        tokio::spawn(<span class="kw">async move </span>{
<a href=#161 id=161 data-nosnippet>161</a>            <span class="kw">while </span><span class="kw-2">*</span>is_processing.read().<span class="kw">await </span>{
<a href=#162 id=162 data-nosnippet>162</a>                <span class="comment">// 从队列中获取任务
<a href=#163 id=163 data-nosnippet>163</a>                </span><span class="kw">let </span>task = {
<a href=#164 id=164 data-nosnippet>164</a>                    <span class="kw">let </span><span class="kw-2">mut </span>queue = task_queue.write().<span class="kw">await</span>;
<a href=#165 id=165 data-nosnippet>165</a>                    queue.pop()
<a href=#166 id=166 data-nosnippet>166</a>                };
<a href=#167 id=167 data-nosnippet>167</a>
<a href=#168 id=168 data-nosnippet>168</a>                <span class="kw">if let </span><span class="prelude-val">Some</span>(priority_task) = task {
<a href=#169 id=169 data-nosnippet>169</a>                    <span class="comment">// 获取信号量许可
<a href=#170 id=170 data-nosnippet>170</a>                    </span><span class="kw">let </span>permit = Arc::clone(<span class="kw-2">&amp;</span>semaphore).acquire_owned().<span class="kw">await</span>.unwrap();
<a href=#171 id=171 data-nosnippet>171</a>
<a href=#172 id=172 data-nosnippet>172</a>                    <span class="comment">// 添加到活跃任务列表
<a href=#173 id=173 data-nosnippet>173</a>                    </span>{
<a href=#174 id=174 data-nosnippet>174</a>                        <span class="kw">let </span><span class="kw-2">mut </span>active = active_tasks.write().<span class="kw">await</span>;
<a href=#175 id=175 data-nosnippet>175</a>                        active.insert(priority_task.task.id.clone(), priority_task.clone());
<a href=#176 id=176 data-nosnippet>176</a>                    }
<a href=#177 id=177 data-nosnippet>177</a>
<a href=#178 id=178 data-nosnippet>178</a>                    <span class="comment">// 更新统计信息
<a href=#179 id=179 data-nosnippet>179</a>                    </span>{
<a href=#180 id=180 data-nosnippet>180</a>                        <span class="kw">let </span><span class="kw-2">mut </span>stats = statistics.write().<span class="kw">await</span>;
<a href=#181 id=181 data-nosnippet>181</a>                        stats.active_tasks = active_tasks.read().<span class="kw">await</span>.len();
<a href=#182 id=182 data-nosnippet>182</a>                        stats.queue_length = task_queue.read().<span class="kw">await</span>.len();
<a href=#183 id=183 data-nosnippet>183</a>                    }
<a href=#184 id=184 data-nosnippet>184</a>
<a href=#185 id=185 data-nosnippet>185</a>                    <span class="comment">// 异步处理任务
<a href=#186 id=186 data-nosnippet>186</a>                    </span><span class="kw">let </span>task_id = priority_task.task.id.clone();
<a href=#187 id=187 data-nosnippet>187</a>                    <span class="kw">let </span>active_tasks_clone = Arc::clone(<span class="kw-2">&amp;</span>active_tasks);
<a href=#188 id=188 data-nosnippet>188</a>                    <span class="kw">let </span>completed_tasks_clone = Arc::clone(<span class="kw-2">&amp;</span>completed_tasks);
<a href=#189 id=189 data-nosnippet>189</a>                    <span class="kw">let </span>failed_tasks_clone = Arc::clone(<span class="kw-2">&amp;</span>failed_tasks);
<a href=#190 id=190 data-nosnippet>190</a>                    <span class="kw">let </span>statistics_clone = Arc::clone(<span class="kw-2">&amp;</span>statistics);
<a href=#191 id=191 data-nosnippet>191</a>
<a href=#192 id=192 data-nosnippet>192</a>                    tokio::spawn(<span class="kw">async move </span>{
<a href=#193 id=193 data-nosnippet>193</a>                        <span class="kw">let </span>start_time = Instant::now();
<a href=#194 id=194 data-nosnippet>194</a>
<a href=#195 id=195 data-nosnippet>195</a>                        <span class="comment">// 模拟任务处理
<a href=#196 id=196 data-nosnippet>196</a>                        </span><span class="kw">let </span>success = <span class="self">Self</span>::process_task(<span class="kw-2">&amp;</span>priority_task.task).<span class="kw">await</span>;
<a href=#197 id=197 data-nosnippet>197</a>
<a href=#198 id=198 data-nosnippet>198</a>                        <span class="kw">let </span>processing_time = start_time.elapsed().as_millis() <span class="kw">as </span>f64;
<a href=#199 id=199 data-nosnippet>199</a>
<a href=#200 id=200 data-nosnippet>200</a>                        <span class="comment">// 从活跃任务列表移除
<a href=#201 id=201 data-nosnippet>201</a>                        </span>{
<a href=#202 id=202 data-nosnippet>202</a>                            <span class="kw">let </span><span class="kw-2">mut </span>active = active_tasks_clone.write().<span class="kw">await</span>;
<a href=#203 id=203 data-nosnippet>203</a>                            active.remove(<span class="kw-2">&amp;</span>task_id);
<a href=#204 id=204 data-nosnippet>204</a>                        }
<a href=#205 id=205 data-nosnippet>205</a>
<a href=#206 id=206 data-nosnippet>206</a>                        <span class="comment">// 更新完成或失败列表
<a href=#207 id=207 data-nosnippet>207</a>                        </span><span class="kw">if </span>success {
<a href=#208 id=208 data-nosnippet>208</a>                            <span class="kw">let </span><span class="kw-2">mut </span>completed = completed_tasks_clone.write().<span class="kw">await</span>;
<a href=#209 id=209 data-nosnippet>209</a>                            completed.push(task_id.clone());
<a href=#210 id=210 data-nosnippet>210</a>                            <span class="macro">info!</span>(<span class="string">"任务 {} 处理成功，耗时: {:.2}ms"</span>, task_id, processing_time);
<a href=#211 id=211 data-nosnippet>211</a>                        } <span class="kw">else </span>{
<a href=#212 id=212 data-nosnippet>212</a>                            <span class="kw">let </span><span class="kw-2">mut </span>failed = failed_tasks_clone.write().<span class="kw">await</span>;
<a href=#213 id=213 data-nosnippet>213</a>                            failed.push(task_id.clone());
<a href=#214 id=214 data-nosnippet>214</a>                            <span class="macro">error!</span>(<span class="string">"任务 {} 处理失败，耗时: {:.2}ms"</span>, task_id, processing_time);
<a href=#215 id=215 data-nosnippet>215</a>                        }
<a href=#216 id=216 data-nosnippet>216</a>
<a href=#217 id=217 data-nosnippet>217</a>                        <span class="comment">// 更新统计信息
<a href=#218 id=218 data-nosnippet>218</a>                        </span>{
<a href=#219 id=219 data-nosnippet>219</a>                            <span class="kw">let </span><span class="kw-2">mut </span>stats = statistics_clone.write().<span class="kw">await</span>;
<a href=#220 id=220 data-nosnippet>220</a>                            stats.total_processed += <span class="number">1</span>;
<a href=#221 id=221 data-nosnippet>221</a>                            <span class="kw">if </span>success {
<a href=#222 id=222 data-nosnippet>222</a>                                stats.total_success += <span class="number">1</span>;
<a href=#223 id=223 data-nosnippet>223</a>                            } <span class="kw">else </span>{
<a href=#224 id=224 data-nosnippet>224</a>                                stats.total_failed += <span class="number">1</span>;
<a href=#225 id=225 data-nosnippet>225</a>                            }
<a href=#226 id=226 data-nosnippet>226</a>
<a href=#227 id=227 data-nosnippet>227</a>                            <span class="comment">// 更新平均处理时间
<a href=#228 id=228 data-nosnippet>228</a>                            </span>stats.average_processing_time =
<a href=#229 id=229 data-nosnippet>229</a>                                (stats.average_processing_time * (stats.total_processed - <span class="number">1</span>) <span class="kw">as </span>f64 + processing_time)
<a href=#230 id=230 data-nosnippet>230</a>                                / stats.total_processed <span class="kw">as </span>f64;
<a href=#231 id=231 data-nosnippet>231</a>
<a href=#232 id=232 data-nosnippet>232</a>                            stats.active_tasks = active_tasks_clone.read().<span class="kw">await</span>.len();
<a href=#233 id=233 data-nosnippet>233</a>                        }
<a href=#234 id=234 data-nosnippet>234</a>
<a href=#235 id=235 data-nosnippet>235</a>                        <span class="comment">// 释放信号量许可
<a href=#236 id=236 data-nosnippet>236</a>                        </span>drop(permit);
<a href=#237 id=237 data-nosnippet>237</a>                    });
<a href=#238 id=238 data-nosnippet>238</a>                } <span class="kw">else </span>{
<a href=#239 id=239 data-nosnippet>239</a>                    <span class="comment">// 队列为空，等待一段时间
<a href=#240 id=240 data-nosnippet>240</a>                    </span>tokio::time::sleep(Duration::from_millis(<span class="number">100</span>)).<span class="kw">await</span>;
<a href=#241 id=241 data-nosnippet>241</a>                }
<a href=#242 id=242 data-nosnippet>242</a>            }
<a href=#243 id=243 data-nosnippet>243</a>        });
<a href=#244 id=244 data-nosnippet>244</a>    }
<a href=#245 id=245 data-nosnippet>245</a>
<a href=#246 id=246 data-nosnippet>246</a>    <span class="kw">async fn </span>process_task(task: <span class="kw-2">&amp;</span>CrawlTask) -&gt; bool {
<a href=#247 id=247 data-nosnippet>247</a>        <span class="comment">// 模拟任务处理时间
<a href=#248 id=248 data-nosnippet>248</a>        </span><span class="kw">let </span>processing_time = <span class="kw">match </span>task.task_type {
<a href=#249 id=249 data-nosnippet>249</a>            TaskType::User =&gt; Duration::from_millis(<span class="number">1000</span>),
<a href=#250 id=250 data-nosnippet>250</a>            TaskType::Post =&gt; Duration::from_millis(<span class="number">800</span>),
<a href=#251 id=251 data-nosnippet>251</a>            TaskType::Comment =&gt; Duration::from_millis(<span class="number">600</span>),
<a href=#252 id=252 data-nosnippet>252</a>            TaskType::Topic =&gt; Duration::from_millis(<span class="number">1200</span>),
<a href=#253 id=253 data-nosnippet>253</a>        };
<a href=#254 id=254 data-nosnippet>254</a>
<a href=#255 id=255 data-nosnippet>255</a>        tokio::time::sleep(processing_time).<span class="kw">await</span>;
<a href=#256 id=256 data-nosnippet>256</a>
<a href=#257 id=257 data-nosnippet>257</a>        <span class="comment">// 模拟90%的成功率
<a href=#258 id=258 data-nosnippet>258</a>        </span>rand::random::&lt;f64&gt;() &gt; <span class="number">0.1
<a href=#259 id=259 data-nosnippet>259</a>    </span>}
<a href=#260 id=260 data-nosnippet>260</a>
<a href=#261 id=261 data-nosnippet>261</a>    <span class="kw">pub async fn </span>get_queue_status(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw">crate</span>::commands::TaskQueueStatus {
<a href=#262 id=262 data-nosnippet>262</a>        <span class="kw">let </span>queue_length = {
<a href=#263 id=263 data-nosnippet>263</a>            <span class="kw">let </span>queue = <span class="self">self</span>.task_queue.read().<span class="kw">await</span>;
<a href=#264 id=264 data-nosnippet>264</a>            queue.len() <span class="kw">as </span>u32
<a href=#265 id=265 data-nosnippet>265</a>        };
<a href=#266 id=266 data-nosnippet>266</a>
<a href=#267 id=267 data-nosnippet>267</a>        <span class="kw">let </span>active_count = {
<a href=#268 id=268 data-nosnippet>268</a>            <span class="kw">let </span>active = <span class="self">self</span>.active_tasks.read().<span class="kw">await</span>;
<a href=#269 id=269 data-nosnippet>269</a>            active.len() <span class="kw">as </span>u32
<a href=#270 id=270 data-nosnippet>270</a>        };
<a href=#271 id=271 data-nosnippet>271</a>
<a href=#272 id=272 data-nosnippet>272</a>        <span class="kw">let </span>(completed_count, failed_count) = {
<a href=#273 id=273 data-nosnippet>273</a>            <span class="kw">let </span>completed = <span class="self">self</span>.completed_tasks.read().<span class="kw">await</span>;
<a href=#274 id=274 data-nosnippet>274</a>            <span class="kw">let </span>failed = <span class="self">self</span>.failed_tasks.read().<span class="kw">await</span>;
<a href=#275 id=275 data-nosnippet>275</a>            (completed.len() <span class="kw">as </span>u64, failed.len() <span class="kw">as </span>u64)
<a href=#276 id=276 data-nosnippet>276</a>        };
<a href=#277 id=277 data-nosnippet>277</a>
<a href=#278 id=278 data-nosnippet>278</a>        <span class="kw">let </span>stats = <span class="self">self</span>.statistics.read().<span class="kw">await</span>;
<a href=#279 id=279 data-nosnippet>279</a>
<a href=#280 id=280 data-nosnippet>280</a>        <span class="kw">crate</span>::commands::TaskQueueStatus {
<a href=#281 id=281 data-nosnippet>281</a>            pending_tasks: queue_length,
<a href=#282 id=282 data-nosnippet>282</a>            running_tasks: active_count,
<a href=#283 id=283 data-nosnippet>283</a>            completed_tasks: completed_count,
<a href=#284 id=284 data-nosnippet>284</a>            failed_tasks: failed_count,
<a href=#285 id=285 data-nosnippet>285</a>            queue_length,
<a href=#286 id=286 data-nosnippet>286</a>            processing_speed: stats.tasks_per_minute,
<a href=#287 id=287 data-nosnippet>287</a>        }
<a href=#288 id=288 data-nosnippet>288</a>    }
<a href=#289 id=289 data-nosnippet>289</a>
<a href=#290 id=290 data-nosnippet>290</a>    <span class="kw">pub async fn </span>get_statistics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw">crate</span>::commands::TaskStatistics {
<a href=#291 id=291 data-nosnippet>291</a>        <span class="kw">let </span>stats = <span class="self">self</span>.statistics.read().<span class="kw">await</span>;
<a href=#292 id=292 data-nosnippet>292</a>        
<a href=#293 id=293 data-nosnippet>293</a>        <span class="kw">crate</span>::commands::TaskStatistics {
<a href=#294 id=294 data-nosnippet>294</a>            total_processed: stats.total_processed,
<a href=#295 id=295 data-nosnippet>295</a>            success_count: stats.total_success,
<a href=#296 id=296 data-nosnippet>296</a>            failure_count: stats.total_failed,
<a href=#297 id=297 data-nosnippet>297</a>            average_duration: stats.average_processing_time / <span class="number">1000.0</span>, <span class="comment">// 转换为秒
<a href=#298 id=298 data-nosnippet>298</a>            </span>success_rate: <span class="kw">if </span>stats.total_processed &gt; <span class="number">0 </span>{
<a href=#299 id=299 data-nosnippet>299</a>                (stats.total_success <span class="kw">as </span>f64 / stats.total_processed <span class="kw">as </span>f64) * <span class="number">100.0
<a href=#300 id=300 data-nosnippet>300</a>            </span>} <span class="kw">else </span>{
<a href=#301 id=301 data-nosnippet>301</a>                <span class="number">0.0
<a href=#302 id=302 data-nosnippet>302</a>            </span>},
<a href=#303 id=303 data-nosnippet>303</a>            tasks_per_hour: stats.tasks_per_minute * <span class="number">60.0</span>,
<a href=#304 id=304 data-nosnippet>304</a>        }
<a href=#305 id=305 data-nosnippet>305</a>    }
<a href=#306 id=306 data-nosnippet>306</a>
<a href=#307 id=307 data-nosnippet>307</a>    <span class="kw">pub async fn </span>retry_failed_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#308 id=308 data-nosnippet>308</a>        <span class="comment">// 这里应该实现重试失败任务的逻辑
<a href=#309 id=309 data-nosnippet>309</a>        // 目前先返回成功
<a href=#310 id=310 data-nosnippet>310</a>        </span><span class="macro">info!</span>(<span class="string">"重试任务: {}"</span>, task_id);
<a href=#311 id=311 data-nosnippet>311</a>        <span class="prelude-val">Ok</span>(())
<a href=#312 id=312 data-nosnippet>312</a>    }
<a href=#313 id=313 data-nosnippet>313</a>
<a href=#314 id=314 data-nosnippet>314</a>    <span class="kw">pub async fn </span>cancel_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#315 id=315 data-nosnippet>315</a>        <span class="comment">// 从队列中移除任务
<a href=#316 id=316 data-nosnippet>316</a>        </span>{
<a href=#317 id=317 data-nosnippet>317</a>            <span class="kw">let </span><span class="kw-2">mut </span>queue = <span class="self">self</span>.task_queue.write().<span class="kw">await</span>;
<a href=#318 id=318 data-nosnippet>318</a>            <span class="kw">let </span><span class="kw-2">mut </span>temp_queue = BinaryHeap::new();
<a href=#319 id=319 data-nosnippet>319</a>            <span class="kw">let </span><span class="kw-2">mut </span>found = <span class="bool-val">false</span>;
<a href=#320 id=320 data-nosnippet>320</a>
<a href=#321 id=321 data-nosnippet>321</a>            <span class="kw">while let </span><span class="prelude-val">Some</span>(priority_task) = queue.pop() {
<a href=#322 id=322 data-nosnippet>322</a>                <span class="kw">if </span>priority_task.task.id == task_id {
<a href=#323 id=323 data-nosnippet>323</a>                    found = <span class="bool-val">true</span>;
<a href=#324 id=324 data-nosnippet>324</a>                    <span class="macro">info!</span>(<span class="string">"任务 {} 已从队列中取消"</span>, task_id);
<a href=#325 id=325 data-nosnippet>325</a>                } <span class="kw">else </span>{
<a href=#326 id=326 data-nosnippet>326</a>                    temp_queue.push(priority_task);
<a href=#327 id=327 data-nosnippet>327</a>                }
<a href=#328 id=328 data-nosnippet>328</a>            }
<a href=#329 id=329 data-nosnippet>329</a>
<a href=#330 id=330 data-nosnippet>330</a>            <span class="kw-2">*</span>queue = temp_queue;
<a href=#331 id=331 data-nosnippet>331</a>
<a href=#332 id=332 data-nosnippet>332</a>            <span class="kw">if </span>!found {
<a href=#333 id=333 data-nosnippet>333</a>                <span class="kw">return </span><span class="prelude-val">Err</span>(AppError::Scheduler(<span class="macro">format!</span>(<span class="string">"任务 {} 不在队列中"</span>, task_id)));
<a href=#334 id=334 data-nosnippet>334</a>            }
<a href=#335 id=335 data-nosnippet>335</a>        }
<a href=#336 id=336 data-nosnippet>336</a>
<a href=#337 id=337 data-nosnippet>337</a>        <span class="prelude-val">Ok</span>(())
<a href=#338 id=338 data-nosnippet>338</a>    }
<a href=#339 id=339 data-nosnippet>339</a>
<a href=#340 id=340 data-nosnippet>340</a>    <span class="kw">pub async fn </span>clear_completed_tasks(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;usize&gt; {
<a href=#341 id=341 data-nosnippet>341</a>        <span class="kw">let </span><span class="kw-2">mut </span>completed = <span class="self">self</span>.completed_tasks.write().<span class="kw">await</span>;
<a href=#342 id=342 data-nosnippet>342</a>        <span class="kw">let </span>count = completed.len();
<a href=#343 id=343 data-nosnippet>343</a>        completed.clear();
<a href=#344 id=344 data-nosnippet>344</a>        
<a href=#345 id=345 data-nosnippet>345</a>        <span class="macro">info!</span>(<span class="string">"清理了 {} 个已完成任务"</span>, count);
<a href=#346 id=346 data-nosnippet>346</a>        <span class="prelude-val">Ok</span>(count)
<a href=#347 id=347 data-nosnippet>347</a>    }
<a href=#348 id=348 data-nosnippet>348</a>
<a href=#349 id=349 data-nosnippet>349</a>    <span class="kw">pub async fn </span>clear_failed_tasks(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;usize&gt; {
<a href=#350 id=350 data-nosnippet>350</a>        <span class="kw">let </span><span class="kw-2">mut </span>failed = <span class="self">self</span>.failed_tasks.write().<span class="kw">await</span>;
<a href=#351 id=351 data-nosnippet>351</a>        <span class="kw">let </span>count = failed.len();
<a href=#352 id=352 data-nosnippet>352</a>        failed.clear();
<a href=#353 id=353 data-nosnippet>353</a>        
<a href=#354 id=354 data-nosnippet>354</a>        <span class="macro">info!</span>(<span class="string">"清理了 {} 个失败任务"</span>, count);
<a href=#355 id=355 data-nosnippet>355</a>        <span class="prelude-val">Ok</span>(count)
<a href=#356 id=356 data-nosnippet>356</a>    }
<a href=#357 id=357 data-nosnippet>357</a>}</code></pre></div></section></main></body></html>