import { FullConfig } from '@playwright/test'

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting E2E test teardown...')
  
  try {
    // 清理测试数据
    console.log('🗑️ Cleaning up test data...')
    
    // 这里可以添加清理逻辑，比如：
    // - 清理测试数据库
    // - 删除测试文件
    // - 重置应用状态
    
    console.log('✅ E2E test teardown completed')
    
  } catch (error) {
    console.error('❌ E2E test teardown failed:', error)
    // 不抛出错误，避免影响测试结果
  }
}

export default globalTeardown
