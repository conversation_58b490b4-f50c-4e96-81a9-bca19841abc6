{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2225463790103693989, "path": 14910021526676293305, "deps": [[2828590642173593838, "cfg_if", false, 799813300105951531], [5491919304041016563, "build_script_build", false, 11162421373214388844], [8995469080876806959, "untrusted", false, 5163980689655363509], [9920160576179037441, "getrandom", false, 18354885549074501724]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-4efe9f1db3dba100\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}