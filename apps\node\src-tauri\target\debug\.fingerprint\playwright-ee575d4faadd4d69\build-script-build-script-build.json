{"rustc": 1842507548689473721, "features": "[\"chrono\", \"default\", \"rt-tokio\"]", "declared_features": "[\"actix-rt\", \"async-std\", \"chrono\", \"default\", \"only-for-docs-rs\", \"rt-actix\", \"rt-async-std\", \"rt-tokio\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 10756528155388353369, "deps": [[7244058819997729774, "reqwest", false, 7529967590660994668]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\playwright-ee575d4faadd4d69\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}