{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10004434995811528692, "build_script_build", false, 13610699593198308953]], "local": [{"RerunIfChanged": {"output": "debug\\build\\rustix-82aa970c33a87741\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_EXPERIMENTAL_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_RUSTC_DEP_OF_STD", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_MIRI", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}