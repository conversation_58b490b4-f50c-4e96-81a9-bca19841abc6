use crate::error::{Result, AppError};
use playwright::{Playwright, api::browser_type::BrowserType};
use std::sync::Arc;
use tokio::sync::Mutex;
use tracing::{info, warn, error, debug};
use serde::{Deserialize, Serialize};
use std::time::Duration;

pub mod weibo_login;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct BrowserConfig {
    pub headless: bool,
    pub user_agent: Option<String>,
    pub viewport_width: u32,
    pub viewport_height: u32,
    pub timeout: u64, // 毫秒
    pub slow_mo: u64, // 毫秒，操作间隔
}

impl Default for BrowserConfig {
    fn default() -> Self {
        Self {
            headless: true,
            user_agent: Some("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36".to_string()),
            viewport_width: 1920,
            viewport_height: 1080,
            timeout: 30000,
            slow_mo: 100,
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoginCredentials {
    pub username: String,
    pub password: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginResult {
    pub success: bool,
    pub message: String,
    pub cookies: Option<String>,
    pub user_info: Option<UserInfo>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub uid: String,
    pub nickname: String,
    pub avatar_url: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QrCodeData {
    pub qr_code_url: String,
    pub qr_code_image: String, // base64编码的图片数据
    pub session_id: String,    // 用于轮询的会话ID
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QrLoginStatus {
    pub status: String, // "waiting", "scanned", "success", "expired", "error"
    pub message: String,
    pub login_result: Option<LoginResult>,
}

pub struct BrowserManager {
    playwright: Arc<Mutex<Option<Playwright>>>,
    config: BrowserConfig,
}

impl BrowserManager {
    pub fn new(config: BrowserConfig) -> Self {
        Self {
            playwright: Arc::new(Mutex::new(None)),
            config,
        }
    }

    pub async fn initialize(&self) -> Result<()> {
        let mut playwright_guard = self.playwright.lock().await;
        
        if playwright_guard.is_some() {
            return Ok(());
        }

        info!("初始化 Playwright...");
        
        match Playwright::initialize().await {
            Ok(playwright) => {
                *playwright_guard = Some(playwright);
                info!("Playwright 初始化成功");
                Ok(())
            }
            Err(e) => {
                error!("Playwright 初始化失败: {}", e);
                Err(AppError::Browser(format!("Failed to initialize Playwright: {}", e)))
            }
        }
    }

    pub async fn shutdown(&self) -> Result<()> {
        let mut playwright_guard = self.playwright.lock().await;
        
        if let Some(playwright) = playwright_guard.take() {
            info!("关闭 Playwright...");
            // Playwright 会在 drop 时自动清理
            drop(playwright);
            info!("Playwright 已关闭");
        }
        
        Ok(())
    }

    pub async fn login_weibo(&self, credentials: LoginCredentials) -> Result<LoginResult> {
        let playwright_guard = self.playwright.lock().await;
        
        let playwright = playwright_guard
            .as_ref()
            .ok_or_else(|| AppError::Browser("Playwright not initialized".to_string()))?;

        weibo_login::login_with_playwright(playwright, &self.config, credentials).await
    }

    pub async fn test_browser(&self) -> Result<String> {
        let playwright_guard = self.playwright.lock().await;
        
        let playwright = playwright_guard
            .as_ref()
            .ok_or_else(|| AppError::Browser("Playwright not initialized".to_string()))?;

        info!("测试浏览器功能...");

        let chromium = playwright.chromium();
        let browser = chromium
            .launcher()
            .headless(self.config.headless)
            .launch()
            .await
            .map_err(|e| AppError::Browser(format!("Failed to launch browser: {}", e)))?;

        let context = browser
            .context_builder()
            .user_agent(&self.config.user_agent.as_deref().unwrap_or(""))
            .build()
            .await
            .map_err(|e| AppError::Browser(format!("Failed to create context: {}", e)))?;

        let page = context
            .new_page()
            .await
            .map_err(|e| AppError::Browser(format!("Failed to create page: {}", e)))?;

        page.goto_builder("https://www.baidu.com")
            .timeout(self.config.timeout as f64)
            .goto()
            .await
            .map_err(|e| AppError::Browser(format!("Failed to navigate: {}", e)))?;

        let title = page
            .title()
            .await
            .map_err(|e| AppError::Browser(format!("Failed to get title: {}", e)))?;

        browser
            .close()
            .await
            .map_err(|e| AppError::Browser(format!("Failed to close browser: {}", e)))?;

        info!("浏览器测试完成，页面标题: {}", title);
        Ok(title)
    }

    pub fn get_config(&self) -> &BrowserConfig {
        &self.config
    }

    pub fn update_config(&mut self, config: BrowserConfig) {
        self.config = config;
    }

    // 获取微博登录二维码
    pub async fn get_weibo_qr_code(&self) -> Result<QrCodeData> {
        let playwright_guard = self.playwright.lock().await;

        let playwright = playwright_guard
            .as_ref()
            .ok_or_else(|| AppError::Browser("Playwright not initialized".to_string()))?;

        weibo_login::get_qr_code_with_playwright(playwright, &self.config).await
    }

    // 检查二维码登录状态
    pub async fn check_qr_login_status(&self, session_id: &str) -> Result<QrLoginStatus> {
        let playwright_guard = self.playwright.lock().await;

        let playwright = playwright_guard
            .as_ref()
            .ok_or_else(|| AppError::Browser("Playwright not initialized".to_string()))?;

        weibo_login::check_qr_login_status_with_playwright(playwright, &self.config, session_id).await
    }
}

// 全局浏览器管理器实例
static BROWSER_MANAGER: once_cell::sync::OnceCell<Arc<Mutex<BrowserManager>>> = once_cell::sync::OnceCell::new();

pub fn get_browser_manager() -> Arc<Mutex<BrowserManager>> {
    BROWSER_MANAGER
        .get_or_init(|| {
            Arc::new(Mutex::new(BrowserManager::new(BrowserConfig::default())))
        })
        .clone()
}

pub async fn initialize_browser() -> Result<()> {
    let manager = get_browser_manager();
    let manager_guard = manager.lock().await;
    manager_guard.initialize().await
}

pub async fn shutdown_browser() -> Result<()> {
    let manager = get_browser_manager();
    let manager_guard = manager.lock().await;
    manager_guard.shutdown().await
}
