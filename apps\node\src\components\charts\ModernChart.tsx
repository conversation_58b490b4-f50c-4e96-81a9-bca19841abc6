import React from 'react';
import {
  LineChart as RechartsLineChart,
  AreaChart as RechartsAreaChart,
  BarChart as RechartsBarChart,
  PieChart as RechartsPieChart,
  Line,
  Area,
  Bar,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  TooltipProps,
} from 'recharts';
import { cn } from '../../lib/utils';

// 现代化的颜色调色板
export const chartColors = {
  primary: 'hsl(var(--primary))',
  secondary: 'hsl(var(--secondary))',
  success: 'hsl(var(--success))',
  warning: 'hsl(var(--warning))',
  destructive: 'hsl(var(--destructive))',
  info: 'hsl(var(--info))',
  muted: 'hsl(var(--muted-foreground))',
  gradient: [
    'hsl(var(--primary))',
    'hsl(var(--info))',
    'hsl(var(--success))',
    'hsl(var(--warning))',
    'hsl(var(--destructive))',
  ],
};

// 基础图表属性
interface BaseChartProps {
  data: any[];
  height?: number;
  className?: string;
  showGrid?: boolean;
  showTooltip?: boolean;
  showLegend?: boolean;
  animate?: boolean;
}

// 自定义Tooltip组件
const CustomTooltip = ({ active, payload, label }: TooltipProps<any, any>) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-card border border-border rounded-lg shadow-lg p-3 animate-scale-in">
        <p className="text-sm font-medium text-foreground mb-2">{label}</p>
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center space-x-2 text-sm">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-muted-foreground">{entry.name}:</span>
            <span className="font-medium text-foreground">{entry.value}</span>
          </div>
        ))}
      </div>
    );
  }
  return null;
};

// 线性图表
export interface LineChartProps extends BaseChartProps {
  xKey?: string;
  yKey?: string;
  color?: string;
  strokeWidth?: number;
  curved?: boolean;
}

export function LineChart({
  data,
  height = 300,
  className,
  xKey = 'name',
  yKey = 'value',
  color = chartColors.primary,
  strokeWidth = 2,
  curved = true,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  animate = true,
}: LineChartProps) {
  return (
    <div className={cn('w-full animate-fade-in', className)}>
      <ResponsiveContainer width="100%" height={height}>
        <RechartsLineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="hsl(var(--border))"
              opacity={0.3}
            />
          )}
          <XAxis 
            dataKey={xKey}
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
          />
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          {showLegend && <Legend />}
          <Line
            type={curved ? 'monotone' : 'linear'}
            dataKey={yKey}
            stroke={color}
            strokeWidth={strokeWidth}
            dot={{ fill: color, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: color, strokeWidth: 2 }}
            animationDuration={animate ? 1000 : 0}
          />
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  );
}

// 面积图表
export interface AreaChartProps extends BaseChartProps {
  xKey?: string;
  yKey?: string;
  color?: string;
  fillOpacity?: number;
  curved?: boolean;
}

export function AreaChart({
  data,
  height = 300,
  className,
  xKey = 'name',
  yKey = 'value',
  color = chartColors.primary,
  fillOpacity = 0.3,
  curved = true,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  animate = true,
}: AreaChartProps) {
  return (
    <div className={cn('w-full animate-fade-in', className)}>
      <ResponsiveContainer width="100%" height={height}>
        <RechartsAreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <defs>
            <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={color} stopOpacity={fillOpacity} />
              <stop offset="95%" stopColor={color} stopOpacity={0} />
            </linearGradient>
          </defs>
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="hsl(var(--border))"
              opacity={0.3}
            />
          )}
          <XAxis 
            dataKey={xKey}
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
          />
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          {showLegend && <Legend />}
          <Area
            type={curved ? 'monotone' : 'linear'}
            dataKey={yKey}
            stroke={color}
            fillOpacity={1}
            fill="url(#colorGradient)"
            animationDuration={animate ? 1000 : 0}
          />
        </RechartsAreaChart>
      </ResponsiveContainer>
    </div>
  );
}

// 柱状图表
export interface BarChartProps extends BaseChartProps {
  xKey?: string;
  yKey?: string;
  color?: string;
  radius?: number;
}

export function BarChart({
  data,
  height = 300,
  className,
  xKey = 'name',
  yKey = 'value',
  color = chartColors.primary,
  radius = 4,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  animate = true,
}: BarChartProps) {
  return (
    <div className={cn('w-full animate-fade-in', className)}>
      <ResponsiveContainer width="100%" height={height}>
        <RechartsBarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke="hsl(var(--border))"
              opacity={0.3}
            />
          )}
          <XAxis 
            dataKey={xKey}
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
          />
          <YAxis 
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
          />
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          {showLegend && <Legend />}
          <Bar
            dataKey={yKey}
            fill={color}
            radius={[radius, radius, 0, 0]}
            animationDuration={animate ? 1000 : 0}
          />
        </RechartsBarChart>
      </ResponsiveContainer>
    </div>
  );
}

// 饼图
export interface PieChartProps extends BaseChartProps {
  dataKey?: string;
  nameKey?: string;
  colors?: string[];
  innerRadius?: number;
  outerRadius?: number;
}

export function PieChart({
  data,
  height = 300,
  className,
  dataKey = 'value',
  nameKey = 'name',
  colors = chartColors.gradient,
  innerRadius = 0,
  outerRadius = 80,
  showTooltip = true,
  showLegend = true,
  animate = true,
}: PieChartProps) {
  return (
    <div className={cn('w-full animate-fade-in', className)}>
      <ResponsiveContainer width="100%" height={height}>
        <RechartsPieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            innerRadius={innerRadius}
            outerRadius={outerRadius}
            paddingAngle={2}
            dataKey={dataKey}
            animationDuration={animate ? 1000 : 0}
          >
            {data.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={colors[index % colors.length]} 
              />
            ))}
          </Pie>
          {showTooltip && <Tooltip content={<CustomTooltip />} />}
          {showLegend && <Legend />}
        </RechartsPieChart>
      </ResponsiveContainer>
    </div>
  );
}
