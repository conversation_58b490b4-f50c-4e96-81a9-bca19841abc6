import { renderHook, act } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest'
import { 
  useDebounce, 
  useDebouncedCallback, 
  useThrottle, 
  useThrottledCallback,
  useSearchDebounce,
  useInputDebounce
} from '../useDebounce'

describe('useDebounce', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should debounce value changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    )

    expect(result.current).toBe('initial')

    // 更新值
    rerender({ value: 'updated', delay: 500 })
    expect(result.current).toBe('initial') // 还没有更新

    // 快进时间但不到延迟时间
    act(() => {
      vi.advanceTimersByTime(300)
    })
    expect(result.current).toBe('initial')

    // 快进到延迟时间
    act(() => {
      vi.advanceTimersByTime(200)
    })
    expect(result.current).toBe('updated')
  })

  it('should reset timer on rapid changes', () => {
    const { result, rerender } = renderHook(
      ({ value, delay }) => useDebounce(value, delay),
      { initialProps: { value: 'initial', delay: 500 } }
    )

    // 快速连续更新
    rerender({ value: 'update1', delay: 500 })
    act(() => {
      vi.advanceTimersByTime(300)
    })

    rerender({ value: 'update2', delay: 500 })
    act(() => {
      vi.advanceTimersByTime(300)
    })

    rerender({ value: 'final', delay: 500 })
    
    // 只有最后一个值应该被应用
    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(result.current).toBe('final')
  })
})

describe('useDebouncedCallback', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should debounce callback execution', () => {
    const callback = vi.fn()
    const { result } = renderHook(() => useDebouncedCallback(callback, 500))

    // 快速连续调用
    act(() => {
      result.current('arg1')
      result.current('arg2')
      result.current('arg3')
    })

    expect(callback).not.toHaveBeenCalled()

    // 快进时间
    act(() => {
      vi.advanceTimersByTime(500)
    })

    // 只应该调用一次，使用最后的参数
    expect(callback).toHaveBeenCalledTimes(1)
    expect(callback).toHaveBeenCalledWith('arg3')
  })

  it('should update callback reference', () => {
    const callback1 = vi.fn()
    const callback2 = vi.fn()
    
    const { result, rerender } = renderHook(
      ({ callback }) => useDebouncedCallback(callback, 500),
      { initialProps: { callback: callback1 } }
    )

    act(() => {
      result.current('test')
    })

    // 更新回调函数
    rerender({ callback: callback2 })

    act(() => {
      vi.advanceTimersByTime(500)
    })

    expect(callback1).not.toHaveBeenCalled()
    expect(callback2).toHaveBeenCalledWith('test')
  })
})

describe('useThrottle', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should throttle value changes', () => {
    const { result, rerender } = renderHook(
      ({ value, limit }) => useThrottle(value, limit),
      { initialProps: { value: 'initial', limit: 1000 } }
    )

    expect(result.current).toBe('initial')

    // 立即更新值
    rerender({ value: 'updated', limit: 1000 })
    
    // 应该立即更新（第一次）
    act(() => {
      vi.advanceTimersByTime(0)
    })
    expect(result.current).toBe('updated')

    // 再次更新
    rerender({ value: 'updated2', limit: 1000 })
    
    // 在限制时间内，不应该更新
    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(result.current).toBe('updated')

    // 超过限制时间后应该更新
    act(() => {
      vi.advanceTimersByTime(500)
    })
    expect(result.current).toBe('updated2')
  })
})

describe('useThrottledCallback', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should throttle callback execution', () => {
    const callback = vi.fn()
    const { result } = renderHook(() => useThrottledCallback(callback, 1000))

    // 第一次调用应该立即执行
    act(() => {
      result.current('arg1')
    })
    expect(callback).toHaveBeenCalledTimes(1)
    expect(callback).toHaveBeenCalledWith('arg1')

    // 在限制时间内的调用应该被忽略
    act(() => {
      result.current('arg2')
      vi.advanceTimersByTime(500)
      result.current('arg3')
    })
    expect(callback).toHaveBeenCalledTimes(1)

    // 超过限制时间后的调用应该执行
    act(() => {
      vi.advanceTimersByTime(500)
      result.current('arg4')
    })
    expect(callback).toHaveBeenCalledTimes(2)
    expect(callback).toHaveBeenLastCalledWith('arg4')
  })
})

describe('useSearchDebounce', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should debounce search term and track searching state', () => {
    const { result, rerender } = renderHook(
      ({ searchTerm }) => useSearchDebounce(searchTerm, 300),
      { initialProps: { searchTerm: '' } }
    )

    expect(result.current.debouncedSearchTerm).toBe('')
    expect(result.current.isSearching).toBe(false)

    // 更新搜索词
    rerender({ searchTerm: 'test' })
    expect(result.current.debouncedSearchTerm).toBe('')
    expect(result.current.isSearching).toBe(true)

    // 快进时间
    act(() => {
      vi.advanceTimersByTime(300)
    })
    expect(result.current.debouncedSearchTerm).toBe('test')
    expect(result.current.isSearching).toBe(false)
  })
})

describe('useInputDebounce', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  it('should manage input value with debouncing', () => {
    const { result } = renderHook(() => useInputDebounce('initial', 300))

    expect(result.current.value).toBe('initial')
    expect(result.current.debouncedValue).toBe('initial')
    expect(result.current.isDebouncing).toBe(false)

    // 更新值
    act(() => {
      result.current.setValue('updated')
    })

    expect(result.current.value).toBe('updated')
    expect(result.current.debouncedValue).toBe('initial')
    expect(result.current.isDebouncing).toBe(true)

    // 快进时间
    act(() => {
      vi.advanceTimersByTime(300)
    })

    expect(result.current.value).toBe('updated')
    expect(result.current.debouncedValue).toBe('updated')
    expect(result.current.isDebouncing).toBe(false)
  })
})
