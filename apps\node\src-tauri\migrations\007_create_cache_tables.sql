-- 用户信息缓存表
CREATE TABLE user_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT UNIQUE NOT NULL,
    username TEXT NOT NULL,
    display_name TEXT,
    avatar_url TEXT,
    followers_count INTEGER DEFAULT 0,
    following_count INTEGER DEFAULT 0,
    posts_count INTEGER DEFAULT 0,
    verified BOOLEA<PERSON> DEFAULT FALSE,
    profile_data TEXT, -- JSON格式的完整用户资料
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 内容数据缓存表
CREATE TABLE content_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content_id TEXT UNIQUE NOT NULL,
    content_type INTEGER NOT NULL, -- 1: Post, 2: Comment, 3: Topic
    author_id TEXT,
    content_text TEXT,
    media_urls TEXT, -- JSON格式的媒体URL列表
    interaction_stats TEXT, -- JSON格式的互动统计(点赞、转发、评论数)
    publish_time DATETIME,
    content_data TEXT, -- JSON格式的完整内容数据
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 话题信息缓存表
CREATE TABLE topic_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    topic_id TEXT UNIQUE NOT NULL,
    topic_name TEXT NOT NULL,
    topic_description TEXT,
    posts_count INTEGER DEFAULT 0,
    participants_count INTEGER DEFAULT 0,
    trending_score REAL DEFAULT 0.0,
    topic_data TEXT, -- JSON格式的完整话题数据
    last_updated DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置缓存表
CREATE TABLE system_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_key TEXT UNIQUE NOT NULL,
    cache_value TEXT NOT NULL,
    cache_type INTEGER DEFAULT 1, -- 1: String, 2: JSON, 3: Binary
    description TEXT,
    expires_at DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_user_cache_user_id ON user_cache(user_id);
CREATE INDEX idx_user_cache_username ON user_cache(username);
CREATE INDEX idx_user_cache_expires_at ON user_cache(expires_at);
CREATE INDEX idx_content_cache_content_id ON content_cache(content_id);
CREATE INDEX idx_content_cache_content_type ON content_cache(content_type);
CREATE INDEX idx_content_cache_author_id ON content_cache(author_id);
CREATE INDEX idx_content_cache_expires_at ON content_cache(expires_at);
CREATE INDEX idx_topic_cache_topic_id ON topic_cache(topic_id);
CREATE INDEX idx_topic_cache_topic_name ON topic_cache(topic_name);
CREATE INDEX idx_topic_cache_expires_at ON topic_cache(expires_at);
CREATE INDEX idx_system_cache_cache_key ON system_cache(cache_key);
CREATE INDEX idx_system_cache_expires_at ON system_cache(expires_at);

-- 创建更新时间触发器
CREATE TRIGGER update_system_cache_updated_at
    AFTER UPDATE ON system_cache
    FOR EACH ROW
BEGIN
    UPDATE system_cache SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
