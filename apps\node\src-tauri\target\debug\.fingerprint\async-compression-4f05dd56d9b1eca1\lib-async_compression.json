{"rustc": 1842507548689473721, "features": "[\"brotli\", \"flate2\", \"gzip\", \"tokio\"]", "declared_features": "[\"all\", \"all-algorithms\", \"all-implementations\", \"brotli\", \"bzip2\", \"deflate\", \"deflate64\", \"flate2\", \"futures-io\", \"gzip\", \"libzstd\", \"lz4\", \"lzma\", \"tokio\", \"xz\", \"xz-parallel\", \"xz2\", \"zlib\", \"zstd\", \"zstd-safe\", \"zstdmt\"]", "target": 7068030942456847288, "profile": 2241668132362809309, "path": 4209321403230616227, "deps": [[1906322745568073236, "pin_project_lite", false, 8108972857832786586], [7620660491849607393, "futures_core", false, 10280731575054067511], [9556762810601084293, "brotli", false, 10805614958872128979], [12944427623413450645, "tokio", false, 5789438060576990513], [15932120279885307830, "memchr", false, 12618675062812920877], [17772299992546037086, "flate2", false, 562275668731591289]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\async-compression-4f05dd56d9b1eca1\\dep-lib-async_compression", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}