{"rustc": 1842507548689473721, "features": "[\"alloc\", \"futures-io\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 8113656176662020586, "path": 14367665871832239459, "deps": [[5103565458935487, "futures_io", false, 7436328532129351512], [1615478164327904835, "pin_utils", false, 7212722111383314367], [1906322745568073236, "pin_project_lite", false, 1635349456338117859], [5451793922601807560, "slab", false, 6389987263278306025], [7013762810557009322, "futures_sink", false, 12127719929253965258], [7620660491849607393, "futures_core", false, 14442838981038780947], [15932120279885307830, "memchr", false, 15571097349483053606], [16240732885093539806, "futures_task", false, 11545115386400559583]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-461638e6f51def1a\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}