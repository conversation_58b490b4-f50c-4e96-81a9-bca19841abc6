{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 2241668132362809309, "path": 16482571177845734215, "deps": [[3007252114546291461, "tao", false, 9988332339480444165], [3150220818285335163, "url", false, 8909501939063229777], [3540822385484940109, "windows_implement", false, 3473423417027385092], [3722963349756955755, "once_cell", false, 6428016074931338643], [4381063397040571828, "webview2_com", false, 1159489376968944555], [4405182208873388884, "http", false, 5525313316207021045], [4684437522915235464, "libc", false, 10409992485030789293], [5986029879202738730, "log", false, 3004741351825838512], [7653476968652377684, "windows", false, 5238405700565607631], [8008191657135824715, "thiserror", false, 4083214613751541842], [8391357152270261188, "build_script_build", false, 15010752959823956639], [8569119365930580996, "serde_json", false, 2740896963817221287], [9689903380558560274, "serde", false, 4202820352788480372], [11989259058781683633, "dunce", false, 16187145314844288619]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-0b432733f78f6864\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}