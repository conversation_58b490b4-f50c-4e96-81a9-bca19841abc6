{"rustc": 1842507548689473721, "features": "[\"__tls\", \"async-compression\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"gzip\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"tokio-native-tls\", \"tokio-util\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 2241668132362809309, "path": 5920118276528983727, "deps": [[40386456601120721, "percent_encoding", false, 4385803709286603540], [95042085696191081, "ipnet", false, 2845239590221129263], [264090853244900308, "sync_wrapper", false, 12296385522371335900], [784494742817713399, "tower_service", false, 15602459978032234038], [1288403060204016458, "tokio_util", false, 5339149626986303070], [1906322745568073236, "pin_project_lite", false, 8108972857832786586], [2779053297469913730, "cookie_crate", false, 18076822850281599928], [3150220818285335163, "url", false, 8909501939063229777], [3722963349756955755, "once_cell", false, 6428016074931338643], [4405182208873388884, "http", false, 5525313316207021045], [5986029879202738730, "log", false, 3004741351825838512], [7414427314941361239, "hyper", false, 9967619935101040741], [7620660491849607393, "futures_core", false, 10280731575054067511], [8405603588346937335, "winreg", false, 14599898139545344329], [8569119365930580996, "serde_json", false, 2740896963817221287], [8915503303801890683, "http_body", false, 12325550372407112062], [9689903380558560274, "serde", false, 4202820352788480372], [10229185211513642314, "mime", false, 15655832992164802297], [10629569228670356391, "futures_util", false, 2925717846322628741], [12186126227181294540, "tokio_native_tls", false, 9540904931545616230], [12367227501898450486, "hyper_tls", false, 5142588795314367314], [12944427623413450645, "tokio", false, 5789438060576990513], [13763625454224483636, "h2", false, 285853507301493475], [14564311161534545801, "encoding_rs", false, 7578403266852120990], [14721851354164625169, "async_compression", false, 509590570863188470], [16066129441945555748, "bytes", false, 2398977389649674023], [16311359161338405624, "rustls_pemfile", false, 17424290552280994911], [16542808166767769916, "serde_urlencoded", false, 16383106972792045983], [16785601910559813697, "native_tls_crate", false, 13938217087447021836], [17973378407174338648, "cookie_store", false, 8219566858163101435], [18066890886671768183, "base64", false, 16103419943145268653]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-9b1400be54bea481\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}