import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi } from 'vitest'

// 创建测试用的 QueryClient
export const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })
}

// 测试包装器组件
interface TestWrapperProps {
  children: React.ReactNode
  queryClient?: QueryClient
  initialEntries?: string[]
}

export const TestWrapper: React.FC<TestWrapperProps> = ({
  children,
  queryClient = createTestQueryClient(),
  initialEntries = ['/'],
}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  )
}

// 自定义渲染函数
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
  initialEntries?: string[]
}

export const renderWithProviders = (
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { queryClient, initialEntries, ...renderOptions } = options

  const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <TestWrapper queryClient={queryClient} initialEntries={initialEntries}>
      {children}
    </TestWrapper>
  )

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock 数据生成器
export const mockTaskData = {
  task: (overrides = {}) => ({
    id: '1',
    task_id: 'task-1',
    task_type: 1,
    target_url: 'https://example.com',
    priority: 1,
    status: 0,
    retry_count: 0,
    max_retries: 3,
    metadata: null,
    assigned_at: null,
    started_at: null,
    completed_at: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  }),

  taskList: (count = 5) => 
    Array.from({ length: count }, (_, i) => 
      mockTaskData.task({ 
        id: String(i + 1), 
        task_id: `task-${i + 1}`,
        target_url: `https://example${i + 1}.com`
      })
    ),

  paginatedTasks: (page = 1, limit = 10, total = 50) => ({
    items: mockTaskData.taskList(Math.min(limit, total - (page - 1) * limit)),
    total,
    page,
    limit,
    total_pages: Math.ceil(total / limit),
  }),
}

export const mockProxyData = {
  proxy: (overrides = {}) => ({
    id: '1',
    proxy_id: 'proxy-1',
    host: '127.0.0.1',
    port: 8080,
    username: 'user',
    password: 'pass',
    proxy_type: 'http',
    status: 1,
    last_check: new Date().toISOString(),
    response_time: 100,
    success_rate: 95.5,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  }),

  proxyList: (count = 5) =>
    Array.from({ length: count }, (_, i) =>
      mockProxyData.proxy({
        id: String(i + 1),
        proxy_id: `proxy-${i + 1}`,
        port: 8080 + i,
      })
    ),
}

export const mockAccountData = {
  account: (overrides = {}) => ({
    id: '1',
    account_id: 'account-1',
    username: 'testuser',
    password: 'password',
    email: '<EMAIL>',
    phone: '***********',
    status: 1,
    risk_level: 0,
    last_login: new Date().toISOString(),
    cookies: null,
    user_agent: 'Mozilla/5.0...',
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  }),

  accountList: (count = 5) =>
    Array.from({ length: count }, (_, i) =>
      mockAccountData.account({
        id: String(i + 1),
        account_id: `account-${i + 1}`,
        username: `testuser${i + 1}`,
        email: `test${i + 1}@example.com`,
      })
    ),
}

// Mock Tauri 命令响应
export const mockTauriCommands = {
  // 任务相关
  get_tasks: vi.fn().mockResolvedValue(mockTaskData.taskList()),
  get_task_statistics: vi.fn().mockResolvedValue({
    total_tasks: 100,
    pending_tasks: 20,
    running_tasks: 5,
    completed_tasks: 70,
    failed_tasks: 5,
    success_rate: 93.3,
    average_processing_time: 2.5,
    tasks_per_hour: 24.0,
    last_updated: new Date().toISOString(),
  }),
  create_task: vi.fn().mockResolvedValue(mockTaskData.task()),
  update_task: vi.fn().mockResolvedValue(mockTaskData.task()),
  delete_task: vi.fn().mockResolvedValue(null),

  // 代理相关
  get_proxies: vi.fn().mockResolvedValue(mockProxyData.proxyList()),
  test_proxy: vi.fn().mockResolvedValue(true),
  add_proxy: vi.fn().mockResolvedValue(mockProxyData.proxy()),

  // 账号相关
  get_accounts: vi.fn().mockResolvedValue(mockAccountData.accountList()),
  login_account: vi.fn().mockResolvedValue({ success: true }),
  add_account: vi.fn().mockResolvedValue(mockAccountData.account()),

  // 系统相关
  get_system_metrics: vi.fn().mockResolvedValue({
    cpu_usage: 45.2,
    memory_usage: 68.5,
    disk_usage: 32.1,
    network_in: 1024,
    network_out: 2048,
    uptime: 86400,
  }),

  get_config: vi.fn().mockResolvedValue({
    crawler: {
      max_concurrent_tasks: 10,
      request_delay: 1000,
      retry_attempts: 3,
    },
    proxy: {
      enabled: true,
      rotation_interval: 300,
    },
    account: {
      max_accounts: 50,
      login_interval: 3600,
    },
  }),
}

// 设置 Tauri mock
export const setupTauriMocks = () => {
  const mockInvoke = vi.fn().mockImplementation((command: string, args?: any) => {
    const handler = mockTauriCommands[command as keyof typeof mockTauriCommands]
    if (handler) {
      return handler(args)
    }
    return Promise.reject(new Error(`Unknown command: ${command}`))
  })

  vi.mocked(window.__TAURI__.invoke).mockImplementation(mockInvoke)
  return mockInvoke
}

// 等待异步操作完成
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0))

// 模拟用户交互延迟
export const userDelay = (ms = 100) => new Promise(resolve => setTimeout(resolve, ms))

// 创建模拟事件
export const createMockEvent = (type: string, data: any = {}) => ({
  type,
  data,
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
})

// 模拟文件上传
export const createMockFile = (name = 'test.txt', content = 'test content', type = 'text/plain') => {
  const file = new File([content], name, { type })
  return file
}

// 模拟图表数据
export const mockChartData = {
  lineChart: Array.from({ length: 10 }, (_, i) => ({
    name: `Day ${i + 1}`,
    value: Math.floor(Math.random() * 100),
    value2: Math.floor(Math.random() * 100),
  })),

  areaChart: Array.from({ length: 7 }, (_, i) => ({
    name: `Week ${i + 1}`,
    tasks: Math.floor(Math.random() * 50),
    completed: Math.floor(Math.random() * 40),
    failed: Math.floor(Math.random() * 10),
  })),

  pieChart: [
    { name: '成功', value: 70, color: '#10b981' },
    { name: '失败', value: 20, color: '#ef4444' },
    { name: '进行中', value: 10, color: '#f59e0b' },
  ],
}

// 断言辅助函数
export const expectElementToBeVisible = (element: HTMLElement) => {
  expect(element).toBeInTheDocument()
  expect(element).toBeVisible()
}

export const expectElementToHaveText = (element: HTMLElement, text: string) => {
  expect(element).toBeInTheDocument()
  expect(element).toHaveTextContent(text)
}

export const expectElementToHaveClass = (element: HTMLElement, className: string) => {
  expect(element).toBeInTheDocument()
  expect(element).toHaveClass(className)
}
