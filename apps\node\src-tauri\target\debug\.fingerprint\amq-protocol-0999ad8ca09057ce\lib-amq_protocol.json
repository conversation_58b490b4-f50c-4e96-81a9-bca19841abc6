{"rustc": 1842507548689473721, "features": "[\"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"vendored-openssl\", \"verbose-errors\"]", "target": 12659125817264579830, "profile": 2241668132362809309, "path": 2622419605622029895, "deps": [[2985572863888970315, "amq_protocol_types", false, 9436021261624982276], [4886105269790530060, "cookie_factory", false, 12468208501160593927], [6502365400774175331, "nom", false, 18316414343240700499], [7048981225526245511, "build_script_build", false, 8167599371888164942], [9689903380558560274, "serde", false, 4202820352788480372], [11096876330329401515, "amq_protocol_uri", false, 8671601887169378153], [12404004217544788841, "amq_protocol_tcp", false, 2369169887888912205]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\amq-protocol-0999ad8ca09057ce\\dep-lib-amq_protocol", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}