{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 13318305459243126790, "path": 693437493755704817, "deps": [[5103565458935487, "futures_io", false, 487272345117200944], [1811549171721445101, "futures_channel", false, 3188382322166671117], [7013762810557009322, "futures_sink", false, 13257182082487644860], [7620660491849607393, "futures_core", false, 6897113057850168815], [10629569228670356391, "futures_util", false, 15644822958053016495], [12779779637805422465, "futures_executor", false, 1081618293400720757], [16240732885093539806, "futures_task", false, 5618792812908536749]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-c97217b5968866b0\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}