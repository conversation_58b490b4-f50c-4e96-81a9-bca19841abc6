use crate::error::{AppError, Result};
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppConfig {
    // 节点配置
    pub node_id: String,
    pub node_name: String,
    pub node_type: String,

    // 数据库配置
    pub database_url: String,
    pub redis_url: String,
    pub rabbitmq_url: String,

    // 管理节点配置
    pub master_node_url: String,
    pub heartbeat_interval: u64,

    // 爬虫配置
    pub max_concurrent_tasks: usize,
    pub request_timeout: u64,
    pub retry_max_attempts: u32,

    // 代理配置
    pub proxy_pool_size: usize,
    pub proxy_health_check_interval: u64,

    // 账号配置
    pub account_pool_size: usize,
    pub account_rotation_interval: u64,

    // 日志配置
    pub log_level: String,
    pub log_file: String,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            node_id: "crawler_node_001".to_string(),
            node_name: "爬虫节点001".to_string(),
            node_type: "crawler".to_string(),
            database_url: "sqlite:./data/crawler_node.db".to_string(),
            redis_url: "redis://localhost:6379".to_string(),
            rabbitmq_url: "amqp://admin:password@localhost:5672".to_string(),
            master_node_url: "http://localhost:8080".to_string(),
            heartbeat_interval: 30,
            max_concurrent_tasks: 100,
            request_timeout: 30,
            retry_max_attempts: 3,
            proxy_pool_size: 50,
            proxy_health_check_interval: 300,
            account_pool_size: 20,
            account_rotation_interval: 3600,
            log_level: "info".to_string(),
            log_file: "./logs/crawler_node.log".to_string(),
        }
    }
}

impl AppConfig {
    pub async fn load() -> Result<Self> {
        let mut config = Self::default();

        // 从环境变量加载配置
        if let Ok(node_id) = env::var("NODE_ID") {
            config.node_id = node_id;
        }

        if let Ok(node_name) = env::var("NODE_NAME") {
            config.node_name = node_name;
        }

        if let Ok(node_type) = env::var("NODE_TYPE") {
            config.node_type = node_type;
        }

        if let Ok(database_url) = env::var("SQLITE_DATABASE_URL") {
            config.database_url = database_url;
        }

        if let Ok(redis_url) = env::var("REDIS_URL") {
            config.redis_url = redis_url;
        }

        if let Ok(rabbitmq_url) = env::var("RABBITMQ_URL") {
            config.rabbitmq_url = rabbitmq_url;
        }

        if let Ok(master_node_url) = env::var("MASTER_NODE_URL") {
            config.master_node_url = master_node_url;
        }

        if let Ok(heartbeat_interval) = env::var("HEARTBEAT_INTERVAL") {
            config.heartbeat_interval = heartbeat_interval.parse().unwrap_or(30);
        }

        if let Ok(max_concurrent_tasks) = env::var("MAX_CONCURRENT_TASKS") {
            config.max_concurrent_tasks = max_concurrent_tasks.parse().unwrap_or(100);
        }

        if let Ok(request_timeout) = env::var("REQUEST_TIMEOUT") {
            config.request_timeout = request_timeout.parse().unwrap_or(30);
        }

        if let Ok(retry_max_attempts) = env::var("RETRY_MAX_ATTEMPTS") {
            config.retry_max_attempts = retry_max_attempts.parse().unwrap_or(3);
        }

        if let Ok(proxy_pool_size) = env::var("PROXY_POOL_SIZE") {
            config.proxy_pool_size = proxy_pool_size.parse().unwrap_or(50);
        }

        if let Ok(proxy_health_check_interval) = env::var("PROXY_HEALTH_CHECK_INTERVAL") {
            config.proxy_health_check_interval = proxy_health_check_interval.parse().unwrap_or(300);
        }

        if let Ok(account_pool_size) = env::var("ACCOUNT_POOL_SIZE") {
            config.account_pool_size = account_pool_size.parse().unwrap_or(20);
        }

        if let Ok(account_rotation_interval) = env::var("ACCOUNT_ROTATION_INTERVAL") {
            config.account_rotation_interval = account_rotation_interval.parse().unwrap_or(3600);
        }

        if let Ok(log_level) = env::var("LOG_LEVEL") {
            config.log_level = log_level;
        }

        if let Ok(log_file) = env::var("LOG_FILE") {
            config.log_file = log_file;
        }

        Ok(config)
    }

    pub async fn save(&self) -> Result<()> {
        // 这里可以实现配置保存到文件的逻辑
        // 目前先返回Ok
        Ok(())
    }

    pub fn validate(&self) -> Result<()> {
        if self.node_id.is_empty() {
            return Err(AppError::Config("节点ID不能为空".to_string()));
        }

        if self.node_name.is_empty() {
            return Err(AppError::Config("节点名称不能为空".to_string()));
        }

        if self.max_concurrent_tasks == 0 {
            return Err(AppError::Config("最大并发任务数必须大于0".to_string()));
        }

        if self.proxy_pool_size == 0 {
            return Err(AppError::Config("代理池大小必须大于0".to_string()));
        }

        if self.account_pool_size == 0 {
            return Err(AppError::Config("账号池大小必须大于0".to_string()));
        }

        Ok(())
    }
}
