{"rustc": 1842507548689473721, "features": "[\"default\", \"rustls\", \"rustls-native-certs\"]", "declared_features": "[\"amq-protocol-codegen\", \"codegen\", \"codegen-internal\", \"default\", \"native-tls\", \"openssl\", \"rustls\", \"rustls-native-certs\", \"rustls-webpki-roots-certs\", \"serde_json\", \"vendored-openssl\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 12960152636159808177, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\lapin-4b3f7e5d04f33fbc\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}