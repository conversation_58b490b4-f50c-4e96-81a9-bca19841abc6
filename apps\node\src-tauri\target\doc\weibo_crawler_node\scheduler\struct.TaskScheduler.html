<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `TaskScheduler` struct in crate `weibo_crawler_node`."><title>TaskScheduler in weibo_crawler_node::scheduler - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc struct"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Task<wbr>Scheduler</a></h2><h3><a href="#fields">Fields</a></h3><ul class="block structfield"><li><a href="#structfield.active_tasks" title="active_tasks">active_tasks</a></li><li><a href="#structfield.completed_tasks" title="completed_tasks">completed_tasks</a></li><li><a href="#structfield.failed_tasks" title="failed_tasks">failed_tasks</a></li><li><a href="#structfield.is_processing" title="is_processing">is_processing</a></li><li><a href="#structfield.max_concurrent" title="max_concurrent">max_concurrent</a></li><li><a href="#structfield.semaphore" title="semaphore">semaphore</a></li><li><a href="#structfield.statistics" title="statistics">statistics</a></li><li><a href="#structfield.task_queue" title="task_queue">task_queue</a></li></ul><h3><a href="#implementations">Methods</a></h3><ul class="block method"><li><a href="#method.add_task" title="add_task">add_task</a></li><li><a href="#method.cancel_task" title="cancel_task">cancel_task</a></li><li><a href="#method.clear_completed_tasks" title="clear_completed_tasks">clear_completed_tasks</a></li><li><a href="#method.clear_failed_tasks" title="clear_failed_tasks">clear_failed_tasks</a></li><li><a href="#method.get_queue_status" title="get_queue_status">get_queue_status</a></li><li><a href="#method.get_statistics" title="get_statistics">get_statistics</a></li><li><a href="#method.new" title="new">new</a></li><li><a href="#method.process_task" title="process_task">process_task</a></li><li><a href="#method.retry_failed_task" title="retry_failed_task">retry_failed_task</a></li><li><a href="#method.spawn_processing_loop" title="spawn_processing_loop">spawn_processing_loop</a></li><li><a href="#method.start_processing" title="start_processing">start_processing</a></li><li><a href="#method.stop_processing" title="stop_processing">stop_processing</a></li></ul><h3><a href="#synthetic-implementations">Auto Trait Implementations</a></h3><ul class="block synthetic-implementation"><li><a href="#impl-RefUnwindSafe-for-TaskScheduler" title="!RefUnwindSafe">!RefUnwindSafe</a></li><li><a href="#impl-UnwindSafe-for-TaskScheduler" title="!UnwindSafe">!UnwindSafe</a></li><li><a href="#impl-Freeze-for-TaskScheduler" title="Freeze">Freeze</a></li><li><a href="#impl-Send-for-TaskScheduler" title="Send">Send</a></li><li><a href="#impl-Sync-for-TaskScheduler" title="Sync">Sync</a></li><li><a href="#impl-Unpin-for-TaskScheduler" title="Unpin">Unpin</a></li></ul><h3><a href="#blanket-implementations">Blanket Implementations</a></h3><ul class="block blanket-implementation"><li><a href="#impl-Any-for-T" title="Any">Any</a></li><li><a href="#impl-Borrow%3CT%3E-for-T" title="Borrow&#60;T&#62;">Borrow&#60;T&#62;</a></li><li><a href="#impl-BorrowMut%3CT%3E-for-T" title="BorrowMut&#60;T&#62;">BorrowMut&#60;T&#62;</a></li><li><a href="#impl-ErasedDestructor-for-T" title="ErasedDestructor">ErasedDestructor</a></li><li><a href="#impl-From%3CT%3E-for-T" title="From&#60;T&#62;">From&#60;T&#62;</a></li><li><a href="#impl-Instrument-for-T" title="Instrument">Instrument</a></li><li><a href="#impl-Into%3CU%3E-for-T" title="Into&#60;U&#62;">Into&#60;U&#62;</a></li><li><a href="#impl-IntoEither-for-T" title="IntoEither">IntoEither</a></li><li><a href="#impl-Pointable-for-T" title="Pointable">Pointable</a></li><li><a href="#impl-Same-for-T" title="Same">Same</a></li><li><a href="#impl-TryFrom%3CU%3E-for-T" title="TryFrom&#60;U&#62;">TryFrom&#60;U&#62;</a></li><li><a href="#impl-TryInto%3CU%3E-for-T" title="TryInto&#60;U&#62;">TryInto&#60;U&#62;</a></li><li><a href="#impl-VZip%3CV%3E-for-T" title="VZip&#60;V&#62;">VZip&#60;V&#62;</a></li><li><a href="#impl-WithSubscriber-for-T" title="WithSubscriber">WithSubscriber</a></li></ul></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>scheduler</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">scheduler</a></div><h1>Struct <span class="struct">TaskScheduler</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/scheduler.rs.html#53-63">Source</a> </span></div><pre class="rust item-decl"><code>pub struct TaskScheduler {
    task_queue: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/collections/binary_heap/struct.BinaryHeap.html" title="struct alloc::collections::binary_heap::BinaryHeap">BinaryHeap</a>&lt;<a class="struct" href="struct.PriorityTask.html" title="struct weibo_crawler_node::scheduler::PriorityTask">PriorityTask</a>&gt;&gt;&gt;,
    active_tasks: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/std/collections/hash/map/struct.HashMap.html" title="struct std::collections::hash::map::HashMap">HashMap</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>, <a class="struct" href="struct.PriorityTask.html" title="struct weibo_crawler_node::scheduler::PriorityTask">PriorityTask</a>&gt;&gt;&gt;,
    completed_tasks: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>&gt;&gt;&gt;,
    failed_tasks: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>&gt;&gt;&gt;,
    semaphore: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;Semaphore&gt;,
    max_concurrent: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>,
    is_processing: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a>&gt;&gt;,
    statistics: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="struct.TaskStatistics.html" title="struct weibo_crawler_node::scheduler::TaskStatistics">TaskStatistics</a>&gt;&gt;,
}</code></pre><h2 id="fields" class="fields section-header">Fields<a href="#fields" class="anchor">§</a></h2><span id="structfield.task_queue" class="structfield section-header"><a href="#structfield.task_queue" class="anchor field">§</a><code>task_queue: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/collections/binary_heap/struct.BinaryHeap.html" title="struct alloc::collections::binary_heap::BinaryHeap">BinaryHeap</a>&lt;<a class="struct" href="struct.PriorityTask.html" title="struct weibo_crawler_node::scheduler::PriorityTask">PriorityTask</a>&gt;&gt;&gt;</code></span><span id="structfield.active_tasks" class="structfield section-header"><a href="#structfield.active_tasks" class="anchor field">§</a><code>active_tasks: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/std/collections/hash/map/struct.HashMap.html" title="struct std::collections::hash::map::HashMap">HashMap</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>, <a class="struct" href="struct.PriorityTask.html" title="struct weibo_crawler_node::scheduler::PriorityTask">PriorityTask</a>&gt;&gt;&gt;</code></span><span id="structfield.completed_tasks" class="structfield section-header"><a href="#structfield.completed_tasks" class="anchor field">§</a><code>completed_tasks: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>&gt;&gt;&gt;</code></span><span id="structfield.failed_tasks" class="structfield section-header"><a href="#structfield.failed_tasks" class="anchor field">§</a><code>failed_tasks: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/vec/struct.Vec.html" title="struct alloc::vec::Vec">Vec</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>&gt;&gt;&gt;</code></span><span id="structfield.semaphore" class="structfield section-header"><a href="#structfield.semaphore" class="anchor field">§</a><code>semaphore: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;Semaphore&gt;</code></span><span id="structfield.max_concurrent" class="structfield section-header"><a href="#structfield.max_concurrent" class="anchor field">§</a><code>max_concurrent: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a></code></span><span id="structfield.is_processing" class="structfield section-header"><a href="#structfield.is_processing" class="anchor field">§</a><code>is_processing: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a>&gt;&gt;</code></span><span id="structfield.statistics" class="structfield section-header"><a href="#structfield.statistics" class="anchor field">§</a><code>statistics: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/sync/struct.Arc.html" title="struct alloc::sync::Arc">Arc</a>&lt;RwLock&lt;<a class="struct" href="struct.TaskStatistics.html" title="struct weibo_crawler_node::scheduler::TaskStatistics">TaskStatistics</a>&gt;&gt;</code></span><h2 id="implementations" class="section-header">Implementations<a href="#implementations" class="anchor">§</a></h2><div id="implementations-list"><details class="toggle implementors-toggle" open><summary><section id="impl-TaskScheduler" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#65-357">Source</a><a href="#impl-TaskScheduler" class="anchor">§</a><h3 class="code-header">impl <a class="struct" href="struct.TaskScheduler.html" title="struct weibo_crawler_node::scheduler::TaskScheduler">TaskScheduler</a></h3></section></summary><div class="impl-items"><section id="method.new" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#66-86">Source</a><h4 class="code-header">pub async fn <a href="#method.new" class="fn">new</a>(max_concurrent: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.add_task" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#88-126">Source</a><h4 class="code-header">pub async fn <a href="#method.add_task" class="fn">add_task</a>(
    &amp;self,
    task_type: <a class="enum" href="../crawler/enum.TaskType.html" title="enum weibo_crawler_node::crawler::TaskType">TaskType</a>,
    target_url: <a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>,
    priority: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.u8.html">u8</a>,
) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/string/struct.String.html" title="struct alloc::string::String">String</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.start_processing" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#128-138">Source</a><h4 class="code-header">pub async fn <a href="#method.start_processing" class="fn">start_processing</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.stop_processing" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#140-149">Source</a><h4 class="code-header">pub async fn <a href="#method.stop_processing" class="fn">stop_processing</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.spawn_processing_loop" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#151-244">Source</a><h4 class="code-header">async fn <a href="#method.spawn_processing_loop" class="fn">spawn_processing_loop</a>(&amp;self)</h4></section><section id="method.process_task" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#246-259">Source</a><h4 class="code-header">async fn <a href="#method.process_task" class="fn">process_task</a>(task: &amp;<a class="struct" href="../crawler/struct.CrawlTask.html" title="struct weibo_crawler_node::crawler::CrawlTask">CrawlTask</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a></h4></section><section id="method.get_queue_status" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#261-288">Source</a><h4 class="code-header">pub async fn <a href="#method.get_queue_status" class="fn">get_queue_status</a>(&amp;self) -&gt; <a class="struct" href="../commands/struct.TaskQueueStatus.html" title="struct weibo_crawler_node::commands::TaskQueueStatus">TaskQueueStatus</a></h4></section><section id="method.get_statistics" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#290-305">Source</a><h4 class="code-header">pub async fn <a href="#method.get_statistics" class="fn">get_statistics</a>(&amp;self) -&gt; <a class="struct" href="../commands/struct.TaskStatistics.html" title="struct weibo_crawler_node::commands::TaskStatistics">TaskStatistics</a></h4></section><section id="method.retry_failed_task" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#307-312">Source</a><h4 class="code-header">pub async fn <a href="#method.retry_failed_task" class="fn">retry_failed_task</a>(&amp;self, task_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.cancel_task" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#314-338">Source</a><h4 class="code-header">pub async fn <a href="#method.cancel_task" class="fn">cancel_task</a>(&amp;self, task_id: &amp;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.clear_completed_tasks" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#340-347">Source</a><h4 class="code-header">pub async fn <a href="#method.clear_completed_tasks" class="fn">clear_completed_tasks</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section><section id="method.clear_failed_tasks" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/scheduler.rs.html#349-356">Source</a><h4 class="code-header">pub async fn <a href="#method.clear_failed_tasks" class="fn">clear_failed_tasks</a>(&amp;self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;</h4></section></div></details></div><h2 id="synthetic-implementations" class="section-header">Auto Trait Implementations<a href="#synthetic-implementations" class="anchor">§</a></h2><div id="synthetic-implementations-list"><section id="impl-Freeze-for-TaskScheduler" class="impl"><a href="#impl-Freeze-for-TaskScheduler" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Freeze.html" title="trait core::marker::Freeze">Freeze</a> for <a class="struct" href="struct.TaskScheduler.html" title="struct weibo_crawler_node::scheduler::TaskScheduler">TaskScheduler</a></h3></section><section id="impl-RefUnwindSafe-for-TaskScheduler" class="impl"><a href="#impl-RefUnwindSafe-for-TaskScheduler" class="anchor">§</a><h3 class="code-header">impl !<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/panic/unwind_safe/trait.RefUnwindSafe.html" title="trait core::panic::unwind_safe::RefUnwindSafe">RefUnwindSafe</a> for <a class="struct" href="struct.TaskScheduler.html" title="struct weibo_crawler_node::scheduler::TaskScheduler">TaskScheduler</a></h3></section><section id="impl-Send-for-TaskScheduler" class="impl"><a href="#impl-Send-for-TaskScheduler" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> for <a class="struct" href="struct.TaskScheduler.html" title="struct weibo_crawler_node::scheduler::TaskScheduler">TaskScheduler</a></h3></section><section id="impl-Sync-for-TaskScheduler" class="impl"><a href="#impl-Sync-for-TaskScheduler" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> for <a class="struct" href="struct.TaskScheduler.html" title="struct weibo_crawler_node::scheduler::TaskScheduler">TaskScheduler</a></h3></section><section id="impl-Unpin-for-TaskScheduler" class="impl"><a href="#impl-Unpin-for-TaskScheduler" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Unpin.html" title="trait core::marker::Unpin">Unpin</a> for <a class="struct" href="struct.TaskScheduler.html" title="struct weibo_crawler_node::scheduler::TaskScheduler">TaskScheduler</a></h3></section><section id="impl-UnwindSafe-for-TaskScheduler" class="impl"><a href="#impl-UnwindSafe-for-TaskScheduler" class="anchor">§</a><h3 class="code-header">impl !<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/panic/unwind_safe/trait.UnwindSafe.html" title="trait core::panic::unwind_safe::UnwindSafe">UnwindSafe</a> for <a class="struct" href="struct.TaskScheduler.html" title="struct weibo_crawler_node::scheduler::TaskScheduler">TaskScheduler</a></h3></section></div><h2 id="blanket-implementations" class="section-header">Blanket Implementations<a href="#blanket-implementations" class="anchor">§</a></h2><div id="blanket-implementations-list"><details class="toggle implementors-toggle"><summary><section id="impl-Any-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/any.rs.html#138">Source</a><a href="#impl-Any-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/any/trait.Any.html" title="trait core::any::Any">Any</a> for T<div class="where">where
    T: 'static + ?<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.type_id" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/any.rs.html#139">Source</a><a href="#method.type_id" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/any/trait.Any.html#tymethod.type_id" class="fn">type_id</a>(&amp;self) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/any/struct.TypeId.html" title="struct core::any::TypeId">TypeId</a></h4></section></summary><div class='docblock'>Gets the <code>TypeId</code> of <code>self</code>. <a href="https://doc.rust-lang.org/1.88.0/core/any/trait.Any.html#tymethod.type_id">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Borrow%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#209">Source</a><a href="#impl-Borrow%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.Borrow.html" title="trait core::borrow::Borrow">Borrow</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#211">Source</a><a href="#method.borrow" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.Borrow.html#tymethod.borrow" class="fn">borrow</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;T</a></h4></section></summary><div class='docblock'>Immutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.Borrow.html#tymethod.borrow">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-BorrowMut%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#217">Source</a><a href="#impl-BorrowMut%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.BorrowMut.html" title="trait core::borrow::BorrowMut">BorrowMut</a>&lt;T&gt; for T<div class="where">where
    T: ?<a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sized.html" title="trait core::marker::Sized">Sized</a>,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.borrow_mut" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/borrow.rs.html#218">Source</a><a href="#method.borrow_mut" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut" class="fn">borrow_mut</a>(&amp;mut self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;mut T</a></h4></section></summary><div class='docblock'>Mutably borrows from an owned value. <a href="https://doc.rust-lang.org/1.88.0/core/borrow/trait.BorrowMut.html#tymethod.borrow_mut">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-From%3CT%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#767">Source</a><a href="#impl-From%3CT%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#770">Source</a><a href="#method.from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html#tymethod.from" class="fn">from</a>(t: T) -&gt; T</h4></section></summary><div class="docblock"><p>Returns the argument unchanged.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Instrument-for-T" class="impl"><a href="#impl-Instrument-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; Instrument for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.instrument" class="method trait-impl"><a href="#method.instrument" class="anchor">§</a><h4 class="code-header">fn <a class="fn">instrument</a>(self, span: Span) -&gt; Instrumented&lt;Self&gt;</h4></section></summary><div class='docblock'>Instruments this type with the provided [<code>Span</code>], returning an
<code>Instrumented</code> wrapper. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.in_current_span" class="method trait-impl"><a href="#method.in_current_span" class="anchor">§</a><h4 class="code-header">fn <a class="fn">in_current_span</a>(self) -&gt; Instrumented&lt;Self&gt;</h4></section></summary><div class='docblock'>Instruments this type with the <a href="super::Span::current()">current</a> <a href="crate::Span"><code>Span</code></a>, returning an
<code>Instrumented</code> wrapper. <a>Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Into%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#750-752">Source</a><a href="#impl-Into%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#760">Source</a><a href="#method.into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html#tymethod.into" class="fn">into</a>(self) -&gt; U</h4></section></summary><div class="docblock"><p>Calls <code>U::from(self)</code>.</p>
<p>That is, this conversion is whatever the implementation of
<code><a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.From.html" title="trait core::convert::From">From</a>&lt;T&gt; for U</code> chooses to do.</p>
</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-IntoEither-for-T" class="impl"><a class="src rightside" href="https://docs.rs/either/1/src/either/into_either.rs.html#64">Source</a><a href="#impl-IntoEither-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html" title="trait either::into_either::IntoEither">IntoEither</a> for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.into_either" class="method trait-impl"><a class="src rightside" href="https://docs.rs/either/1/src/either/into_either.rs.html#29">Source</a><a href="#method.into_either" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either" class="fn">into_either</a>(self, into_left: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a>) -&gt; <a class="enum" href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either">Either</a>&lt;Self, Self&gt;</h4></section></summary><div class='docblock'>Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Left" title="variant either::Either::Left"><code>Left</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
if <code>into_left</code> is <code>true</code>.
Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Right" title="variant either::Either::Right"><code>Right</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
otherwise. <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either">Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.into_either_with" class="method trait-impl"><a class="src rightside" href="https://docs.rs/either/1/src/either/into_either.rs.html#55-57">Source</a><a href="#method.into_either_with" class="anchor">§</a><h4 class="code-header">fn <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either_with" class="fn">into_either_with</a>&lt;F&gt;(self, into_left: F) -&gt; <a class="enum" href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either">Either</a>&lt;Self, Self&gt;<div class="where">where
    F: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/ops/function/trait.FnOnce.html" title="trait core::ops::function::FnOnce">FnOnce</a>(&amp;Self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a>,</div></h4></section></summary><div class='docblock'>Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Left" title="variant either::Either::Left"><code>Left</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
if <code>into_left(&amp;self)</code> returns <code>true</code>.
Converts <code>self</code> into a <a href="https://docs.rs/either/1/either/enum.Either.html#variant.Right" title="variant either::Either::Right"><code>Right</code></a> variant of <a href="https://docs.rs/either/1/either/enum.Either.html" title="enum either::Either"><code>Either&lt;Self, Self&gt;</code></a>
otherwise. <a href="https://docs.rs/either/1/either/into_either/trait.IntoEither.html#method.into_either_with">Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Pointable-for-T" class="impl"><a href="#impl-Pointable-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; Pointable for T</h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedconstant.ALIGN" class="associatedconstant trait-impl"><a href="#associatedconstant.ALIGN" class="anchor">§</a><h4 class="code-header">const <a class="constant">ALIGN</a>: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a></h4></section></summary><div class='docblock'>The alignment of pointer.</div></details><details class="toggle" open><summary><section id="associatedtype.Init" class="associatedtype trait-impl"><a href="#associatedtype.Init" class="anchor">§</a><h4 class="code-header">type <a class="associatedtype">Init</a> = T</h4></section></summary><div class='docblock'>The type for initializers.</div></details><details class="toggle method-toggle" open><summary><section id="method.init" class="method trait-impl"><a href="#method.init" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">init</a>(init: &lt;T as Pointable&gt;::Init) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a></h4></section></summary><div class='docblock'>Initializes a with the given initializer. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.deref" class="method trait-impl"><a href="#method.deref" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">deref</a>&lt;'a&gt;(ptr: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;'a T</a></h4></section></summary><div class='docblock'>Dereferences the given pointer. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.deref_mut" class="method trait-impl"><a href="#method.deref_mut" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">deref_mut</a>&lt;'a&gt;(ptr: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.reference.html">&amp;'a mut T</a></h4></section></summary><div class='docblock'>Mutably dereferences the given pointer. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.drop" class="method trait-impl"><a href="#method.drop" class="anchor">§</a><h4 class="code-header">unsafe fn <a class="fn">drop</a>(ptr: <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.usize.html">usize</a>)</h4></section></summary><div class='docblock'>Drops the object pointed to by the given pointer. <a>Read more</a></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-Same-for-T" class="impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#34">Source</a><a href="#impl-Same-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; <a class="trait" href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html" title="trait typenum::type_operators::Same">Same</a> for T</h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Output" class="associatedtype trait-impl"><a class="src rightside" href="https://docs.rs/typenum/1.18.0/src/typenum/type_operators.rs.html#35">Source</a><a href="#associatedtype.Output" class="anchor">§</a><h4 class="code-header">type <a href="https://docs.rs/typenum/1.18.0/typenum/type_operators/trait.Same.html#associatedtype.Output" class="associatedtype">Output</a> = T</h4></section></summary><div class='docblock'>Should always be <code>Self</code></div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryFrom%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#806-808">Source</a><a href="#impl-TryFrom%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error-1" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#810">Source</a><a href="#associatedtype.Error-1" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" class="associatedtype">Error</a> = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/convert/enum.Infallible.html" title="enum core::convert::Infallible">Infallible</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_from" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#813">Source</a><a href="#method.try_from" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#tymethod.try_from" class="fn">try_from</a>(value: U) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;T, &lt;T as <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;U&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-TryInto%3CU%3E-for-T" class="impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#791-793">Source</a><a href="#impl-TryInto%3CU%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T, U&gt; <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryInto.html" title="trait core::convert::TryInto">TryInto</a>&lt;U&gt; for T<div class="where">where
    U: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><details class="toggle" open><summary><section id="associatedtype.Error" class="associatedtype trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#795">Source</a><a href="#associatedtype.Error" class="anchor">§</a><h4 class="code-header">type <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryInto.html#associatedtype.Error" class="associatedtype">Error</a> = &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a></h4></section></summary><div class='docblock'>The type returned in the event of a conversion error.</div></details><details class="toggle method-toggle" open><summary><section id="method.try_into" class="method trait-impl"><a class="src rightside" href="https://doc.rust-lang.org/1.88.0/src/core/convert/mod.rs.html#798">Source</a><a href="#method.try_into" class="anchor">§</a><h4 class="code-header">fn <a href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryInto.html#tymethod.try_into" class="fn">try_into</a>(self) -&gt; <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;U, &lt;U as <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html" title="trait core::convert::TryFrom">TryFrom</a>&lt;T&gt;&gt;::<a class="associatedtype" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.TryFrom.html#associatedtype.Error" title="type core::convert::TryFrom::Error">Error</a>&gt;</h4></section></summary><div class='docblock'>Performs the conversion.</div></details></div></details><details class="toggle implementors-toggle"><summary><section id="impl-VZip%3CV%3E-for-T" class="impl"><a href="#impl-VZip%3CV%3E-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;V, T&gt; VZip&lt;V&gt; for T<div class="where">where
    V: MultiLane&lt;T&gt;,</div></h3></section></summary><div class="impl-items"><section id="method.vzip" class="method trait-impl"><a href="#method.vzip" class="anchor">§</a><h4 class="code-header">fn <a class="fn">vzip</a>(self) -&gt; V</h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-WithSubscriber-for-T" class="impl"><a href="#impl-WithSubscriber-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; WithSubscriber for T</h3></section></summary><div class="impl-items"><details class="toggle method-toggle" open><summary><section id="method.with_subscriber" class="method trait-impl"><a href="#method.with_subscriber" class="anchor">§</a><h4 class="code-header">fn <a class="fn">with_subscriber</a>&lt;S&gt;(self, subscriber: S) -&gt; WithDispatch&lt;Self&gt;<div class="where">where
    S: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/convert/trait.Into.html" title="trait core::convert::Into">Into</a>&lt;Dispatch&gt;,</div></h4></section></summary><div class='docblock'>Attaches the provided <a href="super::Subscriber"><code>Subscriber</code></a> to this type, returning a
[<code>WithDispatch</code>] wrapper. <a>Read more</a></div></details><details class="toggle method-toggle" open><summary><section id="method.with_current_subscriber" class="method trait-impl"><a href="#method.with_current_subscriber" class="anchor">§</a><h4 class="code-header">fn <a class="fn">with_current_subscriber</a>(self) -&gt; WithDispatch&lt;Self&gt;</h4></section></summary><div class='docblock'>Attaches the current <a href="crate::dispatcher#setting-the-default-subscriber">default</a> <a href="super::Subscriber"><code>Subscriber</code></a> to this type, returning a
[<code>WithDispatch</code>] wrapper. <a>Read more</a></div></details></div></details><section id="impl-ErasedDestructor-for-T" class="impl"><a href="#impl-ErasedDestructor-for-T" class="anchor">§</a><h3 class="code-header">impl&lt;T&gt; ErasedDestructor for T<div class="where">where
    T: 'static,</div></h3></section></div></section></div></main></body></html>