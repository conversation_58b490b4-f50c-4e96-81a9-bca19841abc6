import { useState, useEffect } from "react";
import { 
  Play, 
  Pause, 
  RotateCcw,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { TauriAPI, TaskQueueStatus, TaskStatistics } from "../lib/tauri";
import { formatNumber } from "../lib/utils";

export function TaskManager() {
  const [queueStatus, setQueueStatus] = useState<TaskQueueStatus | null>(null);
  const [statistics, setStatistics] = useState<TaskStatistics | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTaskData();
    const interval = setInterval(loadTaskData, 5000); // 每5秒刷新
    return () => clearInterval(interval);
  }, []);

  const loadTaskData = async () => {
    try {
      const [queue, stats] = await Promise.all([
        TauriAPI.getTaskQueueStatus(),
        TauriAPI.getTaskStatistics(),
      ]);

      setQueueStatus(queue);
      setStatistics(stats);
      setIsProcessing(queue.running_tasks > 0);
    } catch (error) {
      console.error("加载任务数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartProcessing = async () => {
    try {
      await TauriAPI.startTaskProcessing();
      setIsProcessing(true);
      loadTaskData();
    } catch (error) {
      console.error("启动任务处理失败:", error);
    }
  };

  const handleStopProcessing = async () => {
    try {
      await TauriAPI.stopTaskProcessing();
      setIsProcessing(false);
      loadTaskData();
    } catch (error) {
      console.error("停止任务处理失败:", error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和控制按钮 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">任务管理</h1>
          <p className="text-muted-foreground mt-2">
            管理爬虫任务队列和处理状态
          </p>
        </div>
        <div className="flex space-x-2">
          {isProcessing ? (
            <button
              onClick={handleStopProcessing}
              className="btn btn-destructive"
            >
              <Pause className="h-4 w-4 mr-2" />
              停止处理
            </button>
          ) : (
            <button
              onClick={handleStartProcessing}
              className="btn btn-primary"
            >
              <Play className="h-4 w-4 mr-2" />
              开始处理
            </button>
          )}
          <button
            onClick={loadTaskData}
            className="btn btn-outline"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            刷新
          </button>
        </div>
      </div>

      {/* 任务队列状态卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">等待任务</p>
                <p className="text-2xl font-bold">{queueStatus?.pending_tasks || 0}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">处理中</p>
                <p className="text-2xl font-bold">{queueStatus?.running_tasks || 0}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-500" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">已完成</p>
                <p className="text-2xl font-bold">{formatNumber(queueStatus?.completed_tasks || 0)}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">失败</p>
                <p className="text-2xl font-bold">{formatNumber(queueStatus?.failed_tasks || 0)}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </div>
        </div>
      </div>

      {/* 任务统计和性能指标 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">处理统计</h3>
            <p className="card-description">任务处理性能统计</p>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">总处理量</span>
                <span className="font-medium">{formatNumber(statistics?.total_processed || 0)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">成功数量</span>
                <span className="font-medium text-green-600">{formatNumber(statistics?.success_count || 0)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">失败数量</span>
                <span className="font-medium text-red-600">{formatNumber(statistics?.failure_count || 0)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">成功率</span>
                <span className="font-medium">{statistics?.success_rate?.toFixed(1) || 0}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">平均耗时</span>
                <span className="font-medium">{statistics?.average_duration?.toFixed(2) || 0}秒</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">每小时处理量</span>
                <span className="font-medium">{statistics?.tasks_per_hour?.toFixed(0) || 0}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="card-title">队列状态</h3>
            <p className="card-description">当前队列处理状态</p>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>队列长度</span>
                  <span>{queueStatus?.queue_length || 0}</span>
                </div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min((queueStatus?.queue_length || 0) / 100 * 100, 100)}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>处理速度</span>
                  <span>{queueStatus?.processing_speed?.toFixed(1) || 0}/分钟</span>
                </div>
                <div className="w-full bg-secondary rounded-full h-2">
                  <div 
                    className="bg-green-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min((queueStatus?.processing_speed || 0) / 60 * 100, 100)}%` }}
                  ></div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <div className="flex items-center justify-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${isProcessing ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`}></div>
                  <span className="text-sm font-medium">
                    {isProcessing ? '正在处理任务' : '任务处理已停止'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 任务类型分布 */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">任务分布</h3>
          <p className="card-description">不同类型任务的处理情况</p>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-blue-600">25%</div>
              <div className="text-sm text-muted-foreground">用户信息</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-green-600">40%</div>
              <div className="text-sm text-muted-foreground">微博内容</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-orange-600">30%</div>
              <div className="text-sm text-muted-foreground">评论数据</div>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold text-purple-600">5%</div>
              <div className="text-sm text-muted-foreground">话题信息</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 默认导出
export default TaskManager;
