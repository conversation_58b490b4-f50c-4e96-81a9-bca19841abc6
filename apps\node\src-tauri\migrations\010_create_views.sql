-- 创建常用的视图以简化查询

-- 任务统计视图
CREATE VIEW v_task_statistics AS
SELECT 
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN status = 0 THEN 1 END) as pending_tasks,
    COUNT(CASE WHEN status = 1 THEN 1 END) as running_tasks,
    COUNT(CASE WHEN status = 2 THEN 1 END) as completed_tasks,
    COUNT(CASE WHEN status = 3 THEN 1 END) as failed_tasks,
    ROUND(
        CAST(COUNT(CASE WHEN status = 2 THEN 1 END) AS REAL) / 
        NULLIF(COUNT(CASE WHEN status IN (2, 3) THEN 1 END), 0) * 100, 2
    ) as success_rate,
    AVG(CASE WHEN tr.execution_time IS NOT NULL THEN tr.execution_time END) as avg_execution_time
FROM crawl_tasks ct
LEFT JOIN task_results tr ON ct.task_id = tr.task_id;

-- 代理池状态视图
CREATE VIEW v_proxy_pool_status AS
SELECT 
    COUNT(*) as total_proxies,
    COUNT(CASE WHEN status = 1 THEN 1 END) as active_proxies,
    COUNT(CASE WHEN status = 2 THEN 1 END) as unavailable_proxies,
    COUNT(CASE WHEN status = 3 THEN 1 END) as maintenance_proxies,
    COUNT(CASE WHEN status = 4 THEN 1 END) as testing_proxies,
    AVG(response_time) as avg_response_time,
    ROUND(
        CAST(COUNT(CASE WHEN status = 1 THEN 1 END) AS REAL) / 
        NULLIF(COUNT(*), 0) * 100, 2
    ) as health_rate
FROM proxies;

-- 账号池状态视图
CREATE VIEW v_account_pool_status AS
SELECT 
    COUNT(*) as total_accounts,
    COUNT(CASE WHEN status = 1 THEN 1 END) as normal_accounts,
    COUNT(CASE WHEN status = 2 THEN 1 END) as banned_accounts,
    COUNT(CASE WHEN status = 3 THEN 1 END) as abnormal_accounts,
    COUNT(CASE WHEN status = 4 THEN 1 END) as maintenance_accounts,
    COUNT(CASE WHEN last_login > datetime('now', '-24 hours') THEN 1 END) as recently_active,
    AVG(risk_score) as avg_risk_score,
    COUNT(CASE WHEN risk_score > 0.7 THEN 1 END) as high_risk_accounts
FROM accounts;

-- 代理性能详情视图
CREATE VIEW v_proxy_performance AS
SELECT 
    p.proxy_id,
    p.host,
    p.port,
    p.protocol,
    p.country,
    p.status,
    p.success_count,
    p.failure_count,
    p.response_time,
    p.last_used,
    p.last_checked,
    ROUND(
        CAST(p.success_count AS REAL) / 
        NULLIF(p.success_count + p.failure_count, 0) * 100, 2
    ) as success_rate,
    COALESCE(recent_stats.recent_requests, 0) as recent_requests,
    COALESCE(recent_stats.recent_success_rate, 0) as recent_success_rate
FROM proxies p
LEFT JOIN (
    SELECT 
        proxy_id,
        COUNT(*) as recent_requests,
        ROUND(
            CAST(COUNT(CASE WHEN success = 1 THEN 1 END) AS REAL) / 
            NULLIF(COUNT(*), 0) * 100, 2
        ) as recent_success_rate
    FROM proxy_usage 
    WHERE used_at > datetime('now', '-24 hours')
    GROUP BY proxy_id
) recent_stats ON p.proxy_id = recent_stats.proxy_id;

-- 账号使用详情视图
CREATE VIEW v_account_usage AS
SELECT 
    a.account_id,
    a.username,
    a.status,
    a.login_count,
    a.last_login,
    a.last_activity,
    a.risk_score,
    COALESCE(recent_tasks.task_count, 0) as recent_task_count,
    COALESCE(recent_tasks.success_count, 0) as recent_success_count,
    COALESCE(recent_tasks.success_rate, 0) as recent_success_rate,
    CASE 
        WHEN a.last_activity > datetime('now', '-1 hour') THEN 'Active'
        WHEN a.last_activity > datetime('now', '-24 hours') THEN 'Recent'
        WHEN a.last_activity > datetime('now', '-7 days') THEN 'Inactive'
        ELSE 'Dormant'
    END as activity_status
FROM accounts a
LEFT JOIN (
    SELECT 
        account_used as account_id,
        COUNT(*) as task_count,
        COUNT(CASE WHEN result_type = 1 THEN 1 END) as success_count,
        ROUND(
            CAST(COUNT(CASE WHEN result_type = 1 THEN 1 END) AS REAL) / 
            NULLIF(COUNT(*), 0) * 100, 2
        ) as success_rate
    FROM task_results 
    WHERE created_at > datetime('now', '-24 hours')
    AND account_used IS NOT NULL
    GROUP BY account_used
) recent_tasks ON a.account_id = recent_tasks.account_id;

-- 系统健康状况视图
CREATE VIEW v_system_health AS
SELECT 
    (SELECT COUNT(*) FROM crawl_tasks WHERE status = 1) as active_tasks,
    (SELECT COUNT(*) FROM proxies WHERE status = 1) as healthy_proxies,
    (SELECT COUNT(*) FROM accounts WHERE status = 1) as normal_accounts,
    (SELECT AVG(cpu_usage) FROM system_metrics WHERE recorded_at > datetime('now', '-5 minutes')) as avg_cpu_usage,
    (SELECT AVG(memory_usage) FROM system_metrics WHERE recorded_at > datetime('now', '-5 minutes')) as avg_memory_usage,
    (SELECT COUNT(*) FROM error_logs WHERE error_level >= 4 AND created_at > datetime('now', '-1 hour')) as recent_errors,
    datetime('now') as last_updated;
