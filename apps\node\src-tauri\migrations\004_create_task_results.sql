-- 任务执行结果表
CREATE TABLE task_results (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT NOT NULL,
    result_type INTEGER NOT NULL, -- 1: Success, 2: Failed, 3: Partial, 4: Skipped
    result_data TEXT, -- JSON格式的结果数据
    error_message TEXT,
    error_code TEXT,
    execution_time INTEGER, -- 执行时间(毫秒)
    data_count INTEGER DEFAULT 0, -- 采集到的数据条数
    proxy_used TEXT, -- 使用的代理ID
    account_used TEXT, -- 使用的账号ID
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES crawl_tasks(task_id)
);

-- 任务执行日志表
CREATE TABLE task_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id TEXT NOT NULL,
    log_level INTEGER NOT NULL, -- 1: Debug, 2: Info, 3: Warn, 4: Error
    log_message TEXT NOT NULL,
    log_context TEXT, -- JSON格式的上下文信息
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES crawl_tasks(task_id)
);

-- 创建索引
CREATE INDEX idx_task_results_task_id ON task_results(task_id);
CREATE INDEX idx_task_results_result_type ON task_results(result_type);
CREATE INDEX idx_task_results_created_at ON task_results(created_at);
CREATE INDEX idx_task_logs_task_id ON task_logs(task_id);
CREATE INDEX idx_task_logs_log_level ON task_logs(log_level);
CREATE INDEX idx_task_logs_created_at ON task_logs(created_at);
