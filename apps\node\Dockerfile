# 构建阶段
FROM node:18-alpine as frontend-builder

WORKDIR /app

# 复制前端依赖文件
COPY package*.json ./
RUN npm ci

# 复制前端源码并构建
COPY . .
RUN npm run build

# Rust构建阶段
FROM rust:1.70-alpine as backend-builder

# 安装构建依赖
RUN apk add --no-cache \
    musl-dev \
    pkgconfig \
    openssl-dev \
    sqlite-dev

WORKDIR /app

# 复制Rust项目文件
COPY src-tauri/Cargo.toml src-tauri/Cargo.lock ./
COPY src-tauri/src ./src
COPY src-tauri/migrations ./migrations
COPY src-tauri/build.rs ./

# 构建Rust应用
RUN cargo build --release

# 运行时镜像
FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache \
    sqlite \
    ca-certificates \
    tzdata

# 创建应用用户
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# 创建应用目录
WORKDIR /app

# 复制构建产物
COPY --from=backend-builder /app/target/release/weibo-crawler-node /usr/local/bin/
COPY --from=backend-builder /app/migrations ./migrations
COPY --from=frontend-builder /app/dist ./dist

# 创建数据目录
RUN mkdir -p /app/data /app/logs && \
    chown -R appuser:appgroup /app

# 切换到应用用户
USER appuser

# 暴露端口
EXPOSE 8084

# 设置环境变量
ENV NODE_ID=crawler_node_docker
ENV NODE_NAME=Docker爬虫节点
ENV SQLITE_DATABASE_URL=sqlite:/app/data/crawler_node.db
ENV LOG_FILE=/app/logs/crawler_node.log

# 启动命令
CMD ["weibo-crawler-node"]
