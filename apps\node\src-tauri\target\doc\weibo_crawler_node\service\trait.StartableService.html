<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="可启动的服务特征"><title>StartableService in weibo_crawler_node::service - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc trait"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Startable<wbr>Service</a></h2><h3><a href="#required-methods">Required Methods</a></h3><ul class="block"><li><a href="#tymethod.is_running" title="is_running">is_running</a></li><li><a href="#tymethod.start" title="start">start</a></li><li><a href="#tymethod.stop" title="stop">stop</a></li></ul><h3><a href="#provided-methods">Provided Methods</a></h3><ul class="block"><li><a href="#method.restart" title="restart">restart</a></li></ul><h3><a href="#implementors">Implementors</a></h3></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>service</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">service</a></div><h1>Trait <span class="trait">StartableService</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/service/mod.rs.html#45-53">Source</a> </span></div><pre class="rust item-decl"><code>pub trait StartableService: <a class="trait" href="trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a> {
    // Required methods
    fn <a href="#tymethod.start" class="fn">start</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.stop" class="fn">stop</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.is_running" class="fn">is_running</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a>;

    // Provided method
    fn <a href="#method.restart" class="fn">restart</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> + 'async_trait,
             'life0: 'async_trait</span> { ... }
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>可启动的服务特征</p>
</div></details><h2 id="required-methods" class="section-header">Required Methods<a href="#required-methods" class="anchor">§</a></h2><div class="methods"><section id="tymethod.start" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#46">Source</a><h4 class="code-header">fn <a href="#tymethod.start" class="fn">start</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="tymethod.stop" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#47">Source</a><h4 class="code-header">fn <a href="#tymethod.stop" class="fn">stop</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="tymethod.is_running" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#52">Source</a><h4 class="code-header">fn <a href="#tymethod.is_running" class="fn">is_running</a>(&amp;self) -&gt; <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.bool.html">bool</a></h4></section></div><h2 id="provided-methods" class="section-header">Provided Methods<a href="#provided-methods" class="anchor">§</a></h2><div class="methods"><section id="method.restart" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#48-51">Source</a><h4 class="code-header">fn <a href="#method.restart" class="fn">restart</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.unit.html">()</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Sync.html" title="trait core::marker::Sync">Sync</a> + 'async_trait,
    'life0: 'async_trait,</div></h4></section></div><h2 id="implementors" class="section-header">Implementors<a href="#implementors" class="anchor">§</a></h2><div id="implementors-list"><section id="impl-StartableService-for-AccountService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/account_service.rs.html#49-65">Source</a><a href="#impl-StartableService-for-AccountService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.StartableService.html" title="trait weibo_crawler_node::service::StartableService">StartableService</a> for <a class="struct" href="account_service/struct.AccountService.html" title="struct weibo_crawler_node::service::account_service::AccountService">AccountService</a></h3></section><section id="impl-StartableService-for-CrawlerService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/crawler_service.rs.html#51-67">Source</a><a href="#impl-StartableService-for-CrawlerService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.StartableService.html" title="trait weibo_crawler_node::service::StartableService">StartableService</a> for <a class="struct" href="crawler_service/struct.CrawlerService.html" title="struct weibo_crawler_node::service::crawler_service::CrawlerService">CrawlerService</a></h3></section><section id="impl-StartableService-for-MonitorService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/monitor_service.rs.html#51-67">Source</a><a href="#impl-StartableService-for-MonitorService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.StartableService.html" title="trait weibo_crawler_node::service::StartableService">StartableService</a> for <a class="struct" href="monitor_service/struct.MonitorService.html" title="struct weibo_crawler_node::service::monitor_service::MonitorService">MonitorService</a></h3></section><section id="impl-StartableService-for-ProxyService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/proxy_service.rs.html#49-65">Source</a><a href="#impl-StartableService-for-ProxyService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.StartableService.html" title="trait weibo_crawler_node::service::StartableService">StartableService</a> for <a class="struct" href="proxy_service/struct.ProxyService.html" title="struct weibo_crawler_node::service::proxy_service::ProxyService">ProxyService</a></h3></section><section id="impl-StartableService-for-TaskService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/task_service.rs.html#259-287">Source</a><a href="#impl-StartableService-for-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.StartableService.html" title="trait weibo_crawler_node::service::StartableService">StartableService</a> for <a class="struct" href="task_service/struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section></div><script src="../../trait.impl/weibo_crawler_node/service/trait.StartableService.js" async></script></section></div></main></body></html>