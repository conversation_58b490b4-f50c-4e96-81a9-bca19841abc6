<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\storage.rs`."><title>storage.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>storage.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{AppError, <span class="prelude-ty">Result</span>};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>sqlx::SqlitePool;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>std::path::Path;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>tracing::{info, error};
<a href=#6 id=6 data-nosnippet>6</a>
<a href=#7 id=7 data-nosnippet>7</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#8 id=8 data-nosnippet>8</a></span><span class="kw">pub struct </span>TaskRecord {
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">pub </span>id: i64,
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub </span>task_id: String,
<a href=#11 id=11 data-nosnippet>11</a>    <span class="kw">pub </span>task_type: i32,
<a href=#12 id=12 data-nosnippet>12</a>    <span class="kw">pub </span>target_url: String,
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>priority: i32,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>status: i32,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>retry_count: i32,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>max_retries: i32,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>metadata: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">pub </span>assigned_at: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#19 id=19 data-nosnippet>19</a>    <span class="kw">pub </span>started_at: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#20 id=20 data-nosnippet>20</a>    <span class="kw">pub </span>completed_at: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#21 id=21 data-nosnippet>21</a>    <span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">pub </span>updated_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#23 id=23 data-nosnippet>23</a>}
<a href=#24 id=24 data-nosnippet>24</a>
<a href=#25 id=25 data-nosnippet>25</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#26 id=26 data-nosnippet>26</a></span><span class="kw">pub struct </span>ProxyRecord {
<a href=#27 id=27 data-nosnippet>27</a>    <span class="kw">pub </span>id: i64,
<a href=#28 id=28 data-nosnippet>28</a>    <span class="kw">pub </span>proxy_id: String,
<a href=#29 id=29 data-nosnippet>29</a>    <span class="kw">pub </span>host: String,
<a href=#30 id=30 data-nosnippet>30</a>    <span class="kw">pub </span>port: i32,
<a href=#31 id=31 data-nosnippet>31</a>    <span class="kw">pub </span>protocol: String,
<a href=#32 id=32 data-nosnippet>32</a>    <span class="kw">pub </span>username: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#33 id=33 data-nosnippet>33</a>    <span class="kw">pub </span>password: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">pub </span>country: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#35 id=35 data-nosnippet>35</a>    <span class="kw">pub </span>region: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>provider: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#37 id=37 data-nosnippet>37</a>    <span class="kw">pub </span>status: i32,
<a href=#38 id=38 data-nosnippet>38</a>    <span class="kw">pub </span>success_count: i64,
<a href=#39 id=39 data-nosnippet>39</a>    <span class="kw">pub </span>failure_count: i64,
<a href=#40 id=40 data-nosnippet>40</a>    <span class="kw">pub </span>last_used: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#41 id=41 data-nosnippet>41</a>    <span class="kw">pub </span>last_checked: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#42 id=42 data-nosnippet>42</a>    <span class="kw">pub </span>response_time: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#43 id=43 data-nosnippet>43</a>    <span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#44 id=44 data-nosnippet>44</a>    <span class="kw">pub </span>updated_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#45 id=45 data-nosnippet>45</a>}
<a href=#46 id=46 data-nosnippet>46</a>
<a href=#47 id=47 data-nosnippet>47</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#48 id=48 data-nosnippet>48</a></span><span class="kw">pub struct </span>AccountRecord {
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">pub </span>id: i64,
<a href=#50 id=50 data-nosnippet>50</a>    <span class="kw">pub </span>account_id: String,
<a href=#51 id=51 data-nosnippet>51</a>    <span class="kw">pub </span>username: String,
<a href=#52 id=52 data-nosnippet>52</a>    <span class="kw">pub </span>password: String,
<a href=#53 id=53 data-nosnippet>53</a>    <span class="kw">pub </span>phone: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#54 id=54 data-nosnippet>54</a>    <span class="kw">pub </span>email: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#55 id=55 data-nosnippet>55</a>    <span class="kw">pub </span>status: i32,
<a href=#56 id=56 data-nosnippet>56</a>    <span class="kw">pub </span>login_count: i64,
<a href=#57 id=57 data-nosnippet>57</a>    <span class="kw">pub </span>last_login: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#58 id=58 data-nosnippet>58</a>    <span class="kw">pub </span>last_activity: <span class="prelude-ty">Option</span>&lt;chrono::DateTime&lt;chrono::Utc&gt;&gt;,
<a href=#59 id=59 data-nosnippet>59</a>    <span class="kw">pub </span>risk_score: f64,
<a href=#60 id=60 data-nosnippet>60</a>    <span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#61 id=61 data-nosnippet>61</a>    <span class="kw">pub </span>updated_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#62 id=62 data-nosnippet>62</a>}
<a href=#63 id=63 data-nosnippet>63</a>
<a href=#64 id=64 data-nosnippet>64</a><span class="comment">// 新增数据结构
<a href=#65 id=65 data-nosnippet>65</a>
<a href=#66 id=66 data-nosnippet>66</a></span><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#67 id=67 data-nosnippet>67</a></span><span class="kw">pub struct </span>TaskResult {
<a href=#68 id=68 data-nosnippet>68</a>    <span class="kw">pub </span>id: i64,
<a href=#69 id=69 data-nosnippet>69</a>    <span class="kw">pub </span>task_id: String,
<a href=#70 id=70 data-nosnippet>70</a>    <span class="kw">pub </span>result_type: i32,
<a href=#71 id=71 data-nosnippet>71</a>    <span class="kw">pub </span>result_data: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#72 id=72 data-nosnippet>72</a>    <span class="kw">pub </span>error_message: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#73 id=73 data-nosnippet>73</a>    <span class="kw">pub </span>error_code: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#74 id=74 data-nosnippet>74</a>    <span class="kw">pub </span>execution_time: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#75 id=75 data-nosnippet>75</a>    <span class="kw">pub </span>data_count: i32,
<a href=#76 id=76 data-nosnippet>76</a>    <span class="kw">pub </span>proxy_used: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#77 id=77 data-nosnippet>77</a>    <span class="kw">pub </span>account_used: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#78 id=78 data-nosnippet>78</a>    <span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#79 id=79 data-nosnippet>79</a>}
<a href=#80 id=80 data-nosnippet>80</a>
<a href=#81 id=81 data-nosnippet>81</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#82 id=82 data-nosnippet>82</a></span><span class="kw">pub struct </span>TaskLog {
<a href=#83 id=83 data-nosnippet>83</a>    <span class="kw">pub </span>id: i64,
<a href=#84 id=84 data-nosnippet>84</a>    <span class="kw">pub </span>task_id: String,
<a href=#85 id=85 data-nosnippet>85</a>    <span class="kw">pub </span>log_level: i32,
<a href=#86 id=86 data-nosnippet>86</a>    <span class="kw">pub </span>log_message: String,
<a href=#87 id=87 data-nosnippet>87</a>    <span class="kw">pub </span>log_context: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#88 id=88 data-nosnippet>88</a>    <span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#89 id=89 data-nosnippet>89</a>}
<a href=#90 id=90 data-nosnippet>90</a>
<a href=#91 id=91 data-nosnippet>91</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#92 id=92 data-nosnippet>92</a></span><span class="kw">pub struct </span>ProxyUsage {
<a href=#93 id=93 data-nosnippet>93</a>    <span class="kw">pub </span>id: i64,
<a href=#94 id=94 data-nosnippet>94</a>    <span class="kw">pub </span>proxy_id: String,
<a href=#95 id=95 data-nosnippet>95</a>    <span class="kw">pub </span>task_id: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#96 id=96 data-nosnippet>96</a>    <span class="kw">pub </span>usage_type: i32,
<a href=#97 id=97 data-nosnippet>97</a>    <span class="kw">pub </span>success: bool,
<a href=#98 id=98 data-nosnippet>98</a>    <span class="kw">pub </span>response_time: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#99 id=99 data-nosnippet>99</a>    <span class="kw">pub </span>bytes_transferred: i64,
<a href=#100 id=100 data-nosnippet>100</a>    <span class="kw">pub </span>error_message: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#101 id=101 data-nosnippet>101</a>    <span class="kw">pub </span>used_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#102 id=102 data-nosnippet>102</a>}
<a href=#103 id=103 data-nosnippet>103</a>
<a href=#104 id=104 data-nosnippet>104</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#105 id=105 data-nosnippet>105</a></span><span class="kw">pub struct </span>AccountLog {
<a href=#106 id=106 data-nosnippet>106</a>    <span class="kw">pub </span>id: i64,
<a href=#107 id=107 data-nosnippet>107</a>    <span class="kw">pub </span>account_id: String,
<a href=#108 id=108 data-nosnippet>108</a>    <span class="kw">pub </span>operation_type: i32,
<a href=#109 id=109 data-nosnippet>109</a>    <span class="kw">pub </span>operation_result: i32,
<a href=#110 id=110 data-nosnippet>110</a>    <span class="kw">pub </span>operation_details: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#111 id=111 data-nosnippet>111</a>    <span class="kw">pub </span>ip_address: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#112 id=112 data-nosnippet>112</a>    <span class="kw">pub </span>user_agent: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#113 id=113 data-nosnippet>113</a>    <span class="kw">pub </span>proxy_used: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#114 id=114 data-nosnippet>114</a>    <span class="kw">pub </span>error_message: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#115 id=115 data-nosnippet>115</a>    <span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#116 id=116 data-nosnippet>116</a>}
<a href=#117 id=117 data-nosnippet>117</a>
<a href=#118 id=118 data-nosnippet>118</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#119 id=119 data-nosnippet>119</a></span><span class="kw">pub struct </span>SystemMetrics {
<a href=#120 id=120 data-nosnippet>120</a>    <span class="kw">pub </span>id: i64,
<a href=#121 id=121 data-nosnippet>121</a>    <span class="kw">pub </span>node_id: String,
<a href=#122 id=122 data-nosnippet>122</a>    <span class="kw">pub </span>cpu_usage: f64,
<a href=#123 id=123 data-nosnippet>123</a>    <span class="kw">pub </span>memory_usage: f64,
<a href=#124 id=124 data-nosnippet>124</a>    <span class="kw">pub </span>memory_total: i64,
<a href=#125 id=125 data-nosnippet>125</a>    <span class="kw">pub </span>memory_used: i64,
<a href=#126 id=126 data-nosnippet>126</a>    <span class="kw">pub </span>disk_usage: f64,
<a href=#127 id=127 data-nosnippet>127</a>    <span class="kw">pub </span>disk_total: i64,
<a href=#128 id=128 data-nosnippet>128</a>    <span class="kw">pub </span>disk_used: i64,
<a href=#129 id=129 data-nosnippet>129</a>    <span class="kw">pub </span>network_in: i64,
<a href=#130 id=130 data-nosnippet>130</a>    <span class="kw">pub </span>network_out: i64,
<a href=#131 id=131 data-nosnippet>131</a>    <span class="kw">pub </span>active_connections: i32,
<a href=#132 id=132 data-nosnippet>132</a>    <span class="kw">pub </span>recorded_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#133 id=133 data-nosnippet>133</a>}
<a href=#134 id=134 data-nosnippet>134</a>
<a href=#135 id=135 data-nosnippet>135</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#136 id=136 data-nosnippet>136</a></span><span class="kw">pub struct </span>ErrorLog {
<a href=#137 id=137 data-nosnippet>137</a>    <span class="kw">pub </span>id: i64,
<a href=#138 id=138 data-nosnippet>138</a>    <span class="kw">pub </span>error_type: i32,
<a href=#139 id=139 data-nosnippet>139</a>    <span class="kw">pub </span>error_level: i32,
<a href=#140 id=140 data-nosnippet>140</a>    <span class="kw">pub </span>error_code: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#141 id=141 data-nosnippet>141</a>    <span class="kw">pub </span>error_message: String,
<a href=#142 id=142 data-nosnippet>142</a>    <span class="kw">pub </span>error_context: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#143 id=143 data-nosnippet>143</a>    <span class="kw">pub </span>stack_trace: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#144 id=144 data-nosnippet>144</a>    <span class="kw">pub </span>component: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#145 id=145 data-nosnippet>145</a>    <span class="kw">pub </span>task_id: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#146 id=146 data-nosnippet>146</a>    <span class="kw">pub </span>account_id: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#147 id=147 data-nosnippet>147</a>    <span class="kw">pub </span>proxy_id: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#148 id=148 data-nosnippet>148</a>    <span class="kw">pub </span>created_at: chrono::DateTime&lt;chrono::Utc&gt;,
<a href=#149 id=149 data-nosnippet>149</a>}
<a href=#150 id=150 data-nosnippet>150</a>
<a href=#151 id=151 data-nosnippet>151</a><span class="comment">// 统计和视图数据结构
<a href=#152 id=152 data-nosnippet>152</a>
<a href=#153 id=153 data-nosnippet>153</a></span><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#154 id=154 data-nosnippet>154</a></span><span class="kw">pub struct </span>TaskStatistics {
<a href=#155 id=155 data-nosnippet>155</a>    <span class="kw">pub </span>total_tasks: i64,
<a href=#156 id=156 data-nosnippet>156</a>    <span class="kw">pub </span>pending_tasks: i64,
<a href=#157 id=157 data-nosnippet>157</a>    <span class="kw">pub </span>running_tasks: i64,
<a href=#158 id=158 data-nosnippet>158</a>    <span class="kw">pub </span>completed_tasks: i64,
<a href=#159 id=159 data-nosnippet>159</a>    <span class="kw">pub </span>failed_tasks: i64,
<a href=#160 id=160 data-nosnippet>160</a>    <span class="kw">pub </span>success_rate: f64,
<a href=#161 id=161 data-nosnippet>161</a>    <span class="kw">pub </span>avg_execution_time: <span class="prelude-ty">Option</span>&lt;f64&gt;,
<a href=#162 id=162 data-nosnippet>162</a>}
<a href=#163 id=163 data-nosnippet>163</a>
<a href=#164 id=164 data-nosnippet>164</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#165 id=165 data-nosnippet>165</a></span><span class="kw">pub struct </span>ProxyPoolStatus {
<a href=#166 id=166 data-nosnippet>166</a>    <span class="kw">pub </span>total_proxies: i64,
<a href=#167 id=167 data-nosnippet>167</a>    <span class="kw">pub </span>active_proxies: i64,
<a href=#168 id=168 data-nosnippet>168</a>    <span class="kw">pub </span>unavailable_proxies: i64,
<a href=#169 id=169 data-nosnippet>169</a>    <span class="kw">pub </span>maintenance_proxies: i64,
<a href=#170 id=170 data-nosnippet>170</a>    <span class="kw">pub </span>testing_proxies: i64,
<a href=#171 id=171 data-nosnippet>171</a>    <span class="kw">pub </span>avg_response_time: <span class="prelude-ty">Option</span>&lt;f64&gt;,
<a href=#172 id=172 data-nosnippet>172</a>    <span class="kw">pub </span>health_rate: f64,
<a href=#173 id=173 data-nosnippet>173</a>}
<a href=#174 id=174 data-nosnippet>174</a>
<a href=#175 id=175 data-nosnippet>175</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#176 id=176 data-nosnippet>176</a></span><span class="kw">pub struct </span>AccountPoolStatus {
<a href=#177 id=177 data-nosnippet>177</a>    <span class="kw">pub </span>total_accounts: i64,
<a href=#178 id=178 data-nosnippet>178</a>    <span class="kw">pub </span>normal_accounts: i64,
<a href=#179 id=179 data-nosnippet>179</a>    <span class="kw">pub </span>banned_accounts: i64,
<a href=#180 id=180 data-nosnippet>180</a>    <span class="kw">pub </span>abnormal_accounts: i64,
<a href=#181 id=181 data-nosnippet>181</a>    <span class="kw">pub </span>maintenance_accounts: i64,
<a href=#182 id=182 data-nosnippet>182</a>    <span class="kw">pub </span>recently_active: i64,
<a href=#183 id=183 data-nosnippet>183</a>    <span class="kw">pub </span>avg_risk_score: <span class="prelude-ty">Option</span>&lt;f64&gt;,
<a href=#184 id=184 data-nosnippet>184</a>    <span class="kw">pub </span>high_risk_accounts: i64,
<a href=#185 id=185 data-nosnippet>185</a>}
<a href=#186 id=186 data-nosnippet>186</a>
<a href=#187 id=187 data-nosnippet>187</a><span class="kw">pub struct </span>StorageManager {
<a href=#188 id=188 data-nosnippet>188</a>    pool: SqlitePool,
<a href=#189 id=189 data-nosnippet>189</a>}
<a href=#190 id=190 data-nosnippet>190</a>
<a href=#191 id=191 data-nosnippet>191</a><span class="kw">impl </span>StorageManager {
<a href=#192 id=192 data-nosnippet>192</a>    <span class="kw">pub async fn </span>new(database_url: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>&gt; {
<a href=#193 id=193 data-nosnippet>193</a>        <span class="macro">info!</span>(<span class="string">"正在初始化数据库: {}"</span>, database_url);
<a href=#194 id=194 data-nosnippet>194</a>
<a href=#195 id=195 data-nosnippet>195</a>        <span class="comment">// 确保数据目录存在
<a href=#196 id=196 data-nosnippet>196</a>        </span><span class="kw">let </span>db_path = database_url.trim_start_matches(<span class="string">"sqlite:"</span>);
<a href=#197 id=197 data-nosnippet>197</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(parent) = Path::new(db_path).parent() {
<a href=#198 id=198 data-nosnippet>198</a>            <span class="macro">info!</span>(<span class="string">"创建数据库目录: {:?}"</span>, parent);
<a href=#199 id=199 data-nosnippet>199</a>            tokio::fs::create_dir_all(parent).<span class="kw">await
<a href=#200 id=200 data-nosnippet>200</a>                </span>.map_err(|e| {
<a href=#201 id=201 data-nosnippet>201</a>                    <span class="macro">error!</span>(<span class="string">"无法创建数据库目录 {:?}: {}"</span>, parent, e);
<a href=#202 id=202 data-nosnippet>202</a>                    AppError::Database(sqlx::Error::Io(e))
<a href=#203 id=203 data-nosnippet>203</a>                })<span class="question-mark">?</span>;
<a href=#204 id=204 data-nosnippet>204</a>        }
<a href=#205 id=205 data-nosnippet>205</a>
<a href=#206 id=206 data-nosnippet>206</a>        <span class="comment">// 检查数据库文件是否存在，如果不存在则创建
<a href=#207 id=207 data-nosnippet>207</a>        </span><span class="kw">if </span>!Path::new(db_path).exists() {
<a href=#208 id=208 data-nosnippet>208</a>            <span class="macro">info!</span>(<span class="string">"数据库文件不存在，正在创建: {}"</span>, db_path);
<a href=#209 id=209 data-nosnippet>209</a>            tokio::fs::File::create(db_path).<span class="kw">await
<a href=#210 id=210 data-nosnippet>210</a>                </span>.map_err(|e| {
<a href=#211 id=211 data-nosnippet>211</a>                    <span class="macro">error!</span>(<span class="string">"无法创建数据库文件 {}: {}"</span>, db_path, e);
<a href=#212 id=212 data-nosnippet>212</a>                    AppError::Database(sqlx::Error::Io(e))
<a href=#213 id=213 data-nosnippet>213</a>                })<span class="question-mark">?</span>;
<a href=#214 id=214 data-nosnippet>214</a>        }
<a href=#215 id=215 data-nosnippet>215</a>
<a href=#216 id=216 data-nosnippet>216</a>        <span class="macro">info!</span>(<span class="string">"正在连接数据库..."</span>);
<a href=#217 id=217 data-nosnippet>217</a>        <span class="kw">let </span>pool = SqlitePool::connect(database_url).<span class="kw">await
<a href=#218 id=218 data-nosnippet>218</a>            </span>.map_err(|e| {
<a href=#219 id=219 data-nosnippet>219</a>                <span class="macro">error!</span>(<span class="string">"数据库连接失败: {}"</span>, e);
<a href=#220 id=220 data-nosnippet>220</a>                AppError::Database(e)
<a href=#221 id=221 data-nosnippet>221</a>            })<span class="question-mark">?</span>;
<a href=#222 id=222 data-nosnippet>222</a>
<a href=#223 id=223 data-nosnippet>223</a>        <span class="macro">info!</span>(<span class="string">"正在运行数据库迁移..."</span>);
<a href=#224 id=224 data-nosnippet>224</a>        <span class="comment">// 使用SQLx迁移系统
<a href=#225 id=225 data-nosnippet>225</a>        </span><span class="macro">sqlx::migrate!</span>(<span class="string">"./migrations"</span>).run(<span class="kw-2">&amp;</span>pool).<span class="kw">await
<a href=#226 id=226 data-nosnippet>226</a>            </span>.map_err(|e| {
<a href=#227 id=227 data-nosnippet>227</a>                <span class="macro">error!</span>(<span class="string">"数据库迁移失败: {}"</span>, e);
<a href=#228 id=228 data-nosnippet>228</a>                AppError::Migration(<span class="macro">format!</span>(<span class="string">"Migration failed: {}"</span>, e))
<a href=#229 id=229 data-nosnippet>229</a>            })<span class="question-mark">?</span>;
<a href=#230 id=230 data-nosnippet>230</a>
<a href=#231 id=231 data-nosnippet>231</a>        <span class="macro">info!</span>(<span class="string">"数据库初始化完成: {}"</span>, database_url);
<a href=#232 id=232 data-nosnippet>232</a>
<a href=#233 id=233 data-nosnippet>233</a>        <span class="prelude-val">Ok</span>(<span class="self">Self </span>{ pool })
<a href=#234 id=234 data-nosnippet>234</a>    }
<a href=#235 id=235 data-nosnippet>235</a>
<a href=#236 id=236 data-nosnippet>236</a>    <span class="comment">// 任务结果相关方法 (暂时注释，等待迁移完成)
<a href=#237 id=237 data-nosnippet>237</a>    /*
<a href=#238 id=238 data-nosnippet>238</a>    pub async fn insert_task_result(&amp;self, result: &amp;TaskResult) -&gt; Result&lt;i64&gt; {
<a href=#239 id=239 data-nosnippet>239</a>        let id = sqlx::query!(
<a href=#240 id=240 data-nosnippet>240</a>            r#"
<a href=#241 id=241 data-nosnippet>241</a>            INSERT INTO task_results (
<a href=#242 id=242 data-nosnippet>242</a>                task_id, result_type, result_data, error_message, error_code,
<a href=#243 id=243 data-nosnippet>243</a>                execution_time, data_count, proxy_used, account_used
<a href=#244 id=244 data-nosnippet>244</a>            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
<a href=#245 id=245 data-nosnippet>245</a>            "#,
<a href=#246 id=246 data-nosnippet>246</a>            result.task_id,
<a href=#247 id=247 data-nosnippet>247</a>            result.result_type,
<a href=#248 id=248 data-nosnippet>248</a>            result.result_data,
<a href=#249 id=249 data-nosnippet>249</a>            result.error_message,
<a href=#250 id=250 data-nosnippet>250</a>            result.error_code,
<a href=#251 id=251 data-nosnippet>251</a>            result.execution_time,
<a href=#252 id=252 data-nosnippet>252</a>            result.data_count,
<a href=#253 id=253 data-nosnippet>253</a>            result.proxy_used,
<a href=#254 id=254 data-nosnippet>254</a>            result.account_used
<a href=#255 id=255 data-nosnippet>255</a>        )
<a href=#256 id=256 data-nosnippet>256</a>        .execute(&amp;self.pool)
<a href=#257 id=257 data-nosnippet>257</a>        .await
<a href=#258 id=258 data-nosnippet>258</a>        .map_err(AppError::Database)?
<a href=#259 id=259 data-nosnippet>259</a>        .last_insert_rowid();
<a href=#260 id=260 data-nosnippet>260</a>
<a href=#261 id=261 data-nosnippet>261</a>        Ok(id)
<a href=#262 id=262 data-nosnippet>262</a>    }
<a href=#263 id=263 data-nosnippet>263</a>    */
<a href=#264 id=264 data-nosnippet>264</a>
<a href=#265 id=265 data-nosnippet>265</a>    // 新增数据库访问方法 (暂时注释，等待类型匹配问题解决)
<a href=#266 id=266 data-nosnippet>266</a>    /*
<a href=#267 id=267 data-nosnippet>267</a>    pub async fn get_task_results_by_task_id(&amp;self, task_id: &amp;str) -&gt; Result&lt;Vec&lt;TaskResult&gt;&gt; {
<a href=#268 id=268 data-nosnippet>268</a>        let results = sqlx::query_as!(
<a href=#269 id=269 data-nosnippet>269</a>            TaskResult,
<a href=#270 id=270 data-nosnippet>270</a>            r#"
<a href=#271 id=271 data-nosnippet>271</a>            SELECT id, task_id, result_type, result_data, error_message,
<a href=#272 id=272 data-nosnippet>272</a>                   CAST(NULL AS TEXT) as error_code,
<a href=#273 id=273 data-nosnippet>273</a>                   execution_time, data_count, proxy_used, account_used, created_at
<a href=#274 id=274 data-nosnippet>274</a>            FROM task_results
<a href=#275 id=275 data-nosnippet>275</a>            WHERE task_id = ?
<a href=#276 id=276 data-nosnippet>276</a>            ORDER BY created_at DESC
<a href=#277 id=277 data-nosnippet>277</a>            "#,
<a href=#278 id=278 data-nosnippet>278</a>            task_id
<a href=#279 id=279 data-nosnippet>279</a>        )
<a href=#280 id=280 data-nosnippet>280</a>        .fetch_all(&amp;self.pool)
<a href=#281 id=281 data-nosnippet>281</a>        .await
<a href=#282 id=282 data-nosnippet>282</a>        .map_err(AppError::Database)?;
<a href=#283 id=283 data-nosnippet>283</a>
<a href=#284 id=284 data-nosnippet>284</a>        Ok(results)
<a href=#285 id=285 data-nosnippet>285</a>    }
<a href=#286 id=286 data-nosnippet>286</a>
<a href=#287 id=287 data-nosnippet>287</a>    // 任务日志相关方法
<a href=#288 id=288 data-nosnippet>288</a>    pub async fn insert_task_log(&amp;self, log: &amp;TaskLog) -&gt; Result&lt;i64&gt; {
<a href=#289 id=289 data-nosnippet>289</a>        let id = sqlx::query!(
<a href=#290 id=290 data-nosnippet>290</a>            r#"
<a href=#291 id=291 data-nosnippet>291</a>            INSERT INTO task_logs (task_id, log_level, log_message, log_context)
<a href=#292 id=292 data-nosnippet>292</a>            VALUES (?, ?, ?, ?)
<a href=#293 id=293 data-nosnippet>293</a>            "#,
<a href=#294 id=294 data-nosnippet>294</a>            log.task_id,
<a href=#295 id=295 data-nosnippet>295</a>            log.log_level,
<a href=#296 id=296 data-nosnippet>296</a>            log.log_message,
<a href=#297 id=297 data-nosnippet>297</a>            log.log_context
<a href=#298 id=298 data-nosnippet>298</a>        )
<a href=#299 id=299 data-nosnippet>299</a>        .execute(&amp;self.pool)
<a href=#300 id=300 data-nosnippet>300</a>        .await
<a href=#301 id=301 data-nosnippet>301</a>        .map_err(AppError::Database)?
<a href=#302 id=302 data-nosnippet>302</a>        .last_insert_rowid();
<a href=#303 id=303 data-nosnippet>303</a>
<a href=#304 id=304 data-nosnippet>304</a>        Ok(id)
<a href=#305 id=305 data-nosnippet>305</a>    }
<a href=#306 id=306 data-nosnippet>306</a>
<a href=#307 id=307 data-nosnippet>307</a>    pub async fn get_task_logs(&amp;self, task_id: &amp;str, limit: Option&lt;i32&gt;) -&gt; Result&lt;Vec&lt;TaskLog&gt;&gt; {
<a href=#308 id=308 data-nosnippet>308</a>        let limit = limit.unwrap_or(100);
<a href=#309 id=309 data-nosnippet>309</a>        let logs = sqlx::query_as!(
<a href=#310 id=310 data-nosnippet>310</a>            TaskLog,
<a href=#311 id=311 data-nosnippet>311</a>            r#"
<a href=#312 id=312 data-nosnippet>312</a>            SELECT id, task_id, log_level, log_message, log_context, created_at
<a href=#313 id=313 data-nosnippet>313</a>            FROM task_logs
<a href=#314 id=314 data-nosnippet>314</a>            WHERE task_id = ?
<a href=#315 id=315 data-nosnippet>315</a>            ORDER BY created_at DESC
<a href=#316 id=316 data-nosnippet>316</a>            LIMIT ?
<a href=#317 id=317 data-nosnippet>317</a>            "#,
<a href=#318 id=318 data-nosnippet>318</a>            task_id,
<a href=#319 id=319 data-nosnippet>319</a>            limit
<a href=#320 id=320 data-nosnippet>320</a>        )
<a href=#321 id=321 data-nosnippet>321</a>        .fetch_all(&amp;self.pool)
<a href=#322 id=322 data-nosnippet>322</a>        .await
<a href=#323 id=323 data-nosnippet>323</a>        .map_err(AppError::Database)?;
<a href=#324 id=324 data-nosnippet>324</a>
<a href=#325 id=325 data-nosnippet>325</a>        Ok(logs)
<a href=#326 id=326 data-nosnippet>326</a>    }
<a href=#327 id=327 data-nosnippet>327</a>
<a href=#328 id=328 data-nosnippet>328</a>    // 代理使用统计相关方法
<a href=#329 id=329 data-nosnippet>329</a>    pub async fn insert_proxy_usage(&amp;self, usage: &amp;ProxyUsage) -&gt; Result&lt;i64&gt; {
<a href=#330 id=330 data-nosnippet>330</a>        let id = sqlx::query!(
<a href=#331 id=331 data-nosnippet>331</a>            r#"
<a href=#332 id=332 data-nosnippet>332</a>            INSERT INTO proxy_usage (
<a href=#333 id=333 data-nosnippet>333</a>                proxy_id, task_id, usage_type, success, response_time,
<a href=#334 id=334 data-nosnippet>334</a>                bytes_transferred, error_message
<a href=#335 id=335 data-nosnippet>335</a>            ) VALUES (?, ?, ?, ?, ?, ?, ?)
<a href=#336 id=336 data-nosnippet>336</a>            "#,
<a href=#337 id=337 data-nosnippet>337</a>            usage.proxy_id,
<a href=#338 id=338 data-nosnippet>338</a>            usage.task_id,
<a href=#339 id=339 data-nosnippet>339</a>            usage.usage_type,
<a href=#340 id=340 data-nosnippet>340</a>            usage.success,
<a href=#341 id=341 data-nosnippet>341</a>            usage.response_time,
<a href=#342 id=342 data-nosnippet>342</a>            usage.bytes_transferred,
<a href=#343 id=343 data-nosnippet>343</a>            usage.error_message
<a href=#344 id=344 data-nosnippet>344</a>        )
<a href=#345 id=345 data-nosnippet>345</a>        .execute(&amp;self.pool)
<a href=#346 id=346 data-nosnippet>346</a>        .await
<a href=#347 id=347 data-nosnippet>347</a>        .map_err(AppError::Database)?
<a href=#348 id=348 data-nosnippet>348</a>        .last_insert_rowid();
<a href=#349 id=349 data-nosnippet>349</a>
<a href=#350 id=350 data-nosnippet>350</a>        Ok(id)
<a href=#351 id=351 data-nosnippet>351</a>    }
<a href=#352 id=352 data-nosnippet>352</a>
<a href=#353 id=353 data-nosnippet>353</a>    // 账号日志相关方法
<a href=#354 id=354 data-nosnippet>354</a>    pub async fn insert_account_log(&amp;self, log: &amp;AccountLog) -&gt; Result&lt;i64&gt; {
<a href=#355 id=355 data-nosnippet>355</a>        let id = sqlx::query!(
<a href=#356 id=356 data-nosnippet>356</a>            r#"
<a href=#357 id=357 data-nosnippet>357</a>            INSERT INTO account_logs (
<a href=#358 id=358 data-nosnippet>358</a>                account_id, operation_type, operation_result, operation_details,
<a href=#359 id=359 data-nosnippet>359</a>                ip_address, user_agent, proxy_used, error_message
<a href=#360 id=360 data-nosnippet>360</a>            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
<a href=#361 id=361 data-nosnippet>361</a>            "#,
<a href=#362 id=362 data-nosnippet>362</a>            log.account_id,
<a href=#363 id=363 data-nosnippet>363</a>            log.operation_type,
<a href=#364 id=364 data-nosnippet>364</a>            log.operation_result,
<a href=#365 id=365 data-nosnippet>365</a>            log.operation_details,
<a href=#366 id=366 data-nosnippet>366</a>            log.ip_address,
<a href=#367 id=367 data-nosnippet>367</a>            log.user_agent,
<a href=#368 id=368 data-nosnippet>368</a>            log.proxy_used,
<a href=#369 id=369 data-nosnippet>369</a>            log.error_message
<a href=#370 id=370 data-nosnippet>370</a>        )
<a href=#371 id=371 data-nosnippet>371</a>        .execute(&amp;self.pool)
<a href=#372 id=372 data-nosnippet>372</a>        .await
<a href=#373 id=373 data-nosnippet>373</a>        .map_err(AppError::Database)?
<a href=#374 id=374 data-nosnippet>374</a>        .last_insert_rowid();
<a href=#375 id=375 data-nosnippet>375</a>
<a href=#376 id=376 data-nosnippet>376</a>        Ok(id)
<a href=#377 id=377 data-nosnippet>377</a>    }
<a href=#378 id=378 data-nosnippet>378</a>
<a href=#379 id=379 data-nosnippet>379</a>    // 系统监控相关方法
<a href=#380 id=380 data-nosnippet>380</a>    pub async fn insert_system_metrics(&amp;self, metrics: &amp;SystemMetrics) -&gt; Result&lt;i64&gt; {
<a href=#381 id=381 data-nosnippet>381</a>        let id = sqlx::query!(
<a href=#382 id=382 data-nosnippet>382</a>            r#"
<a href=#383 id=383 data-nosnippet>383</a>            INSERT INTO system_metrics (
<a href=#384 id=384 data-nosnippet>384</a>                node_id, cpu_usage, memory_usage, memory_total, memory_used,
<a href=#385 id=385 data-nosnippet>385</a>                disk_usage, disk_total, disk_used, network_in, network_out, active_connections
<a href=#386 id=386 data-nosnippet>386</a>            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
<a href=#387 id=387 data-nosnippet>387</a>            "#,
<a href=#388 id=388 data-nosnippet>388</a>            metrics.node_id,
<a href=#389 id=389 data-nosnippet>389</a>            metrics.cpu_usage,
<a href=#390 id=390 data-nosnippet>390</a>            metrics.memory_usage,
<a href=#391 id=391 data-nosnippet>391</a>            metrics.memory_total,
<a href=#392 id=392 data-nosnippet>392</a>            metrics.memory_used,
<a href=#393 id=393 data-nosnippet>393</a>            metrics.disk_usage,
<a href=#394 id=394 data-nosnippet>394</a>            metrics.disk_total,
<a href=#395 id=395 data-nosnippet>395</a>            metrics.disk_used,
<a href=#396 id=396 data-nosnippet>396</a>            metrics.network_in,
<a href=#397 id=397 data-nosnippet>397</a>            metrics.network_out,
<a href=#398 id=398 data-nosnippet>398</a>            metrics.active_connections
<a href=#399 id=399 data-nosnippet>399</a>        )
<a href=#400 id=400 data-nosnippet>400</a>        .execute(&amp;self.pool)
<a href=#401 id=401 data-nosnippet>401</a>        .await
<a href=#402 id=402 data-nosnippet>402</a>        .map_err(AppError::Database)?
<a href=#403 id=403 data-nosnippet>403</a>        .last_insert_rowid();
<a href=#404 id=404 data-nosnippet>404</a>
<a href=#405 id=405 data-nosnippet>405</a>        Ok(id)
<a href=#406 id=406 data-nosnippet>406</a>    }
<a href=#407 id=407 data-nosnippet>407</a>
<a href=#408 id=408 data-nosnippet>408</a>    pub async fn get_latest_system_metrics(&amp;self, node_id: &amp;str) -&gt; Result&lt;Option&lt;SystemMetrics&gt;&gt; {
<a href=#409 id=409 data-nosnippet>409</a>        let metrics = sqlx::query_as!(
<a href=#410 id=410 data-nosnippet>410</a>            SystemMetrics,
<a href=#411 id=411 data-nosnippet>411</a>            r#"
<a href=#412 id=412 data-nosnippet>412</a>            SELECT id, node_id, cpu_usage, memory_usage, memory_total, memory_used,
<a href=#413 id=413 data-nosnippet>413</a>                   disk_usage, disk_total, disk_used, network_in, network_out,
<a href=#414 id=414 data-nosnippet>414</a>                   active_connections, recorded_at
<a href=#415 id=415 data-nosnippet>415</a>            FROM system_metrics
<a href=#416 id=416 data-nosnippet>416</a>            WHERE node_id = ?
<a href=#417 id=417 data-nosnippet>417</a>            ORDER BY recorded_at DESC
<a href=#418 id=418 data-nosnippet>418</a>            LIMIT 1
<a href=#419 id=419 data-nosnippet>419</a>            "#,
<a href=#420 id=420 data-nosnippet>420</a>            node_id
<a href=#421 id=421 data-nosnippet>421</a>        )
<a href=#422 id=422 data-nosnippet>422</a>        .fetch_optional(&amp;self.pool)
<a href=#423 id=423 data-nosnippet>423</a>        .await
<a href=#424 id=424 data-nosnippet>424</a>        .map_err(AppError::Database)?;
<a href=#425 id=425 data-nosnippet>425</a>
<a href=#426 id=426 data-nosnippet>426</a>        Ok(metrics)
<a href=#427 id=427 data-nosnippet>427</a>    }
<a href=#428 id=428 data-nosnippet>428</a>
<a href=#429 id=429 data-nosnippet>429</a>    // 错误日志相关方法
<a href=#430 id=430 data-nosnippet>430</a>    pub async fn insert_error_log(&amp;self, log: &amp;ErrorLog) -&gt; Result&lt;i64&gt; {
<a href=#431 id=431 data-nosnippet>431</a>        let id = sqlx::query!(
<a href=#432 id=432 data-nosnippet>432</a>            r#"
<a href=#433 id=433 data-nosnippet>433</a>            INSERT INTO error_logs (
<a href=#434 id=434 data-nosnippet>434</a>                error_type, error_level, error_code, error_message, error_context,
<a href=#435 id=435 data-nosnippet>435</a>                stack_trace, component, task_id, account_id, proxy_id
<a href=#436 id=436 data-nosnippet>436</a>            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
<a href=#437 id=437 data-nosnippet>437</a>            "#,
<a href=#438 id=438 data-nosnippet>438</a>            log.error_type,
<a href=#439 id=439 data-nosnippet>439</a>            log.error_level,
<a href=#440 id=440 data-nosnippet>440</a>            log.error_code,
<a href=#441 id=441 data-nosnippet>441</a>            log.error_message,
<a href=#442 id=442 data-nosnippet>442</a>            log.error_context,
<a href=#443 id=443 data-nosnippet>443</a>            log.stack_trace,
<a href=#444 id=444 data-nosnippet>444</a>            log.component,
<a href=#445 id=445 data-nosnippet>445</a>            log.task_id,
<a href=#446 id=446 data-nosnippet>446</a>            log.account_id,
<a href=#447 id=447 data-nosnippet>447</a>            log.proxy_id
<a href=#448 id=448 data-nosnippet>448</a>        )
<a href=#449 id=449 data-nosnippet>449</a>        .execute(&amp;self.pool)
<a href=#450 id=450 data-nosnippet>450</a>        .await
<a href=#451 id=451 data-nosnippet>451</a>        .map_err(AppError::Database)?
<a href=#452 id=452 data-nosnippet>452</a>        .last_insert_rowid();
<a href=#453 id=453 data-nosnippet>453</a>
<a href=#454 id=454 data-nosnippet>454</a>        Ok(id)
<a href=#455 id=455 data-nosnippet>455</a>    }
<a href=#456 id=456 data-nosnippet>456</a>    */
<a href=#457 id=457 data-nosnippet>457</a>
<a href=#458 id=458 data-nosnippet>458</a>    // 统计查询方法 (暂时注释，等待迁移完成)
<a href=#459 id=459 data-nosnippet>459</a>    /*
<a href=#460 id=460 data-nosnippet>460</a>    pub async fn get_task_statistics(&amp;self) -&gt; Result&lt;TaskStatistics&gt; {
<a href=#461 id=461 data-nosnippet>461</a>        let row = sqlx::query!(
<a href=#462 id=462 data-nosnippet>462</a>            r#"
<a href=#463 id=463 data-nosnippet>463</a>            SELECT
<a href=#464 id=464 data-nosnippet>464</a>                COUNT(*) as total_tasks,
<a href=#465 id=465 data-nosnippet>465</a>                COUNT(CASE WHEN status = 0 THEN 1 END) as pending_tasks,
<a href=#466 id=466 data-nosnippet>466</a>                COUNT(CASE WHEN status = 1 THEN 1 END) as running_tasks,
<a href=#467 id=467 data-nosnippet>467</a>                COUNT(CASE WHEN status = 2 THEN 1 END) as completed_tasks,
<a href=#468 id=468 data-nosnippet>468</a>                COUNT(CASE WHEN status = 3 THEN 1 END) as failed_tasks,
<a href=#469 id=469 data-nosnippet>469</a>                CAST(COUNT(CASE WHEN status = 2 THEN 1 END) AS REAL) /
<a href=#470 id=470 data-nosnippet>470</a>                NULLIF(COUNT(CASE WHEN status IN (2, 3) THEN 1 END), 0) * 100 as success_rate
<a href=#471 id=471 data-nosnippet>471</a>            FROM crawl_tasks
<a href=#472 id=472 data-nosnippet>472</a>            "#
<a href=#473 id=473 data-nosnippet>473</a>        )
<a href=#474 id=474 data-nosnippet>474</a>        .fetch_one(&amp;self.pool)
<a href=#475 id=475 data-nosnippet>475</a>        .await
<a href=#476 id=476 data-nosnippet>476</a>        .map_err(AppError::Database)?;
<a href=#477 id=477 data-nosnippet>477</a>
<a href=#478 id=478 data-nosnippet>478</a>        // 获取平均执行时间
<a href=#479 id=479 data-nosnippet>479</a>        let avg_time_row = sqlx::query!(
<a href=#480 id=480 data-nosnippet>480</a>            r#"
<a href=#481 id=481 data-nosnippet>481</a>            SELECT AVG(execution_time) as avg_execution_time
<a href=#482 id=482 data-nosnippet>482</a>            FROM task_results
<a href=#483 id=483 data-nosnippet>483</a>            WHERE execution_time IS NOT NULL
<a href=#484 id=484 data-nosnippet>484</a>            "#
<a href=#485 id=485 data-nosnippet>485</a>        )
<a href=#486 id=486 data-nosnippet>486</a>        .fetch_one(&amp;self.pool)
<a href=#487 id=487 data-nosnippet>487</a>        .await
<a href=#488 id=488 data-nosnippet>488</a>        .map_err(AppError::Database)?;
<a href=#489 id=489 data-nosnippet>489</a>
<a href=#490 id=490 data-nosnippet>490</a>        Ok(TaskStatistics {
<a href=#491 id=491 data-nosnippet>491</a>            total_tasks: row.total_tasks,
<a href=#492 id=492 data-nosnippet>492</a>            pending_tasks: row.pending_tasks.unwrap_or(0),
<a href=#493 id=493 data-nosnippet>493</a>            running_tasks: row.running_tasks.unwrap_or(0),
<a href=#494 id=494 data-nosnippet>494</a>            completed_tasks: row.completed_tasks.unwrap_or(0),
<a href=#495 id=495 data-nosnippet>495</a>            failed_tasks: row.failed_tasks.unwrap_or(0),
<a href=#496 id=496 data-nosnippet>496</a>            success_rate: row.success_rate.unwrap_or(0.0),
<a href=#497 id=497 data-nosnippet>497</a>            avg_execution_time: avg_time_row.avg_execution_time,
<a href=#498 id=498 data-nosnippet>498</a>        })
<a href=#499 id=499 data-nosnippet>499</a>    }
<a href=#500 id=500 data-nosnippet>500</a>
<a href=#501 id=501 data-nosnippet>501</a>    pub async fn get_proxy_pool_status(&amp;self) -&gt; Result&lt;ProxyPoolStatus&gt; {
<a href=#502 id=502 data-nosnippet>502</a>        let row = sqlx::query!(
<a href=#503 id=503 data-nosnippet>503</a>            r#"
<a href=#504 id=504 data-nosnippet>504</a>            SELECT
<a href=#505 id=505 data-nosnippet>505</a>                COUNT(*) as total_proxies,
<a href=#506 id=506 data-nosnippet>506</a>                COUNT(CASE WHEN status = 1 THEN 1 END) as active_proxies,
<a href=#507 id=507 data-nosnippet>507</a>                COUNT(CASE WHEN status = 2 THEN 1 END) as unavailable_proxies,
<a href=#508 id=508 data-nosnippet>508</a>                COUNT(CASE WHEN status = 3 THEN 1 END) as maintenance_proxies,
<a href=#509 id=509 data-nosnippet>509</a>                COUNT(CASE WHEN status = 4 THEN 1 END) as testing_proxies,
<a href=#510 id=510 data-nosnippet>510</a>                AVG(response_time) as avg_response_time,
<a href=#511 id=511 data-nosnippet>511</a>                CAST(COUNT(CASE WHEN status = 1 THEN 1 END) AS REAL) /
<a href=#512 id=512 data-nosnippet>512</a>                NULLIF(COUNT(*), 0) * 100 as health_rate
<a href=#513 id=513 data-nosnippet>513</a>            FROM proxies
<a href=#514 id=514 data-nosnippet>514</a>            "#
<a href=#515 id=515 data-nosnippet>515</a>        )
<a href=#516 id=516 data-nosnippet>516</a>        .fetch_one(&amp;self.pool)
<a href=#517 id=517 data-nosnippet>517</a>        .await
<a href=#518 id=518 data-nosnippet>518</a>        .map_err(AppError::Database)?;
<a href=#519 id=519 data-nosnippet>519</a>
<a href=#520 id=520 data-nosnippet>520</a>        Ok(ProxyPoolStatus {
<a href=#521 id=521 data-nosnippet>521</a>            total_proxies: row.total_proxies,
<a href=#522 id=522 data-nosnippet>522</a>            active_proxies: row.active_proxies.unwrap_or(0),
<a href=#523 id=523 data-nosnippet>523</a>            unavailable_proxies: row.unavailable_proxies.unwrap_or(0),
<a href=#524 id=524 data-nosnippet>524</a>            maintenance_proxies: row.maintenance_proxies.unwrap_or(0),
<a href=#525 id=525 data-nosnippet>525</a>            testing_proxies: row.testing_proxies.unwrap_or(0),
<a href=#526 id=526 data-nosnippet>526</a>            avg_response_time: row.avg_response_time,
<a href=#527 id=527 data-nosnippet>527</a>            health_rate: row.health_rate.unwrap_or(0.0),
<a href=#528 id=528 data-nosnippet>528</a>        })
<a href=#529 id=529 data-nosnippet>529</a>    }
<a href=#530 id=530 data-nosnippet>530</a>
<a href=#531 id=531 data-nosnippet>531</a>    pub async fn get_account_pool_status(&amp;self) -&gt; Result&lt;AccountPoolStatus&gt; {
<a href=#532 id=532 data-nosnippet>532</a>        let row = sqlx::query!(
<a href=#533 id=533 data-nosnippet>533</a>            r#"
<a href=#534 id=534 data-nosnippet>534</a>            SELECT
<a href=#535 id=535 data-nosnippet>535</a>                COUNT(*) as total_accounts,
<a href=#536 id=536 data-nosnippet>536</a>                COUNT(CASE WHEN status = 1 THEN 1 END) as normal_accounts,
<a href=#537 id=537 data-nosnippet>537</a>                COUNT(CASE WHEN status = 2 THEN 1 END) as banned_accounts,
<a href=#538 id=538 data-nosnippet>538</a>                COUNT(CASE WHEN status = 3 THEN 1 END) as abnormal_accounts,
<a href=#539 id=539 data-nosnippet>539</a>                COUNT(CASE WHEN status = 4 THEN 1 END) as maintenance_accounts,
<a href=#540 id=540 data-nosnippet>540</a>                COUNT(CASE WHEN last_login &gt; datetime('now', '-24 hours') THEN 1 END) as recently_active,
<a href=#541 id=541 data-nosnippet>541</a>                AVG(risk_score) as avg_risk_score,
<a href=#542 id=542 data-nosnippet>542</a>                COUNT(CASE WHEN risk_score &gt; 0.7 THEN 1 END) as high_risk_accounts
<a href=#543 id=543 data-nosnippet>543</a>            FROM accounts
<a href=#544 id=544 data-nosnippet>544</a>            "#
<a href=#545 id=545 data-nosnippet>545</a>        )
<a href=#546 id=546 data-nosnippet>546</a>        .fetch_one(&amp;self.pool)
<a href=#547 id=547 data-nosnippet>547</a>        .await
<a href=#548 id=548 data-nosnippet>548</a>        .map_err(AppError::Database)?;
<a href=#549 id=549 data-nosnippet>549</a>
<a href=#550 id=550 data-nosnippet>550</a>        Ok(AccountPoolStatus {
<a href=#551 id=551 data-nosnippet>551</a>            total_accounts: row.total_accounts,
<a href=#552 id=552 data-nosnippet>552</a>            normal_accounts: row.normal_accounts.unwrap_or(0),
<a href=#553 id=553 data-nosnippet>553</a>            banned_accounts: row.banned_accounts.unwrap_or(0),
<a href=#554 id=554 data-nosnippet>554</a>            abnormal_accounts: row.abnormal_accounts.unwrap_or(0),
<a href=#555 id=555 data-nosnippet>555</a>            maintenance_accounts: row.maintenance_accounts.unwrap_or(0),
<a href=#556 id=556 data-nosnippet>556</a>            recently_active: row.recently_active.unwrap_or(0),
<a href=#557 id=557 data-nosnippet>557</a>            avg_risk_score: row.avg_risk_score,
<a href=#558 id=558 data-nosnippet>558</a>            high_risk_accounts: row.high_risk_accounts.unwrap_or(0),
<a href=#559 id=559 data-nosnippet>559</a>        })
<a href=#560 id=560 data-nosnippet>560</a>    }
<a href=#561 id=561 data-nosnippet>561</a>    */
<a href=#562 id=562 data-nosnippet>562</a>
<a href=#563 id=563 data-nosnippet>563</a>    // 任务相关操作
<a href=#564 id=564 data-nosnippet>564</a>    </span><span class="kw">pub async fn </span>save_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task: <span class="kw-2">&amp;</span><span class="kw">crate</span>::crawler::CrawlTask) -&gt; <span class="prelude-ty">Result</span>&lt;i64&gt; {
<a href=#565 id=565 data-nosnippet>565</a>        <span class="kw">let </span>task_type = <span class="kw">match </span>task.task_type {
<a href=#566 id=566 data-nosnippet>566</a>            <span class="kw">crate</span>::crawler::TaskType::User =&gt; <span class="number">1</span>,
<a href=#567 id=567 data-nosnippet>567</a>            <span class="kw">crate</span>::crawler::TaskType::Post =&gt; <span class="number">2</span>,
<a href=#568 id=568 data-nosnippet>568</a>            <span class="kw">crate</span>::crawler::TaskType::Comment =&gt; <span class="number">3</span>,
<a href=#569 id=569 data-nosnippet>569</a>            <span class="kw">crate</span>::crawler::TaskType::Topic =&gt; <span class="number">4</span>,
<a href=#570 id=570 data-nosnippet>570</a>        };
<a href=#571 id=571 data-nosnippet>571</a>
<a href=#572 id=572 data-nosnippet>572</a>        <span class="kw">let </span>metadata = serde_json::to_string(<span class="kw-2">&amp;</span>task.metadata)
<a href=#573 id=573 data-nosnippet>573</a>            .map_err(|e| AppError::Serialization(e))<span class="question-mark">?</span>;
<a href=#574 id=574 data-nosnippet>574</a>
<a href=#575 id=575 data-nosnippet>575</a>        <span class="kw">let </span>result = <span class="macro">sqlx::query!</span>(
<a href=#576 id=576 data-nosnippet>576</a>            <span class="string">r#"
<a href=#577 id=577 data-nosnippet>577</a>            INSERT INTO crawl_tasks (task_id, task_type, target_url, priority, status, retry_count, max_retries, metadata)
<a href=#578 id=578 data-nosnippet>578</a>            VALUES (?1, ?2, ?3, ?4, 0, ?5, ?6, ?7)
<a href=#579 id=579 data-nosnippet>579</a>            "#</span>,
<a href=#580 id=580 data-nosnippet>580</a>            task.id,
<a href=#581 id=581 data-nosnippet>581</a>            task_type,
<a href=#582 id=582 data-nosnippet>582</a>            task.target_url,
<a href=#583 id=583 data-nosnippet>583</a>            task.priority,
<a href=#584 id=584 data-nosnippet>584</a>            task.retry_count,
<a href=#585 id=585 data-nosnippet>585</a>            task.max_retries,
<a href=#586 id=586 data-nosnippet>586</a>            metadata
<a href=#587 id=587 data-nosnippet>587</a>        )
<a href=#588 id=588 data-nosnippet>588</a>        .execute(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#589 id=589 data-nosnippet>589</a>        .<span class="kw">await
<a href=#590 id=590 data-nosnippet>590</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#591 id=591 data-nosnippet>591</a>
<a href=#592 id=592 data-nosnippet>592</a>        <span class="prelude-val">Ok</span>(result.last_insert_rowid())
<a href=#593 id=593 data-nosnippet>593</a>    }
<a href=#594 id=594 data-nosnippet>594</a>
<a href=#595 id=595 data-nosnippet>595</a>    <span class="kw">pub async fn </span>update_task_status(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str, status: i32) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#596 id=596 data-nosnippet>596</a>        <span class="macro">sqlx::query!</span>(
<a href=#597 id=597 data-nosnippet>597</a>            <span class="string">"UPDATE crawl_tasks SET status = ?1, updated_at = CURRENT_TIMESTAMP WHERE task_id = ?2"</span>,
<a href=#598 id=598 data-nosnippet>598</a>            status,
<a href=#599 id=599 data-nosnippet>599</a>            task_id
<a href=#600 id=600 data-nosnippet>600</a>        )
<a href=#601 id=601 data-nosnippet>601</a>        .execute(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#602 id=602 data-nosnippet>602</a>        .<span class="kw">await
<a href=#603 id=603 data-nosnippet>603</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#604 id=604 data-nosnippet>604</a>
<a href=#605 id=605 data-nosnippet>605</a>        <span class="prelude-val">Ok</span>(())
<a href=#606 id=606 data-nosnippet>606</a>    }
<a href=#607 id=607 data-nosnippet>607</a>
<a href=#608 id=608 data-nosnippet>608</a>    <span class="kw">pub async fn </span>get_task_by_id(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;TaskRecord&gt;&gt; {
<a href=#609 id=609 data-nosnippet>609</a>        <span class="kw">let </span>row = <span class="macro">sqlx::query!</span>(
<a href=#610 id=610 data-nosnippet>610</a>            <span class="string">"SELECT * FROM crawl_tasks WHERE task_id = ?1"</span>,
<a href=#611 id=611 data-nosnippet>611</a>            task_id
<a href=#612 id=612 data-nosnippet>612</a>        )
<a href=#613 id=613 data-nosnippet>613</a>        .fetch_optional(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#614 id=614 data-nosnippet>614</a>        .<span class="kw">await
<a href=#615 id=615 data-nosnippet>615</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#616 id=616 data-nosnippet>616</a>
<a href=#617 id=617 data-nosnippet>617</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(row) = row {
<a href=#618 id=618 data-nosnippet>618</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">Some</span>(TaskRecord {
<a href=#619 id=619 data-nosnippet>619</a>                id: row.id.unwrap_or(<span class="number">0</span>),
<a href=#620 id=620 data-nosnippet>620</a>                task_id: row.task_id,
<a href=#621 id=621 data-nosnippet>621</a>                task_type: row.task_type <span class="kw">as </span>i32,
<a href=#622 id=622 data-nosnippet>622</a>                target_url: row.target_url,
<a href=#623 id=623 data-nosnippet>623</a>                priority: row.priority <span class="kw">as </span>i32,
<a href=#624 id=624 data-nosnippet>624</a>                status: row.status <span class="kw">as </span>i32,
<a href=#625 id=625 data-nosnippet>625</a>                retry_count: row.retry_count <span class="kw">as </span>i32,
<a href=#626 id=626 data-nosnippet>626</a>                max_retries: row.max_retries <span class="kw">as </span>i32,
<a href=#627 id=627 data-nosnippet>627</a>                metadata: row.metadata,
<a href=#628 id=628 data-nosnippet>628</a>                assigned_at: row.assigned_at.map(|dt| chrono::DateTime::from_naive_utc_and_offset(dt, chrono::Utc)),
<a href=#629 id=629 data-nosnippet>629</a>                started_at: row.started_at.map(|dt| chrono::DateTime::from_naive_utc_and_offset(dt, chrono::Utc)),
<a href=#630 id=630 data-nosnippet>630</a>                completed_at: row.completed_at.map(|dt| chrono::DateTime::from_naive_utc_and_offset(dt, chrono::Utc)),
<a href=#631 id=631 data-nosnippet>631</a>                created_at: chrono::DateTime::from_naive_utc_and_offset(row.created_at, chrono::Utc),
<a href=#632 id=632 data-nosnippet>632</a>                updated_at: chrono::DateTime::from_naive_utc_and_offset(row.updated_at, chrono::Utc),
<a href=#633 id=633 data-nosnippet>633</a>            }))
<a href=#634 id=634 data-nosnippet>634</a>        } <span class="kw">else </span>{
<a href=#635 id=635 data-nosnippet>635</a>            <span class="prelude-val">Ok</span>(<span class="prelude-val">None</span>)
<a href=#636 id=636 data-nosnippet>636</a>        }
<a href=#637 id=637 data-nosnippet>637</a>    }
<a href=#638 id=638 data-nosnippet>638</a>
<a href=#639 id=639 data-nosnippet>639</a>    <span class="comment">// 代理相关操作
<a href=#640 id=640 data-nosnippet>640</a>    </span><span class="kw">pub async fn </span>save_proxy(<span class="kw-2">&amp;</span><span class="self">self</span>, proxy: <span class="kw-2">&amp;</span><span class="kw">crate</span>::proxy::Proxy) -&gt; <span class="prelude-ty">Result</span>&lt;i64&gt; {
<a href=#641 id=641 data-nosnippet>641</a>        <span class="kw">let </span>protocol = <span class="kw">match </span>proxy.protocol {
<a href=#642 id=642 data-nosnippet>642</a>            <span class="kw">crate</span>::proxy::ProxyProtocol::Http =&gt; <span class="string">"http"</span>,
<a href=#643 id=643 data-nosnippet>643</a>            <span class="kw">crate</span>::proxy::ProxyProtocol::Https =&gt; <span class="string">"https"</span>,
<a href=#644 id=644 data-nosnippet>644</a>            <span class="kw">crate</span>::proxy::ProxyProtocol::Socks5 =&gt; <span class="string">"socks5"</span>,
<a href=#645 id=645 data-nosnippet>645</a>        };
<a href=#646 id=646 data-nosnippet>646</a>
<a href=#647 id=647 data-nosnippet>647</a>        <span class="kw">let </span>status = <span class="kw">match </span>proxy.status {
<a href=#648 id=648 data-nosnippet>648</a>            <span class="kw">crate</span>::proxy::ProxyStatus::Available =&gt; <span class="number">1</span>,
<a href=#649 id=649 data-nosnippet>649</a>            <span class="kw">crate</span>::proxy::ProxyStatus::Unavailable =&gt; <span class="number">2</span>,
<a href=#650 id=650 data-nosnippet>650</a>            <span class="kw">crate</span>::proxy::ProxyStatus::Maintenance =&gt; <span class="number">3</span>,
<a href=#651 id=651 data-nosnippet>651</a>            <span class="kw">crate</span>::proxy::ProxyStatus::Testing =&gt; <span class="number">4</span>,
<a href=#652 id=652 data-nosnippet>652</a>        };
<a href=#653 id=653 data-nosnippet>653</a>
<a href=#654 id=654 data-nosnippet>654</a>        <span class="kw">let </span>port = proxy.port <span class="kw">as </span>i32;
<a href=#655 id=655 data-nosnippet>655</a>        <span class="kw">let </span>success_count = proxy.success_count <span class="kw">as </span>i64;
<a href=#656 id=656 data-nosnippet>656</a>        <span class="kw">let </span>failure_count = proxy.failure_count <span class="kw">as </span>i64;
<a href=#657 id=657 data-nosnippet>657</a>        <span class="kw">let </span>response_time = proxy.response_time.map(|t| t <span class="kw">as </span>i32);
<a href=#658 id=658 data-nosnippet>658</a>
<a href=#659 id=659 data-nosnippet>659</a>        <span class="kw">let </span>result = <span class="macro">sqlx::query!</span>(
<a href=#660 id=660 data-nosnippet>660</a>            <span class="string">r#"
<a href=#661 id=661 data-nosnippet>661</a>            INSERT INTO proxies (proxy_id, host, port, protocol, username, password, country, region, provider, status, success_count, failure_count, response_time)
<a href=#662 id=662 data-nosnippet>662</a>            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13)
<a href=#663 id=663 data-nosnippet>663</a>            "#</span>,
<a href=#664 id=664 data-nosnippet>664</a>            proxy.id,
<a href=#665 id=665 data-nosnippet>665</a>            proxy.host,
<a href=#666 id=666 data-nosnippet>666</a>            port,
<a href=#667 id=667 data-nosnippet>667</a>            protocol,
<a href=#668 id=668 data-nosnippet>668</a>            proxy.username,
<a href=#669 id=669 data-nosnippet>669</a>            proxy.password,
<a href=#670 id=670 data-nosnippet>670</a>            proxy.country,
<a href=#671 id=671 data-nosnippet>671</a>            proxy.region,
<a href=#672 id=672 data-nosnippet>672</a>            proxy.provider,
<a href=#673 id=673 data-nosnippet>673</a>            status,
<a href=#674 id=674 data-nosnippet>674</a>            success_count,
<a href=#675 id=675 data-nosnippet>675</a>            failure_count,
<a href=#676 id=676 data-nosnippet>676</a>            response_time
<a href=#677 id=677 data-nosnippet>677</a>        )
<a href=#678 id=678 data-nosnippet>678</a>        .execute(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#679 id=679 data-nosnippet>679</a>        .<span class="kw">await
<a href=#680 id=680 data-nosnippet>680</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#681 id=681 data-nosnippet>681</a>
<a href=#682 id=682 data-nosnippet>682</a>        <span class="prelude-val">Ok</span>(result.last_insert_rowid())
<a href=#683 id=683 data-nosnippet>683</a>    }
<a href=#684 id=684 data-nosnippet>684</a>
<a href=#685 id=685 data-nosnippet>685</a>    <span class="kw">pub async fn </span>update_proxy_status(<span class="kw-2">&amp;</span><span class="self">self</span>, proxy_id: <span class="kw-2">&amp;</span>str, status: i32, response_time: <span class="prelude-ty">Option</span>&lt;u64&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#686 id=686 data-nosnippet>686</a>        <span class="kw">let </span>response_time_i32 = response_time.map(|t| t <span class="kw">as </span>i32);
<a href=#687 id=687 data-nosnippet>687</a>
<a href=#688 id=688 data-nosnippet>688</a>        <span class="macro">sqlx::query!</span>(
<a href=#689 id=689 data-nosnippet>689</a>            <span class="string">"UPDATE proxies SET status = ?1, response_time = ?2, last_checked = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE proxy_id = ?3"</span>,
<a href=#690 id=690 data-nosnippet>690</a>            status,
<a href=#691 id=691 data-nosnippet>691</a>            response_time_i32,
<a href=#692 id=692 data-nosnippet>692</a>            proxy_id
<a href=#693 id=693 data-nosnippet>693</a>        )
<a href=#694 id=694 data-nosnippet>694</a>        .execute(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#695 id=695 data-nosnippet>695</a>        .<span class="kw">await
<a href=#696 id=696 data-nosnippet>696</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#697 id=697 data-nosnippet>697</a>
<a href=#698 id=698 data-nosnippet>698</a>        <span class="prelude-val">Ok</span>(())
<a href=#699 id=699 data-nosnippet>699</a>    }
<a href=#700 id=700 data-nosnippet>700</a>
<a href=#701 id=701 data-nosnippet>701</a>    <span class="comment">// 账号相关操作
<a href=#702 id=702 data-nosnippet>702</a>    </span><span class="kw">pub async fn </span>save_account(<span class="kw-2">&amp;</span><span class="self">self</span>, account: <span class="kw-2">&amp;</span><span class="kw">crate</span>::account::Account) -&gt; <span class="prelude-ty">Result</span>&lt;i64&gt; {
<a href=#703 id=703 data-nosnippet>703</a>        <span class="kw">let </span>status = <span class="kw">match </span>account.status {
<a href=#704 id=704 data-nosnippet>704</a>            <span class="kw">crate</span>::account::AccountStatus::Normal =&gt; <span class="number">1</span>,
<a href=#705 id=705 data-nosnippet>705</a>            <span class="kw">crate</span>::account::AccountStatus::Banned =&gt; <span class="number">2</span>,
<a href=#706 id=706 data-nosnippet>706</a>            <span class="kw">crate</span>::account::AccountStatus::Abnormal =&gt; <span class="number">3</span>,
<a href=#707 id=707 data-nosnippet>707</a>            <span class="kw">crate</span>::account::AccountStatus::Maintenance =&gt; <span class="number">4</span>,
<a href=#708 id=708 data-nosnippet>708</a>        };
<a href=#709 id=709 data-nosnippet>709</a>
<a href=#710 id=710 data-nosnippet>710</a>        <span class="kw">let </span>login_count_i64 = account.login_count <span class="kw">as </span>i64;
<a href=#711 id=711 data-nosnippet>711</a>
<a href=#712 id=712 data-nosnippet>712</a>        <span class="kw">let </span>result = <span class="macro">sqlx::query!</span>(
<a href=#713 id=713 data-nosnippet>713</a>            <span class="string">r#"
<a href=#714 id=714 data-nosnippet>714</a>            INSERT INTO accounts (account_id, username, password, phone, email, status, login_count, risk_score)
<a href=#715 id=715 data-nosnippet>715</a>            VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)
<a href=#716 id=716 data-nosnippet>716</a>            "#</span>,
<a href=#717 id=717 data-nosnippet>717</a>            account.id,
<a href=#718 id=718 data-nosnippet>718</a>            account.username,
<a href=#719 id=719 data-nosnippet>719</a>            account.password,
<a href=#720 id=720 data-nosnippet>720</a>            account.phone,
<a href=#721 id=721 data-nosnippet>721</a>            account.email,
<a href=#722 id=722 data-nosnippet>722</a>            status,
<a href=#723 id=723 data-nosnippet>723</a>            login_count_i64,
<a href=#724 id=724 data-nosnippet>724</a>            account.risk_score
<a href=#725 id=725 data-nosnippet>725</a>        )
<a href=#726 id=726 data-nosnippet>726</a>        .execute(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#727 id=727 data-nosnippet>727</a>        .<span class="kw">await
<a href=#728 id=728 data-nosnippet>728</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#729 id=729 data-nosnippet>729</a>
<a href=#730 id=730 data-nosnippet>730</a>        <span class="prelude-val">Ok</span>(result.last_insert_rowid())
<a href=#731 id=731 data-nosnippet>731</a>    }
<a href=#732 id=732 data-nosnippet>732</a>
<a href=#733 id=733 data-nosnippet>733</a>    <span class="kw">pub async fn </span>update_account_login(<span class="kw-2">&amp;</span><span class="self">self</span>, account_id: <span class="kw-2">&amp;</span>str, login_count: u64, risk_score: f64) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#734 id=734 data-nosnippet>734</a>        <span class="kw">let </span>login_count_i64 = login_count <span class="kw">as </span>i64;
<a href=#735 id=735 data-nosnippet>735</a>
<a href=#736 id=736 data-nosnippet>736</a>        <span class="macro">sqlx::query!</span>(
<a href=#737 id=737 data-nosnippet>737</a>            <span class="string">"UPDATE accounts SET login_count = ?1, risk_score = ?2, last_login = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP WHERE account_id = ?3"</span>,
<a href=#738 id=738 data-nosnippet>738</a>            login_count_i64,
<a href=#739 id=739 data-nosnippet>739</a>            risk_score,
<a href=#740 id=740 data-nosnippet>740</a>            account_id
<a href=#741 id=741 data-nosnippet>741</a>        )
<a href=#742 id=742 data-nosnippet>742</a>        .execute(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#743 id=743 data-nosnippet>743</a>        .<span class="kw">await
<a href=#744 id=744 data-nosnippet>744</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#745 id=745 data-nosnippet>745</a>
<a href=#746 id=746 data-nosnippet>746</a>        <span class="prelude-val">Ok</span>(())
<a href=#747 id=747 data-nosnippet>747</a>    }
<a href=#748 id=748 data-nosnippet>748</a>
<a href=#749 id=749 data-nosnippet>749</a>    <span class="comment">// 缓存相关操作
<a href=#750 id=750 data-nosnippet>750</a>    </span><span class="kw">pub async fn </span>save_cache(<span class="kw-2">&amp;</span><span class="self">self</span>, key: <span class="kw-2">&amp;</span>str, value: <span class="kw-2">&amp;</span>str, expires_at: chrono::DateTime&lt;chrono::Utc&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#751 id=751 data-nosnippet>751</a>        <span class="macro">sqlx::query!</span>(
<a href=#752 id=752 data-nosnippet>752</a>            <span class="string">"INSERT OR REPLACE INTO request_cache (cache_key, cache_value, expires_at) VALUES (?1, ?2, ?3)"</span>,
<a href=#753 id=753 data-nosnippet>753</a>            key,
<a href=#754 id=754 data-nosnippet>754</a>            value,
<a href=#755 id=755 data-nosnippet>755</a>            expires_at
<a href=#756 id=756 data-nosnippet>756</a>        )
<a href=#757 id=757 data-nosnippet>757</a>        .execute(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#758 id=758 data-nosnippet>758</a>        .<span class="kw">await
<a href=#759 id=759 data-nosnippet>759</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#760 id=760 data-nosnippet>760</a>
<a href=#761 id=761 data-nosnippet>761</a>        <span class="prelude-val">Ok</span>(())
<a href=#762 id=762 data-nosnippet>762</a>    }
<a href=#763 id=763 data-nosnippet>763</a>
<a href=#764 id=764 data-nosnippet>764</a>    <span class="kw">pub async fn </span>get_cache(<span class="kw-2">&amp;</span><span class="self">self</span>, key: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;String&gt;&gt; {
<a href=#765 id=765 data-nosnippet>765</a>        <span class="kw">let </span>row = <span class="macro">sqlx::query!</span>(
<a href=#766 id=766 data-nosnippet>766</a>            <span class="string">"SELECT cache_value FROM request_cache WHERE cache_key = ?1 AND expires_at &gt; CURRENT_TIMESTAMP"</span>,
<a href=#767 id=767 data-nosnippet>767</a>            key
<a href=#768 id=768 data-nosnippet>768</a>        )
<a href=#769 id=769 data-nosnippet>769</a>        .fetch_optional(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#770 id=770 data-nosnippet>770</a>        .<span class="kw">await
<a href=#771 id=771 data-nosnippet>771</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#772 id=772 data-nosnippet>772</a>
<a href=#773 id=773 data-nosnippet>773</a>        <span class="prelude-val">Ok</span>(row.map(|r| r.cache_value))
<a href=#774 id=774 data-nosnippet>774</a>    }
<a href=#775 id=775 data-nosnippet>775</a>
<a href=#776 id=776 data-nosnippet>776</a>    <span class="kw">pub async fn </span>cleanup_expired_cache(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#777 id=777 data-nosnippet>777</a>        <span class="kw">let </span>result = <span class="macro">sqlx::query!</span>(
<a href=#778 id=778 data-nosnippet>778</a>            <span class="string">"DELETE FROM request_cache WHERE expires_at &lt;= CURRENT_TIMESTAMP"
<a href=#779 id=779 data-nosnippet>779</a>        </span>)
<a href=#780 id=780 data-nosnippet>780</a>        .execute(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#781 id=781 data-nosnippet>781</a>        .<span class="kw">await
<a href=#782 id=782 data-nosnippet>782</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#783 id=783 data-nosnippet>783</a>
<a href=#784 id=784 data-nosnippet>784</a>        <span class="prelude-val">Ok</span>(result.rows_affected())
<a href=#785 id=785 data-nosnippet>785</a>    }
<a href=#786 id=786 data-nosnippet>786</a>
<a href=#787 id=787 data-nosnippet>787</a>    <span class="comment">// 统计相关操作
<a href=#788 id=788 data-nosnippet>788</a>    </span><span class="kw">pub async fn </span>get_task_statistics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;(u64, u64, u64)&gt; {
<a href=#789 id=789 data-nosnippet>789</a>        <span class="kw">let </span>row = <span class="macro">sqlx::query!</span>(
<a href=#790 id=790 data-nosnippet>790</a>            <span class="string">r#"
<a href=#791 id=791 data-nosnippet>791</a>            SELECT 
<a href=#792 id=792 data-nosnippet>792</a>                COUNT(*) as total,
<a href=#793 id=793 data-nosnippet>793</a>                SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed,
<a href=#794 id=794 data-nosnippet>794</a>                SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as failed
<a href=#795 id=795 data-nosnippet>795</a>            FROM crawl_tasks
<a href=#796 id=796 data-nosnippet>796</a>            "#
<a href=#797 id=797 data-nosnippet>797</a>        </span>)
<a href=#798 id=798 data-nosnippet>798</a>        .fetch_one(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#799 id=799 data-nosnippet>799</a>        .<span class="kw">await
<a href=#800 id=800 data-nosnippet>800</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#801 id=801 data-nosnippet>801</a>
<a href=#802 id=802 data-nosnippet>802</a>        <span class="prelude-val">Ok</span>((row.total <span class="kw">as </span>u64, row.completed.unwrap_or(<span class="number">0</span>) <span class="kw">as </span>u64, row.failed.unwrap_or(<span class="number">0</span>) <span class="kw">as </span>u64))
<a href=#803 id=803 data-nosnippet>803</a>    }
<a href=#804 id=804 data-nosnippet>804</a>
<a href=#805 id=805 data-nosnippet>805</a>    <span class="kw">pub async fn </span>get_proxy_statistics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;(u64, u64)&gt; {
<a href=#806 id=806 data-nosnippet>806</a>        <span class="kw">let </span>row = <span class="macro">sqlx::query!</span>(
<a href=#807 id=807 data-nosnippet>807</a>            <span class="string">r#"
<a href=#808 id=808 data-nosnippet>808</a>            SELECT 
<a href=#809 id=809 data-nosnippet>809</a>                COUNT(*) as total,
<a href=#810 id=810 data-nosnippet>810</a>                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as available
<a href=#811 id=811 data-nosnippet>811</a>            FROM proxies
<a href=#812 id=812 data-nosnippet>812</a>            "#
<a href=#813 id=813 data-nosnippet>813</a>        </span>)
<a href=#814 id=814 data-nosnippet>814</a>        .fetch_one(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#815 id=815 data-nosnippet>815</a>        .<span class="kw">await
<a href=#816 id=816 data-nosnippet>816</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#817 id=817 data-nosnippet>817</a>
<a href=#818 id=818 data-nosnippet>818</a>        <span class="prelude-val">Ok</span>((row.total <span class="kw">as </span>u64, row.available.unwrap_or(<span class="number">0</span>) <span class="kw">as </span>u64))
<a href=#819 id=819 data-nosnippet>819</a>    }
<a href=#820 id=820 data-nosnippet>820</a>
<a href=#821 id=821 data-nosnippet>821</a>    <span class="kw">pub async fn </span>get_account_statistics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;(u64, u64)&gt; {
<a href=#822 id=822 data-nosnippet>822</a>        <span class="kw">let </span>row = <span class="macro">sqlx::query!</span>(
<a href=#823 id=823 data-nosnippet>823</a>            <span class="string">r#"
<a href=#824 id=824 data-nosnippet>824</a>            SELECT 
<a href=#825 id=825 data-nosnippet>825</a>                COUNT(*) as total,
<a href=#826 id=826 data-nosnippet>826</a>                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as normal
<a href=#827 id=827 data-nosnippet>827</a>            FROM accounts
<a href=#828 id=828 data-nosnippet>828</a>            "#
<a href=#829 id=829 data-nosnippet>829</a>        </span>)
<a href=#830 id=830 data-nosnippet>830</a>        .fetch_one(<span class="kw-2">&amp;</span><span class="self">self</span>.pool)
<a href=#831 id=831 data-nosnippet>831</a>        .<span class="kw">await
<a href=#832 id=832 data-nosnippet>832</a>        </span>.map_err(AppError::Database)<span class="question-mark">?</span>;
<a href=#833 id=833 data-nosnippet>833</a>
<a href=#834 id=834 data-nosnippet>834</a>        <span class="prelude-val">Ok</span>((row.total <span class="kw">as </span>u64, row.normal.unwrap_or(<span class="number">0</span>) <span class="kw">as </span>u64))
<a href=#835 id=835 data-nosnippet>835</a>    }
<a href=#836 id=836 data-nosnippet>836</a>
<a href=#837 id=837 data-nosnippet>837</a>    <span class="kw">pub async fn </span>close(<span class="kw-2">&amp;</span><span class="self">self</span>) {
<a href=#838 id=838 data-nosnippet>838</a>        <span class="self">self</span>.pool.close().<span class="kw">await</span>;
<a href=#839 id=839 data-nosnippet>839</a>        <span class="macro">info!</span>(<span class="string">"数据库连接已关闭"</span>);
<a href=#840 id=840 data-nosnippet>840</a>    }
<a href=#841 id=841 data-nosnippet>841</a>}</code></pre></div></section></main></body></html>