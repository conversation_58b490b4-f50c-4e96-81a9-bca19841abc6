use crate::error::{Result, AppError};
use playwright::{<PERSON><PERSON>, api::{page::Page, browser::<PERSON><PERSON><PERSON>, browser_context::BrowserContext}};
use tracing::{info, warn, error, debug};
use std::time::Duration;
use tokio::time::sleep;
use super::{BrowserConfig, LoginCredentials, LoginResult, UserInfo, QrCodeData, QrLoginStatus};

const WEIBO_LOGIN_URL: &str = "https://s.weibo.com/";
const LOGIN_TIMEOUT: u64 = 30000; // 30秒

pub async fn login_with_playwright(
    playwright: &Playwright,
    config: &BrowserConfig,
    credentials: LoginCredentials,
) -> Result<LoginResult> {
    info!("开始微博登录流程，用户名: {}", credentials.username);

    // 启动浏览器
    let chromium = playwright.chromium();
    let browser = chromium
        .launcher()
        .headless(config.headless)
        .launch()
        .await
        .map_err(|e| AppError::Browser(format!("启动浏览器失败: {}", e)))?;

    let result = async {
        // 创建浏览器上下文
        let mut context_builder = browser.context_builder();

        if let Some(user_agent) = &config.user_agent {
            context_builder = context_builder.user_agent(user_agent);
        }

        let context = context_builder
            .build()
            .await
            .map_err(|e| AppError::Browser(format!("创建浏览器上下文失败: {}", e)))?;

        // 创建新页面
        let page = context
            .new_page()
            .await
            .map_err(|e| AppError::Browser(format!("创建页面失败: {}", e)))?;

        // 执行登录流程
        perform_login(&page, config, credentials).await
    }.await;

    // 关闭浏览器
    if let Err(e) = browser.close().await {
        warn!("关闭浏览器时出错: {}", e);
    }

    result
}

async fn perform_login(
    page: &Page,
    config: &BrowserConfig,
    credentials: LoginCredentials,
) -> Result<LoginResult> {
    // 导航到微博搜索页面
    info!("导航到微博搜索页面...");
    page.goto_builder(WEIBO_LOGIN_URL)
        .timeout(config.timeout as f64)
        .goto()
        .await
        .map_err(|e| AppError::Browser(format!("导航到微博页面失败: {}", e)))?;

    // 等待页面加载
    sleep(Duration::from_millis(2000)).await;

    // 检查是否已经登录
    if is_already_logged_in(page).await? {
        info!("检测到已登录状态");
        return extract_login_info(page).await;
    }

    // 查找并点击登录按钮 
    // #searchapps > div > div.Nav_wrap_gHB1a > div > div > div.woo-box-flex.woo-box-alignCenter.woo-box-justifyEnd.Nav_right_pDw0F > div:nth-child(1) > div > a.LoginBtn_btn_10QRY.LoginBtn_btna_1hH9H
    info!("查找登录入口...");
    let login_selectors = vec![
        "a[href*='login']",
        ".gn_login",
        ".login",
        "a:has-text('登录')",
        "button:has-text('登录')",
    ];

    let mut login_clicked = false;
    for selector in login_selectors {
        if let Ok(_) = page.click_builder(selector).timeout(1000.0).click().await {
            info!("点击登录按钮: {}", selector);
            login_clicked = true;
            break;
        }
    }

    if !login_clicked {
        return Ok(LoginResult {
            success: false,
            message: "未找到登录入口".to_string(),
            cookies: None,
            user_info: None,
        });
    }

    // 等待登录表单出现
    sleep(Duration::from_millis(2000)).await;

    // 填写用户名
    info!("填写登录信息...");
    let username_selectors = vec![
        "input[name='username']",
        "input[placeholder*='手机']",
        "input[placeholder*='邮箱']",
        "input[placeholder*='用户名']",
        "#loginname",
        ".username input",
    ];

    let mut username_filled = false;
    for selector in username_selectors {
        if let Ok(_) = page.fill_builder(selector, &credentials.username).fill().await {
            info!("填写用户名成功: {}", selector);
            username_filled = true;
            break;
        }
    }

    if !username_filled {
        return Ok(LoginResult {
            success: false,
            message: "未找到用户名输入框".to_string(),
            cookies: None,
            user_info: None,
        });
    }

    // 填写密码
    let password_selectors = vec![
        "input[name='password']",
        "input[type='password']",
        "#password",
        ".password input",
    ];

    let mut password_filled = false;
    for selector in password_selectors {
        if let Ok(_) = page.fill_builder(selector, &credentials.password).fill().await {
            info!("填写密码成功: {}", selector);
            password_filled = true;
            break;
        }
    }

    if !password_filled {
        return Ok(LoginResult {
            success: false,
            message: "未找到密码输入框".to_string(),
            cookies: None,
            user_info: None,
        });
    }

    // 点击登录按钮
    let submit_selectors = vec![
        "button[type='submit']",
        "input[type='submit']",
        ".btn_login",
        "button:has-text('登录')",
        "a:has-text('登录')",
    ];

    let mut submit_clicked = false;
    for selector in submit_selectors {
        if let Ok(_) = page.click_builder(selector).timeout(1000.0).click().await {
            info!("点击登录提交按钮: {}", selector);
            submit_clicked = true;
            break;
        }
    }

    if !submit_clicked {
        return Ok(LoginResult {
            success: false,
            message: "未找到登录提交按钮".to_string(),
            cookies: None,
            user_info: None,
        });
    }

    // 等待登录结果
    info!("等待登录结果...");
    sleep(Duration::from_millis(3000)).await;

    // 检查是否需要验证码
    if is_captcha_required(page).await? {
        return Ok(LoginResult {
            success: false,
            message: "需要验证码，请手动完成登录".to_string(),
            cookies: None,
            user_info: None,
        });
    }

    // 检查登录是否成功
    if is_login_successful(page).await? {
        info!("登录成功");
        extract_login_info(page).await
    } else {
        let error_msg = get_login_error_message(page).await?;
        Ok(LoginResult {
            success: false,
            message: error_msg,
            cookies: None,
            user_info: None,
        })
    }
}

async fn is_already_logged_in(page: &Page) -> Result<bool> {
    // 检查是否存在用户信息相关元素
    let logged_in_selectors = vec![
        ".gn_name",
        ".user-info",
        "a[href*='u/']",
        ".avatar",
    ];

    for selector in logged_in_selectors {
        if page.query_selector(selector).await.is_ok() {
            return Ok(true);
        }
    }

    Ok(false)
}

async fn is_captcha_required(page: &Page) -> Result<bool> {
    let captcha_selectors = vec![
        ".captcha",
        ".verify",
        "img[src*='captcha']",
        ".verification",
    ];

    for selector in captcha_selectors {
        if page.query_selector(selector).await.is_ok() {
            return Ok(true);
        }
    }

    Ok(false)
}

async fn is_login_successful(page: &Page) -> Result<bool> {
    // 检查URL变化或页面元素变化来判断登录是否成功
    let current_url = page.url()?;
    
    // 如果URL包含用户相关信息，说明登录成功
    if current_url.contains("/u/") || current_url.contains("home") {
        return Ok(true);
    }

    // 检查是否存在登录后的元素
    is_already_logged_in(page).await
}

async fn get_login_error_message(page: &Page) -> Result<String> {
    let error_selectors = vec![
        ".error",
        ".alert",
        ".warning",
        ".message",
        ".tip",
    ];

    for selector in error_selectors {
        if let Ok(element) = page.query_selector(selector).await {
            if let Some(element) = element {
                if let Ok(text) = element.text_content().await {
                    if let Some(text) = text {
                        if !text.trim().is_empty() {
                            return Ok(text.trim().to_string());
                        }
                    }
                }
            }
        }
    }

    Ok("登录失败，原因未知".to_string())
}

async fn extract_login_info(page: &Page) -> Result<LoginResult> {
    // 获取cookies
    let cookies = get_cookies_string(page).await?;
    
    // 获取用户信息
    let user_info = extract_user_info(page).await?;

    Ok(LoginResult {
        success: true,
        message: "登录成功".to_string(),
        cookies: Some(cookies),
        user_info,
    })
}

async fn get_cookies_string(page: &Page) -> Result<String> {
    let context = page.context();
    let cookies = context.cookies(&[]).await?;
    
    let cookie_strings: Vec<String> = cookies
        .iter()
        .map(|cookie| format!("{}={}", cookie.name, cookie.value))
        .collect();
    
    Ok(cookie_strings.join("; "))
}

async fn extract_user_info(page: &Page) -> Result<Option<UserInfo>> {
    // 尝试提取用户信息
    let mut uid = String::new();
    let mut nickname = String::new();
    let mut avatar_url: Option<String> = None;

    // 从URL中提取UID
    let current_url = page.url()?;
    if let Some(uid_match) = current_url.split("/u/").nth(1) {
        if let Some(uid_part) = uid_match.split("?").next() {
            uid = uid_part.to_string();
        }
    }

    // 提取昵称
    let nickname_selectors = vec![
        ".gn_name",
        ".user-name",
        ".nickname",
        ".username",
    ];

    for selector in nickname_selectors {
        if let Ok(Some(element)) = page.query_selector(selector).await {
            if let Ok(Some(text)) = element.text_content().await {
                if !text.trim().is_empty() {
                    nickname = text.trim().to_string();
                    break;
                }
            }
        }
    }

    // 提取头像URL
    let avatar_selectors = vec![
        ".avatar img",
        ".user-avatar img",
        ".head-img img",
    ];

    for selector in avatar_selectors {
        if let Ok(Some(element)) = page.query_selector(selector).await {
            if let Ok(Some(src)) = element.get_attribute("src").await {
                avatar_url = Some(src);
                break;
            }
        }
    }

    if !uid.is_empty() || !nickname.is_empty() {
        Ok(Some(UserInfo {
            uid,
            nickname,
            avatar_url,
        }))
    } else {
        Ok(None)
    }
}

// 二维码登录相关函数

/// 获取微博登录二维码
pub async fn get_qr_code_with_playwright(
    playwright: &Playwright,
    config: &BrowserConfig,
) -> Result<QrCodeData> {
    info!("开始获取微博登录二维码...");

    // 启动浏览器
    let chromium = playwright.chromium();
    let browser = chromium
        .launcher()
        .headless(config.headless)
        .launch()
        .await
        .map_err(|e| AppError::Browser(format!("启动浏览器失败: {}", e)))?;

    let result = async {
        // 创建浏览器上下文
        let mut context_builder = browser.context_builder();

        if let Some(user_agent) = &config.user_agent {
            context_builder = context_builder.user_agent(user_agent);
        }

        let context = context_builder
            .build()
            .await
            .map_err(|e| AppError::Browser(format!("创建浏览器上下文失败: {}", e)))?;

        // 创建新页面
        let page = context
            .new_page()
            .await
            .map_err(|e| AppError::Browser(format!("创建页面失败: {}", e)))?;

        // 导航到微博登录页面
        info!("导航到微博登录页面...");
        page.goto_builder("https://passport.weibo.com/signin")
            .timeout(config.timeout as f64)
            .goto()
            .await
            .map_err(|e| AppError::Browser(format!("导航到微博登录页面失败: {}", e)))?;

        // 等待页面加载
        sleep(Duration::from_millis(3000)).await;

        // 查找二维码登录按钮并点击
        // 尝试多个可能的二维码登录按钮选择器
        let selectors = vec![
            "a[href*='qrcode']",
            ".qrcode-login",
            ".login-qr",
            "[data-node-type='qrLogin']",
            ".W_btn_qr",
            ".qr_code_btn"
        ];

        let mut qr_button_found = false;
        for selector in selectors {
            if let Ok(element) = page.query_selector(selector).await {
                if let Some(button) = element {
                    info!("找到二维码登录按钮: {}", selector);
                    button.click_builder().click().await.map_err(|e| AppError::Browser(format!("点击二维码登录按钮失败: {}", e)))?;
                    qr_button_found = true;
                    break;
                }
            }
        }

        if !qr_button_found {
            // 如果没有找到二维码按钮，直接导航到二维码登录页面
            info!("未找到二维码登录按钮，直接导航到二维码登录页面...");
            page.goto_builder("https://passport.weibo.com/signin?entry=sso&source=blog&disp=popup&retcode=6102&r=https%3A%2F%2Fpassport.weibo.com%2Fwbsso%2Flogin%3Furl%3Dhttps%253A%252F%252Fs.weibo.com%252F&qrcode=1")
                .timeout(config.timeout as f64)
                .goto()
                .await
                .map_err(|e| AppError::Browser(format!("导航到二维码登录页面失败: {}", e)))?;
        }

        // 等待二维码加载
        sleep(Duration::from_millis(3000)).await;

        // 查找二维码图片
        let qr_selectors = vec![
            "img[src*='qrcode']",
            ".qrcode img",
            ".qr-code img",
            ".login-qr img",
            "#qr_img",
            ".qr_code_img"
        ];

        let mut qr_code_url = String::new();
        for selector in qr_selectors {
            if let Ok(element) = page.query_selector(selector).await {
                if let Some(img) = element {
                    if let Ok(src) = img.get_attribute("src").await {
                        if let Some(src_value) = src {
                            if !src_value.is_empty() && src_value.contains("qr") {
                                qr_code_url = src_value;
                                info!("找到二维码图片: {}", qr_code_url);
                                break;
                            }
                        }
                    }
                }
            }
        }

        if qr_code_url.is_empty() {
            return Err(AppError::Browser("未找到二维码图片".to_string()));
        }

        // 获取二维码图片的base64数据
        let qr_code_image = get_qr_code_image_base64(&page, &qr_code_url).await?;

        // 生成会话ID用于后续轮询
        let session_id = format!("qr_session_{}", std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis());

        Ok(QrCodeData {
            qr_code_url,
            qr_code_image,
            session_id,
        })
    }.await;

    // 关闭浏览器
    let _ = browser.close().await;

    result
}

/// 获取二维码图片的base64数据
async fn get_qr_code_image_base64(_page: &Page, qr_code_url: &str) -> Result<String> {
    // 如果是相对URL，转换为绝对URL
    let full_url = if qr_code_url.starts_with("http") {
        qr_code_url.to_string()
    } else if qr_code_url.starts_with("//") {
        format!("https:{}", qr_code_url)
    } else if qr_code_url.starts_with("/") {
        format!("https://passport.weibo.com{}", qr_code_url)
    } else {
        format!("https://passport.weibo.com/{}", qr_code_url)
    };

    info!("获取二维码图片: {}", full_url);

    // 简化实现：直接返回URL，前端可以直接使用
    // 在实际应用中，可以使用reqwest等HTTP客户端获取图片并转换为base64
    use base64::{Engine as _, engine::general_purpose};
    Ok(format!("data:image/png;base64,{}", general_purpose::STANDARD.encode(full_url.as_bytes())))
}

/// 检查二维码登录状态
pub async fn check_qr_login_status_with_playwright(
    playwright: &Playwright,
    config: &BrowserConfig,
    session_id: &str,
) -> Result<QrLoginStatus> {
    info!("检查二维码登录状态，会话ID: {}", session_id);

    // 启动浏览器
    let chromium = playwright.chromium();
    let browser = chromium
        .launcher()
        .headless(config.headless)
        .launch()
        .await
        .map_err(|e| AppError::Browser(format!("启动浏览器失败: {}", e)))?;

    let result = async {
        // 创建浏览器上下文
        let mut context_builder = browser.context_builder();

        if let Some(user_agent) = &config.user_agent {
            context_builder = context_builder.user_agent(user_agent);
        }

        let context = context_builder
            .build()
            .await
            .map_err(|e| AppError::Browser(format!("创建浏览器上下文失败: {}", e)))?;

        // 创建新页面
        let page = context
            .new_page()
            .await
            .map_err(|e| AppError::Browser(format!("创建页面失败: {}", e)))?;

        // 导航到二维码登录页面
        page.goto_builder("https://passport.weibo.com/signin?entry=sso&source=blog&disp=popup&retcode=6102&r=https%3A%2F%2Fpassport.weibo.com%2Fwbsso%2Flogin%3Furl%3Dhttps%253A%252F%252Fs.weibo.com%252F&qrcode=1")
            .timeout(config.timeout as f64)
            .goto()
            .await
            .map_err(|e| AppError::Browser(format!("导航到二维码登录页面失败: {}", e)))?;

        // 等待页面加载
        sleep(Duration::from_millis(2000)).await;

        // 检查登录状态
        check_login_status(&page).await
    }.await;

    // 关闭浏览器
    let _ = browser.close().await;

    result
}

/// 检查当前页面的登录状态
async fn check_login_status(page: &Page) -> Result<QrLoginStatus> {
    // 检查是否已经登录成功（URL变化或特定元素出现）
    let current_url = page.url()?;

    if current_url.contains("s.weibo.com") || current_url.contains("weibo.com/u/") {
        // 登录成功，获取用户信息和cookies
        info!("检测到登录成功");

        let user_info = extract_user_info(&page).await.unwrap_or(None);
        let cookies = get_cookies_string(&page).await.unwrap_or_default();

        return Ok(QrLoginStatus {
            status: "success".to_string(),
            message: "登录成功".to_string(),
            login_result: Some(LoginResult {
                success: true,
                message: "二维码登录成功".to_string(),
                user_info,
                cookies: Some(cookies),
            }),
        });
    }

    // 检查二维码是否过期
    let expired_selectors = vec![
        ".qr-refresh",
        ".qr-expired",
        "[data-node-type='qrRefresh']",
        ".refresh-qr"
    ];

    for selector in expired_selectors {
        if let Ok(element) = page.query_selector(selector).await {
            if element.is_some() {
                return Ok(QrLoginStatus {
                    status: "expired".to_string(),
                    message: "二维码已过期".to_string(),
                    login_result: None,
                });
            }
        }
    }

    // 检查是否已扫码
    let scanned_selectors = vec![
        ".qr-scanned",
        ".scan-success",
        "[data-node-type='qrScanned']"
    ];

    for selector in scanned_selectors {
        if let Ok(element) = page.query_selector(selector).await {
            if element.is_some() {
                return Ok(QrLoginStatus {
                    status: "scanned".to_string(),
                    message: "已扫码，等待确认".to_string(),
                    login_result: None,
                });
            }
        }
    }

    // 默认状态：等待扫码
    Ok(QrLoginStatus {
        status: "waiting".to_string(),
        message: "等待扫码".to_string(),
        login_result: None,
    })
}
