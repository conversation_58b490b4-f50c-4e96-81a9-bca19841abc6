<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\commands.rs`."><title>commands.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>commands.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use crate</span>::{AppState, error::Result, config::AppConfig};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span><span class="kw">crate</span>::browser::{get_browser_manager, BrowserConfig, LoginCredentials};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>tauri::State;
<a href=#5 id=5 data-nosnippet>5</a>
<a href=#6 id=6 data-nosnippet>6</a><span class="comment">// 系统状态相关结构体
<a href=#7 id=7 data-nosnippet>7</a></span><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#8 id=8 data-nosnippet>8</a></span><span class="kw">pub struct </span>NodeStatus {
<a href=#9 id=9 data-nosnippet>9</a>    <span class="kw">pub </span>node_id: String,
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub </span>node_name: String,
<a href=#11 id=11 data-nosnippet>11</a>    <span class="kw">pub </span>status: String,
<a href=#12 id=12 data-nosnippet>12</a>    <span class="kw">pub </span>uptime: u64,
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>cpu_usage: f64,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>memory_usage: f64,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>disk_usage: f64,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>active_tasks: u32,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>total_tasks: u64,
<a href=#18 id=18 data-nosnippet>18</a>    <span class="kw">pub </span>success_rate: f64,
<a href=#19 id=19 data-nosnippet>19</a>}
<a href=#20 id=20 data-nosnippet>20</a>
<a href=#21 id=21 data-nosnippet>21</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#22 id=22 data-nosnippet>22</a></span><span class="kw">pub struct </span>TaskQueueStatus {
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">pub </span>pending_tasks: u32,
<a href=#24 id=24 data-nosnippet>24</a>    <span class="kw">pub </span>running_tasks: u32,
<a href=#25 id=25 data-nosnippet>25</a>    <span class="kw">pub </span>completed_tasks: u64,
<a href=#26 id=26 data-nosnippet>26</a>    <span class="kw">pub </span>failed_tasks: u64,
<a href=#27 id=27 data-nosnippet>27</a>    <span class="kw">pub </span>queue_length: u32,
<a href=#28 id=28 data-nosnippet>28</a>    <span class="kw">pub </span>processing_speed: f64,
<a href=#29 id=29 data-nosnippet>29</a>}
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#32 id=32 data-nosnippet>32</a></span><span class="kw">pub struct </span>TaskStatistics {
<a href=#33 id=33 data-nosnippet>33</a>    <span class="kw">pub </span>total_processed: u64,
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">pub </span>success_count: u64,
<a href=#35 id=35 data-nosnippet>35</a>    <span class="kw">pub </span>failure_count: u64,
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>average_duration: f64,
<a href=#37 id=37 data-nosnippet>37</a>    <span class="kw">pub </span>success_rate: f64,
<a href=#38 id=38 data-nosnippet>38</a>    <span class="kw">pub </span>tasks_per_hour: f64,
<a href=#39 id=39 data-nosnippet>39</a>}
<a href=#40 id=40 data-nosnippet>40</a>
<a href=#41 id=41 data-nosnippet>41</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#42 id=42 data-nosnippet>42</a></span><span class="kw">pub struct </span>ProxyPoolStatus {
<a href=#43 id=43 data-nosnippet>43</a>    <span class="kw">pub </span>total_proxies: u32,
<a href=#44 id=44 data-nosnippet>44</a>    <span class="kw">pub </span>active_proxies: u32,
<a href=#45 id=45 data-nosnippet>45</a>    <span class="kw">pub </span>healthy_proxies: u32,
<a href=#46 id=46 data-nosnippet>46</a>    <span class="kw">pub </span>average_response_time: f64,
<a href=#47 id=47 data-nosnippet>47</a>    <span class="kw">pub </span>success_rate: f64,
<a href=#48 id=48 data-nosnippet>48</a>}
<a href=#49 id=49 data-nosnippet>49</a>
<a href=#50 id=50 data-nosnippet>50</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#51 id=51 data-nosnippet>51</a></span><span class="kw">pub struct </span>ProxyConfig {
<a href=#52 id=52 data-nosnippet>52</a>    <span class="kw">pub </span>host: String,
<a href=#53 id=53 data-nosnippet>53</a>    <span class="kw">pub </span>port: u16,
<a href=#54 id=54 data-nosnippet>54</a>    <span class="kw">pub </span>protocol: String,
<a href=#55 id=55 data-nosnippet>55</a>    <span class="kw">pub </span>username: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#56 id=56 data-nosnippet>56</a>    <span class="kw">pub </span>password: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#57 id=57 data-nosnippet>57</a>    <span class="kw">pub </span>country: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#58 id=58 data-nosnippet>58</a>    <span class="kw">pub </span>provider: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#59 id=59 data-nosnippet>59</a>}
<a href=#60 id=60 data-nosnippet>60</a>
<a href=#61 id=61 data-nosnippet>61</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#62 id=62 data-nosnippet>62</a></span><span class="kw">pub struct </span>ProxyTestResult {
<a href=#63 id=63 data-nosnippet>63</a>    <span class="kw">pub </span>proxy_id: String,
<a href=#64 id=64 data-nosnippet>64</a>    <span class="kw">pub </span>is_working: bool,
<a href=#65 id=65 data-nosnippet>65</a>    <span class="kw">pub </span>response_time: <span class="prelude-ty">Option</span>&lt;u64&gt;,
<a href=#66 id=66 data-nosnippet>66</a>    <span class="kw">pub </span>error_message: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#67 id=67 data-nosnippet>67</a>}
<a href=#68 id=68 data-nosnippet>68</a>
<a href=#69 id=69 data-nosnippet>69</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#70 id=70 data-nosnippet>70</a></span><span class="kw">pub struct </span>AccountPoolStatus {
<a href=#71 id=71 data-nosnippet>71</a>    <span class="kw">pub </span>total_accounts: u32,
<a href=#72 id=72 data-nosnippet>72</a>    <span class="kw">pub </span>active_accounts: u32,
<a href=#73 id=73 data-nosnippet>73</a>    <span class="kw">pub </span>logged_in_accounts: u32,
<a href=#74 id=74 data-nosnippet>74</a>    <span class="kw">pub </span>healthy_accounts: u32,
<a href=#75 id=75 data-nosnippet>75</a>    <span class="kw">pub </span>average_risk_score: f64,
<a href=#76 id=76 data-nosnippet>76</a>}
<a href=#77 id=77 data-nosnippet>77</a>
<a href=#78 id=78 data-nosnippet>78</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#79 id=79 data-nosnippet>79</a></span><span class="kw">pub struct </span>AccountConfig {
<a href=#80 id=80 data-nosnippet>80</a>    <span class="kw">pub </span>username: String,
<a href=#81 id=81 data-nosnippet>81</a>    <span class="kw">pub </span>password: String,
<a href=#82 id=82 data-nosnippet>82</a>    <span class="kw">pub </span>phone: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#83 id=83 data-nosnippet>83</a>    <span class="kw">pub </span>email: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#84 id=84 data-nosnippet>84</a>}
<a href=#85 id=85 data-nosnippet>85</a>
<a href=#86 id=86 data-nosnippet>86</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#87 id=87 data-nosnippet>87</a></span><span class="kw">pub struct </span>LoginResult {
<a href=#88 id=88 data-nosnippet>88</a>    <span class="kw">pub </span>account_id: String,
<a href=#89 id=89 data-nosnippet>89</a>    <span class="kw">pub </span>success: bool,
<a href=#90 id=90 data-nosnippet>90</a>    <span class="kw">pub </span>error_message: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#91 id=91 data-nosnippet>91</a>    <span class="kw">pub </span>cookies: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#92 id=92 data-nosnippet>92</a>}
<a href=#93 id=93 data-nosnippet>93</a>
<a href=#94 id=94 data-nosnippet>94</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#95 id=95 data-nosnippet>95</a></span><span class="kw">pub struct </span>SystemMetrics {
<a href=#96 id=96 data-nosnippet>96</a>    <span class="kw">pub </span>cpu_usage: f64,
<a href=#97 id=97 data-nosnippet>97</a>    <span class="kw">pub </span>memory_usage: f64,
<a href=#98 id=98 data-nosnippet>98</a>    <span class="kw">pub </span>disk_usage: f64,
<a href=#99 id=99 data-nosnippet>99</a>    <span class="kw">pub </span>network_in: u64,
<a href=#100 id=100 data-nosnippet>100</a>    <span class="kw">pub </span>network_out: u64,
<a href=#101 id=101 data-nosnippet>101</a>    <span class="kw">pub </span>timestamp: u64,
<a href=#102 id=102 data-nosnippet>102</a>}
<a href=#103 id=103 data-nosnippet>103</a>
<a href=#104 id=104 data-nosnippet>104</a><span class="attr">#[derive(Debug, Serialize, Deserialize)]
<a href=#105 id=105 data-nosnippet>105</a></span><span class="kw">pub struct </span>PerformanceStats {
<a href=#106 id=106 data-nosnippet>106</a>    <span class="kw">pub </span>requests_per_second: f64,
<a href=#107 id=107 data-nosnippet>107</a>    <span class="kw">pub </span>average_response_time: f64,
<a href=#108 id=108 data-nosnippet>108</a>    <span class="kw">pub </span>error_rate: f64,
<a href=#109 id=109 data-nosnippet>109</a>    <span class="kw">pub </span>concurrent_connections: u32,
<a href=#110 id=110 data-nosnippet>110</a>    <span class="kw">pub </span>queue_depth: u32,
<a href=#111 id=111 data-nosnippet>111</a>}
<a href=#112 id=112 data-nosnippet>112</a>
<a href=#113 id=113 data-nosnippet>113</a><span class="comment">// 系统管理命令
<a href=#114 id=114 data-nosnippet>114</a></span><span class="attr">#[tauri::command]
<a href=#115 id=115 data-nosnippet>115</a></span><span class="kw">pub async fn </span>get_node_status(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;NodeStatus&gt; {
<a href=#116 id=116 data-nosnippet>116</a>    <span class="comment">// 这里应该从系统监控器获取实际状态
<a href=#117 id=117 data-nosnippet>117</a>    // 目前返回模拟数据
<a href=#118 id=118 data-nosnippet>118</a>    </span><span class="kw">let </span>config = state.config.lock().<span class="kw">await</span>;
<a href=#119 id=119 data-nosnippet>119</a>    
<a href=#120 id=120 data-nosnippet>120</a>    <span class="prelude-val">Ok</span>(NodeStatus {
<a href=#121 id=121 data-nosnippet>121</a>        node_id: config.node_id.clone(),
<a href=#122 id=122 data-nosnippet>122</a>        node_name: config.node_name.clone(),
<a href=#123 id=123 data-nosnippet>123</a>        status: <span class="string">"online"</span>.to_string(),
<a href=#124 id=124 data-nosnippet>124</a>        uptime: <span class="number">3600</span>, <span class="comment">// 1小时
<a href=#125 id=125 data-nosnippet>125</a>        </span>cpu_usage: <span class="number">45.2</span>,
<a href=#126 id=126 data-nosnippet>126</a>        memory_usage: <span class="number">67.8</span>,
<a href=#127 id=127 data-nosnippet>127</a>        disk_usage: <span class="number">23.5</span>,
<a href=#128 id=128 data-nosnippet>128</a>        active_tasks: <span class="number">15</span>,
<a href=#129 id=129 data-nosnippet>129</a>        total_tasks: <span class="number">1250</span>,
<a href=#130 id=130 data-nosnippet>130</a>        success_rate: <span class="number">98.5</span>,
<a href=#131 id=131 data-nosnippet>131</a>    })
<a href=#132 id=132 data-nosnippet>132</a>}
<a href=#133 id=133 data-nosnippet>133</a>
<a href=#134 id=134 data-nosnippet>134</a><span class="attr">#[tauri::command]
<a href=#135 id=135 data-nosnippet>135</a></span><span class="kw">pub async fn </span>start_crawler(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#136 id=136 data-nosnippet>136</a>    <span class="comment">// 启动爬虫引擎
<a href=#137 id=137 data-nosnippet>137</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>crawler_engine = state.crawler_engine.lock().<span class="kw">await</span>;
<a href=#138 id=138 data-nosnippet>138</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_engine) = crawler_engine.as_mut() {
<a href=#139 id=139 data-nosnippet>139</a>        <span class="comment">// engine.start().await?;
<a href=#140 id=140 data-nosnippet>140</a>        </span><span class="prelude-val">Ok</span>(<span class="string">"爬虫引擎启动成功"</span>.to_string())
<a href=#141 id=141 data-nosnippet>141</a>    } <span class="kw">else </span>{
<a href=#142 id=142 data-nosnippet>142</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Crawler(<span class="string">"爬虫引擎未初始化"</span>.to_string()))
<a href=#143 id=143 data-nosnippet>143</a>    }
<a href=#144 id=144 data-nosnippet>144</a>}
<a href=#145 id=145 data-nosnippet>145</a>
<a href=#146 id=146 data-nosnippet>146</a><span class="attr">#[tauri::command]
<a href=#147 id=147 data-nosnippet>147</a></span><span class="kw">pub async fn </span>stop_crawler(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#148 id=148 data-nosnippet>148</a>    <span class="comment">// 停止爬虫引擎
<a href=#149 id=149 data-nosnippet>149</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>crawler_engine = state.crawler_engine.lock().<span class="kw">await</span>;
<a href=#150 id=150 data-nosnippet>150</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_engine) = crawler_engine.as_mut() {
<a href=#151 id=151 data-nosnippet>151</a>        <span class="comment">// engine.stop().await?;
<a href=#152 id=152 data-nosnippet>152</a>        </span><span class="prelude-val">Ok</span>(<span class="string">"爬虫引擎停止成功"</span>.to_string())
<a href=#153 id=153 data-nosnippet>153</a>    } <span class="kw">else </span>{
<a href=#154 id=154 data-nosnippet>154</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Crawler(<span class="string">"爬虫引擎未初始化"</span>.to_string()))
<a href=#155 id=155 data-nosnippet>155</a>    }
<a href=#156 id=156 data-nosnippet>156</a>}
<a href=#157 id=157 data-nosnippet>157</a>
<a href=#158 id=158 data-nosnippet>158</a><span class="attr">#[tauri::command]
<a href=#159 id=159 data-nosnippet>159</a></span><span class="kw">pub async fn </span>restart_crawler(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#160 id=160 data-nosnippet>160</a>    <span class="comment">// 重启爬虫引擎
<a href=#161 id=161 data-nosnippet>161</a>    </span>stop_crawler(state.clone()).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#162 id=162 data-nosnippet>162</a>    tokio::time::sleep(tokio::time::Duration::from_secs(<span class="number">2</span>)).<span class="kw">await</span>;
<a href=#163 id=163 data-nosnippet>163</a>    start_crawler(state).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#164 id=164 data-nosnippet>164</a>    <span class="prelude-val">Ok</span>(<span class="string">"爬虫引擎重启成功"</span>.to_string())
<a href=#165 id=165 data-nosnippet>165</a>}
<a href=#166 id=166 data-nosnippet>166</a>
<a href=#167 id=167 data-nosnippet>167</a><span class="comment">// 任务管理命令
<a href=#168 id=168 data-nosnippet>168</a></span><span class="attr">#[tauri::command]
<a href=#169 id=169 data-nosnippet>169</a></span><span class="kw">pub async fn </span>get_task_queue_status(_state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;TaskQueueStatus&gt; {
<a href=#170 id=170 data-nosnippet>170</a>    <span class="comment">// 从任务调度器获取队列状态
<a href=#171 id=171 data-nosnippet>171</a>    </span><span class="prelude-val">Ok</span>(TaskQueueStatus {
<a href=#172 id=172 data-nosnippet>172</a>        pending_tasks: <span class="number">25</span>,
<a href=#173 id=173 data-nosnippet>173</a>        running_tasks: <span class="number">15</span>,
<a href=#174 id=174 data-nosnippet>174</a>        completed_tasks: <span class="number">1200</span>,
<a href=#175 id=175 data-nosnippet>175</a>        failed_tasks: <span class="number">30</span>,
<a href=#176 id=176 data-nosnippet>176</a>        queue_length: <span class="number">40</span>,
<a href=#177 id=177 data-nosnippet>177</a>        processing_speed: <span class="number">12.5</span>,
<a href=#178 id=178 data-nosnippet>178</a>    })
<a href=#179 id=179 data-nosnippet>179</a>}
<a href=#180 id=180 data-nosnippet>180</a>
<a href=#181 id=181 data-nosnippet>181</a><span class="attr">#[tauri::command]
<a href=#182 id=182 data-nosnippet>182</a></span><span class="kw">pub async fn </span>start_task_processing(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#183 id=183 data-nosnippet>183</a>    <span class="comment">// 启动任务处理
<a href=#184 id=184 data-nosnippet>184</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>task_scheduler = state.task_scheduler.lock().<span class="kw">await</span>;
<a href=#185 id=185 data-nosnippet>185</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_scheduler) = task_scheduler.as_mut() {
<a href=#186 id=186 data-nosnippet>186</a>        <span class="comment">// scheduler.start_processing().await?;
<a href=#187 id=187 data-nosnippet>187</a>        </span><span class="prelude-val">Ok</span>(<span class="string">"任务处理启动成功"</span>.to_string())
<a href=#188 id=188 data-nosnippet>188</a>    } <span class="kw">else </span>{
<a href=#189 id=189 data-nosnippet>189</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Scheduler(<span class="string">"任务调度器未初始化"</span>.to_string()))
<a href=#190 id=190 data-nosnippet>190</a>    }
<a href=#191 id=191 data-nosnippet>191</a>}
<a href=#192 id=192 data-nosnippet>192</a>
<a href=#193 id=193 data-nosnippet>193</a><span class="attr">#[tauri::command]
<a href=#194 id=194 data-nosnippet>194</a></span><span class="kw">pub async fn </span>stop_task_processing(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#195 id=195 data-nosnippet>195</a>    <span class="comment">// 停止任务处理
<a href=#196 id=196 data-nosnippet>196</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>task_scheduler = state.task_scheduler.lock().<span class="kw">await</span>;
<a href=#197 id=197 data-nosnippet>197</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_scheduler) = task_scheduler.as_mut() {
<a href=#198 id=198 data-nosnippet>198</a>        <span class="comment">// scheduler.stop_processing().await?;
<a href=#199 id=199 data-nosnippet>199</a>        </span><span class="prelude-val">Ok</span>(<span class="string">"任务处理停止成功"</span>.to_string())
<a href=#200 id=200 data-nosnippet>200</a>    } <span class="kw">else </span>{
<a href=#201 id=201 data-nosnippet>201</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Scheduler(<span class="string">"任务调度器未初始化"</span>.to_string()))
<a href=#202 id=202 data-nosnippet>202</a>    }
<a href=#203 id=203 data-nosnippet>203</a>}
<a href=#204 id=204 data-nosnippet>204</a>
<a href=#205 id=205 data-nosnippet>205</a><span class="attr">#[tauri::command]
<a href=#206 id=206 data-nosnippet>206</a></span><span class="kw">pub async fn </span>get_task_statistics(_state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;TaskStatistics&gt; {
<a href=#207 id=207 data-nosnippet>207</a>    <span class="comment">// 获取任务统计信息
<a href=#208 id=208 data-nosnippet>208</a>    </span><span class="prelude-val">Ok</span>(TaskStatistics {
<a href=#209 id=209 data-nosnippet>209</a>        total_processed: <span class="number">1230</span>,
<a href=#210 id=210 data-nosnippet>210</a>        success_count: <span class="number">1200</span>,
<a href=#211 id=211 data-nosnippet>211</a>        failure_count: <span class="number">30</span>,
<a href=#212 id=212 data-nosnippet>212</a>        average_duration: <span class="number">2.5</span>,
<a href=#213 id=213 data-nosnippet>213</a>        success_rate: <span class="number">97.6</span>,
<a href=#214 id=214 data-nosnippet>214</a>        tasks_per_hour: <span class="number">480.0</span>,
<a href=#215 id=215 data-nosnippet>215</a>    })
<a href=#216 id=216 data-nosnippet>216</a>}
<a href=#217 id=217 data-nosnippet>217</a>
<a href=#218 id=218 data-nosnippet>218</a><span class="comment">// 代理池管理命令
<a href=#219 id=219 data-nosnippet>219</a></span><span class="attr">#[tauri::command]
<a href=#220 id=220 data-nosnippet>220</a></span><span class="kw">pub async fn </span>get_proxy_pool_status(_state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;ProxyPoolStatus&gt; {
<a href=#221 id=221 data-nosnippet>221</a>    <span class="comment">// 从代理管理器获取状态
<a href=#222 id=222 data-nosnippet>222</a>    </span><span class="prelude-val">Ok</span>(ProxyPoolStatus {
<a href=#223 id=223 data-nosnippet>223</a>        total_proxies: <span class="number">50</span>,
<a href=#224 id=224 data-nosnippet>224</a>        active_proxies: <span class="number">45</span>,
<a href=#225 id=225 data-nosnippet>225</a>        healthy_proxies: <span class="number">42</span>,
<a href=#226 id=226 data-nosnippet>226</a>        average_response_time: <span class="number">1.2</span>,
<a href=#227 id=227 data-nosnippet>227</a>        success_rate: <span class="number">94.0</span>,
<a href=#228 id=228 data-nosnippet>228</a>    })
<a href=#229 id=229 data-nosnippet>229</a>}
<a href=#230 id=230 data-nosnippet>230</a>
<a href=#231 id=231 data-nosnippet>231</a><span class="attr">#[tauri::command]
<a href=#232 id=232 data-nosnippet>232</a></span><span class="kw">pub async fn </span>add_proxy(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;, _proxy: ProxyConfig) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#233 id=233 data-nosnippet>233</a>    <span class="comment">// 添加代理
<a href=#234 id=234 data-nosnippet>234</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>proxy_manager = state.proxy_manager.lock().<span class="kw">await</span>;
<a href=#235 id=235 data-nosnippet>235</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_manager) = proxy_manager.as_mut() {
<a href=#236 id=236 data-nosnippet>236</a>        <span class="comment">// manager.add_proxy(proxy).await?;
<a href=#237 id=237 data-nosnippet>237</a>        </span><span class="prelude-val">Ok</span>(<span class="string">"代理添加成功"</span>.to_string())
<a href=#238 id=238 data-nosnippet>238</a>    } <span class="kw">else </span>{
<a href=#239 id=239 data-nosnippet>239</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Proxy(<span class="string">"代理管理器未初始化"</span>.to_string()))
<a href=#240 id=240 data-nosnippet>240</a>    }
<a href=#241 id=241 data-nosnippet>241</a>}
<a href=#242 id=242 data-nosnippet>242</a>
<a href=#243 id=243 data-nosnippet>243</a><span class="attr">#[tauri::command]
<a href=#244 id=244 data-nosnippet>244</a></span><span class="kw">pub async fn </span>remove_proxy(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;, _proxy_id: String) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#245 id=245 data-nosnippet>245</a>    <span class="comment">// 移除代理
<a href=#246 id=246 data-nosnippet>246</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>proxy_manager = state.proxy_manager.lock().<span class="kw">await</span>;
<a href=#247 id=247 data-nosnippet>247</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_manager) = proxy_manager.as_mut() {
<a href=#248 id=248 data-nosnippet>248</a>        <span class="comment">// manager.remove_proxy(&amp;proxy_id).await?;
<a href=#249 id=249 data-nosnippet>249</a>        </span><span class="prelude-val">Ok</span>(<span class="string">"代理移除成功"</span>.to_string())
<a href=#250 id=250 data-nosnippet>250</a>    } <span class="kw">else </span>{
<a href=#251 id=251 data-nosnippet>251</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Proxy(<span class="string">"代理管理器未初始化"</span>.to_string()))
<a href=#252 id=252 data-nosnippet>252</a>    }
<a href=#253 id=253 data-nosnippet>253</a>}
<a href=#254 id=254 data-nosnippet>254</a>
<a href=#255 id=255 data-nosnippet>255</a><span class="attr">#[tauri::command]
<a href=#256 id=256 data-nosnippet>256</a></span><span class="kw">pub async fn </span>test_proxy(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;, proxy_id: String) -&gt; <span class="prelude-ty">Result</span>&lt;ProxyTestResult&gt; {
<a href=#257 id=257 data-nosnippet>257</a>    <span class="comment">// 测试代理
<a href=#258 id=258 data-nosnippet>258</a>    </span><span class="kw">let </span>proxy_manager = state.proxy_manager.lock().<span class="kw">await</span>;
<a href=#259 id=259 data-nosnippet>259</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_manager) = proxy_manager.as_ref() {
<a href=#260 id=260 data-nosnippet>260</a>        <span class="comment">// let result = manager.test_proxy(&amp;proxy_id).await?;
<a href=#261 id=261 data-nosnippet>261</a>        </span><span class="prelude-val">Ok</span>(ProxyTestResult {
<a href=#262 id=262 data-nosnippet>262</a>            proxy_id,
<a href=#263 id=263 data-nosnippet>263</a>            is_working: <span class="bool-val">true</span>,
<a href=#264 id=264 data-nosnippet>264</a>            response_time: <span class="prelude-val">Some</span>(<span class="number">1200</span>),
<a href=#265 id=265 data-nosnippet>265</a>            error_message: <span class="prelude-val">None</span>,
<a href=#266 id=266 data-nosnippet>266</a>        })
<a href=#267 id=267 data-nosnippet>267</a>    } <span class="kw">else </span>{
<a href=#268 id=268 data-nosnippet>268</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Proxy(<span class="string">"代理管理器未初始化"</span>.to_string()))
<a href=#269 id=269 data-nosnippet>269</a>    }
<a href=#270 id=270 data-nosnippet>270</a>}
<a href=#271 id=271 data-nosnippet>271</a>
<a href=#272 id=272 data-nosnippet>272</a><span class="comment">// 账号管理命令
<a href=#273 id=273 data-nosnippet>273</a></span><span class="attr">#[tauri::command]
<a href=#274 id=274 data-nosnippet>274</a></span><span class="kw">pub async fn </span>get_account_pool_status(_state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;AccountPoolStatus&gt; {
<a href=#275 id=275 data-nosnippet>275</a>    <span class="comment">// 从账号管理器获取状态
<a href=#276 id=276 data-nosnippet>276</a>    </span><span class="prelude-val">Ok</span>(AccountPoolStatus {
<a href=#277 id=277 data-nosnippet>277</a>        total_accounts: <span class="number">20</span>,
<a href=#278 id=278 data-nosnippet>278</a>        active_accounts: <span class="number">18</span>,
<a href=#279 id=279 data-nosnippet>279</a>        logged_in_accounts: <span class="number">15</span>,
<a href=#280 id=280 data-nosnippet>280</a>        healthy_accounts: <span class="number">17</span>,
<a href=#281 id=281 data-nosnippet>281</a>        average_risk_score: <span class="number">0.15</span>,
<a href=#282 id=282 data-nosnippet>282</a>    })
<a href=#283 id=283 data-nosnippet>283</a>}
<a href=#284 id=284 data-nosnippet>284</a>
<a href=#285 id=285 data-nosnippet>285</a><span class="attr">#[tauri::command]
<a href=#286 id=286 data-nosnippet>286</a></span><span class="kw">pub async fn </span>add_account(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;, _account: AccountConfig) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#287 id=287 data-nosnippet>287</a>    <span class="comment">// 添加账号
<a href=#288 id=288 data-nosnippet>288</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>account_manager = state.account_manager.lock().<span class="kw">await</span>;
<a href=#289 id=289 data-nosnippet>289</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_manager) = account_manager.as_mut() {
<a href=#290 id=290 data-nosnippet>290</a>        <span class="comment">// manager.add_account(account).await?;
<a href=#291 id=291 data-nosnippet>291</a>        </span><span class="prelude-val">Ok</span>(<span class="string">"账号添加成功"</span>.to_string())
<a href=#292 id=292 data-nosnippet>292</a>    } <span class="kw">else </span>{
<a href=#293 id=293 data-nosnippet>293</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Account(<span class="string">"账号管理器未初始化"</span>.to_string()))
<a href=#294 id=294 data-nosnippet>294</a>    }
<a href=#295 id=295 data-nosnippet>295</a>}
<a href=#296 id=296 data-nosnippet>296</a>
<a href=#297 id=297 data-nosnippet>297</a><span class="attr">#[tauri::command]
<a href=#298 id=298 data-nosnippet>298</a></span><span class="kw">pub async fn </span>login_account(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;, account_id: String) -&gt; <span class="prelude-ty">Result</span>&lt;LoginResult&gt; {
<a href=#299 id=299 data-nosnippet>299</a>    <span class="comment">// 登录账号
<a href=#300 id=300 data-nosnippet>300</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>account_manager = state.account_manager.lock().<span class="kw">await</span>;
<a href=#301 id=301 data-nosnippet>301</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_manager) = account_manager.as_mut() {
<a href=#302 id=302 data-nosnippet>302</a>        <span class="comment">// let result = manager.login_account(&amp;account_id).await?;
<a href=#303 id=303 data-nosnippet>303</a>        </span><span class="prelude-val">Ok</span>(LoginResult {
<a href=#304 id=304 data-nosnippet>304</a>            account_id,
<a href=#305 id=305 data-nosnippet>305</a>            success: <span class="bool-val">true</span>,
<a href=#306 id=306 data-nosnippet>306</a>            error_message: <span class="prelude-val">None</span>,
<a href=#307 id=307 data-nosnippet>307</a>            cookies: <span class="prelude-val">Some</span>(<span class="string">"session_cookies"</span>.to_string()),
<a href=#308 id=308 data-nosnippet>308</a>        })
<a href=#309 id=309 data-nosnippet>309</a>    } <span class="kw">else </span>{
<a href=#310 id=310 data-nosnippet>310</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Account(<span class="string">"账号管理器未初始化"</span>.to_string()))
<a href=#311 id=311 data-nosnippet>311</a>    }
<a href=#312 id=312 data-nosnippet>312</a>}
<a href=#313 id=313 data-nosnippet>313</a>
<a href=#314 id=314 data-nosnippet>314</a><span class="attr">#[tauri::command]
<a href=#315 id=315 data-nosnippet>315</a></span><span class="kw">pub async fn </span>refresh_account_cookies(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;, _account_id: String) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#316 id=316 data-nosnippet>316</a>    <span class="comment">// 刷新账号Cookie
<a href=#317 id=317 data-nosnippet>317</a>    </span><span class="kw">let </span><span class="kw-2">mut </span>account_manager = state.account_manager.lock().<span class="kw">await</span>;
<a href=#318 id=318 data-nosnippet>318</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_manager) = account_manager.as_mut() {
<a href=#319 id=319 data-nosnippet>319</a>        <span class="comment">// manager.refresh_cookies(&amp;account_id).await?;
<a href=#320 id=320 data-nosnippet>320</a>        </span><span class="prelude-val">Ok</span>(<span class="string">"Cookie刷新成功"</span>.to_string())
<a href=#321 id=321 data-nosnippet>321</a>    } <span class="kw">else </span>{
<a href=#322 id=322 data-nosnippet>322</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Account(<span class="string">"账号管理器未初始化"</span>.to_string()))
<a href=#323 id=323 data-nosnippet>323</a>    }
<a href=#324 id=324 data-nosnippet>324</a>}
<a href=#325 id=325 data-nosnippet>325</a>
<a href=#326 id=326 data-nosnippet>326</a><span class="comment">// 监控命令
<a href=#327 id=327 data-nosnippet>327</a></span><span class="attr">#[tauri::command]
<a href=#328 id=328 data-nosnippet>328</a></span><span class="kw">pub async fn </span>get_system_metrics(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;SystemMetrics&gt; {
<a href=#329 id=329 data-nosnippet>329</a>    <span class="comment">// 获取系统指标
<a href=#330 id=330 data-nosnippet>330</a>    </span><span class="kw">let </span>system_monitor = state.system_monitor.lock().<span class="kw">await</span>;
<a href=#331 id=331 data-nosnippet>331</a>    <span class="kw">if let </span><span class="prelude-val">Some</span>(_monitor) = system_monitor.as_ref() {
<a href=#332 id=332 data-nosnippet>332</a>        <span class="comment">// let metrics = monitor.get_current_metrics().await?;
<a href=#333 id=333 data-nosnippet>333</a>        </span><span class="prelude-val">Ok</span>(SystemMetrics {
<a href=#334 id=334 data-nosnippet>334</a>            cpu_usage: <span class="number">45.2</span>,
<a href=#335 id=335 data-nosnippet>335</a>            memory_usage: <span class="number">67.8</span>,
<a href=#336 id=336 data-nosnippet>336</a>            disk_usage: <span class="number">23.5</span>,
<a href=#337 id=337 data-nosnippet>337</a>            network_in: <span class="number">1024000</span>,
<a href=#338 id=338 data-nosnippet>338</a>            network_out: <span class="number">512000</span>,
<a href=#339 id=339 data-nosnippet>339</a>            timestamp: chrono::Utc::now().timestamp() <span class="kw">as </span>u64,
<a href=#340 id=340 data-nosnippet>340</a>        })
<a href=#341 id=341 data-nosnippet>341</a>    } <span class="kw">else </span>{
<a href=#342 id=342 data-nosnippet>342</a>        <span class="prelude-val">Err</span>(<span class="kw">crate</span>::error::AppError::Monitor(<span class="string">"系统监控器未初始化"</span>.to_string()))
<a href=#343 id=343 data-nosnippet>343</a>    }
<a href=#344 id=344 data-nosnippet>344</a>}
<a href=#345 id=345 data-nosnippet>345</a>
<a href=#346 id=346 data-nosnippet>346</a><span class="attr">#[tauri::command]
<a href=#347 id=347 data-nosnippet>347</a></span><span class="kw">pub async fn </span>get_performance_stats(_state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;PerformanceStats&gt; {
<a href=#348 id=348 data-nosnippet>348</a>    <span class="comment">// 获取性能统计
<a href=#349 id=349 data-nosnippet>349</a>    </span><span class="prelude-val">Ok</span>(PerformanceStats {
<a href=#350 id=350 data-nosnippet>350</a>        requests_per_second: <span class="number">12.5</span>,
<a href=#351 id=351 data-nosnippet>351</a>        average_response_time: <span class="number">2.3</span>,
<a href=#352 id=352 data-nosnippet>352</a>        error_rate: <span class="number">2.4</span>,
<a href=#353 id=353 data-nosnippet>353</a>        concurrent_connections: <span class="number">15</span>,
<a href=#354 id=354 data-nosnippet>354</a>        queue_depth: <span class="number">25</span>,
<a href=#355 id=355 data-nosnippet>355</a>    })
<a href=#356 id=356 data-nosnippet>356</a>}
<a href=#357 id=357 data-nosnippet>357</a>
<a href=#358 id=358 data-nosnippet>358</a><span class="comment">// 配置管理命令
<a href=#359 id=359 data-nosnippet>359</a></span><span class="attr">#[tauri::command]
<a href=#360 id=360 data-nosnippet>360</a></span><span class="kw">pub async fn </span>get_config(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;AppConfig&gt; {
<a href=#361 id=361 data-nosnippet>361</a>    <span class="comment">// 获取当前配置
<a href=#362 id=362 data-nosnippet>362</a>    </span><span class="kw">let </span>config = state.config.lock().<span class="kw">await</span>;
<a href=#363 id=363 data-nosnippet>363</a>    <span class="prelude-val">Ok</span>(config.clone())
<a href=#364 id=364 data-nosnippet>364</a>}
<a href=#365 id=365 data-nosnippet>365</a>
<a href=#366 id=366 data-nosnippet>366</a><span class="attr">#[tauri::command]
<a href=#367 id=367 data-nosnippet>367</a></span><span class="kw">pub async fn </span>update_config(state: State&lt;<span class="lifetime">'_</span>, AppState&gt;, new_config: AppConfig) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#368 id=368 data-nosnippet>368</a>    <span class="comment">// 更新配置
<a href=#369 id=369 data-nosnippet>369</a>    </span>new_config.validate()<span class="question-mark">?</span>;
<a href=#370 id=370 data-nosnippet>370</a>
<a href=#371 id=371 data-nosnippet>371</a>    <span class="kw">let </span><span class="kw-2">mut </span>config = state.config.lock().<span class="kw">await</span>;
<a href=#372 id=372 data-nosnippet>372</a>    <span class="kw-2">*</span>config = new_config.clone();
<a href=#373 id=373 data-nosnippet>373</a>
<a href=#374 id=374 data-nosnippet>374</a>    <span class="comment">// 保存配置到文件
<a href=#375 id=375 data-nosnippet>375</a>    </span>config.save().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#376 id=376 data-nosnippet>376</a>
<a href=#377 id=377 data-nosnippet>377</a>    <span class="prelude-val">Ok</span>(<span class="string">"配置更新成功"</span>.to_string())
<a href=#378 id=378 data-nosnippet>378</a>}
<a href=#379 id=379 data-nosnippet>379</a>
<a href=#380 id=380 data-nosnippet>380</a><span class="comment">// ============ 浏览器相关命令 ============
<a href=#381 id=381 data-nosnippet>381</a>
<a href=#382 id=382 data-nosnippet>382</a></span><span class="attr">#[tauri::command]
<a href=#383 id=383 data-nosnippet>383</a></span><span class="kw">pub async fn </span>test_browser() -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#384 id=384 data-nosnippet>384</a>    <span class="kw">let </span>manager = get_browser_manager();
<a href=#385 id=385 data-nosnippet>385</a>    <span class="kw">let </span>manager_guard = manager.lock().<span class="kw">await</span>;
<a href=#386 id=386 data-nosnippet>386</a>    manager_guard.test_browser().<span class="kw">await
<a href=#387 id=387 data-nosnippet>387</a></span>}
<a href=#388 id=388 data-nosnippet>388</a>
<a href=#389 id=389 data-nosnippet>389</a><span class="attr">#[tauri::command]
<a href=#390 id=390 data-nosnippet>390</a></span><span class="kw">pub async fn </span>login_weibo(username: String, password: String) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="kw">crate</span>::browser::LoginResult&gt; {
<a href=#391 id=391 data-nosnippet>391</a>    <span class="kw">let </span>credentials = LoginCredentials { username, password };
<a href=#392 id=392 data-nosnippet>392</a>    <span class="kw">let </span>manager = get_browser_manager();
<a href=#393 id=393 data-nosnippet>393</a>    <span class="kw">let </span>manager_guard = manager.lock().<span class="kw">await</span>;
<a href=#394 id=394 data-nosnippet>394</a>    manager_guard.login_weibo(credentials).<span class="kw">await
<a href=#395 id=395 data-nosnippet>395</a></span>}
<a href=#396 id=396 data-nosnippet>396</a>
<a href=#397 id=397 data-nosnippet>397</a><span class="attr">#[tauri::command]
<a href=#398 id=398 data-nosnippet>398</a></span><span class="kw">pub async fn </span>update_browser_config(config: BrowserConfig) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#399 id=399 data-nosnippet>399</a>    <span class="kw">let </span>manager = get_browser_manager();
<a href=#400 id=400 data-nosnippet>400</a>    <span class="kw">let </span><span class="kw-2">mut </span>manager_guard = manager.lock().<span class="kw">await</span>;
<a href=#401 id=401 data-nosnippet>401</a>    manager_guard.update_config(config);
<a href=#402 id=402 data-nosnippet>402</a>    <span class="prelude-val">Ok</span>(<span class="string">"浏览器配置更新成功"</span>.to_string())
<a href=#403 id=403 data-nosnippet>403</a>}
<a href=#404 id=404 data-nosnippet>404</a>
<a href=#405 id=405 data-nosnippet>405</a><span class="attr">#[tauri::command]
<a href=#406 id=406 data-nosnippet>406</a></span><span class="kw">pub async fn </span>get_browser_config() -&gt; <span class="prelude-ty">Result</span>&lt;BrowserConfig&gt; {
<a href=#407 id=407 data-nosnippet>407</a>    <span class="kw">let </span>manager = get_browser_manager();
<a href=#408 id=408 data-nosnippet>408</a>    <span class="kw">let </span>manager_guard = manager.lock().<span class="kw">await</span>;
<a href=#409 id=409 data-nosnippet>409</a>    <span class="prelude-val">Ok</span>(manager_guard.get_config().clone())
<a href=#410 id=410 data-nosnippet>410</a>}</code></pre></div></section></main></body></html>