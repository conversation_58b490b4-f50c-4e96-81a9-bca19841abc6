import React from 'react'
import {
  AreaChart as RechartsAreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import {
  BaseChartProps,
  ChartContainer,
  CustomTooltip,
  chartColorPalette,
  defaultGridConfig,
  defaultXAxisConfig,
  defaultYAxisConfig,
  defaultLegendConfig,
  formatters
} from './BaseChart'

export interface AreaChartProps extends BaseChartProps {
  areas?: Array<{
    key: string
    name?: string
    color?: string
    fillOpacity?: number
    strokeWidth?: number
    stackId?: string
  }>
  stacked?: boolean
  smooth?: boolean
  connectNulls?: boolean
  formatter?: (value: any, name: string) => [React.ReactNode, string]
  labelFormatter?: (label: any) => React.ReactNode
}

export const AreaChart: React.FC<AreaChartProps> = ({
  data,
  height = 300,
  className,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  showXAxis = true,
  showYAxis = true,
  xAxisKey = 'name',
  color = chartColorPalette[0],
  colors = chartColorPalette,
  loading,
  error,
  title,
  subtitle,
  areas,
  stacked = false,
  smooth = true,
  connectNulls = false,
  formatter,
  labelFormatter
}) => {
  // 如果没有指定 areas，则从数据中推断
  const chartAreas = areas || Object.keys(data[0] || {})
    .filter(key => key !== xAxisKey && typeof data[0]?.[key] === 'number')
    .map((key, index) => ({
      key,
      name: key,
      color: colors[index % colors.length],
      stackId: stacked ? 'stack' : undefined
    }))

  return (
    <ChartContainer
      title={title}
      subtitle={subtitle}
      loading={loading}
      error={error}
      className={className}
      height={height}
    >
      <RechartsAreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <defs>
          {chartAreas.map((area, index) => (
            <linearGradient key={area.key} id={`gradient-${area.key}`} x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={area.color} stopOpacity={0.8} />
              <stop offset="95%" stopColor={area.color} stopOpacity={0.1} />
            </linearGradient>
          ))}
        </defs>
        
        {showGrid && <CartesianGrid {...defaultGridConfig} />}
        
        {showXAxis && (
          <XAxis 
            dataKey={xAxisKey}
            {...defaultXAxisConfig}
          />
        )}
        
        {showYAxis && (
          <YAxis {...defaultYAxisConfig} />
        )}
        
        {showTooltip && (
          <Tooltip
            content={<CustomTooltip formatter={formatter} labelFormatter={labelFormatter} />}
          />
        )}
        
        {showLegend && <Legend {...defaultLegendConfig} />}
        
        {chartAreas.map((area, index) => (
          <Area
            key={area.key}
            type={smooth ? "monotone" : "linear"}
            dataKey={area.key}
            name={area.name}
            stackId={area.stackId}
            stroke={area.color}
            fill={`url(#gradient-${area.key})`}
            fillOpacity={area.fillOpacity || 1}
            strokeWidth={area.strokeWidth || 2}
            connectNulls={connectNulls}
          />
        ))}
      </RechartsAreaChart>
    </ChartContainer>
  )
}

// 简单面积图
export const SimpleAreaChart: React.FC<Omit<AreaChartProps, 'areas'> & {
  yKey?: string
}> = ({ yKey = 'value', ...props }) => {
  return (
    <AreaChart
      {...props}
      areas={[{
        key: yKey,
        name: yKey,
        color: props.color
      }]}
    />
  )
}

// 堆叠面积图
export const StackedAreaChart: React.FC<AreaChartProps & {
  metrics: Array<{
    key: string
    name: string
    color?: string
    unit?: string
  }>
}> = ({ metrics, formatter, ...props }) => {
  const areas = metrics.map((metric, index) => ({
    key: metric.key,
    name: metric.name,
    color: metric.color || chartColorPalette[index % chartColorPalette.length],
    stackId: 'stack'
  }))

  const customFormatter = formatter || ((value: any, name: string) => {
    const metric = metrics.find(m => m.name === name)
    const unit = metric?.unit || ''
    return [
      typeof value === 'number' ? formatters.number(value) + unit : value,
      name
    ]
  })

  return (
    <AreaChart
      {...props}
      areas={areas}
      stacked={true}
      showLegend={true}
      formatter={customFormatter}
    />
  )
}

// 百分比堆叠面积图
export const PercentageAreaChart: React.FC<AreaChartProps & {
  metrics: Array<{
    key: string
    name: string
    color?: string
  }>
}> = ({ data, metrics, ...props }) => {
  // 计算百分比数据
  const percentageData = React.useMemo(() => {
    return data.map(item => {
      const total = metrics.reduce((sum, metric) => sum + (item[metric.key] || 0), 0)
      const percentageItem = { ...item }
      
      metrics.forEach(metric => {
        percentageItem[metric.key] = total > 0 ? ((item[metric.key] || 0) / total) * 100 : 0
      })
      
      return percentageItem
    })
  }, [data, metrics])

  const areas = metrics.map((metric, index) => ({
    key: metric.key,
    name: metric.name,
    color: metric.color || chartColorPalette[index % chartColorPalette.length],
    stackId: 'stack'
  }))

  const formatter = (value: any, name: string) => {
    return [formatters.percentage(value, 1), name]
  }

  return (
    <AreaChart
      {...props}
      data={percentageData}
      areas={areas}
      stacked={true}
      showLegend={true}
      formatter={formatter}
    />
  )
}

// 流量面积图
export const StreamAreaChart: React.FC<AreaChartProps & {
  baseline?: 'zero' | 'wiggle' | 'silhouette'
}> = ({ baseline = 'zero', ...props }) => {
  return (
    <AreaChart
      {...props}
      stacked={true}
      smooth={true}
    />
  )
}

// 范围面积图
export const RangeAreaChart: React.FC<Omit<AreaChartProps, 'areas'> & {
  minKey: string
  maxKey: string
  avgKey?: string
  rangeColor?: string
  avgColor?: string
}> = ({ 
  minKey, 
  maxKey, 
  avgKey,
  rangeColor = chartColorPalette[0],
  avgColor = chartColorPalette[1],
  ...props 
}) => {
  const areas = [
    {
      key: maxKey,
      name: '最大值',
      color: rangeColor,
      fillOpacity: 0.3
    },
    {
      key: minKey,
      name: '最小值',
      color: rangeColor,
      fillOpacity: 0.3
    }
  ]

  if (avgKey) {
    areas.push({
      key: avgKey,
      name: '平均值',
      color: avgColor,
      fillOpacity: 0.6
    })
  }

  return (
    <AreaChart
      {...props}
      areas={areas}
      showLegend={true}
    />
  )
}

// 性能监控面积图
export const PerformanceAreaChart: React.FC<Omit<AreaChartProps, 'formatter' | 'areas'> & {
  metrics: Array<{
    key: string
    name: string
    color?: string
    type: 'percentage' | 'number' | 'bytes' | 'duration'
  }>
  stacked?: boolean
}> = ({ metrics, stacked = false, ...props }) => {
  const areas = metrics.map((metric, index) => ({
    key: metric.key,
    name: metric.name,
    color: metric.color || chartColorPalette[index % chartColorPalette.length],
    stackId: stacked ? 'stack' : undefined
  }))

  const formatter = (value: any, name: string) => {
    const metric = metrics.find(m => m.name === name)
    if (!metric) return [value, name]

    let formattedValue: string
    switch (metric.type) {
      case 'percentage':
        formattedValue = formatters.percentage(value)
        break
      case 'bytes':
        formattedValue = formatters.bytes(value)
        break
      case 'duration':
        formattedValue = formatters.duration(value)
        break
      default:
        formattedValue = formatters.number(value)
    }

    return [formattedValue, name]
  }

  return (
    <AreaChart
      {...props}
      areas={areas}
      stacked={stacked}
      formatter={formatter}
      showLegend={true}
    />
  )
}
