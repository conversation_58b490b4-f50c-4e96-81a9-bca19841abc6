<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `weibo_crawler_node` crate."><title>weibo_crawler_node - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../" data-static-root-path="../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../static.files/storage-4e99c027.js"></script><script defer src="../crates.js"></script><script defer src="../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../static.files/favicon-044be391.svg"></head><body class="rustdoc mod crate"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><ul class="block"><li><a id="all-types" href="all.html">All Items</a></li></ul><section id="rustdoc-toc"><h3><a href="#modules">Crate Items</a></h3><ul class="block"><li><a href="#modules" title="Modules">Modules</a></li><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#functions" title="Functions">Functions</a></li></ul></section><div id="rustdoc-modnav"></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1>Crate <span>weibo_crawler_node</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../src/weibo_crawler_node/main.rs.html#2-416">Source</a> </span></div><h2 id="modules" class="section-header">Modules<a href="#modules" class="anchor">§</a></h2><dl class="item-table"><dt><a class="mod" href="account/index.html" title="mod weibo_crawler_node::account">account</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="browser/index.html" title="mod weibo_crawler_node::browser">browser</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="commands/index.html" title="mod weibo_crawler_node::commands">commands</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="config/index.html" title="mod weibo_crawler_node::config">config</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="crawler/index.html" title="mod weibo_crawler_node::crawler">crawler</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="error/index.html" title="mod weibo_crawler_node::error">error</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="monitor/index.html" title="mod weibo_crawler_node::monitor">monitor</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="notification/index.html" title="mod weibo_crawler_node::notification">notification</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="proxy/index.html" title="mod weibo_crawler_node::proxy">proxy</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="repository/index.html" title="mod weibo_crawler_node::repository">repository</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="scheduler/index.html" title="mod weibo_crawler_node::scheduler">scheduler</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="service/index.html" title="mod weibo_crawler_node::service">service</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="storage/index.html" title="mod weibo_crawler_node::storage">storage</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="updater/index.html" title="mod weibo_crawler_node::updater">updater</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="mod" href="window_manager/index.html" title="mod weibo_crawler_node::window_manager">window_<wbr>manager</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt></dl><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.AppState.html" title="struct weibo_crawler_node::AppState">AppState</a></dt></dl><h2 id="functions" class="section-header">Functions<a href="#functions" class="anchor">§</a></h2><dl class="item-table"><dt><a class="fn" href="fn.cleanup_on_exit.html" title="fn weibo_crawler_node::cleanup_on_exit">cleanup_<wbr>on_<wbr>exit</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.handle_window_event.html" title="fn weibo_crawler_node::handle_window_event">handle_<wbr>window_<wbr>event</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.initialize_system_components.html" title="fn weibo_crawler_node::initialize_system_components">initialize_<wbr>system_<wbr>components</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.main.html" title="fn weibo_crawler_node::main">main</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.setup_app_initialization.html" title="fn weibo_crawler_node::setup_app_initialization">setup_<wbr>app_<wbr>initialization</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.show_about_dialog.html" title="fn weibo_crawler_node::show_about_dialog">show_<wbr>about_<wbr>dialog</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt></dl></section></div></main></body></html>