<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `weibo_login` mod in crate `weibo_crawler_node`."><title>weibo_crawler_node::browser::weibo_login - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../sidebar-items.js"></script><script defer src="../../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc mod"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Module weibo_<wbr>login</a></h2><h3><a href="#constants">Module Items</a></h3><ul class="block"><li><a href="#constants" title="Constants">Constants</a></li><li><a href="#functions" title="Functions">Functions</a></li></ul></section><div id="rustdoc-modnav"><h2><a href="../index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>browser</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../../index.html">weibo_crawler_node</a>::<wbr><a href="../index.html">browser</a></div><h1>Module <span>weibo_login</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../../src/weibo_crawler_node/browser/weibo_login.rs.html#1-383">Source</a> </span></div><h2 id="constants" class="section-header">Constants<a href="#constants" class="anchor">§</a></h2><dl class="item-table"><dt><a class="constant" href="constant.LOGIN_TIMEOUT.html" title="constant weibo_crawler_node::browser::weibo_login::LOGIN_TIMEOUT">LOGIN_<wbr>TIMEOUT</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="constant" href="constant.WEIBO_LOGIN_URL.html" title="constant weibo_crawler_node::browser::weibo_login::WEIBO_LOGIN_URL">WEIBO_<wbr>LOGIN_<wbr>URL</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt></dl><h2 id="functions" class="section-header">Functions<a href="#functions" class="anchor">§</a></h2><dl class="item-table"><dt><a class="fn" href="fn.extract_login_info.html" title="fn weibo_crawler_node::browser::weibo_login::extract_login_info">extract_<wbr>login_<wbr>info</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.extract_user_info.html" title="fn weibo_crawler_node::browser::weibo_login::extract_user_info">extract_<wbr>user_<wbr>info</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.get_cookies_string.html" title="fn weibo_crawler_node::browser::weibo_login::get_cookies_string">get_<wbr>cookies_<wbr>string</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.get_login_error_message.html" title="fn weibo_crawler_node::browser::weibo_login::get_login_error_message">get_<wbr>login_<wbr>error_<wbr>message</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.is_already_logged_in.html" title="fn weibo_crawler_node::browser::weibo_login::is_already_logged_in">is_<wbr>already_<wbr>logged_<wbr>in</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.is_captcha_required.html" title="fn weibo_crawler_node::browser::weibo_login::is_captcha_required">is_<wbr>captcha_<wbr>required</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.is_login_successful.html" title="fn weibo_crawler_node::browser::weibo_login::is_login_successful">is_<wbr>login_<wbr>successful</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt><dt><a class="fn" href="fn.login_with_playwright.html" title="fn weibo_crawler_node::browser::weibo_login::login_with_playwright">login_<wbr>with_<wbr>playwright</a></dt><dt><a class="fn" href="fn.perform_login.html" title="fn weibo_crawler_node::browser::weibo_login::perform_login">perform_<wbr>login</a><span title="Restricted Visibility">&nbsp;🔒</span> </dt></dl></section></div></main></body></html>