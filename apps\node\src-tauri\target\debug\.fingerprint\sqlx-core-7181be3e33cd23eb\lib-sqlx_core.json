{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 2241668132362809309, "path": 1859583268020246255, "deps": [[5103565458935487, "futures_io", false, 483540127987625433], [40386456601120721, "percent_encoding", false, 4385803709286603540], [530211389790465181, "hex", false, 10041131410227241740], [788558663644978524, "crossbeam_queue", false, 824132666155103493], [966925859616469517, "ahash", false, 14005381909897973836], [1162433738665300155, "crc", false, 15192303933102624098], [1464803193346256239, "event_listener", false, 13182245420540046310], [1811549171721445101, "futures_channel", false, 4452726279273970274], [3150220818285335163, "url", false, 8909501939063229777], [3405817021026194662, "hashlink", false, 17686869096490725196], [3646857438214563691, "futures_intrusive", false, 6237962348392829158], [3666196340704888985, "smallvec", false, 7465509138763068331], [3712811570531045576, "byteorder", false, 16715455526165868325], [3722963349756955755, "once_cell", false, 6428016074931338643], [5986029879202738730, "log", false, 3004741351825838512], [6493259146304816786, "indexmap", false, 10583438374253715528], [7620660491849607393, "futures_core", false, 10280731575054067511], [8008191657135824715, "thiserror", false, 4083214613751541842], [8319709847752024821, "uuid", false, 1140400842430028499], [8569119365930580996, "serde_json", false, 2740896963817221287], [8606274917505247608, "tracing", false, 5492527985859474487], [9689903380558560274, "serde", false, 4202820352788480372], [9857275760291862238, "sha2", false, 3683602715446497376], [9897246384292347999, "chrono", false, 15810629356749029949], [10629569228670356391, "futures_util", false, 2925717846322628741], [10862088793507253106, "sqlformat", false, 16125663392132962311], [11295624341523567602, "rustls", false, 2156011601754642200], [12170264697963848012, "either", false, 11871523782071162857], [12944427623413450645, "tokio", false, 5789438060576990513], [15932120279885307830, "memchr", false, 12618675062812920877], [16066129441945555748, "bytes", false, 2398977389649674023], [16311359161338405624, "rustls_pemfile", false, 17424290552280994911], [16973251432615581304, "tokio_stream", false, 7463431742446949561], [17106256174509013259, "atoi", false, 10014849170561178073], [17605717126308396068, "paste", false, 265303868298634720], [17652733826348741533, "webpki_roots", false, 15434312497983735214]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-7181be3e33cd23eb\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}