{"rustc": 1842507548689473721, "features": "[\"parking\", \"std\"]", "declared_features": "[\"critical-section\", \"default\", \"loom\", \"parking\", \"portable-atomic\", \"portable-atomic-util\", \"portable_atomic_crate\", \"std\"]", "target": 8831420706606120547, "profile": 13827760451848848284, "path": 2403256403827496564, "deps": [[189982446159473706, "parking", false, 15482578812019104169], [1906322745568073236, "pin_project_lite", false, 8108972857832786586], [12100481297174703255, "concurrent_queue", false, 15820679900862136403]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\event-listener-623304047ba1a659\\dep-lib-event_listener", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}