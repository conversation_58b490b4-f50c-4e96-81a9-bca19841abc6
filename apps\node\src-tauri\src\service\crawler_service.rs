use super::{Service, StartableService, MonitorableService, HealthStatus};
use crate::error::Result;
use crate::repository::RepositoryManager;
use async_trait::async_trait;
use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use serde::{Serialize, Deserialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CrawlerMetrics {
    pub active_crawlers: u32,
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub average_response_time: f64,
    pub requests_per_minute: f64,
}

pub struct CrawlerService {
    repository_manager: Arc<RepositoryManager>,
    is_running: AtomicBool,
}

impl CrawlerService {
    pub fn new(repository_manager: Arc<RepositoryManager>) -> Self {
        Self {
            repository_manager,
            is_running: AtomicBool::new(false),
        }
    }

    pub async fn get_crawler_statistics(&self) -> Result<CrawlerMetrics> {
        // 暂时返回模拟数据
        Ok(CrawlerMetrics {
            active_crawlers: 0,
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            average_response_time: 0.0,
            requests_per_minute: 0.0,
        })
    }
}

impl Service for CrawlerService {
    fn name(&self) -> &'static str {
        "CrawlerService"
    }
}

#[async_trait]
impl StartableService for CrawlerService {
    async fn start(&self) -> Result<()> {
        self.is_running.store(true, Ordering::Relaxed);
        tracing::info!("Crawler service started");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        self.is_running.store(false, Ordering::Relaxed);
        tracing::info!("Crawler service stopped");
        Ok(())
    }

    fn is_running(&self) -> bool {
        self.is_running.load(Ordering::Relaxed)
    }
}

#[async_trait]
impl MonitorableService for CrawlerService {
    type Metrics = CrawlerMetrics;

    async fn get_metrics(&self) -> Result<Self::Metrics> {
        self.get_crawler_statistics().await
    }

    async fn get_health_status(&self) -> Result<HealthStatus> {
        if !self.is_running() {
            return Ok(HealthStatus::Unhealthy("Service is not running".to_string()));
        }
        Ok(HealthStatus::Healthy)
    }
}
