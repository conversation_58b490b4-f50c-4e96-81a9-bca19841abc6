use crate::error::{AppError, Result};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tracing::{info, warn, error};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SystemMetrics {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub disk_usage: f64,
    pub network_in: u64,
    pub network_out: u64,
    pub timestamp: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeHealth {
    pub node_id: String,
    pub status: String,
    pub uptime: u64,
    pub last_heartbeat: chrono::DateTime<chrono::Utc>,
    pub active_connections: u32,
    pub error_rate: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub requests_per_second: f64,
    pub average_response_time: f64,
    pub error_rate: f64,
    pub concurrent_connections: u32,
    pub queue_depth: u32,
    pub memory_usage_mb: f64,
    pub cpu_usage_percent: f64,
}

pub struct SystemMonitor {
    metrics_history: Arc<RwLock<Vec<SystemMetrics>>>,
    performance_history: Arc<RwLock<Vec<PerformanceMetrics>>>,
    start_time: SystemTime,
    max_history_size: usize,
}

impl SystemMonitor {
    pub async fn new() -> Result<Self> {
        Ok(Self {
            metrics_history: Arc::new(RwLock::new(Vec::new())),
            performance_history: Arc::new(RwLock::new(Vec::new())),
            start_time: SystemTime::now(),
            max_history_size: 1000, // 保留最近1000个数据点
        })
    }

    pub async fn start_monitoring(&self) -> Result<()> {
        info!("启动系统监控");
        
        // 启动指标收集循环
        self.start_metrics_collection().await;
        
        // 启动性能监控循环
        self.start_performance_monitoring().await;
        
        // 启动心跳发送循环
        self.start_heartbeat_loop().await;
        
        Ok(())
    }

    async fn start_metrics_collection(&self) {
        let metrics_history = Arc::clone(&self.metrics_history);
        let max_history_size = self.max_history_size;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(10)); // 每10秒收集一次
            
            loop {
                interval.tick().await;
                
                let metrics = Self::collect_system_metrics().await;
                
                let mut history = metrics_history.write().await;
                history.push(metrics);
                
                // 限制历史记录大小
                if history.len() > max_history_size {
                    let excess = history.len() - max_history_size;
                    history.drain(0..excess);
                }
            }
        });
    }

    async fn start_performance_monitoring(&self) {
        let performance_history = Arc::clone(&self.performance_history);
        let max_history_size = self.max_history_size;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30)); // 每30秒收集一次
            
            loop {
                interval.tick().await;
                
                let performance = Self::collect_performance_metrics().await;
                
                let mut history = performance_history.write().await;
                history.push(performance);
                
                // 限制历史记录大小
                if history.len() > max_history_size {
                    let excess = history.len() - max_history_size;
                    history.drain(0..excess);
                }
            }
        });
    }

    async fn start_heartbeat_loop(&self) {
        let start_time = self.start_time;

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(30)); // 每30秒发送心跳
            
            loop {
                interval.tick().await;
                
                // 发送心跳到管理节点
                if let Err(e) = Self::send_heartbeat(start_time).await {
                    error!("发送心跳失败: {}", e);
                }
            }
        });
    }

    async fn collect_system_metrics() -> SystemMetrics {
        // 这里应该实现真实的系统指标收集
        // 目前返回模拟数据
        
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        SystemMetrics {
            cpu_usage: Self::get_cpu_usage().await,
            memory_usage: Self::get_memory_usage().await,
            disk_usage: Self::get_disk_usage().await,
            network_in: Self::get_network_in().await,
            network_out: Self::get_network_out().await,
            timestamp,
        }
    }

    async fn collect_performance_metrics() -> PerformanceMetrics {
        // 这里应该实现真实的性能指标收集
        // 目前返回模拟数据
        
        PerformanceMetrics {
            requests_per_second: 12.5,
            average_response_time: 2.3,
            error_rate: 2.4,
            concurrent_connections: 15,
            queue_depth: 25,
            memory_usage_mb: 256.8,
            cpu_usage_percent: 45.2,
        }
    }

    async fn send_heartbeat(start_time: SystemTime) -> Result<()> {
        let uptime = start_time.elapsed()
            .map_err(|e| AppError::Monitor(format!("获取运行时间失败: {}", e)))?
            .as_secs();

        let heartbeat = NodeHealth {
            node_id: std::env::var("NODE_ID").unwrap_or_else(|_| "crawler_node_001".to_string()),
            status: "online".to_string(),
            uptime,
            last_heartbeat: chrono::Utc::now(),
            active_connections: 15,
            error_rate: 2.4,
        };

        // 这里应该发送心跳到管理节点
        // 目前只记录日志
        info!("发送心跳: 节点 {}, 运行时间: {}秒", heartbeat.node_id, heartbeat.uptime);
        
        Ok(())
    }

    // 系统指标收集方法（模拟实现）
    async fn get_cpu_usage() -> f64 {
        // 在实际实现中，这里应该调用系统API获取CPU使用率
        // 可以使用 sysinfo crate
        45.2 + (rand::random::<f64>() - 0.5) * 10.0
    }

    async fn get_memory_usage() -> f64 {
        // 在实际实现中，这里应该调用系统API获取内存使用率
        67.8 + (rand::random::<f64>() - 0.5) * 20.0
    }

    async fn get_disk_usage() -> f64 {
        // 在实际实现中，这里应该调用系统API获取磁盘使用率
        23.5 + (rand::random::<f64>() - 0.5) * 5.0
    }

    async fn get_network_in() -> u64 {
        // 在实际实现中，这里应该获取网络入流量
        1024000 + (rand::random::<u64>() % 512000)
    }

    async fn get_network_out() -> u64 {
        // 在实际实现中，这里应该获取网络出流量
        512000 + (rand::random::<u64>() % 256000)
    }

    // 公共方法
    pub async fn get_current_metrics(&self) -> Result<SystemMetrics> {
        Ok(Self::collect_system_metrics().await)
    }

    pub async fn get_performance_stats(&self) -> Result<PerformanceMetrics> {
        Ok(Self::collect_performance_metrics().await)
    }

    pub async fn get_metrics_history(&self, limit: Option<usize>) -> Vec<SystemMetrics> {
        let history = self.metrics_history.read().await;
        
        if let Some(limit) = limit {
            if history.len() > limit {
                history[history.len() - limit..].to_vec()
            } else {
                history.clone()
            }
        } else {
            history.clone()
        }
    }

    pub async fn get_performance_history(&self, limit: Option<usize>) -> Vec<PerformanceMetrics> {
        let history = self.performance_history.read().await;
        
        if let Some(limit) = limit {
            if history.len() > limit {
                history[history.len() - limit..].to_vec()
            } else {
                history.clone()
            }
        } else {
            history.clone()
        }
    }

    pub async fn get_node_health(&self) -> Result<NodeHealth> {
        let uptime = self.start_time.elapsed()
            .map_err(|e| AppError::Monitor(format!("获取运行时间失败: {}", e)))?
            .as_secs();

        Ok(NodeHealth {
            node_id: std::env::var("NODE_ID").unwrap_or_else(|_| "crawler_node_001".to_string()),
            status: "online".to_string(),
            uptime,
            last_heartbeat: chrono::Utc::now(),
            active_connections: 15,
            error_rate: 2.4,
        })
    }

    pub async fn check_system_health(&self) -> Result<bool> {
        let metrics = self.get_current_metrics().await?;
        
        // 检查系统健康状态
        let is_healthy = metrics.cpu_usage < 90.0 
            && metrics.memory_usage < 90.0 
            && metrics.disk_usage < 95.0;

        if !is_healthy {
            warn!("系统健康检查失败: CPU: {:.1}%, 内存: {:.1}%, 磁盘: {:.1}%", 
                  metrics.cpu_usage, metrics.memory_usage, metrics.disk_usage);
        }

        Ok(is_healthy)
    }

    pub async fn get_uptime(&self) -> Result<u64> {
        Ok(self.start_time.elapsed()
            .map_err(|e| AppError::Monitor(format!("获取运行时间失败: {}", e)))?
            .as_secs())
    }

    pub async fn reset_metrics_history(&self) {
        let mut history = self.metrics_history.write().await;
        history.clear();
        info!("系统指标历史记录已清空");
    }

    pub async fn reset_performance_history(&self) {
        let mut history = self.performance_history.write().await;
        history.clear();
        info!("性能指标历史记录已清空");
    }
}
