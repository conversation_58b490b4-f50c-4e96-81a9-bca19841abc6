<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\error.rs`."><title>error.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../../static.files/src-script-63605ae7.js"></script><script defer src="../../src-files.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node/</div>error.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span>thiserror::Error;
<a href=#2 id=2 data-nosnippet>2</a>
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">pub type </span><span class="prelude-ty">Result</span>&lt;T&gt; = std::result::Result&lt;T, AppError&gt;;
<a href=#4 id=4 data-nosnippet>4</a>
<a href=#5 id=5 data-nosnippet>5</a><span class="attr">#[derive(Error, Debug)]
<a href=#6 id=6 data-nosnippet>6</a></span><span class="kw">pub enum </span>AppError {
<a href=#7 id=7 data-nosnippet>7</a>    <span class="attr">#[error(<span class="string">"配置错误: {0}"</span>)]
<a href=#8 id=8 data-nosnippet>8</a>    </span>Config(String),
<a href=#9 id=9 data-nosnippet>9</a>
<a href=#10 id=10 data-nosnippet>10</a>    <span class="attr">#[error(<span class="string">"数据库错误: {0}"</span>)]
<a href=#11 id=11 data-nosnippet>11</a>    </span>Database(<span class="attr">#[from] </span>sqlx::Error),
<a href=#12 id=12 data-nosnippet>12</a>
<a href=#13 id=13 data-nosnippet>13</a>    <span class="attr">#[error(<span class="string">"数据库迁移错误: {0}"</span>)]
<a href=#14 id=14 data-nosnippet>14</a>    </span>Migration(String),
<a href=#15 id=15 data-nosnippet>15</a>
<a href=#16 id=16 data-nosnippet>16</a>    <span class="attr">#[error(<span class="string">"网络请求错误: {0}"</span>)]
<a href=#17 id=17 data-nosnippet>17</a>    </span>Request(<span class="attr">#[from] </span>reqwest::Error),
<a href=#18 id=18 data-nosnippet>18</a>
<a href=#19 id=19 data-nosnippet>19</a>    <span class="attr">#[error(<span class="string">"Redis错误: {0}"</span>)]
<a href=#20 id=20 data-nosnippet>20</a>    </span>Redis(<span class="attr">#[from] </span>redis::RedisError),
<a href=#21 id=21 data-nosnippet>21</a>
<a href=#22 id=22 data-nosnippet>22</a>    <span class="attr">#[error(<span class="string">"消息队列错误: {0}"</span>)]
<a href=#23 id=23 data-nosnippet>23</a>    </span>MessageQueue(String),
<a href=#24 id=24 data-nosnippet>24</a>
<a href=#25 id=25 data-nosnippet>25</a>    <span class="attr">#[error(<span class="string">"代理错误: {0}"</span>)]
<a href=#26 id=26 data-nosnippet>26</a>    </span>Proxy(String),
<a href=#27 id=27 data-nosnippet>27</a>
<a href=#28 id=28 data-nosnippet>28</a>    <span class="attr">#[error(<span class="string">"账号错误: {0}"</span>)]
<a href=#29 id=29 data-nosnippet>29</a>    </span>Account(String),
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a>    <span class="attr">#[error(<span class="string">"爬虫错误: {0}"</span>)]
<a href=#32 id=32 data-nosnippet>32</a>    </span>Crawler(String),
<a href=#33 id=33 data-nosnippet>33</a>
<a href=#34 id=34 data-nosnippet>34</a>    <span class="attr">#[error(<span class="string">"任务调度错误: {0}"</span>)]
<a href=#35 id=35 data-nosnippet>35</a>    </span>Scheduler(String),
<a href=#36 id=36 data-nosnippet>36</a>
<a href=#37 id=37 data-nosnippet>37</a>    <span class="attr">#[error(<span class="string">"系统监控错误: {0}"</span>)]
<a href=#38 id=38 data-nosnippet>38</a>    </span>Monitor(String),
<a href=#39 id=39 data-nosnippet>39</a>
<a href=#40 id=40 data-nosnippet>40</a>    <span class="attr">#[error(<span class="string">"浏览器错误: {0}"</span>)]
<a href=#41 id=41 data-nosnippet>41</a>    </span>Browser(String),
<a href=#42 id=42 data-nosnippet>42</a>
<a href=#43 id=43 data-nosnippet>43</a>    <span class="attr">#[error(<span class="string">"IO错误: {0}"</span>)]
<a href=#44 id=44 data-nosnippet>44</a>    </span>Io(<span class="attr">#[from] </span>std::io::Error),
<a href=#45 id=45 data-nosnippet>45</a>
<a href=#46 id=46 data-nosnippet>46</a>    <span class="attr">#[error(<span class="string">"序列化错误: {0}"</span>)]
<a href=#47 id=47 data-nosnippet>47</a>    </span>Serialization(<span class="attr">#[from] </span>serde_json::Error),
<a href=#48 id=48 data-nosnippet>48</a>
<a href=#49 id=49 data-nosnippet>49</a>    <span class="attr">#[error(<span class="string">"其他错误: {0}"</span>)]
<a href=#50 id=50 data-nosnippet>50</a>    </span>Other(String),
<a href=#51 id=51 data-nosnippet>51</a>}
<a href=#52 id=52 data-nosnippet>52</a>
<a href=#53 id=53 data-nosnippet>53</a><span class="kw">impl </span>From&lt;AppError&gt; <span class="kw">for </span>String {
<a href=#54 id=54 data-nosnippet>54</a>    <span class="kw">fn </span>from(error: AppError) -&gt; <span class="self">Self </span>{
<a href=#55 id=55 data-nosnippet>55</a>        error.to_string()
<a href=#56 id=56 data-nosnippet>56</a>    }
<a href=#57 id=57 data-nosnippet>57</a>}
<a href=#58 id=58 data-nosnippet>58</a>
<a href=#59 id=59 data-nosnippet>59</a><span class="comment">// 为Tauri命令提供错误转换
<a href=#60 id=60 data-nosnippet>60</a></span><span class="kw">impl </span>serde::Serialize <span class="kw">for </span>AppError {
<a href=#61 id=61 data-nosnippet>61</a>    <span class="kw">fn </span>serialize&lt;S&gt;(<span class="kw-2">&amp;</span><span class="self">self</span>, serializer: S) -&gt; std::result::Result&lt;S::Ok, S::Error&gt;
<a href=#62 id=62 data-nosnippet>62</a>    <span class="kw">where
<a href=#63 id=63 data-nosnippet>63</a>        </span>S: serde::ser::Serializer,
<a href=#64 id=64 data-nosnippet>64</a>    {
<a href=#65 id=65 data-nosnippet>65</a>        serializer.serialize_str(<span class="self">self</span>.to_string().as_ref())
<a href=#66 id=66 data-nosnippet>66</a>    }
<a href=#67 id=67 data-nosnippet>67</a>}
<a href=#68 id=68 data-nosnippet>68</a>
<a href=#69 id=69 data-nosnippet>69</a><span class="comment">// 从anyhow::Error转换
<a href=#70 id=70 data-nosnippet>70</a></span><span class="kw">impl </span>From&lt;anyhow::Error&gt; <span class="kw">for </span>AppError {
<a href=#71 id=71 data-nosnippet>71</a>    <span class="kw">fn </span>from(error: anyhow::Error) -&gt; <span class="self">Self </span>{
<a href=#72 id=72 data-nosnippet>72</a>        AppError::Browser(error.to_string())
<a href=#73 id=73 data-nosnippet>73</a>    }
<a href=#74 id=74 data-nosnippet>74</a>}</code></pre></div></section></main></body></html>