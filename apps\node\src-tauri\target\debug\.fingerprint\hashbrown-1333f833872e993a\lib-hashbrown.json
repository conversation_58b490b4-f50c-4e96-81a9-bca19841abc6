{"rustc": 1842507548689473721, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2241668132362809309, "path": 1699537137492259686, "deps": [[966925859616469517, "ahash", false, 14005381909897973836], [9150530836556604396, "allocator_api2", false, 1044223891855974889]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hashbrown-1333f833872e993a\\dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}