{"build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devPath": "http://localhost:3001", "distDir": "../dist", "withGlobalTauri": false}, "package": {"productName": "Weibo Crawler Node", "version": "0.1.0"}, "tauri": {"allowlist": {"all": false, "shell": {"all": false, "open": true}, "window": {"all": false, "close": true, "hide": true, "show": true, "maximize": true, "minimize": true, "unmaximize": true, "unminimize": true, "startDragging": true}, "fs": {"all": true, "scope": ["$APPDATA", "$APPDATA/**", "$RESOURCE", "$RESOURCE/**"]}, "path": {"all": true}, "os": {"all": true}}, "bundle": {"active": true, "targets": "all", "identifier": "com.weibo.crawler.node", "icon": ["icons/icon.ico"]}, "security": {"csp": null}, "windows": [{"fullscreen": false, "resizable": true, "title": "Weibo Crawler Node", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}]}}