#!/usr/bin/env python3
import struct

def create_ico_file(filename):
    # ICO file header
    ico_header = struct.pack('<HHH', 0, 1, 1)  # Reserved, Type (1=ICO), Count
    
    # ICO directory entry for 16x16 icon
    width = 16
    height = 16
    colors = 0  # 0 means 256 or more colors
    reserved = 0
    planes = 1
    bits_per_pixel = 32
    size_in_bytes = 40 + (width * height * 4)  # DIB header + pixel data
    offset = 22  # 6 bytes header + 16 bytes directory entry
    
    ico_dir_entry = struct.pack('<BBBBHHLL', 
                                width, height, colors, reserved,
                                planes, bits_per_pixel, size_in_bytes, offset)
    
    # DIB header (BITMAPINFOHEADER)
    dib_header = struct.pack('<LLLHHLLLLLL',
                            40,  # header size
                            width,  # width
                            height * 2,  # height (doubled for ICO format)
                            1,  # planes
                            bits_per_pixel,  # bits per pixel
                            0,  # compression
                            width * height * 4,  # image size
                            0,  # x pixels per meter
                            0,  # y pixels per meter
                            0,  # colors used
                            0)  # important colors
    
    # Create simple blue pixel data (BGRA format)
    pixel_data = b''
    for y in range(height):
        for x in range(width):
            # Blue pixel with full alpha
            pixel_data += struct.pack('<BBBB', 255, 0, 0, 255)  # B, G, R, A
    
    # Write the ICO file
    with open(filename, 'wb') as f:
        f.write(ico_header)
        f.write(ico_dir_entry)
        f.write(dib_header)
        f.write(pixel_data)

if __name__ == '__main__':
    create_ico_file('src-tauri/icons/icon.ico')
    print("Created icon.ico successfully")
