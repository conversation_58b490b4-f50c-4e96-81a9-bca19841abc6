{"rustc": 1842507548689473721, "features": "[\"alloc\", \"bytes\", \"futures-core-03\", \"pin-project-lite\", \"std\", \"tokio\", \"tokio-dep\", \"tokio-util\"]", "declared_features": "[\"alloc\", \"bytes\", \"bytes_05\", \"default\", \"futures-03\", \"futures-core-03\", \"futures-io-03\", \"mp4\", \"pin-project\", \"pin-project-lite\", \"regex\", \"std\", \"tokio\", \"tokio-02\", \"tokio-02-dep\", \"tokio-03\", \"tokio-03-dep\", \"tokio-dep\", \"tokio-util\"]", "target": 2090804380371586739, "profile": 15657897354478470176, "path": 15307606537240818418, "deps": [[1288403060204016458, "tokio_util", false, 11334306296847631233], [1906322745568073236, "pin_project_lite", false, 18131270670966278584], [7620660491849607393, "futures_core_03", false, 6897113057850168815], [12944427623413450645, "tokio_dep", false, 13941386680418085679], [15932120279885307830, "memchr", false, 13280053228737166842], [16066129441945555748, "bytes", false, 256859762049119040]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\combine-3386b9433d221049\\dep-lib-combine", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}