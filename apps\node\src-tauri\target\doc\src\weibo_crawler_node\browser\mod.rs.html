<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\browser\mod.rs`."><title>mod.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node\browser/</div>mod.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use </span><span class="kw">crate</span>::error::{<span class="prelude-ty">Result</span>, AppError};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span>playwright::{Playwright, api::browser_type::BrowserType};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span>std::sync::Arc;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>tokio::sync::Mutex;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>tracing::{info, warn, error, debug};
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>serde::{Deserialize, Serialize};
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span>std::time::Duration;
<a href=#8 id=8 data-nosnippet>8</a>
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">pub mod </span>weibo_login;
<a href=#10 id=10 data-nosnippet>10</a>
<a href=#11 id=11 data-nosnippet>11</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#12 id=12 data-nosnippet>12</a></span><span class="kw">pub struct </span>BrowserConfig {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>headless: bool,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>user_agent: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>viewport_width: u32,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>viewport_height: u32,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>timeout: u64, <span class="comment">// 毫秒
<a href=#18 id=18 data-nosnippet>18</a>    </span><span class="kw">pub </span>slow_mo: u64, <span class="comment">// 毫秒，操作间隔
<a href=#19 id=19 data-nosnippet>19</a></span>}
<a href=#20 id=20 data-nosnippet>20</a>
<a href=#21 id=21 data-nosnippet>21</a><span class="kw">impl </span>Default <span class="kw">for </span>BrowserConfig {
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">fn </span>default() -&gt; <span class="self">Self </span>{
<a href=#23 id=23 data-nosnippet>23</a>        <span class="self">Self </span>{
<a href=#24 id=24 data-nosnippet>24</a>            headless: <span class="bool-val">true</span>,
<a href=#25 id=25 data-nosnippet>25</a>            user_agent: <span class="prelude-val">Some</span>(<span class="string">"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"</span>.to_string()),
<a href=#26 id=26 data-nosnippet>26</a>            viewport_width: <span class="number">1920</span>,
<a href=#27 id=27 data-nosnippet>27</a>            viewport_height: <span class="number">1080</span>,
<a href=#28 id=28 data-nosnippet>28</a>            timeout: <span class="number">30000</span>,
<a href=#29 id=29 data-nosnippet>29</a>            slow_mo: <span class="number">100</span>,
<a href=#30 id=30 data-nosnippet>30</a>        }
<a href=#31 id=31 data-nosnippet>31</a>    }
<a href=#32 id=32 data-nosnippet>32</a>}
<a href=#33 id=33 data-nosnippet>33</a>
<a href=#34 id=34 data-nosnippet>34</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#35 id=35 data-nosnippet>35</a></span><span class="kw">pub struct </span>LoginCredentials {
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>username: String,
<a href=#37 id=37 data-nosnippet>37</a>    <span class="kw">pub </span>password: String,
<a href=#38 id=38 data-nosnippet>38</a>}
<a href=#39 id=39 data-nosnippet>39</a>
<a href=#40 id=40 data-nosnippet>40</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#41 id=41 data-nosnippet>41</a></span><span class="kw">pub struct </span>LoginResult {
<a href=#42 id=42 data-nosnippet>42</a>    <span class="kw">pub </span>success: bool,
<a href=#43 id=43 data-nosnippet>43</a>    <span class="kw">pub </span>message: String,
<a href=#44 id=44 data-nosnippet>44</a>    <span class="kw">pub </span>cookies: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#45 id=45 data-nosnippet>45</a>    <span class="kw">pub </span>user_info: <span class="prelude-ty">Option</span>&lt;UserInfo&gt;,
<a href=#46 id=46 data-nosnippet>46</a>}
<a href=#47 id=47 data-nosnippet>47</a>
<a href=#48 id=48 data-nosnippet>48</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#49 id=49 data-nosnippet>49</a></span><span class="kw">pub struct </span>UserInfo {
<a href=#50 id=50 data-nosnippet>50</a>    <span class="kw">pub </span>uid: String,
<a href=#51 id=51 data-nosnippet>51</a>    <span class="kw">pub </span>nickname: String,
<a href=#52 id=52 data-nosnippet>52</a>    <span class="kw">pub </span>avatar_url: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#53 id=53 data-nosnippet>53</a>}
<a href=#54 id=54 data-nosnippet>54</a>
<a href=#55 id=55 data-nosnippet>55</a><span class="kw">pub struct </span>BrowserManager {
<a href=#56 id=56 data-nosnippet>56</a>    playwright: Arc&lt;Mutex&lt;<span class="prelude-ty">Option</span>&lt;Playwright&gt;&gt;&gt;,
<a href=#57 id=57 data-nosnippet>57</a>    config: BrowserConfig,
<a href=#58 id=58 data-nosnippet>58</a>}
<a href=#59 id=59 data-nosnippet>59</a>
<a href=#60 id=60 data-nosnippet>60</a><span class="kw">impl </span>BrowserManager {
<a href=#61 id=61 data-nosnippet>61</a>    <span class="kw">pub fn </span>new(config: BrowserConfig) -&gt; <span class="self">Self </span>{
<a href=#62 id=62 data-nosnippet>62</a>        <span class="self">Self </span>{
<a href=#63 id=63 data-nosnippet>63</a>            playwright: Arc::new(Mutex::new(<span class="prelude-val">None</span>)),
<a href=#64 id=64 data-nosnippet>64</a>            config,
<a href=#65 id=65 data-nosnippet>65</a>        }
<a href=#66 id=66 data-nosnippet>66</a>    }
<a href=#67 id=67 data-nosnippet>67</a>
<a href=#68 id=68 data-nosnippet>68</a>    <span class="kw">pub async fn </span>initialize(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#69 id=69 data-nosnippet>69</a>        <span class="kw">let </span><span class="kw-2">mut </span>playwright_guard = <span class="self">self</span>.playwright.lock().<span class="kw">await</span>;
<a href=#70 id=70 data-nosnippet>70</a>        
<a href=#71 id=71 data-nosnippet>71</a>        <span class="kw">if </span>playwright_guard.is_some() {
<a href=#72 id=72 data-nosnippet>72</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#73 id=73 data-nosnippet>73</a>        }
<a href=#74 id=74 data-nosnippet>74</a>
<a href=#75 id=75 data-nosnippet>75</a>        <span class="macro">info!</span>(<span class="string">"初始化 Playwright..."</span>);
<a href=#76 id=76 data-nosnippet>76</a>        
<a href=#77 id=77 data-nosnippet>77</a>        <span class="kw">match </span>Playwright::initialize().<span class="kw">await </span>{
<a href=#78 id=78 data-nosnippet>78</a>            <span class="prelude-val">Ok</span>(playwright) =&gt; {
<a href=#79 id=79 data-nosnippet>79</a>                <span class="kw-2">*</span>playwright_guard = <span class="prelude-val">Some</span>(playwright);
<a href=#80 id=80 data-nosnippet>80</a>                <span class="macro">info!</span>(<span class="string">"Playwright 初始化成功"</span>);
<a href=#81 id=81 data-nosnippet>81</a>                <span class="prelude-val">Ok</span>(())
<a href=#82 id=82 data-nosnippet>82</a>            }
<a href=#83 id=83 data-nosnippet>83</a>            <span class="prelude-val">Err</span>(e) =&gt; {
<a href=#84 id=84 data-nosnippet>84</a>                <span class="macro">error!</span>(<span class="string">"Playwright 初始化失败: {}"</span>, e);
<a href=#85 id=85 data-nosnippet>85</a>                <span class="prelude-val">Err</span>(AppError::Browser(<span class="macro">format!</span>(<span class="string">"Failed to initialize Playwright: {}"</span>, e)))
<a href=#86 id=86 data-nosnippet>86</a>            }
<a href=#87 id=87 data-nosnippet>87</a>        }
<a href=#88 id=88 data-nosnippet>88</a>    }
<a href=#89 id=89 data-nosnippet>89</a>
<a href=#90 id=90 data-nosnippet>90</a>    <span class="kw">pub async fn </span>shutdown(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#91 id=91 data-nosnippet>91</a>        <span class="kw">let </span><span class="kw-2">mut </span>playwright_guard = <span class="self">self</span>.playwright.lock().<span class="kw">await</span>;
<a href=#92 id=92 data-nosnippet>92</a>        
<a href=#93 id=93 data-nosnippet>93</a>        <span class="kw">if let </span><span class="prelude-val">Some</span>(playwright) = playwright_guard.take() {
<a href=#94 id=94 data-nosnippet>94</a>            <span class="macro">info!</span>(<span class="string">"关闭 Playwright..."</span>);
<a href=#95 id=95 data-nosnippet>95</a>            <span class="comment">// Playwright 会在 drop 时自动清理
<a href=#96 id=96 data-nosnippet>96</a>            </span>drop(playwright);
<a href=#97 id=97 data-nosnippet>97</a>            <span class="macro">info!</span>(<span class="string">"Playwright 已关闭"</span>);
<a href=#98 id=98 data-nosnippet>98</a>        }
<a href=#99 id=99 data-nosnippet>99</a>        
<a href=#100 id=100 data-nosnippet>100</a>        <span class="prelude-val">Ok</span>(())
<a href=#101 id=101 data-nosnippet>101</a>    }
<a href=#102 id=102 data-nosnippet>102</a>
<a href=#103 id=103 data-nosnippet>103</a>    <span class="kw">pub async fn </span>login_weibo(<span class="kw-2">&amp;</span><span class="self">self</span>, credentials: LoginCredentials) -&gt; <span class="prelude-ty">Result</span>&lt;LoginResult&gt; {
<a href=#104 id=104 data-nosnippet>104</a>        <span class="kw">let </span>playwright_guard = <span class="self">self</span>.playwright.lock().<span class="kw">await</span>;
<a href=#105 id=105 data-nosnippet>105</a>        
<a href=#106 id=106 data-nosnippet>106</a>        <span class="kw">let </span>playwright = playwright_guard
<a href=#107 id=107 data-nosnippet>107</a>            .as_ref()
<a href=#108 id=108 data-nosnippet>108</a>            .ok_or_else(|| AppError::Browser(<span class="string">"Playwright not initialized"</span>.to_string()))<span class="question-mark">?</span>;
<a href=#109 id=109 data-nosnippet>109</a>
<a href=#110 id=110 data-nosnippet>110</a>        weibo_login::login_with_playwright(playwright, <span class="kw-2">&amp;</span><span class="self">self</span>.config, credentials).<span class="kw">await
<a href=#111 id=111 data-nosnippet>111</a>    </span>}
<a href=#112 id=112 data-nosnippet>112</a>
<a href=#113 id=113 data-nosnippet>113</a>    <span class="kw">pub async fn </span>test_browser(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;String&gt; {
<a href=#114 id=114 data-nosnippet>114</a>        <span class="kw">let </span>playwright_guard = <span class="self">self</span>.playwright.lock().<span class="kw">await</span>;
<a href=#115 id=115 data-nosnippet>115</a>        
<a href=#116 id=116 data-nosnippet>116</a>        <span class="kw">let </span>playwright = playwright_guard
<a href=#117 id=117 data-nosnippet>117</a>            .as_ref()
<a href=#118 id=118 data-nosnippet>118</a>            .ok_or_else(|| AppError::Browser(<span class="string">"Playwright not initialized"</span>.to_string()))<span class="question-mark">?</span>;
<a href=#119 id=119 data-nosnippet>119</a>
<a href=#120 id=120 data-nosnippet>120</a>        <span class="macro">info!</span>(<span class="string">"测试浏览器功能..."</span>);
<a href=#121 id=121 data-nosnippet>121</a>
<a href=#122 id=122 data-nosnippet>122</a>        <span class="kw">let </span>chromium = playwright.chromium();
<a href=#123 id=123 data-nosnippet>123</a>        <span class="kw">let </span>browser = chromium
<a href=#124 id=124 data-nosnippet>124</a>            .launcher()
<a href=#125 id=125 data-nosnippet>125</a>            .headless(<span class="self">self</span>.config.headless)
<a href=#126 id=126 data-nosnippet>126</a>            .launch()
<a href=#127 id=127 data-nosnippet>127</a>            .<span class="kw">await
<a href=#128 id=128 data-nosnippet>128</a>            </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"Failed to launch browser: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#129 id=129 data-nosnippet>129</a>
<a href=#130 id=130 data-nosnippet>130</a>        <span class="kw">let </span>context = browser
<a href=#131 id=131 data-nosnippet>131</a>            .context_builder()
<a href=#132 id=132 data-nosnippet>132</a>            .user_agent(<span class="kw-2">&amp;</span><span class="self">self</span>.config.user_agent.as_deref().unwrap_or(<span class="string">""</span>))
<a href=#133 id=133 data-nosnippet>133</a>            .viewport(<span class="self">self</span>.config.viewport_width, <span class="self">self</span>.config.viewport_height)
<a href=#134 id=134 data-nosnippet>134</a>            .build()
<a href=#135 id=135 data-nosnippet>135</a>            .<span class="kw">await
<a href=#136 id=136 data-nosnippet>136</a>            </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"Failed to create context: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#137 id=137 data-nosnippet>137</a>
<a href=#138 id=138 data-nosnippet>138</a>        <span class="kw">let </span>page = context
<a href=#139 id=139 data-nosnippet>139</a>            .new_page()
<a href=#140 id=140 data-nosnippet>140</a>            .<span class="kw">await
<a href=#141 id=141 data-nosnippet>141</a>            </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"Failed to create page: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#142 id=142 data-nosnippet>142</a>
<a href=#143 id=143 data-nosnippet>143</a>        page.goto_builder(<span class="string">"https://www.baidu.com"</span>)
<a href=#144 id=144 data-nosnippet>144</a>            .timeout(<span class="self">self</span>.config.timeout <span class="kw">as </span>f64)
<a href=#145 id=145 data-nosnippet>145</a>            .goto()
<a href=#146 id=146 data-nosnippet>146</a>            .<span class="kw">await
<a href=#147 id=147 data-nosnippet>147</a>            </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"Failed to navigate: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#148 id=148 data-nosnippet>148</a>
<a href=#149 id=149 data-nosnippet>149</a>        <span class="kw">let </span>title = page
<a href=#150 id=150 data-nosnippet>150</a>            .title()
<a href=#151 id=151 data-nosnippet>151</a>            .<span class="kw">await
<a href=#152 id=152 data-nosnippet>152</a>            </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"Failed to get title: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#153 id=153 data-nosnippet>153</a>
<a href=#154 id=154 data-nosnippet>154</a>        browser
<a href=#155 id=155 data-nosnippet>155</a>            .close()
<a href=#156 id=156 data-nosnippet>156</a>            .<span class="kw">await
<a href=#157 id=157 data-nosnippet>157</a>            </span>.map_err(|e| AppError::Browser(<span class="macro">format!</span>(<span class="string">"Failed to close browser: {}"</span>, e)))<span class="question-mark">?</span>;
<a href=#158 id=158 data-nosnippet>158</a>
<a href=#159 id=159 data-nosnippet>159</a>        <span class="macro">info!</span>(<span class="string">"浏览器测试完成，页面标题: {}"</span>, title);
<a href=#160 id=160 data-nosnippet>160</a>        <span class="prelude-val">Ok</span>(title)
<a href=#161 id=161 data-nosnippet>161</a>    }
<a href=#162 id=162 data-nosnippet>162</a>
<a href=#163 id=163 data-nosnippet>163</a>    <span class="kw">pub fn </span>get_config(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span>BrowserConfig {
<a href=#164 id=164 data-nosnippet>164</a>        <span class="kw-2">&amp;</span><span class="self">self</span>.config
<a href=#165 id=165 data-nosnippet>165</a>    }
<a href=#166 id=166 data-nosnippet>166</a>
<a href=#167 id=167 data-nosnippet>167</a>    <span class="kw">pub fn </span>update_config(<span class="kw-2">&amp;mut </span><span class="self">self</span>, config: BrowserConfig) {
<a href=#168 id=168 data-nosnippet>168</a>        <span class="self">self</span>.config = config;
<a href=#169 id=169 data-nosnippet>169</a>    }
<a href=#170 id=170 data-nosnippet>170</a>}
<a href=#171 id=171 data-nosnippet>171</a>
<a href=#172 id=172 data-nosnippet>172</a><span class="comment">// 全局浏览器管理器实例
<a href=#173 id=173 data-nosnippet>173</a></span><span class="kw">static </span>BROWSER_MANAGER: once_cell::sync::OnceCell&lt;Arc&lt;Mutex&lt;BrowserManager&gt;&gt;&gt; = once_cell::sync::OnceCell::new();
<a href=#174 id=174 data-nosnippet>174</a>
<a href=#175 id=175 data-nosnippet>175</a><span class="kw">pub fn </span>get_browser_manager() -&gt; Arc&lt;Mutex&lt;BrowserManager&gt;&gt; {
<a href=#176 id=176 data-nosnippet>176</a>    BROWSER_MANAGER
<a href=#177 id=177 data-nosnippet>177</a>        .get_or_init(|| {
<a href=#178 id=178 data-nosnippet>178</a>            Arc::new(Mutex::new(BrowserManager::new(BrowserConfig::default())))
<a href=#179 id=179 data-nosnippet>179</a>        })
<a href=#180 id=180 data-nosnippet>180</a>        .clone()
<a href=#181 id=181 data-nosnippet>181</a>}
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a><span class="kw">pub async fn </span>initialize_browser() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#184 id=184 data-nosnippet>184</a>    <span class="kw">let </span>manager = get_browser_manager();
<a href=#185 id=185 data-nosnippet>185</a>    <span class="kw">let </span>manager_guard = manager.lock().<span class="kw">await</span>;
<a href=#186 id=186 data-nosnippet>186</a>    manager_guard.initialize().<span class="kw">await
<a href=#187 id=187 data-nosnippet>187</a></span>}
<a href=#188 id=188 data-nosnippet>188</a>
<a href=#189 id=189 data-nosnippet>189</a><span class="kw">pub async fn </span>shutdown_browser() -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#190 id=190 data-nosnippet>190</a>    <span class="kw">let </span>manager = get_browser_manager();
<a href=#191 id=191 data-nosnippet>191</a>    <span class="kw">let </span>manager_guard = manager.lock().<span class="kw">await</span>;
<a href=#192 id=192 data-nosnippet>192</a>    manager_guard.shutdown().<span class="kw">await
<a href=#193 id=193 data-nosnippet>193</a></span>}</code></pre></div></section></main></body></html>