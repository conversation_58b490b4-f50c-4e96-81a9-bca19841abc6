use crate::error::Result;
use crate::repository::RepositoryManager;
use std::sync::Arc;

pub mod task_service;
pub mod proxy_service;
pub mod account_service;
pub mod crawler_service;
pub mod monitor_service;

pub use task_service::TaskService;
pub use proxy_service::ProxyService;
pub use account_service::AccountService;
pub use crawler_service::CrawlerService;
pub use monitor_service::MonitorService;

/// 服务管理器 - 统一管理所有服务
pub struct ServiceManager {
    pub task: TaskService,
    pub proxy: ProxyService,
    pub account: AccountService,
    pub crawler: CrawlerService,
    pub monitor: MonitorService,
}

impl ServiceManager {
    pub fn new(repository_manager: Arc<RepositoryManager>) -> Self {
        Self {
            task: TaskService::new(repository_manager.clone()),
            proxy: ProxyService::new(repository_manager.clone()),
            account: AccountService::new(repository_manager.clone()),
            crawler: CrawlerService::new(repository_manager.clone()),
            monitor: MonitorService::new(repository_manager),
        }
    }
}

/// 基础服务特征
pub trait Service {
    fn name(&self) -> &'static str;
}

/// 可启动的服务特征
#[async_trait::async_trait]
pub trait StartableService: Service {
    async fn start(&self) -> Result<()>;
    async fn stop(&self) -> Result<()>;
    async fn restart(&self) -> Result<()> {
        self.stop().await?;
        self.start().await
    }
    fn is_running(&self) -> bool;
}

/// 可配置的服务特征
pub trait ConfigurableService: Service {
    type Config;
    
    fn get_config(&self) -> &Self::Config;
    fn update_config(&mut self, config: Self::Config) -> Result<()>;
}

/// 可监控的服务特征
#[async_trait::async_trait]
pub trait MonitorableService: Service {
    type Metrics;
    
    async fn get_metrics(&self) -> Result<Self::Metrics>;
    async fn get_health_status(&self) -> Result<HealthStatus>;
}

/// 健康状态
#[derive(Debug, Clone, PartialEq)]
pub enum HealthStatus {
    Healthy,
    Degraded(String),
    Unhealthy(String),
}

impl HealthStatus {
    pub fn is_healthy(&self) -> bool {
        matches!(self, HealthStatus::Healthy)
    }

    pub fn is_degraded(&self) -> bool {
        matches!(self, HealthStatus::Degraded(_))
    }

    pub fn is_unhealthy(&self) -> bool {
        matches!(self, HealthStatus::Unhealthy(_))
    }

    pub fn message(&self) -> Option<&str> {
        match self {
            HealthStatus::Healthy => None,
            HealthStatus::Degraded(msg) | HealthStatus::Unhealthy(msg) => Some(msg),
        }
    }
}

/// 服务事件
#[derive(Debug, Clone)]
pub enum ServiceEvent {
    Started(String),
    Stopped(String),
    Error(String, String),
    ConfigUpdated(String),
    HealthChanged(String, HealthStatus),
}

/// 事件监听器特征
#[async_trait::async_trait]
pub trait EventListener {
    async fn on_event(&self, event: ServiceEvent);
}

/// 服务注册表
pub struct ServiceRegistry {
    services: std::collections::HashMap<String, Box<dyn Service + Send + Sync>>,
    event_listeners: Vec<Box<dyn EventListener + Send + Sync>>,
}

impl ServiceRegistry {
    pub fn new() -> Self {
        Self {
            services: std::collections::HashMap::new(),
            event_listeners: Vec::new(),
        }
    }

    pub fn register<S>(&mut self, service: S) 
    where 
        S: Service + Send + Sync + 'static 
    {
        let name = service.name().to_string();
        self.services.insert(name, Box::new(service));
    }

    pub fn get_service(&self, name: &str) -> Option<&(dyn Service + Send + Sync)> {
        self.services.get(name).map(|s| s.as_ref())
    }

    pub fn add_event_listener<L>(&mut self, listener: L)
    where
        L: EventListener + Send + Sync + 'static
    {
        self.event_listeners.push(Box::new(listener));
    }

    pub async fn emit_event(&self, event: ServiceEvent) {
        for listener in &self.event_listeners {
            listener.on_event(event.clone()).await;
        }
    }

    pub fn list_services(&self) -> Vec<&str> {
        self.services.keys().map(|s| s.as_str()).collect()
    }
}

impl Default for ServiceRegistry {
    fn default() -> Self {
        Self::new()
    }
}

/// 服务生命周期管理器
pub struct ServiceLifecycleManager {
    registry: ServiceRegistry,
}

impl ServiceLifecycleManager {
    pub fn new() -> Self {
        Self {
            registry: ServiceRegistry::new(),
        }
    }

    pub fn with_registry(registry: ServiceRegistry) -> Self {
        Self { registry }
    }

    pub async fn start_all_services(&self) -> Result<()> {
        for (name, _service) in &self.registry.services {
            // 这里需要类型转换，实际实现中需要更复杂的处理
            tracing::info!("Starting service: {}", name);
            self.registry.emit_event(ServiceEvent::Started(name.clone())).await;
        }
        Ok(())
    }

    pub async fn stop_all_services(&self) -> Result<()> {
        for (name, _service) in &self.registry.services {
            tracing::info!("Stopping service: {}", name);
            self.registry.emit_event(ServiceEvent::Stopped(name.clone())).await;
        }
        Ok(())
    }

    pub fn get_registry(&self) -> &ServiceRegistry {
        &self.registry
    }

    pub fn get_registry_mut(&mut self) -> &mut ServiceRegistry {
        &mut self.registry
    }
}

impl Default for ServiceLifecycleManager {
    fn default() -> Self {
        Self::new()
    }
}

/// 服务依赖注入容器
pub struct ServiceContainer {
    repository_manager: Arc<RepositoryManager>,
    service_manager: Arc<ServiceManager>,
    lifecycle_manager: ServiceLifecycleManager,
}

impl ServiceContainer {
    pub fn new(repository_manager: Arc<RepositoryManager>) -> Self {
        let service_manager = Arc::new(ServiceManager::new(repository_manager.clone()));
        let lifecycle_manager = ServiceLifecycleManager::new();

        Self {
            repository_manager,
            service_manager,
            lifecycle_manager,
        }
    }

    pub fn get_repository_manager(&self) -> &Arc<RepositoryManager> {
        &self.repository_manager
    }

    pub fn get_service_manager(&self) -> &Arc<ServiceManager> {
        &self.service_manager
    }

    pub fn get_lifecycle_manager(&self) -> &ServiceLifecycleManager {
        &self.lifecycle_manager
    }

    pub fn get_lifecycle_manager_mut(&mut self) -> &mut ServiceLifecycleManager {
        &mut self.lifecycle_manager
    }

    pub async fn initialize(&mut self) -> Result<()> {
        // 注册所有服务
        // 这里需要实际的服务注册逻辑
        tracing::info!("Initializing service container");
        
        // 启动所有服务
        self.lifecycle_manager.start_all_services().await?;
        
        Ok(())
    }

    pub async fn shutdown(&self) -> Result<()> {
        tracing::info!("Shutting down service container");
        self.lifecycle_manager.stop_all_services().await?;
        Ok(())
    }
}

/// 服务配置
#[derive(Debug, Clone)]
pub struct ServiceConfig {
    pub name: String,
    pub enabled: bool,
    pub auto_start: bool,
    pub restart_on_failure: bool,
    pub max_restart_attempts: u32,
    pub health_check_interval: std::time::Duration,
    pub config: serde_json::Value,
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self {
            name: String::new(),
            enabled: true,
            auto_start: true,
            restart_on_failure: true,
            max_restart_attempts: 3,
            health_check_interval: std::time::Duration::from_secs(30),
            config: serde_json::Value::Null,
        }
    }
}
