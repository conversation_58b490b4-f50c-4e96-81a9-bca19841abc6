import { test, expect } from '@playwright/test'

test.describe('Application E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到应用首页
    await page.goto('/')
    
    // 等待应用加载完成
    await page.waitForSelector('[data-testid="app-root"]')
  })

  test('should load the application successfully', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/微博爬虫节点/)
    
    // 检查主要UI元素
    await expect(page.locator('[data-testid="sidebar"]')).toBeVisible()
    await expect(page.locator('[data-testid="main-content"]')).toBeVisible()
    
    // 检查导航菜单
    await expect(page.locator('text=仪表板')).toBeVisible()
    await expect(page.locator('text=任务管理')).toBeVisible()
    await expect(page.locator('text=代理管理')).toBeVisible()
    await expect(page.locator('text=账号管理')).toBeVisible()
  })

  test('should navigate between pages', async ({ page }) => {
    // 点击任务管理
    await page.click('text=任务管理')
    await expect(page).toHaveURL(/.*\/tasks/)
    await expect(page.locator('h1')).toContainText('任务管理')
    
    // 点击代理管理
    await page.click('text=代理管理')
    await expect(page).toHaveURL(/.*\/proxies/)
    await expect(page.locator('h1')).toContainText('代理管理')
    
    // 点击账号管理
    await page.click('text=账号管理')
    await expect(page).toHaveURL(/.*\/accounts/)
    await expect(page.locator('h1')).toContainText('账号管理')
    
    // 返回仪表板
    await page.click('text=仪表板')
    await expect(page).toHaveURL(/.*\/$/)
  })

  test('should toggle sidebar', async ({ page }) => {
    const sidebar = page.locator('[data-testid="sidebar"]')
    const toggleButton = page.locator('[data-testid="sidebar-toggle"]')
    
    // 初始状态应该是展开的
    await expect(sidebar).not.toHaveClass(/collapsed/)
    
    // 点击切换按钮
    await toggleButton.click()
    await expect(sidebar).toHaveClass(/collapsed/)
    
    // 再次点击应该展开
    await toggleButton.click()
    await expect(sidebar).not.toHaveClass(/collapsed/)
  })

  test('should display system status', async ({ page }) => {
    // 检查系统状态指示器
    const statusIndicator = page.locator('[data-testid="system-status"]')
    await expect(statusIndicator).toBeVisible()
    
    // 检查状态文本或图标
    await expect(statusIndicator).toContainText(/运行中|正常|在线/)
  })

  test('should handle theme toggle', async ({ page }) => {
    const themeToggle = page.locator('[data-testid="theme-toggle"]')
    
    // 点击主题切换
    await themeToggle.click()
    
    // 检查主题是否切换
    const html = page.locator('html')
    await expect(html).toHaveClass(/dark/)
    
    // 再次点击切换回来
    await themeToggle.click()
    await expect(html).not.toHaveClass(/dark/)
  })

  test('should display notifications', async ({ page }) => {
    const notificationButton = page.locator('[data-testid="notifications-button"]')
    
    // 点击通知按钮
    await notificationButton.click()
    
    // 检查通知面板是否显示
    const notificationPanel = page.locator('[data-testid="notifications-panel"]')
    await expect(notificationPanel).toBeVisible()
    
    // 点击外部关闭通知面板
    await page.click('body')
    await expect(notificationPanel).not.toBeVisible()
  })

  test('should handle search functionality', async ({ page }) => {
    const searchInput = page.locator('input[placeholder*="搜索"]')
    
    // 输入搜索内容
    await searchInput.fill('test search')
    await expect(searchInput).toHaveValue('test search')
    
    // 按回车搜索
    await searchInput.press('Enter')
    
    // 检查搜索结果
    const searchResults = page.locator('[data-testid="search-results"]')
    await expect(searchResults).toBeVisible()
  })

  test('should be responsive on mobile', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 })
    
    const sidebar = page.locator('[data-testid="sidebar"]')
    
    // 在移动端，侧边栏应该默认折叠
    await expect(sidebar).toHaveClass(/mobile-collapsed/)
    
    // 点击菜单按钮应该展开侧边栏
    const menuButton = page.locator('[data-testid="mobile-menu-button"]')
    await menuButton.click()
    await expect(sidebar).not.toHaveClass(/mobile-collapsed/)
  })

  test('should handle keyboard shortcuts', async ({ page }) => {
    // 测试侧边栏切换快捷键 (Ctrl+B)
    await page.keyboard.press('Control+b')
    
    const sidebar = page.locator('[data-testid="sidebar"]')
    await expect(sidebar).toHaveClass(/collapsed/)
    
    // 再次按快捷键应该展开
    await page.keyboard.press('Control+b')
    await expect(sidebar).not.toHaveClass(/collapsed/)
  })

  test('should handle window controls on desktop', async ({ page }) => {
    // 检查窗口控制按钮（仅在Tauri环境中）
    const minimizeButton = page.locator('[data-testid="window-minimize"]')
    const maximizeButton = page.locator('[data-testid="window-maximize"]')
    const closeButton = page.locator('[data-testid="window-close"]')
    
    if (await minimizeButton.isVisible()) {
      // 在Tauri环境中，这些按钮应该存在
      await expect(minimizeButton).toBeVisible()
      await expect(maximizeButton).toBeVisible()
      await expect(closeButton).toBeVisible()
    }
  })

  test('should load and display charts', async ({ page }) => {
    // 导航到仪表板
    await page.goto('/')
    
    // 等待图表加载
    const charts = page.locator('[data-testid*="chart"]')
    await expect(charts.first()).toBeVisible({ timeout: 10000 })
    
    // 检查图表是否正确渲染
    const chartContainer = page.locator('.recharts-responsive-container')
    await expect(chartContainer.first()).toBeVisible()
  })

  test('should handle error states gracefully', async ({ page }) => {
    // 模拟网络错误
    await page.route('**/api/**', route => route.abort())
    
    // 刷新页面
    await page.reload()
    
    // 检查错误状态显示
    const errorMessage = page.locator('[data-testid="error-message"]')
    await expect(errorMessage).toBeVisible({ timeout: 10000 })
  })

  test('should persist user preferences', async ({ page }) => {
    // 切换主题
    const themeToggle = page.locator('[data-testid="theme-toggle"]')
    await themeToggle.click()
    
    // 折叠侧边栏
    const sidebarToggle = page.locator('[data-testid="sidebar-toggle"]')
    await sidebarToggle.click()
    
    // 刷新页面
    await page.reload()
    await page.waitForSelector('[data-testid="app-root"]')
    
    // 检查设置是否持久化
    const html = page.locator('html')
    await expect(html).toHaveClass(/dark/)
    
    const sidebar = page.locator('[data-testid="sidebar"]')
    await expect(sidebar).toHaveClass(/collapsed/)
  })
})
