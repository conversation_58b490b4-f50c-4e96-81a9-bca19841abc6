import React from 'react';
import { cn } from '../../lib/utils';

// 按钮变体类型定义
export type ButtonVariant = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'success' | 'warning' | 'info';
export type ButtonSize = 'default' | 'sm' | 'lg' | 'xl' | 'icon';

// 按钮变体样式函数
const getButtonVariantStyles = (variant: ButtonVariant = 'default') => {
  const variants = {
    default: "bg-primary text-primary-foreground hover:bg-primary-hover shadow-sm hover:shadow-md",
    destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary-hover shadow-sm",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    link: "text-primary underline-offset-4 hover:underline",
    success: "bg-success text-success-foreground hover:bg-success/90 shadow-sm hover:shadow-md",
    warning: "bg-warning text-warning-foreground hover:bg-warning/90 shadow-sm hover:shadow-md",
    info: "bg-info text-info-foreground hover:bg-info/90 shadow-sm hover:shadow-md",
  };
  return variants[variant];
};

const getButtonSizeStyles = (size: ButtonSize = 'default') => {
  const sizes = {
    default: "h-10 px-4 py-2",
    sm: "h-9 rounded-md px-3 text-xs",
    lg: "h-12 rounded-lg px-8 text-base",
    xl: "h-14 rounded-xl px-10 text-lg",
    icon: "h-10 w-10",
  };
  return sizes[size];
};

// 光泽效果
const shimmerEffect = `
  before:content-[''] before:absolute before:top-0 before:left-[-100%] before:w-full before:h-full 
  before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent 
  before:transition-all before:duration-500 hover:before:left-[100%]
`;

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  asChild?: boolean;
  loading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'default',
    size = 'default',
    asChild = false,
    loading = false,
    leftIcon,
    rightIcon,
    children,
    disabled,
    ...props
  }, ref) => {
    const isDisabled = disabled || loading;

    return (
      <button
        className={cn(
          "inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background relative overflow-hidden",
          getButtonVariantStyles(variant),
          getButtonSizeStyles(size),
          shimmerEffect,
          loading && "cursor-not-allowed",
          className
        )}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {loading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          </div>
        )}
        
        <div className={cn("flex items-center gap-2", loading && "opacity-0")}>
          {leftIcon && <span className="flex-shrink-0">{leftIcon}</span>}
          {children}
          {rightIcon && <span className="flex-shrink-0">{rightIcon}</span>}
        </div>
      </button>
    );
  }
);

Button.displayName = "Button";

export { Button, getButtonVariantStyles, getButtonSizeStyles };
