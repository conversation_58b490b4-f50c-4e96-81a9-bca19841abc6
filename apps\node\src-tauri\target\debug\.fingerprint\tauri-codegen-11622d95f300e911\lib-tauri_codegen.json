{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 9830821840458823691, "deps": [[3060637413840920116, "proc_macro2", false, 10993881616374897205], [4899080583175475170, "semver", false, 2022204265236511886], [7392050791754369441, "ico", false, 915045816337453702], [8008191657135824715, "thiserror", false, 5187766954170195458], [8292277814562636972, "tauri_utils", false, 14620296119465351041], [8319709847752024821, "uuid", false, 2829323984500747669], [8569119365930580996, "serde_json", false, 15813735211382407096], [9451456094439810778, "regex", false, 15788905184249453724], [9689903380558560274, "serde", false, 7307778708627749338], [9857275760291862238, "sha2", false, 1393316979248719060], [10301936376833819828, "json_patch", false, 15513032560915199314], [12687914511023397207, "png", false, 15916973379642107061], [14132538657330703225, "brotli", false, 1752055330440202943], [15622660310229662834, "walkdir", false, 3722475293331183017], [17990358020177143287, "quote", false, 8157755205024365785], [18066890886671768183, "base64", false, 4992665974578841187]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-11622d95f300e911\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}