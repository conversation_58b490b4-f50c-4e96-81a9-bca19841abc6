import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import Dashboard from '../pages/Dashboard';

// Mock Zustand stores
vi.mock('../lib/tauri', () => ({
  useNodeStore: () => ({
    nodeInfo: {
      nodeId: 'test-node-001',
      nodeName: 'Test Node',
      status: 'online',
      uptime: 3600,
      version: '0.1.0'
    },
    systemMetrics: {
      cpu_usage: 45.2,
      memory_usage: 67.8,
      disk_usage: 23.1,
      network_in: 0,
      network_out: 0,
      timestamp: Date.now()
    },
    activeTasks: 5,
    totalTasks: 100,
    successRate: 95.5,
    loading: false,
    fetchNodeStatus: vi.fn(),
  }),
  useTaskStore: () => ({
    queueInfo: {
      pending_tasks: 10,
      running_tasks: 3,
      completed_tasks: 87,
      failed_tasks: 5,
      queue_length: 13,
      processing_speed: 2.5,
    },
    loading: false,
    fetchQueueStatus: vi.fn(),
    fetchTaskStatistics: vi.fn(),
  }),
  useProxyStore: () => ({
    statistics: {
      total_proxies: 50,
      active_proxies: 45,
      healthy_proxies: 42,
      average_response_time: 250,
      success_rate: 84.0,
    },
    loading: false,
    fetchProxyStatistics: vi.fn(),
  }),
  useAccountStore: () => ({
    statistics: {
      total_accounts: 20,
      active_accounts: 18,
      logged_in_accounts: 15,
      healthy_accounts: 17,
      average_risk_score: 2.3,
    },
    loading: false,
    fetchAccountStatistics: vi.fn(),
  }),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Dashboard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render without hooks errors', () => {
    expect(() => {
      renderWithRouter(<Dashboard />);
    }).not.toThrow();
  });

  it('should display dashboard title', () => {
    renderWithRouter(<Dashboard />);
    expect(screen.getByText('仪表板')).toBeInTheDocument();
  });

  it('should display node status', () => {
    renderWithRouter(<Dashboard />);
    expect(screen.getByText('节点状态')).toBeInTheDocument();
    expect(screen.getByText('Test Node (test-node-001)')).toBeInTheDocument();
  });

  it('should display stat cards', () => {
    renderWithRouter(<Dashboard />);
    expect(screen.getByText('活跃任务')).toBeInTheDocument();
    expect(screen.getByText('代理池状态')).toBeInTheDocument();
    expect(screen.getByText('账号池状态')).toBeInTheDocument();
    expect(screen.getByText('成功率')).toBeInTheDocument();
  });
});
