@echo off
echo 🚀 开始设置微博爬虫节点...

REM 检查Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+
    exit /b 1
)

REM 检查Rust
cargo --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Rust 未安装，请先安装 Rust 1.70+
    exit /b 1
)

REM 检查Tauri CLI
cargo tauri --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装 Tauri CLI...
    cargo install tauri-cli
)

REM 安装前端依赖
echo 📦 安装前端依赖...
npm install

REM 创建必要的目录
echo 📁 创建目录结构...
if not exist "data" mkdir data
if not exist "logs" mkdir logs

REM 复制环境配置文件
if not exist ".env" (
    echo 📝 创建环境配置文件...
    copy .env.example .env
    echo ✅ 请编辑 .env 文件配置您的环境变量
)

REM 运行数据库迁移
echo 🗄️ 初始化数据库...
cd src-tauri
cargo run --bin migrate
cargo run --bin seed
cd ..

echo ✅ 设置完成！
echo.
echo 🎯 下一步：
echo 1. 编辑 .env 文件配置环境变量
echo 2. 运行 'npm run tauri:dev' 启动开发服务器
echo 3. 或运行 'npm run tauri:build' 构建生产版本
pause
