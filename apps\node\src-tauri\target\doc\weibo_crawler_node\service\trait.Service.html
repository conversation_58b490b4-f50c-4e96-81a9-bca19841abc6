<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="基础服务特征"><title>Service in weibo_crawler_node::service - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc trait"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Service</a></h2><h3><a href="#required-methods">Required Methods</a></h3><ul class="block"><li><a href="#tymethod.name" title="name">name</a></li></ul><h3><a href="#implementors">Implementors</a></h3></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>service</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">service</a></div><h1>Trait <span class="trait">Service</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/service/mod.rs.html#39-41">Source</a> </span></div><pre class="rust item-decl"><code>pub trait Service {
    // Required method
    fn <a href="#tymethod.name" class="fn">name</a>(&amp;self) -&gt; &amp;'static <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a>;
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>基础服务特征</p>
</div></details><h2 id="required-methods" class="section-header">Required Methods<a href="#required-methods" class="anchor">§</a></h2><div class="methods"><section id="tymethod.name" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#40">Source</a><h4 class="code-header">fn <a href="#tymethod.name" class="fn">name</a>(&amp;self) -&gt; &amp;'static <a class="primitive" href="https://doc.rust-lang.org/1.88.0/std/primitive.str.html">str</a></h4></section></div><h2 id="implementors" class="section-header">Implementors<a href="#implementors" class="anchor">§</a></h2><div id="implementors-list"><section id="impl-Service-for-AccountService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/account_service.rs.html#42-46">Source</a><a href="#impl-Service-for-AccountService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a> for <a class="struct" href="account_service/struct.AccountService.html" title="struct weibo_crawler_node::service::account_service::AccountService">AccountService</a></h3></section><section id="impl-Service-for-CrawlerService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/crawler_service.rs.html#44-48">Source</a><a href="#impl-Service-for-CrawlerService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a> for <a class="struct" href="crawler_service/struct.CrawlerService.html" title="struct weibo_crawler_node::service::crawler_service::CrawlerService">CrawlerService</a></h3></section><section id="impl-Service-for-MonitorService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/monitor_service.rs.html#44-48">Source</a><a href="#impl-Service-for-MonitorService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a> for <a class="struct" href="monitor_service/struct.MonitorService.html" title="struct weibo_crawler_node::service::monitor_service::MonitorService">MonitorService</a></h3></section><section id="impl-Service-for-ProxyService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/proxy_service.rs.html#42-46">Source</a><a href="#impl-Service-for-ProxyService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a> for <a class="struct" href="proxy_service/struct.ProxyService.html" title="struct weibo_crawler_node::service::proxy_service::ProxyService">ProxyService</a></h3></section><section id="impl-Service-for-TaskService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/task_service.rs.html#252-256">Source</a><a href="#impl-Service-for-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a> for <a class="struct" href="task_service/struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section></div><script src="../../trait.impl/weibo_crawler_node/service/trait.Service.js" async></script></section></div></main></body></html>