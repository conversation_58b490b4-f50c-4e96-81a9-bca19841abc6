(function() {
    var implementors = Object.fromEntries([["weibo_crawler_node",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/account/enum.AccountStatus.html\" title=\"enum weibo_crawler_node::account::AccountStatus\">AccountStatus</a>",1,["weibo_crawler_node::account::AccountStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/crawler/enum.TaskType.html\" title=\"enum weibo_crawler_node::crawler::TaskType\">TaskType</a>",1,["weibo_crawler_node::crawler::TaskType"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/error/enum.AppError.html\" title=\"enum weibo_crawler_node::error::AppError\">AppError</a>",1,["weibo_crawler_node::error::AppError"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/notification/enum.ActionType.html\" title=\"enum weibo_crawler_node::notification::ActionType\">ActionType</a>",1,["weibo_crawler_node::notification::ActionType"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/notification/enum.NotificationType.html\" title=\"enum weibo_crawler_node::notification::NotificationType\">NotificationType</a>",1,["weibo_crawler_node::notification::NotificationType"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/proxy/enum.ProxyProtocol.html\" title=\"enum weibo_crawler_node::proxy::ProxyProtocol\">ProxyProtocol</a>",1,["weibo_crawler_node::proxy::ProxyProtocol"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/proxy/enum.ProxyStatus.html\" title=\"enum weibo_crawler_node::proxy::ProxyStatus\">ProxyStatus</a>",1,["weibo_crawler_node::proxy::ProxyStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/repository/enum.FilterOperator.html\" title=\"enum weibo_crawler_node::repository::FilterOperator\">FilterOperator</a>",1,["weibo_crawler_node::repository::FilterOperator"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/repository/enum.FilterValue.html\" title=\"enum weibo_crawler_node::repository::FilterValue\">FilterValue</a>",1,["weibo_crawler_node::repository::FilterValue"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/repository/enum.OrderDirection.html\" title=\"enum weibo_crawler_node::repository::OrderDirection\">OrderDirection</a>",1,["weibo_crawler_node::repository::OrderDirection"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/service/enum.HealthStatus.html\" title=\"enum weibo_crawler_node::service::HealthStatus\">HealthStatus</a>",1,["weibo_crawler_node::service::HealthStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"enum\" href=\"weibo_crawler_node/service/enum.ServiceEvent.html\" title=\"enum weibo_crawler_node::service::ServiceEvent\">ServiceEvent</a>",1,["weibo_crawler_node::service::ServiceEvent"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/account/struct.Account.html\" title=\"struct weibo_crawler_node::account::Account\">Account</a>",1,["weibo_crawler_node::account::Account"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/account/struct.AccountManager.html\" title=\"struct weibo_crawler_node::account::AccountManager\">AccountManager</a>",1,["weibo_crawler_node::account::AccountManager"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/account/struct.LoginSession.html\" title=\"struct weibo_crawler_node::account::LoginSession\">LoginSession</a>",1,["weibo_crawler_node::account::LoginSession"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.BrowserConfig.html\" title=\"struct weibo_crawler_node::browser::BrowserConfig\">BrowserConfig</a>",1,["weibo_crawler_node::browser::BrowserConfig"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.BrowserManager.html\" title=\"struct weibo_crawler_node::browser::BrowserManager\">BrowserManager</a>",1,["weibo_crawler_node::browser::BrowserManager"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.LoginCredentials.html\" title=\"struct weibo_crawler_node::browser::LoginCredentials\">LoginCredentials</a>",1,["weibo_crawler_node::browser::LoginCredentials"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.LoginResult.html\" title=\"struct weibo_crawler_node::browser::LoginResult\">LoginResult</a>",1,["weibo_crawler_node::browser::LoginResult"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.UserInfo.html\" title=\"struct weibo_crawler_node::browser::UserInfo\">UserInfo</a>",1,["weibo_crawler_node::browser::UserInfo"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.AccountConfig.html\" title=\"struct weibo_crawler_node::commands::AccountConfig\">AccountConfig</a>",1,["weibo_crawler_node::commands::AccountConfig"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.AccountPoolStatus.html\" title=\"struct weibo_crawler_node::commands::AccountPoolStatus\">AccountPoolStatus</a>",1,["weibo_crawler_node::commands::AccountPoolStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.LoginResult.html\" title=\"struct weibo_crawler_node::commands::LoginResult\">LoginResult</a>",1,["weibo_crawler_node::commands::LoginResult"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.NodeStatus.html\" title=\"struct weibo_crawler_node::commands::NodeStatus\">NodeStatus</a>",1,["weibo_crawler_node::commands::NodeStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.PerformanceStats.html\" title=\"struct weibo_crawler_node::commands::PerformanceStats\">PerformanceStats</a>",1,["weibo_crawler_node::commands::PerformanceStats"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.ProxyConfig.html\" title=\"struct weibo_crawler_node::commands::ProxyConfig\">ProxyConfig</a>",1,["weibo_crawler_node::commands::ProxyConfig"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.ProxyPoolStatus.html\" title=\"struct weibo_crawler_node::commands::ProxyPoolStatus\">ProxyPoolStatus</a>",1,["weibo_crawler_node::commands::ProxyPoolStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.ProxyTestResult.html\" title=\"struct weibo_crawler_node::commands::ProxyTestResult\">ProxyTestResult</a>",1,["weibo_crawler_node::commands::ProxyTestResult"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.SystemMetrics.html\" title=\"struct weibo_crawler_node::commands::SystemMetrics\">SystemMetrics</a>",1,["weibo_crawler_node::commands::SystemMetrics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.TaskQueueStatus.html\" title=\"struct weibo_crawler_node::commands::TaskQueueStatus\">TaskQueueStatus</a>",1,["weibo_crawler_node::commands::TaskQueueStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/commands/struct.TaskStatistics.html\" title=\"struct weibo_crawler_node::commands::TaskStatistics\">TaskStatistics</a>",1,["weibo_crawler_node::commands::TaskStatistics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/config/struct.AppConfig.html\" title=\"struct weibo_crawler_node::config::AppConfig\">AppConfig</a>",1,["weibo_crawler_node::config::AppConfig"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/crawler/struct.CrawlResult.html\" title=\"struct weibo_crawler_node::crawler::CrawlResult\">CrawlResult</a>",1,["weibo_crawler_node::crawler::CrawlResult"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/crawler/struct.CrawlTask.html\" title=\"struct weibo_crawler_node::crawler::CrawlTask\">CrawlTask</a>",1,["weibo_crawler_node::crawler::CrawlTask"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/crawler/struct.CrawlerEngine.html\" title=\"struct weibo_crawler_node::crawler::CrawlerEngine\">CrawlerEngine</a>",1,["weibo_crawler_node::crawler::CrawlerEngine"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/monitor/struct.NodeHealth.html\" title=\"struct weibo_crawler_node::monitor::NodeHealth\">NodeHealth</a>",1,["weibo_crawler_node::monitor::NodeHealth"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/monitor/struct.PerformanceMetrics.html\" title=\"struct weibo_crawler_node::monitor::PerformanceMetrics\">PerformanceMetrics</a>",1,["weibo_crawler_node::monitor::PerformanceMetrics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/monitor/struct.SystemMetrics.html\" title=\"struct weibo_crawler_node::monitor::SystemMetrics\">SystemMetrics</a>",1,["weibo_crawler_node::monitor::SystemMetrics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/monitor/struct.SystemMonitor.html\" title=\"struct weibo_crawler_node::monitor::SystemMonitor\">SystemMonitor</a>",1,["weibo_crawler_node::monitor::SystemMonitor"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/notification/struct.Notification.html\" title=\"struct weibo_crawler_node::notification::Notification\">Notification</a>",1,["weibo_crawler_node::notification::Notification"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/notification/struct.NotificationAction.html\" title=\"struct weibo_crawler_node::notification::NotificationAction\">NotificationAction</a>",1,["weibo_crawler_node::notification::NotificationAction"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/notification/struct.NotificationManager.html\" title=\"struct weibo_crawler_node::notification::NotificationManager\">NotificationManager</a>",1,["weibo_crawler_node::notification::NotificationManager"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/proxy/struct.Proxy.html\" title=\"struct weibo_crawler_node::proxy::Proxy\">Proxy</a>",1,["weibo_crawler_node::proxy::Proxy"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/proxy/struct.ProxyHealthCheck.html\" title=\"struct weibo_crawler_node::proxy::ProxyHealthCheck\">ProxyHealthCheck</a>",1,["weibo_crawler_node::proxy::ProxyHealthCheck"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/proxy/struct.ProxyManager.html\" title=\"struct weibo_crawler_node::proxy::ProxyManager\">ProxyManager</a>",1,["weibo_crawler_node::proxy::ProxyManager"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/account_repository/struct.AccountRepository.html\" title=\"struct weibo_crawler_node::repository::account_repository::AccountRepository\">AccountRepository</a>",1,["weibo_crawler_node::repository::account_repository::AccountRepository"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/proxy_repository/struct.ProxyRepository.html\" title=\"struct weibo_crawler_node::repository::proxy_repository::ProxyRepository\">ProxyRepository</a>",1,["weibo_crawler_node::repository::proxy_repository::ProxyRepository"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/struct.Filter.html\" title=\"struct weibo_crawler_node::repository::Filter\">Filter</a>",1,["weibo_crawler_node::repository::Filter"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/struct.QueryParams.html\" title=\"struct weibo_crawler_node::repository::QueryParams\">QueryParams</a>",1,["weibo_crawler_node::repository::QueryParams"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/struct.RepositoryManager.html\" title=\"struct weibo_crawler_node::repository::RepositoryManager\">RepositoryManager</a>",1,["weibo_crawler_node::repository::RepositoryManager"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/task_repository/struct.TaskRepository.html\" title=\"struct weibo_crawler_node::repository::task_repository::TaskRepository\">TaskRepository</a>",1,["weibo_crawler_node::repository::task_repository::TaskRepository"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/task_repository/struct.TaskStatistics.html\" title=\"struct weibo_crawler_node::repository::task_repository::TaskStatistics\">TaskStatistics</a>",1,["weibo_crawler_node::repository::task_repository::TaskStatistics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/scheduler/struct.PriorityTask.html\" title=\"struct weibo_crawler_node::scheduler::PriorityTask\">PriorityTask</a>",1,["weibo_crawler_node::scheduler::PriorityTask"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/scheduler/struct.TaskScheduler.html\" title=\"struct weibo_crawler_node::scheduler::TaskScheduler\">TaskScheduler</a>",1,["weibo_crawler_node::scheduler::TaskScheduler"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/scheduler/struct.TaskStatistics.html\" title=\"struct weibo_crawler_node::scheduler::TaskStatistics\">TaskStatistics</a>",1,["weibo_crawler_node::scheduler::TaskStatistics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/account_service/struct.AccountMetrics.html\" title=\"struct weibo_crawler_node::service::account_service::AccountMetrics\">AccountMetrics</a>",1,["weibo_crawler_node::service::account_service::AccountMetrics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/account_service/struct.AccountService.html\" title=\"struct weibo_crawler_node::service::account_service::AccountService\">AccountService</a>",1,["weibo_crawler_node::service::account_service::AccountService"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/crawler_service/struct.CrawlerMetrics.html\" title=\"struct weibo_crawler_node::service::crawler_service::CrawlerMetrics\">CrawlerMetrics</a>",1,["weibo_crawler_node::service::crawler_service::CrawlerMetrics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/crawler_service/struct.CrawlerService.html\" title=\"struct weibo_crawler_node::service::crawler_service::CrawlerService\">CrawlerService</a>",1,["weibo_crawler_node::service::crawler_service::CrawlerService"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/monitor_service/struct.MonitorService.html\" title=\"struct weibo_crawler_node::service::monitor_service::MonitorService\">MonitorService</a>",1,["weibo_crawler_node::service::monitor_service::MonitorService"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/monitor_service/struct.SystemMetrics.html\" title=\"struct weibo_crawler_node::service::monitor_service::SystemMetrics\">SystemMetrics</a>",1,["weibo_crawler_node::service::monitor_service::SystemMetrics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/proxy_service/struct.ProxyMetrics.html\" title=\"struct weibo_crawler_node::service::proxy_service::ProxyMetrics\">ProxyMetrics</a>",1,["weibo_crawler_node::service::proxy_service::ProxyMetrics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/proxy_service/struct.ProxyService.html\" title=\"struct weibo_crawler_node::service::proxy_service::ProxyService\">ProxyService</a>",1,["weibo_crawler_node::service::proxy_service::ProxyService"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/struct.ServiceConfig.html\" title=\"struct weibo_crawler_node::service::ServiceConfig\">ServiceConfig</a>",1,["weibo_crawler_node::service::ServiceConfig"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/struct.ServiceContainer.html\" title=\"struct weibo_crawler_node::service::ServiceContainer\">ServiceContainer</a>",1,["weibo_crawler_node::service::ServiceContainer"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/struct.ServiceLifecycleManager.html\" title=\"struct weibo_crawler_node::service::ServiceLifecycleManager\">ServiceLifecycleManager</a>",1,["weibo_crawler_node::service::ServiceLifecycleManager"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/struct.ServiceManager.html\" title=\"struct weibo_crawler_node::service::ServiceManager\">ServiceManager</a>",1,["weibo_crawler_node::service::ServiceManager"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/struct.ServiceRegistry.html\" title=\"struct weibo_crawler_node::service::ServiceRegistry\">ServiceRegistry</a>",1,["weibo_crawler_node::service::ServiceRegistry"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/task_service/struct.CreateTaskRequest.html\" title=\"struct weibo_crawler_node::service::task_service::CreateTaskRequest\">CreateTaskRequest</a>",1,["weibo_crawler_node::service::task_service::CreateTaskRequest"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/task_service/struct.TaskMetrics.html\" title=\"struct weibo_crawler_node::service::task_service::TaskMetrics\">TaskMetrics</a>",1,["weibo_crawler_node::service::task_service::TaskMetrics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/task_service/struct.TaskQuery.html\" title=\"struct weibo_crawler_node::service::task_service::TaskQuery\">TaskQuery</a>",1,["weibo_crawler_node::service::task_service::TaskQuery"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/task_service/struct.TaskService.html\" title=\"struct weibo_crawler_node::service::task_service::TaskService\">TaskService</a>",1,["weibo_crawler_node::service::task_service::TaskService"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/task_service/struct.UpdateTaskRequest.html\" title=\"struct weibo_crawler_node::service::task_service::UpdateTaskRequest\">UpdateTaskRequest</a>",1,["weibo_crawler_node::service::task_service::UpdateTaskRequest"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.AccountLog.html\" title=\"struct weibo_crawler_node::storage::AccountLog\">AccountLog</a>",1,["weibo_crawler_node::storage::AccountLog"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.AccountPoolStatus.html\" title=\"struct weibo_crawler_node::storage::AccountPoolStatus\">AccountPoolStatus</a>",1,["weibo_crawler_node::storage::AccountPoolStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.AccountRecord.html\" title=\"struct weibo_crawler_node::storage::AccountRecord\">AccountRecord</a>",1,["weibo_crawler_node::storage::AccountRecord"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.ErrorLog.html\" title=\"struct weibo_crawler_node::storage::ErrorLog\">ErrorLog</a>",1,["weibo_crawler_node::storage::ErrorLog"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.ProxyPoolStatus.html\" title=\"struct weibo_crawler_node::storage::ProxyPoolStatus\">ProxyPoolStatus</a>",1,["weibo_crawler_node::storage::ProxyPoolStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.ProxyRecord.html\" title=\"struct weibo_crawler_node::storage::ProxyRecord\">ProxyRecord</a>",1,["weibo_crawler_node::storage::ProxyRecord"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.ProxyUsage.html\" title=\"struct weibo_crawler_node::storage::ProxyUsage\">ProxyUsage</a>",1,["weibo_crawler_node::storage::ProxyUsage"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.StorageManager.html\" title=\"struct weibo_crawler_node::storage::StorageManager\">StorageManager</a>",1,["weibo_crawler_node::storage::StorageManager"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.SystemMetrics.html\" title=\"struct weibo_crawler_node::storage::SystemMetrics\">SystemMetrics</a>",1,["weibo_crawler_node::storage::SystemMetrics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.TaskLog.html\" title=\"struct weibo_crawler_node::storage::TaskLog\">TaskLog</a>",1,["weibo_crawler_node::storage::TaskLog"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.TaskRecord.html\" title=\"struct weibo_crawler_node::storage::TaskRecord\">TaskRecord</a>",1,["weibo_crawler_node::storage::TaskRecord"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.TaskResult.html\" title=\"struct weibo_crawler_node::storage::TaskResult\">TaskResult</a>",1,["weibo_crawler_node::storage::TaskResult"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/storage/struct.TaskStatistics.html\" title=\"struct weibo_crawler_node::storage::TaskStatistics\">TaskStatistics</a>",1,["weibo_crawler_node::storage::TaskStatistics"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/struct.AppState.html\" title=\"struct weibo_crawler_node::AppState\">AppState</a>",1,["weibo_crawler_node::AppState"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/updater/struct.UpdateInfo.html\" title=\"struct weibo_crawler_node::updater::UpdateInfo\">UpdateInfo</a>",1,["weibo_crawler_node::updater::UpdateInfo"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/updater/struct.UpdateManager.html\" title=\"struct weibo_crawler_node::updater::UpdateManager\">UpdateManager</a>",1,["weibo_crawler_node::updater::UpdateManager"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/updater/struct.UpdateStatus.html\" title=\"struct weibo_crawler_node::updater::UpdateStatus\">UpdateStatus</a>",1,["weibo_crawler_node::updater::UpdateStatus"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/window_manager/struct.WindowConfig.html\" title=\"struct weibo_crawler_node::window_manager::WindowConfig\">WindowConfig</a>",1,["weibo_crawler_node::window_manager::WindowConfig"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/window_manager/struct.WindowInfo.html\" title=\"struct weibo_crawler_node::window_manager::WindowInfo\">WindowInfo</a>",1,["weibo_crawler_node::window_manager::WindowInfo"]],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/window_manager/struct.WindowManager.html\" title=\"struct weibo_crawler_node::window_manager::WindowManager\">WindowManager</a>",1,["weibo_crawler_node::window_manager::WindowManager"]],["impl&lt;T&gt; <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/marker/trait.UnsafeUnpin.html\" title=\"trait core::marker::UnsafeUnpin\">UnsafeUnpin</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/struct.PaginatedResult.html\" title=\"struct weibo_crawler_node::repository::PaginatedResult\">PaginatedResult</a>&lt;T&gt;",1,["weibo_crawler_node::repository::PaginatedResult"]]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[36238]}