import React from 'react'
import {
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'
import {
  BaseChartProps,
  ChartContainer,
  CustomTooltip,
  chartColorPalette,
  defaultGridConfig,
  defaultXAxisConfig,
  defaultYAxisConfig,
  defaultLegendConfig,
  formatters
} from './BaseChart'

export interface LineChartProps extends BaseChartProps {
  lines?: Array<{
    key: string
    name?: string
    color?: string
    strokeWidth?: number
    strokeDasharray?: string
    dot?: boolean
    activeDot?: boolean
  }>
  smooth?: boolean
  connectNulls?: boolean
  formatter?: (value: any, name: string) => [React.ReactNode, string]
  labelFormatter?: (label: any) => React.ReactNode
}

export const LineChart: React.FC<LineChartProps> = ({
  data,
  height = 300,
  className,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  showXAxis = true,
  showYAxis = true,
  xAxisKey = 'name',
  color = chartColorPalette[0],
  colors = chartColorPalette,
  loading,
  error,
  title,
  subtitle,
  lines,
  smooth = false,
  connectNulls = false,
  formatter,
  labelFormatter
}) => {
  // 如果没有指定 lines，则从数据中推断
  const chartLines = lines || Object.keys(data[0] || {})
    .filter(key => key !== xAxisKey && typeof data[0]?.[key] === 'number')
    .map((key, index) => ({
      key,
      name: key,
      color: colors[index % colors.length]
    }))

  return (
    <ChartContainer
      title={title}
      subtitle={subtitle}
      loading={loading}
      error={error}
      className={className}
      height={height}
    >
      <RechartsLineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        {showGrid && <CartesianGrid {...defaultGridConfig} />}
        
        {showXAxis && (
          <XAxis 
            dataKey={xAxisKey}
            {...defaultXAxisConfig}
          />
        )}
        
        {showYAxis && (
          <YAxis {...defaultYAxisConfig} />
        )}
        
        {showTooltip && (
          <Tooltip
            content={<CustomTooltip formatter={formatter} labelFormatter={labelFormatter} />}
          />
        )}
        
        {showLegend && <Legend {...defaultLegendConfig} />}
        
        {chartLines.map((line, index) => (
          <Line
            key={line.key}
            type={smooth ? "monotone" : "linear"}
            dataKey={line.key}
            name={line.name}
            stroke={line.color || color}
            strokeWidth={line.strokeWidth || 2}
            strokeDasharray={line.strokeDasharray}
            dot={line.dot !== false ? { fill: line.color || color, strokeWidth: 2, r: 4 } : false}
            activeDot={line.activeDot !== false ? { r: 6, fill: line.color || color } : false}
            connectNulls={connectNulls}
          />
        ))}
      </RechartsLineChart>
    </ChartContainer>
  )
}

// 预设的线图组件
export const SimpleLineChart: React.FC<Omit<LineChartProps, 'lines'> & {
  yKey?: string
}> = ({ yKey = 'value', ...props }) => {
  return (
    <LineChart
      {...props}
      lines={[{
        key: yKey,
        name: yKey,
        color: props.color
      }]}
    />
  )
}

// 多线图组件
export const MultiLineChart: React.FC<LineChartProps & {
  metrics: Array<{
    key: string
    name: string
    color?: string
    unit?: string
  }>
}> = ({ metrics, formatter, ...props }) => {
  const lines = metrics.map((metric, index) => ({
    key: metric.key,
    name: metric.name,
    color: metric.color || chartColorPalette[index % chartColorPalette.length]
  }))

  const customFormatter = formatter || ((value: any, name: string) => {
    const metric = metrics.find(m => m.name === name)
    const unit = metric?.unit || ''
    return [
      typeof value === 'number' ? formatters.number(value) + unit : value,
      name
    ]
  })

  return (
    <LineChart
      {...props}
      lines={lines}
      showLegend={true}
      formatter={customFormatter}
    />
  )
}

// 趋势线图组件
export const TrendLineChart: React.FC<LineChartProps & {
  trendKey?: string
  showTrend?: boolean
  trendColor?: string
}> = ({ 
  trendKey = 'trend', 
  showTrend = true, 
  trendColor = '#ef4444',
  lines,
  ...props 
}) => {
  const chartLines = [...(lines || []), ...(showTrend ? [{
    key: trendKey,
    name: '趋势线',
    color: trendColor,
    strokeDasharray: '5 5',
    dot: false,
    activeDot: false
  }] : [])]

  return (
    <LineChart
      {...props}
      lines={chartLines}
      smooth={true}
    />
  )
}

// 实时线图组件
export const RealtimeLineChart: React.FC<LineChartProps & {
  maxDataPoints?: number
  updateInterval?: number
}> = ({ 
  data, 
  maxDataPoints = 50,
  ...props 
}) => {
  // 限制数据点数量以提高性能
  const limitedData = React.useMemo(() => {
    if (data.length <= maxDataPoints) return data
    return data.slice(-maxDataPoints)
  }, [data, maxDataPoints])

  return (
    <LineChart
      {...props}
      data={limitedData}
      smooth={true}
      connectNulls={true}
    />
  )
}

// 性能监控线图
export const PerformanceLineChart: React.FC<Omit<LineChartProps, 'formatter' | 'lines'> & {
  metrics: Array<{
    key: string
    name: string
    color?: string
    type: 'percentage' | 'number' | 'bytes' | 'duration'
  }>
}> = ({ metrics, ...props }) => {
  const lines = metrics.map((metric, index) => ({
    key: metric.key,
    name: metric.name,
    color: metric.color || chartColorPalette[index % chartColorPalette.length]
  }))

  const formatter = (value: any, name: string) => {
    const metric = metrics.find(m => m.name === name)
    if (!metric) return [value, name]

    let formattedValue: string
    switch (metric.type) {
      case 'percentage':
        formattedValue = formatters.percentage(value)
        break
      case 'bytes':
        formattedValue = formatters.bytes(value)
        break
      case 'duration':
        formattedValue = formatters.duration(value)
        break
      default:
        formattedValue = formatters.number(value)
    }

    return [formattedValue, name]
  }

  return (
    <LineChart
      {...props}
      lines={lines}
      formatter={formatter}
      showLegend={true}
    />
  )
}
