<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\service\task_service.rs`."><title>task_service.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node\service/</div>task_service.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-3"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use super</span>::{Service, StartableService, MonitorableService, HealthStatus};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span><span class="kw">crate</span>::error::{AppError, <span class="prelude-ty">Result</span>};
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span><span class="kw">crate</span>::repository::{RepositoryManager, PaginatedResult, Repository, PaginatedRepository};
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span><span class="kw">crate</span>::storage::TaskRecord;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>async_trait::async_trait;
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>std::sync::{Arc, atomic::{AtomicBool, AtomicU64, Ordering}};
<a href=#7 id=7 data-nosnippet>7</a><span class="kw">use </span>tokio::sync::RwLock;
<a href=#8 id=8 data-nosnippet>8</a><span class="kw">use </span>chrono::{DateTime, Utc};
<a href=#9 id=9 data-nosnippet>9</a><span class="kw">use </span>serde::{Serialize, Deserialize};
<a href=#10 id=10 data-nosnippet>10</a>
<a href=#11 id=11 data-nosnippet>11</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#12 id=12 data-nosnippet>12</a></span><span class="kw">pub struct </span>CreateTaskRequest {
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>task_type: i32,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>target_url: String,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>priority: i32,
<a href=#16 id=16 data-nosnippet>16</a>    <span class="kw">pub </span>max_retries: i32,
<a href=#17 id=17 data-nosnippet>17</a>    <span class="kw">pub </span>metadata: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#18 id=18 data-nosnippet>18</a>}
<a href=#19 id=19 data-nosnippet>19</a>
<a href=#20 id=20 data-nosnippet>20</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#21 id=21 data-nosnippet>21</a></span><span class="kw">pub struct </span>UpdateTaskRequest {
<a href=#22 id=22 data-nosnippet>22</a>    <span class="kw">pub </span>task_type: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#23 id=23 data-nosnippet>23</a>    <span class="kw">pub </span>target_url: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#24 id=24 data-nosnippet>24</a>    <span class="kw">pub </span>priority: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#25 id=25 data-nosnippet>25</a>    <span class="kw">pub </span>max_retries: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#26 id=26 data-nosnippet>26</a>    <span class="kw">pub </span>metadata: <span class="prelude-ty">Option</span>&lt;String&gt;,
<a href=#27 id=27 data-nosnippet>27</a>}
<a href=#28 id=28 data-nosnippet>28</a>
<a href=#29 id=29 data-nosnippet>29</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#30 id=30 data-nosnippet>30</a></span><span class="kw">pub struct </span>TaskQuery {
<a href=#31 id=31 data-nosnippet>31</a>    <span class="kw">pub </span>status: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#32 id=32 data-nosnippet>32</a>    <span class="kw">pub </span>task_type: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#33 id=33 data-nosnippet>33</a>    <span class="kw">pub </span>priority_min: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#34 id=34 data-nosnippet>34</a>    <span class="kw">pub </span>priority_max: <span class="prelude-ty">Option</span>&lt;i32&gt;,
<a href=#35 id=35 data-nosnippet>35</a>    <span class="kw">pub </span>created_after: <span class="prelude-ty">Option</span>&lt;DateTime&lt;Utc&gt;&gt;,
<a href=#36 id=36 data-nosnippet>36</a>    <span class="kw">pub </span>created_before: <span class="prelude-ty">Option</span>&lt;DateTime&lt;Utc&gt;&gt;,
<a href=#37 id=37 data-nosnippet>37</a>}
<a href=#38 id=38 data-nosnippet>38</a>
<a href=#39 id=39 data-nosnippet>39</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#40 id=40 data-nosnippet>40</a></span><span class="kw">pub struct </span>TaskMetrics {
<a href=#41 id=41 data-nosnippet>41</a>    <span class="kw">pub </span>total_tasks: u64,
<a href=#42 id=42 data-nosnippet>42</a>    <span class="kw">pub </span>pending_tasks: u32,
<a href=#43 id=43 data-nosnippet>43</a>    <span class="kw">pub </span>running_tasks: u32,
<a href=#44 id=44 data-nosnippet>44</a>    <span class="kw">pub </span>completed_tasks: u64,
<a href=#45 id=45 data-nosnippet>45</a>    <span class="kw">pub </span>failed_tasks: u64,
<a href=#46 id=46 data-nosnippet>46</a>    <span class="kw">pub </span>success_rate: f64,
<a href=#47 id=47 data-nosnippet>47</a>    <span class="kw">pub </span>average_processing_time: f64,
<a href=#48 id=48 data-nosnippet>48</a>    <span class="kw">pub </span>tasks_per_hour: f64,
<a href=#49 id=49 data-nosnippet>49</a>    <span class="kw">pub </span>last_updated: DateTime&lt;Utc&gt;,
<a href=#50 id=50 data-nosnippet>50</a>}
<a href=#51 id=51 data-nosnippet>51</a>
<a href=#52 id=52 data-nosnippet>52</a><span class="kw">pub struct </span>TaskService {
<a href=#53 id=53 data-nosnippet>53</a>    repository_manager: Arc&lt;RepositoryManager&gt;,
<a href=#54 id=54 data-nosnippet>54</a>    is_running: AtomicBool,
<a href=#55 id=55 data-nosnippet>55</a>    processed_count: AtomicU64,
<a href=#56 id=56 data-nosnippet>56</a>    failed_count: AtomicU64,
<a href=#57 id=57 data-nosnippet>57</a>    start_time: RwLock&lt;<span class="prelude-ty">Option</span>&lt;DateTime&lt;Utc&gt;&gt;&gt;,
<a href=#58 id=58 data-nosnippet>58</a>}
<a href=#59 id=59 data-nosnippet>59</a>
<a href=#60 id=60 data-nosnippet>60</a><span class="kw">impl </span>TaskService {
<a href=#61 id=61 data-nosnippet>61</a>    <span class="kw">pub fn </span>new(repository_manager: Arc&lt;RepositoryManager&gt;) -&gt; <span class="self">Self </span>{
<a href=#62 id=62 data-nosnippet>62</a>        <span class="self">Self </span>{
<a href=#63 id=63 data-nosnippet>63</a>            repository_manager,
<a href=#64 id=64 data-nosnippet>64</a>            is_running: AtomicBool::new(<span class="bool-val">false</span>),
<a href=#65 id=65 data-nosnippet>65</a>            processed_count: AtomicU64::new(<span class="number">0</span>),
<a href=#66 id=66 data-nosnippet>66</a>            failed_count: AtomicU64::new(<span class="number">0</span>),
<a href=#67 id=67 data-nosnippet>67</a>            start_time: RwLock::new(<span class="prelude-val">None</span>),
<a href=#68 id=68 data-nosnippet>68</a>        }
<a href=#69 id=69 data-nosnippet>69</a>    }
<a href=#70 id=70 data-nosnippet>70</a>
<a href=#71 id=71 data-nosnippet>71</a>    <span class="doccomment">/// 创建新任务
<a href=#72 id=72 data-nosnippet>72</a>    </span><span class="kw">pub async fn </span>create_task(<span class="kw-2">&amp;</span><span class="self">self</span>, request: CreateTaskRequest) -&gt; <span class="prelude-ty">Result</span>&lt;TaskRecord&gt; {
<a href=#73 id=73 data-nosnippet>73</a>        <span class="kw">let </span>task_id = uuid::Uuid::new_v4().to_string();
<a href=#74 id=74 data-nosnippet>74</a>        <span class="kw">let </span>now = Utc::now();
<a href=#75 id=75 data-nosnippet>75</a>
<a href=#76 id=76 data-nosnippet>76</a>        <span class="kw">let </span>task = TaskRecord {
<a href=#77 id=77 data-nosnippet>77</a>            id: <span class="number">0</span>, <span class="comment">// 将由数据库自动生成
<a href=#78 id=78 data-nosnippet>78</a>            </span>task_id,
<a href=#79 id=79 data-nosnippet>79</a>            task_type: request.task_type,
<a href=#80 id=80 data-nosnippet>80</a>            target_url: request.target_url,
<a href=#81 id=81 data-nosnippet>81</a>            priority: request.priority,
<a href=#82 id=82 data-nosnippet>82</a>            status: <span class="number">0</span>, <span class="comment">// 待处理
<a href=#83 id=83 data-nosnippet>83</a>            </span>retry_count: <span class="number">0</span>,
<a href=#84 id=84 data-nosnippet>84</a>            max_retries: request.max_retries,
<a href=#85 id=85 data-nosnippet>85</a>            metadata: request.metadata,
<a href=#86 id=86 data-nosnippet>86</a>            assigned_at: <span class="prelude-val">None</span>,
<a href=#87 id=87 data-nosnippet>87</a>            started_at: <span class="prelude-val">None</span>,
<a href=#88 id=88 data-nosnippet>88</a>            completed_at: <span class="prelude-val">None</span>,
<a href=#89 id=89 data-nosnippet>89</a>            created_at: now,
<a href=#90 id=90 data-nosnippet>90</a>            updated_at: now,
<a href=#91 id=91 data-nosnippet>91</a>        };
<a href=#92 id=92 data-nosnippet>92</a>
<a href=#93 id=93 data-nosnippet>93</a>        <span class="kw">let </span>created_task = <span class="self">self</span>.repository_manager.task.create(task).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#94 id=94 data-nosnippet>94</a>        <span class="macro">tracing::info!</span>(<span class="string">"Created task: {}"</span>, created_task.task_id);
<a href=#95 id=95 data-nosnippet>95</a>        
<a href=#96 id=96 data-nosnippet>96</a>        <span class="prelude-val">Ok</span>(created_task)
<a href=#97 id=97 data-nosnippet>97</a>    }
<a href=#98 id=98 data-nosnippet>98</a>
<a href=#99 id=99 data-nosnippet>99</a>    <span class="doccomment">/// 批量创建任务
<a href=#100 id=100 data-nosnippet>100</a>    </span><span class="kw">pub async fn </span>create_tasks_batch(<span class="kw-2">&amp;</span><span class="self">self</span>, requests: Vec&lt;CreateTaskRequest&gt;) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;TaskRecord&gt;&gt; {
<a href=#101 id=101 data-nosnippet>101</a>        <span class="kw">let </span><span class="kw-2">mut </span>created_tasks = Vec::new();
<a href=#102 id=102 data-nosnippet>102</a>        
<a href=#103 id=103 data-nosnippet>103</a>        <span class="kw">for </span>request <span class="kw">in </span>requests {
<a href=#104 id=104 data-nosnippet>104</a>            <span class="kw">match </span><span class="self">self</span>.create_task(request).<span class="kw">await </span>{
<a href=#105 id=105 data-nosnippet>105</a>                <span class="prelude-val">Ok</span>(task) =&gt; created_tasks.push(task),
<a href=#106 id=106 data-nosnippet>106</a>                <span class="prelude-val">Err</span>(e) =&gt; {
<a href=#107 id=107 data-nosnippet>107</a>                    <span class="macro">tracing::error!</span>(<span class="string">"Failed to create task: {}"</span>, e);
<a href=#108 id=108 data-nosnippet>108</a>                    <span class="comment">// 继续处理其他任务，不中断整个批次
<a href=#109 id=109 data-nosnippet>109</a>                </span>}
<a href=#110 id=110 data-nosnippet>110</a>            }
<a href=#111 id=111 data-nosnippet>111</a>        }
<a href=#112 id=112 data-nosnippet>112</a>        
<a href=#113 id=113 data-nosnippet>113</a>        <span class="prelude-val">Ok</span>(created_tasks)
<a href=#114 id=114 data-nosnippet>114</a>    }
<a href=#115 id=115 data-nosnippet>115</a>
<a href=#116 id=116 data-nosnippet>116</a>    <span class="doccomment">/// 获取任务详情
<a href=#117 id=117 data-nosnippet>117</a>    </span><span class="kw">pub async fn </span>get_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="prelude-ty">Option</span>&lt;TaskRecord&gt;&gt; {
<a href=#118 id=118 data-nosnippet>118</a>        <span class="comment">// 这里需要通过 task_id 查找，但当前仓库只支持通过 id 查找
<a href=#119 id=119 data-nosnippet>119</a>        // 需要扩展仓库方法或在这里实现查找逻辑
<a href=#120 id=120 data-nosnippet>120</a>        </span><span class="kw">let </span>tasks = <span class="self">self</span>.repository_manager.task.find_all().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#121 id=121 data-nosnippet>121</a>        <span class="prelude-val">Ok</span>(tasks.into_iter().find(|t| t.task_id == task_id))
<a href=#122 id=122 data-nosnippet>122</a>    }
<a href=#123 id=123 data-nosnippet>123</a>
<a href=#124 id=124 data-nosnippet>124</a>    <span class="doccomment">/// 更新任务
<a href=#125 id=125 data-nosnippet>125</a>    </span><span class="kw">pub async fn </span>update_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str, request: UpdateTaskRequest) -&gt; <span class="prelude-ty">Result</span>&lt;TaskRecord&gt; {
<a href=#126 id=126 data-nosnippet>126</a>        <span class="kw">let </span>task = <span class="self">self</span>.get_task(task_id).<span class="kw">await</span><span class="question-mark">?
<a href=#127 id=127 data-nosnippet>127</a>            </span>.ok_or_else(|| AppError::Other(<span class="macro">format!</span>(<span class="string">"Task not found: {}"</span>, task_id)))<span class="question-mark">?</span>;
<a href=#128 id=128 data-nosnippet>128</a>
<a href=#129 id=129 data-nosnippet>129</a>        <span class="kw">let </span>updated_task = TaskRecord {
<a href=#130 id=130 data-nosnippet>130</a>            task_type: request.task_type.unwrap_or(task.task_type),
<a href=#131 id=131 data-nosnippet>131</a>            target_url: request.target_url.unwrap_or(task.target_url),
<a href=#132 id=132 data-nosnippet>132</a>            priority: request.priority.unwrap_or(task.priority),
<a href=#133 id=133 data-nosnippet>133</a>            max_retries: request.max_retries.unwrap_or(task.max_retries),
<a href=#134 id=134 data-nosnippet>134</a>            metadata: request.metadata.or(task.metadata),
<a href=#135 id=135 data-nosnippet>135</a>            updated_at: Utc::now(),
<a href=#136 id=136 data-nosnippet>136</a>            ..task
<a href=#137 id=137 data-nosnippet>137</a>        };
<a href=#138 id=138 data-nosnippet>138</a>
<a href=#139 id=139 data-nosnippet>139</a>        <span class="self">self</span>.repository_manager.task.update(updated_task.id, updated_task).<span class="kw">await
<a href=#140 id=140 data-nosnippet>140</a>    </span>}
<a href=#141 id=141 data-nosnippet>141</a>
<a href=#142 id=142 data-nosnippet>142</a>    <span class="doccomment">/// 删除任务
<a href=#143 id=143 data-nosnippet>143</a>    </span><span class="kw">pub async fn </span>delete_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#144 id=144 data-nosnippet>144</a>        <span class="kw">let </span>task = <span class="self">self</span>.get_task(task_id).<span class="kw">await</span><span class="question-mark">?
<a href=#145 id=145 data-nosnippet>145</a>            </span>.ok_or_else(|| AppError::Other(<span class="macro">format!</span>(<span class="string">"Task not found: {}"</span>, task_id)))<span class="question-mark">?</span>;
<a href=#146 id=146 data-nosnippet>146</a>
<a href=#147 id=147 data-nosnippet>147</a>        <span class="self">self</span>.repository_manager.task.delete(task.id).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#148 id=148 data-nosnippet>148</a>        <span class="macro">tracing::info!</span>(<span class="string">"Deleted task: {}"</span>, task_id);
<a href=#149 id=149 data-nosnippet>149</a>        
<a href=#150 id=150 data-nosnippet>150</a>        <span class="prelude-val">Ok</span>(())
<a href=#151 id=151 data-nosnippet>151</a>    }
<a href=#152 id=152 data-nosnippet>152</a>
<a href=#153 id=153 data-nosnippet>153</a>    <span class="doccomment">/// 获取任务列表（分页）
<a href=#154 id=154 data-nosnippet>154</a>    </span><span class="kw">pub async fn </span>list_tasks(<span class="kw-2">&amp;</span><span class="self">self</span>, page: u32, limit: u32) -&gt; <span class="prelude-ty">Result</span>&lt;PaginatedResult&lt;TaskRecord&gt;&gt; {
<a href=#155 id=155 data-nosnippet>155</a>        <span class="self">self</span>.repository_manager.task.find_paginated(page, limit).<span class="kw">await
<a href=#156 id=156 data-nosnippet>156</a>    </span>}
<a href=#157 id=157 data-nosnippet>157</a>
<a href=#158 id=158 data-nosnippet>158</a>    <span class="doccomment">/// 根据条件查询任务
<a href=#159 id=159 data-nosnippet>159</a>    </span><span class="kw">pub async fn </span>query_tasks(<span class="kw-2">&amp;</span><span class="self">self</span>, query: TaskQuery) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;TaskRecord&gt;&gt; {
<a href=#160 id=160 data-nosnippet>160</a>        <span class="comment">// 这里需要实现复杂查询逻辑
<a href=#161 id=161 data-nosnippet>161</a>        // 当前简化实现，只支持按状态查询
<a href=#162 id=162 data-nosnippet>162</a>        </span><span class="kw">if let </span><span class="prelude-val">Some</span>(status) = query.status {
<a href=#163 id=163 data-nosnippet>163</a>            <span class="self">self</span>.repository_manager.task.find_by_status(status).<span class="kw">await
<a href=#164 id=164 data-nosnippet>164</a>        </span>} <span class="kw">else if let </span><span class="prelude-val">Some</span>(task_type) = query.task_type {
<a href=#165 id=165 data-nosnippet>165</a>            <span class="self">self</span>.repository_manager.task.find_by_type(task_type).<span class="kw">await
<a href=#166 id=166 data-nosnippet>166</a>        </span>} <span class="kw">else </span>{
<a href=#167 id=167 data-nosnippet>167</a>            <span class="self">self</span>.repository_manager.task.find_all().<span class="kw">await
<a href=#168 id=168 data-nosnippet>168</a>        </span>}
<a href=#169 id=169 data-nosnippet>169</a>    }
<a href=#170 id=170 data-nosnippet>170</a>
<a href=#171 id=171 data-nosnippet>171</a>    <span class="doccomment">/// 获取待处理任务
<a href=#172 id=172 data-nosnippet>172</a>    </span><span class="kw">pub async fn </span>get_pending_tasks(<span class="kw-2">&amp;</span><span class="self">self</span>, limit: u32) -&gt; <span class="prelude-ty">Result</span>&lt;Vec&lt;TaskRecord&gt;&gt; {
<a href=#173 id=173 data-nosnippet>173</a>        <span class="self">self</span>.repository_manager.task.get_pending_tasks(limit).<span class="kw">await
<a href=#174 id=174 data-nosnippet>174</a>    </span>}
<a href=#175 id=175 data-nosnippet>175</a>
<a href=#176 id=176 data-nosnippet>176</a>    <span class="doccomment">/// 开始处理任务
<a href=#177 id=177 data-nosnippet>177</a>    </span><span class="kw">pub async fn </span>start_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#178 id=178 data-nosnippet>178</a>        <span class="self">self</span>.repository_manager.task.start_task(task_id).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#179 id=179 data-nosnippet>179</a>        <span class="macro">tracing::info!</span>(<span class="string">"Started task: {}"</span>, task_id);
<a href=#180 id=180 data-nosnippet>180</a>        <span class="prelude-val">Ok</span>(())
<a href=#181 id=181 data-nosnippet>181</a>    }
<a href=#182 id=182 data-nosnippet>182</a>
<a href=#183 id=183 data-nosnippet>183</a>    <span class="doccomment">/// 完成任务
<a href=#184 id=184 data-nosnippet>184</a>    </span><span class="kw">pub async fn </span>complete_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#185 id=185 data-nosnippet>185</a>        <span class="self">self</span>.repository_manager.task.complete_task(task_id).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#186 id=186 data-nosnippet>186</a>        <span class="self">self</span>.processed_count.fetch_add(<span class="number">1</span>, Ordering::Relaxed);
<a href=#187 id=187 data-nosnippet>187</a>        <span class="macro">tracing::info!</span>(<span class="string">"Completed task: {}"</span>, task_id);
<a href=#188 id=188 data-nosnippet>188</a>        <span class="prelude-val">Ok</span>(())
<a href=#189 id=189 data-nosnippet>189</a>    }
<a href=#190 id=190 data-nosnippet>190</a>
<a href=#191 id=191 data-nosnippet>191</a>    <span class="doccomment">/// 任务失败
<a href=#192 id=192 data-nosnippet>192</a>    </span><span class="kw">pub async fn </span>fail_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#193 id=193 data-nosnippet>193</a>        <span class="self">self</span>.repository_manager.task.fail_task(task_id).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#194 id=194 data-nosnippet>194</a>        <span class="self">self</span>.failed_count.fetch_add(<span class="number">1</span>, Ordering::Relaxed);
<a href=#195 id=195 data-nosnippet>195</a>        <span class="macro">tracing::warn!</span>(<span class="string">"Failed task: {}"</span>, task_id);
<a href=#196 id=196 data-nosnippet>196</a>        <span class="prelude-val">Ok</span>(())
<a href=#197 id=197 data-nosnippet>197</a>    }
<a href=#198 id=198 data-nosnippet>198</a>
<a href=#199 id=199 data-nosnippet>199</a>    <span class="doccomment">/// 重置任务状态
<a href=#200 id=200 data-nosnippet>200</a>    </span><span class="kw">pub async fn </span>reset_task(<span class="kw-2">&amp;</span><span class="self">self</span>, task_id: <span class="kw-2">&amp;</span>str) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#201 id=201 data-nosnippet>201</a>        <span class="self">self</span>.repository_manager.task.update_status(task_id, <span class="number">0</span>).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#202 id=202 data-nosnippet>202</a>        <span class="macro">tracing::info!</span>(<span class="string">"Reset task: {}"</span>, task_id);
<a href=#203 id=203 data-nosnippet>203</a>        <span class="prelude-val">Ok</span>(())
<a href=#204 id=204 data-nosnippet>204</a>    }
<a href=#205 id=205 data-nosnippet>205</a>
<a href=#206 id=206 data-nosnippet>206</a>    <span class="doccomment">/// 获取任务统计信息
<a href=#207 id=207 data-nosnippet>207</a>    </span><span class="kw">pub async fn </span>get_task_statistics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;TaskMetrics&gt; {
<a href=#208 id=208 data-nosnippet>208</a>        <span class="kw">let </span>stats = <span class="self">self</span>.repository_manager.task.get_statistics().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#209 id=209 data-nosnippet>209</a>        <span class="kw">let </span>processed = <span class="self">self</span>.processed_count.load(Ordering::Relaxed);
<a href=#210 id=210 data-nosnippet>210</a>        <span class="kw">let </span>failed = <span class="self">self</span>.failed_count.load(Ordering::Relaxed);
<a href=#211 id=211 data-nosnippet>211</a>        
<a href=#212 id=212 data-nosnippet>212</a>        <span class="kw">let </span>success_rate = <span class="kw">if </span>processed + failed &gt; <span class="number">0 </span>{
<a href=#213 id=213 data-nosnippet>213</a>            (processed <span class="kw">as </span>f64) / ((processed + failed) <span class="kw">as </span>f64) * <span class="number">100.0
<a href=#214 id=214 data-nosnippet>214</a>        </span>} <span class="kw">else </span>{
<a href=#215 id=215 data-nosnippet>215</a>            <span class="number">0.0
<a href=#216 id=216 data-nosnippet>216</a>        </span>};
<a href=#217 id=217 data-nosnippet>217</a>
<a href=#218 id=218 data-nosnippet>218</a>        <span class="kw">let </span>start_time = <span class="self">self</span>.start_time.read().<span class="kw">await</span>;
<a href=#219 id=219 data-nosnippet>219</a>        <span class="kw">let </span>tasks_per_hour = <span class="kw">if let </span><span class="prelude-val">Some</span>(start) = <span class="kw-2">*</span>start_time {
<a href=#220 id=220 data-nosnippet>220</a>            <span class="kw">let </span>duration = Utc::now().signed_duration_since(start);
<a href=#221 id=221 data-nosnippet>221</a>            <span class="kw">let </span>hours = duration.num_seconds() <span class="kw">as </span>f64 / <span class="number">3600.0</span>;
<a href=#222 id=222 data-nosnippet>222</a>            <span class="kw">if </span>hours &gt; <span class="number">0.0 </span>{
<a href=#223 id=223 data-nosnippet>223</a>                processed <span class="kw">as </span>f64 / hours
<a href=#224 id=224 data-nosnippet>224</a>            } <span class="kw">else </span>{
<a href=#225 id=225 data-nosnippet>225</a>                <span class="number">0.0
<a href=#226 id=226 data-nosnippet>226</a>            </span>}
<a href=#227 id=227 data-nosnippet>227</a>        } <span class="kw">else </span>{
<a href=#228 id=228 data-nosnippet>228</a>            <span class="number">0.0
<a href=#229 id=229 data-nosnippet>229</a>        </span>};
<a href=#230 id=230 data-nosnippet>230</a>
<a href=#231 id=231 data-nosnippet>231</a>        <span class="prelude-val">Ok</span>(TaskMetrics {
<a href=#232 id=232 data-nosnippet>232</a>            total_tasks: stats.total,
<a href=#233 id=233 data-nosnippet>233</a>            pending_tasks: stats.pending,
<a href=#234 id=234 data-nosnippet>234</a>            running_tasks: stats.running,
<a href=#235 id=235 data-nosnippet>235</a>            completed_tasks: stats.completed,
<a href=#236 id=236 data-nosnippet>236</a>            failed_tasks: stats.failed,
<a href=#237 id=237 data-nosnippet>237</a>            success_rate,
<a href=#238 id=238 data-nosnippet>238</a>            average_processing_time: <span class="number">0.0</span>, <span class="comment">// 需要额外计算
<a href=#239 id=239 data-nosnippet>239</a>            </span>tasks_per_hour,
<a href=#240 id=240 data-nosnippet>240</a>            last_updated: Utc::now(),
<a href=#241 id=241 data-nosnippet>241</a>        })
<a href=#242 id=242 data-nosnippet>242</a>    }
<a href=#243 id=243 data-nosnippet>243</a>
<a href=#244 id=244 data-nosnippet>244</a>    <span class="doccomment">/// 清理旧任务
<a href=#245 id=245 data-nosnippet>245</a>    </span><span class="kw">pub async fn </span>cleanup_old_tasks(<span class="kw-2">&amp;</span><span class="self">self</span>, days: i32) -&gt; <span class="prelude-ty">Result</span>&lt;u64&gt; {
<a href=#246 id=246 data-nosnippet>246</a>        <span class="kw">let </span>deleted_count = <span class="self">self</span>.repository_manager.task.cleanup_old_tasks(days).<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#247 id=247 data-nosnippet>247</a>        <span class="macro">tracing::info!</span>(<span class="string">"Cleaned up {} old tasks"</span>, deleted_count);
<a href=#248 id=248 data-nosnippet>248</a>        <span class="prelude-val">Ok</span>(deleted_count)
<a href=#249 id=249 data-nosnippet>249</a>    }
<a href=#250 id=250 data-nosnippet>250</a>}
<a href=#251 id=251 data-nosnippet>251</a>
<a href=#252 id=252 data-nosnippet>252</a><span class="kw">impl </span>Service <span class="kw">for </span>TaskService {
<a href=#253 id=253 data-nosnippet>253</a>    <span class="kw">fn </span>name(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span><span class="lifetime">'static </span>str {
<a href=#254 id=254 data-nosnippet>254</a>        <span class="string">"TaskService"
<a href=#255 id=255 data-nosnippet>255</a>    </span>}
<a href=#256 id=256 data-nosnippet>256</a>}
<a href=#257 id=257 data-nosnippet>257</a>
<a href=#258 id=258 data-nosnippet>258</a><span class="attr">#[async_trait]
<a href=#259 id=259 data-nosnippet>259</a></span><span class="kw">impl </span>StartableService <span class="kw">for </span>TaskService {
<a href=#260 id=260 data-nosnippet>260</a>    <span class="kw">async fn </span>start(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#261 id=261 data-nosnippet>261</a>        <span class="kw">if </span><span class="self">self</span>.is_running.load(Ordering::Relaxed) {
<a href=#262 id=262 data-nosnippet>262</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#263 id=263 data-nosnippet>263</a>        }
<a href=#264 id=264 data-nosnippet>264</a>
<a href=#265 id=265 data-nosnippet>265</a>        <span class="self">self</span>.is_running.store(<span class="bool-val">true</span>, Ordering::Relaxed);
<a href=#266 id=266 data-nosnippet>266</a>        <span class="kw-2">*</span><span class="self">self</span>.start_time.write().<span class="kw">await </span>= <span class="prelude-val">Some</span>(Utc::now());
<a href=#267 id=267 data-nosnippet>267</a>        
<a href=#268 id=268 data-nosnippet>268</a>        <span class="macro">tracing::info!</span>(<span class="string">"Task service started"</span>);
<a href=#269 id=269 data-nosnippet>269</a>        <span class="prelude-val">Ok</span>(())
<a href=#270 id=270 data-nosnippet>270</a>    }
<a href=#271 id=271 data-nosnippet>271</a>
<a href=#272 id=272 data-nosnippet>272</a>    <span class="kw">async fn </span>stop(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#273 id=273 data-nosnippet>273</a>        <span class="kw">if </span>!<span class="self">self</span>.is_running.load(Ordering::Relaxed) {
<a href=#274 id=274 data-nosnippet>274</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(());
<a href=#275 id=275 data-nosnippet>275</a>        }
<a href=#276 id=276 data-nosnippet>276</a>
<a href=#277 id=277 data-nosnippet>277</a>        <span class="self">self</span>.is_running.store(<span class="bool-val">false</span>, Ordering::Relaxed);
<a href=#278 id=278 data-nosnippet>278</a>        <span class="kw-2">*</span><span class="self">self</span>.start_time.write().<span class="kw">await </span>= <span class="prelude-val">None</span>;
<a href=#279 id=279 data-nosnippet>279</a>        
<a href=#280 id=280 data-nosnippet>280</a>        <span class="macro">tracing::info!</span>(<span class="string">"Task service stopped"</span>);
<a href=#281 id=281 data-nosnippet>281</a>        <span class="prelude-val">Ok</span>(())
<a href=#282 id=282 data-nosnippet>282</a>    }
<a href=#283 id=283 data-nosnippet>283</a>
<a href=#284 id=284 data-nosnippet>284</a>    <span class="kw">fn </span>is_running(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#285 id=285 data-nosnippet>285</a>        <span class="self">self</span>.is_running.load(Ordering::Relaxed)
<a href=#286 id=286 data-nosnippet>286</a>    }
<a href=#287 id=287 data-nosnippet>287</a>}
<a href=#288 id=288 data-nosnippet>288</a>
<a href=#289 id=289 data-nosnippet>289</a><span class="attr">#[async_trait]
<a href=#290 id=290 data-nosnippet>290</a></span><span class="kw">impl </span>MonitorableService <span class="kw">for </span>TaskService {
<a href=#291 id=291 data-nosnippet>291</a>    <span class="kw">type </span>Metrics = TaskMetrics;
<a href=#292 id=292 data-nosnippet>292</a>
<a href=#293 id=293 data-nosnippet>293</a>    <span class="kw">async fn </span>get_metrics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>::Metrics&gt; {
<a href=#294 id=294 data-nosnippet>294</a>        <span class="self">self</span>.get_task_statistics().<span class="kw">await
<a href=#295 id=295 data-nosnippet>295</a>    </span>}
<a href=#296 id=296 data-nosnippet>296</a>
<a href=#297 id=297 data-nosnippet>297</a>    <span class="kw">async fn </span>get_health_status(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;HealthStatus&gt; {
<a href=#298 id=298 data-nosnippet>298</a>        <span class="kw">if </span>!<span class="self">self</span>.is_running() {
<a href=#299 id=299 data-nosnippet>299</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(HealthStatus::Unhealthy(<span class="string">"Service is not running"</span>.to_string()));
<a href=#300 id=300 data-nosnippet>300</a>        }
<a href=#301 id=301 data-nosnippet>301</a>
<a href=#302 id=302 data-nosnippet>302</a>        <span class="kw">let </span>metrics = <span class="self">self</span>.get_metrics().<span class="kw">await</span><span class="question-mark">?</span>;
<a href=#303 id=303 data-nosnippet>303</a>        
<a href=#304 id=304 data-nosnippet>304</a>        <span class="comment">// 检查失败率
<a href=#305 id=305 data-nosnippet>305</a>        </span><span class="kw">if </span>metrics.success_rate &lt; <span class="number">50.0 </span>&amp;&amp; metrics.total_tasks &gt; <span class="number">10 </span>{
<a href=#306 id=306 data-nosnippet>306</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(HealthStatus::Degraded(
<a href=#307 id=307 data-nosnippet>307</a>                <span class="macro">format!</span>(<span class="string">"Low success rate: {:.1}%"</span>, metrics.success_rate)
<a href=#308 id=308 data-nosnippet>308</a>            ));
<a href=#309 id=309 data-nosnippet>309</a>        }
<a href=#310 id=310 data-nosnippet>310</a>
<a href=#311 id=311 data-nosnippet>311</a>        <span class="comment">// 检查是否有太多待处理任务
<a href=#312 id=312 data-nosnippet>312</a>        </span><span class="kw">if </span>metrics.pending_tasks &gt; <span class="number">1000 </span>{
<a href=#313 id=313 data-nosnippet>313</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(HealthStatus::Degraded(
<a href=#314 id=314 data-nosnippet>314</a>                <span class="macro">format!</span>(<span class="string">"High pending task count: {}"</span>, metrics.pending_tasks)
<a href=#315 id=315 data-nosnippet>315</a>            ));
<a href=#316 id=316 data-nosnippet>316</a>        }
<a href=#317 id=317 data-nosnippet>317</a>
<a href=#318 id=318 data-nosnippet>318</a>        <span class="prelude-val">Ok</span>(HealthStatus::Healthy)
<a href=#319 id=319 data-nosnippet>319</a>    }
<a href=#320 id=320 data-nosnippet>320</a>}
<a href=#321 id=321 data-nosnippet>321</a>
<a href=#322 id=322 data-nosnippet>322</a><span class="attr">#[cfg(test)]
<a href=#323 id=323 data-nosnippet>323</a></span><span class="kw">mod </span>tests {
<a href=#324 id=324 data-nosnippet>324</a>    <span class="kw">use super</span>::<span class="kw-2">*</span>;
<a href=#325 id=325 data-nosnippet>325</a>    <span class="kw">use </span><span class="kw">crate</span>::repository::{RepositoryManager, TaskRepository};
<a href=#326 id=326 data-nosnippet>326</a>    <span class="kw">use </span>sqlx::SqlitePool;
<a href=#327 id=327 data-nosnippet>327</a>    <span class="kw">use </span>std::sync::Arc;
<a href=#328 id=328 data-nosnippet>328</a>
<a href=#329 id=329 data-nosnippet>329</a>    <span class="kw">async fn </span>create_test_service() -&gt; TaskService {
<a href=#330 id=330 data-nosnippet>330</a>        <span class="comment">// 创建内存数据库用于测试
<a href=#331 id=331 data-nosnippet>331</a>        </span><span class="kw">let </span>pool = SqlitePool::connect(<span class="string">":memory:"</span>).<span class="kw">await</span>.unwrap();
<a href=#332 id=332 data-nosnippet>332</a>        <span class="kw">let </span>pool = Arc::new(pool);
<a href=#333 id=333 data-nosnippet>333</a>        <span class="kw">let </span>repo_manager = Arc::new(RepositoryManager::new(pool));
<a href=#334 id=334 data-nosnippet>334</a>        TaskService::new(repo_manager)
<a href=#335 id=335 data-nosnippet>335</a>    }
<a href=#336 id=336 data-nosnippet>336</a>
<a href=#337 id=337 data-nosnippet>337</a>    <span class="attr">#[tokio::test]
<a href=#338 id=338 data-nosnippet>338</a>    </span><span class="kw">async fn </span>test_service_lifecycle() {
<a href=#339 id=339 data-nosnippet>339</a>        <span class="kw">let </span>service = create_test_service().<span class="kw">await</span>;
<a href=#340 id=340 data-nosnippet>340</a>
<a href=#341 id=341 data-nosnippet>341</a>        <span class="macro">assert!</span>(!service.is_running());
<a href=#342 id=342 data-nosnippet>342</a>
<a href=#343 id=343 data-nosnippet>343</a>        service.start().<span class="kw">await</span>.unwrap();
<a href=#344 id=344 data-nosnippet>344</a>        <span class="macro">assert!</span>(service.is_running());
<a href=#345 id=345 data-nosnippet>345</a>
<a href=#346 id=346 data-nosnippet>346</a>        service.stop().<span class="kw">await</span>.unwrap();
<a href=#347 id=347 data-nosnippet>347</a>        <span class="macro">assert!</span>(!service.is_running());
<a href=#348 id=348 data-nosnippet>348</a>    }
<a href=#349 id=349 data-nosnippet>349</a>
<a href=#350 id=350 data-nosnippet>350</a>    <span class="attr">#[tokio::test]
<a href=#351 id=351 data-nosnippet>351</a>    </span><span class="kw">async fn </span>test_create_task() {
<a href=#352 id=352 data-nosnippet>352</a>        <span class="kw">let </span>service = create_test_service().<span class="kw">await</span>;
<a href=#353 id=353 data-nosnippet>353</a>
<a href=#354 id=354 data-nosnippet>354</a>        <span class="kw">let </span>request = CreateTaskRequest {
<a href=#355 id=355 data-nosnippet>355</a>            task_type: <span class="number">1</span>,
<a href=#356 id=356 data-nosnippet>356</a>            target_url: <span class="string">"https://example.com"</span>.to_string(),
<a href=#357 id=357 data-nosnippet>357</a>            priority: <span class="number">1</span>,
<a href=#358 id=358 data-nosnippet>358</a>            max_retries: <span class="number">3</span>,
<a href=#359 id=359 data-nosnippet>359</a>            metadata: <span class="prelude-val">None</span>,
<a href=#360 id=360 data-nosnippet>360</a>        };
<a href=#361 id=361 data-nosnippet>361</a>
<a href=#362 id=362 data-nosnippet>362</a>        <span class="kw">let </span>result = service.create_task(request).<span class="kw">await</span>;
<a href=#363 id=363 data-nosnippet>363</a>        <span class="macro">assert!</span>(result.is_ok());
<a href=#364 id=364 data-nosnippet>364</a>
<a href=#365 id=365 data-nosnippet>365</a>        <span class="kw">let </span>task = result.unwrap();
<a href=#366 id=366 data-nosnippet>366</a>        <span class="macro">assert_eq!</span>(task.task_type, <span class="number">1</span>);
<a href=#367 id=367 data-nosnippet>367</a>        <span class="macro">assert_eq!</span>(task.target_url, <span class="string">"https://example.com"</span>);
<a href=#368 id=368 data-nosnippet>368</a>        <span class="macro">assert_eq!</span>(task.status, <span class="number">0</span>); <span class="comment">// 待处理
<a href=#369 id=369 data-nosnippet>369</a>    </span>}
<a href=#370 id=370 data-nosnippet>370</a>
<a href=#371 id=371 data-nosnippet>371</a>    <span class="attr">#[tokio::test]
<a href=#372 id=372 data-nosnippet>372</a>    </span><span class="kw">async fn </span>test_batch_create_tasks() {
<a href=#373 id=373 data-nosnippet>373</a>        <span class="kw">let </span>service = create_test_service().<span class="kw">await</span>;
<a href=#374 id=374 data-nosnippet>374</a>
<a href=#375 id=375 data-nosnippet>375</a>        <span class="kw">let </span>requests = <span class="macro">vec!</span>[
<a href=#376 id=376 data-nosnippet>376</a>            CreateTaskRequest {
<a href=#377 id=377 data-nosnippet>377</a>                task_type: <span class="number">1</span>,
<a href=#378 id=378 data-nosnippet>378</a>                target_url: <span class="string">"https://example1.com"</span>.to_string(),
<a href=#379 id=379 data-nosnippet>379</a>                priority: <span class="number">1</span>,
<a href=#380 id=380 data-nosnippet>380</a>                max_retries: <span class="number">3</span>,
<a href=#381 id=381 data-nosnippet>381</a>                metadata: <span class="prelude-val">None</span>,
<a href=#382 id=382 data-nosnippet>382</a>            },
<a href=#383 id=383 data-nosnippet>383</a>            CreateTaskRequest {
<a href=#384 id=384 data-nosnippet>384</a>                task_type: <span class="number">2</span>,
<a href=#385 id=385 data-nosnippet>385</a>                target_url: <span class="string">"https://example2.com"</span>.to_string(),
<a href=#386 id=386 data-nosnippet>386</a>                priority: <span class="number">2</span>,
<a href=#387 id=387 data-nosnippet>387</a>                max_retries: <span class="number">3</span>,
<a href=#388 id=388 data-nosnippet>388</a>                metadata: <span class="prelude-val">None</span>,
<a href=#389 id=389 data-nosnippet>389</a>            },
<a href=#390 id=390 data-nosnippet>390</a>        ];
<a href=#391 id=391 data-nosnippet>391</a>
<a href=#392 id=392 data-nosnippet>392</a>        <span class="kw">let </span>result = service.create_tasks_batch(requests).<span class="kw">await</span>;
<a href=#393 id=393 data-nosnippet>393</a>        <span class="macro">assert!</span>(result.is_ok());
<a href=#394 id=394 data-nosnippet>394</a>
<a href=#395 id=395 data-nosnippet>395</a>        <span class="kw">let </span>tasks = result.unwrap();
<a href=#396 id=396 data-nosnippet>396</a>        <span class="macro">assert_eq!</span>(tasks.len(), <span class="number">2</span>);
<a href=#397 id=397 data-nosnippet>397</a>    }
<a href=#398 id=398 data-nosnippet>398</a>
<a href=#399 id=399 data-nosnippet>399</a>    <span class="attr">#[tokio::test]
<a href=#400 id=400 data-nosnippet>400</a>    </span><span class="kw">async fn </span>test_health_status() {
<a href=#401 id=401 data-nosnippet>401</a>        <span class="kw">let </span>service = create_test_service().<span class="kw">await</span>;
<a href=#402 id=402 data-nosnippet>402</a>
<a href=#403 id=403 data-nosnippet>403</a>        <span class="comment">// 服务未启动时应该是不健康的
<a href=#404 id=404 data-nosnippet>404</a>        </span><span class="kw">let </span>health = service.get_health_status().<span class="kw">await</span>.unwrap();
<a href=#405 id=405 data-nosnippet>405</a>        <span class="macro">assert!</span>(health.is_unhealthy());
<a href=#406 id=406 data-nosnippet>406</a>
<a href=#407 id=407 data-nosnippet>407</a>        <span class="comment">// 启动服务后应该是健康的
<a href=#408 id=408 data-nosnippet>408</a>        </span>service.start().<span class="kw">await</span>.unwrap();
<a href=#409 id=409 data-nosnippet>409</a>        <span class="kw">let </span>health = service.get_health_status().<span class="kw">await</span>.unwrap();
<a href=#410 id=410 data-nosnippet>410</a>        <span class="macro">assert!</span>(health.is_healthy());
<a href=#411 id=411 data-nosnippet>411</a>    }
<a href=#412 id=412 data-nosnippet>412</a>
<a href=#413 id=413 data-nosnippet>413</a>    <span class="attr">#[tokio::test]
<a href=#414 id=414 data-nosnippet>414</a>    </span><span class="kw">async fn </span>test_task_statistics() {
<a href=#415 id=415 data-nosnippet>415</a>        <span class="kw">let </span>service = create_test_service().<span class="kw">await</span>;
<a href=#416 id=416 data-nosnippet>416</a>        service.start().<span class="kw">await</span>.unwrap();
<a href=#417 id=417 data-nosnippet>417</a>
<a href=#418 id=418 data-nosnippet>418</a>        <span class="kw">let </span>stats = service.get_task_statistics().<span class="kw">await</span>.unwrap();
<a href=#419 id=419 data-nosnippet>419</a>        <span class="macro">assert_eq!</span>(stats.total_tasks, <span class="number">0</span>);
<a href=#420 id=420 data-nosnippet>420</a>        <span class="macro">assert_eq!</span>(stats.pending_tasks, <span class="number">0</span>);
<a href=#421 id=421 data-nosnippet>421</a>        <span class="macro">assert_eq!</span>(stats.running_tasks, <span class="number">0</span>);
<a href=#422 id=422 data-nosnippet>422</a>        <span class="macro">assert_eq!</span>(stats.completed_tasks, <span class="number">0</span>);
<a href=#423 id=423 data-nosnippet>423</a>        <span class="macro">assert_eq!</span>(stats.failed_tasks, <span class="number">0</span>);
<a href=#424 id=424 data-nosnippet>424</a>    }
<a href=#425 id=425 data-nosnippet>425</a>}</code></pre></div></section></main></body></html>