-- 账号管理表
CREATE TABLE accounts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id TEXT UNIQUE NOT NULL,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL, -- 应该加密存储
    phone TEXT,
    email TEXT,
    status INTEGER NOT NULL DEFAULT 1, -- 1: Normal, 2: Banned, 3: Abnormal, 4: Maintenance
    login_count INTEGER NOT NULL DEFAULT 0,
    last_login DATETIME,
    last_activity DATETIME,
    risk_score REAL NOT NULL DEFAULT 0.0, -- 风险评分 0.0-1.0
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 账号Cookie存储表
CREATE TABLE account_cookies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    account_id TEXT NOT NULL,
    session_id TEXT NOT NULL,
    cookies TEXT NOT NULL,
    user_agent TEXT NOT NULL,
    login_time DATETIME NOT NULL,
    last_used DATETIME NOT NULL,
    is_valid BOOLEAN NOT NULL DEFAULT 1,
    expires_at DATETIME,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(account_id)
);

-- 请求缓存表
CREATE TABLE request_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_key TEXT UNIQUE NOT NULL,
    cache_value TEXT NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_accounts_account_id ON accounts(account_id);
CREATE INDEX idx_accounts_username ON accounts(username);
CREATE INDEX idx_accounts_status ON accounts(status);
CREATE INDEX idx_account_cookies_account_id ON account_cookies(account_id);
CREATE INDEX idx_account_cookies_session_id ON account_cookies(session_id);
CREATE INDEX idx_request_cache_key ON request_cache(cache_key);
CREATE INDEX idx_request_cache_expires_at ON request_cache(expires_at);

-- 创建更新时间触发器
CREATE TRIGGER update_accounts_updated_at
    AFTER UPDATE ON accounts
    FOR EACH ROW
BEGIN
    UPDATE accounts SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
