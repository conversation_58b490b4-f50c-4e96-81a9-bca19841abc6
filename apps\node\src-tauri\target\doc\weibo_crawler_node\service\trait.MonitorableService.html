<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="可监控的服务特征"><title>MonitorableService in weibo_crawler_node::service - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc trait"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Monitorable<wbr>Service</a></h2><h3><a href="#required-associated-types">Required Associated Types</a></h3><ul class="block"><li><a href="#associatedtype.Metrics" title="Metrics">Metrics</a></li></ul><h3><a href="#required-methods">Required Methods</a></h3><ul class="block"><li><a href="#tymethod.get_health_status" title="get_health_status">get_health_status</a></li><li><a href="#tymethod.get_metrics" title="get_metrics">get_metrics</a></li></ul><h3><a href="#implementors">Implementors</a></h3></section><div id="rustdoc-modnav"><h2><a href="index.html">In weibo_<wbr>crawler_<wbr>node::<wbr>service</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a>::<wbr><a href="index.html">service</a></div><h1>Trait <span class="trait">MonitorableService</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/service/mod.rs.html#65-70">Source</a> </span></div><pre class="rust item-decl"><code>pub trait MonitorableService: <a class="trait" href="trait.Service.html" title="trait weibo_crawler_node::service::Service">Service</a> {
    type <a href="#associatedtype.Metrics" class="associatedtype">Metrics</a>;

    // Required methods
    fn <a href="#tymethod.get_metrics" class="fn">get_metrics</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self::<a class="associatedtype" href="trait.MonitorableService.html#associatedtype.Metrics" title="type weibo_crawler_node::service::MonitorableService::Metrics">Metrics</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
<span class="item-spacer"></span>    fn <a href="#tymethod.get_health_status" class="fn">get_health_status</a>&lt;'life0, 'async_trait&gt;(
        &amp;'life0 self,
    ) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="enum" href="enum.HealthStatus.html" title="enum weibo_crawler_node::service::HealthStatus">HealthStatus</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;
       <span class="where">where Self: 'async_trait,
             'life0: 'async_trait</span>;
}</code></pre><details class="toggle top-doc" open><summary class="hideme"><span>Expand description</span></summary><div class="docblock"><p>可监控的服务特征</p>
</div></details><h2 id="required-associated-types" class="section-header">Required Associated Types<a href="#required-associated-types" class="anchor">§</a></h2><div class="methods"><section id="associatedtype.Metrics" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#66">Source</a><h4 class="code-header">type <a href="#associatedtype.Metrics" class="associatedtype">Metrics</a></h4></section></div><h2 id="required-methods" class="section-header">Required Methods<a href="#required-methods" class="anchor">§</a></h2><div class="methods"><section id="tymethod.get_metrics" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#68">Source</a><h4 class="code-header">fn <a href="#tymethod.get_metrics" class="fn">get_metrics</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;Self::<a class="associatedtype" href="trait.MonitorableService.html#associatedtype.Metrics" title="type weibo_crawler_node::service::MonitorableService::Metrics">Metrics</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section><section id="tymethod.get_health_status" class="method"><a class="src rightside" href="../../src/weibo_crawler_node/service/mod.rs.html#69">Source</a><h4 class="code-header">fn <a href="#tymethod.get_health_status" class="fn">get_health_status</a>&lt;'life0, 'async_trait&gt;(
    &amp;'life0 self,
) -&gt; <a class="struct" href="https://doc.rust-lang.org/1.88.0/core/pin/struct.Pin.html" title="struct core::pin::Pin">Pin</a>&lt;<a class="struct" href="https://doc.rust-lang.org/1.88.0/alloc/boxed/struct.Box.html" title="struct alloc::boxed::Box">Box</a>&lt;dyn <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/future/future/trait.Future.html" title="trait core::future::future::Future">Future</a>&lt;Output = <a class="enum" href="https://doc.rust-lang.org/1.88.0/core/result/enum.Result.html" title="enum core::result::Result">Result</a>&lt;<a class="enum" href="enum.HealthStatus.html" title="enum weibo_crawler_node::service::HealthStatus">HealthStatus</a>, <a class="enum" href="../error/enum.AppError.html" title="enum weibo_crawler_node::error::AppError">AppError</a>&gt;&gt; + <a class="trait" href="https://doc.rust-lang.org/1.88.0/core/marker/trait.Send.html" title="trait core::marker::Send">Send</a> + 'async_trait&gt;&gt;<div class="where">where
    Self: 'async_trait,
    'life0: 'async_trait,</div></h4></section></div><h2 id="implementors" class="section-header">Implementors<a href="#implementors" class="anchor">§</a></h2><div id="implementors-list"><details class="toggle implementors-toggle"><summary><section id="impl-MonitorableService-for-AccountService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/account_service.rs.html#68-81">Source</a><a href="#impl-MonitorableService-for-AccountService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.MonitorableService.html" title="trait weibo_crawler_node::service::MonitorableService">MonitorableService</a> for <a class="struct" href="account_service/struct.AccountService.html" title="struct weibo_crawler_node::service::account_service::AccountService">AccountService</a></h3></section></summary><div class="impl-items"><section id="associatedtype.Metrics-1" class="associatedtype trait-impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/account_service.rs.html#69">Source</a><a href="#associatedtype.Metrics-1" class="anchor">§</a><h4 class="code-header">type <a href="#associatedtype.Metrics" class="associatedtype">Metrics</a> = <a class="struct" href="account_service/struct.AccountMetrics.html" title="struct weibo_crawler_node::service::account_service::AccountMetrics">AccountMetrics</a></h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-MonitorableService-for-CrawlerService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/crawler_service.rs.html#70-83">Source</a><a href="#impl-MonitorableService-for-CrawlerService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.MonitorableService.html" title="trait weibo_crawler_node::service::MonitorableService">MonitorableService</a> for <a class="struct" href="crawler_service/struct.CrawlerService.html" title="struct weibo_crawler_node::service::crawler_service::CrawlerService">CrawlerService</a></h3></section></summary><div class="impl-items"><section id="associatedtype.Metrics-2" class="associatedtype trait-impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/crawler_service.rs.html#71">Source</a><a href="#associatedtype.Metrics-2" class="anchor">§</a><h4 class="code-header">type <a href="#associatedtype.Metrics" class="associatedtype">Metrics</a> = <a class="struct" href="crawler_service/struct.CrawlerMetrics.html" title="struct weibo_crawler_node::service::crawler_service::CrawlerMetrics">CrawlerMetrics</a></h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-MonitorableService-for-MonitorService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/monitor_service.rs.html#70-83">Source</a><a href="#impl-MonitorableService-for-MonitorService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.MonitorableService.html" title="trait weibo_crawler_node::service::MonitorableService">MonitorableService</a> for <a class="struct" href="monitor_service/struct.MonitorService.html" title="struct weibo_crawler_node::service::monitor_service::MonitorService">MonitorService</a></h3></section></summary><div class="impl-items"><section id="associatedtype.Metrics-3" class="associatedtype trait-impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/monitor_service.rs.html#71">Source</a><a href="#associatedtype.Metrics-3" class="anchor">§</a><h4 class="code-header">type <a href="#associatedtype.Metrics" class="associatedtype">Metrics</a> = <a class="struct" href="monitor_service/struct.SystemMetrics.html" title="struct weibo_crawler_node::service::monitor_service::SystemMetrics">SystemMetrics</a></h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-MonitorableService-for-ProxyService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/proxy_service.rs.html#68-81">Source</a><a href="#impl-MonitorableService-for-ProxyService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.MonitorableService.html" title="trait weibo_crawler_node::service::MonitorableService">MonitorableService</a> for <a class="struct" href="proxy_service/struct.ProxyService.html" title="struct weibo_crawler_node::service::proxy_service::ProxyService">ProxyService</a></h3></section></summary><div class="impl-items"><section id="associatedtype.Metrics-4" class="associatedtype trait-impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/proxy_service.rs.html#69">Source</a><a href="#associatedtype.Metrics-4" class="anchor">§</a><h4 class="code-header">type <a href="#associatedtype.Metrics" class="associatedtype">Metrics</a> = <a class="struct" href="proxy_service/struct.ProxyMetrics.html" title="struct weibo_crawler_node::service::proxy_service::ProxyMetrics">ProxyMetrics</a></h4></section></div></details><details class="toggle implementors-toggle"><summary><section id="impl-MonitorableService-for-TaskService" class="impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/task_service.rs.html#290-320">Source</a><a href="#impl-MonitorableService-for-TaskService" class="anchor">§</a><h3 class="code-header">impl <a class="trait" href="trait.MonitorableService.html" title="trait weibo_crawler_node::service::MonitorableService">MonitorableService</a> for <a class="struct" href="task_service/struct.TaskService.html" title="struct weibo_crawler_node::service::task_service::TaskService">TaskService</a></h3></section></summary><div class="impl-items"><section id="associatedtype.Metrics-5" class="associatedtype trait-impl"><a class="src rightside" href="../../src/weibo_crawler_node/service/task_service.rs.html#291">Source</a><a href="#associatedtype.Metrics-5" class="anchor">§</a><h4 class="code-header">type <a href="#associatedtype.Metrics" class="associatedtype">Metrics</a> = <a class="struct" href="task_service/struct.TaskMetrics.html" title="struct weibo_crawler_node::service::task_service::TaskMetrics">TaskMetrics</a></h4></section></div></details></div><script src="../../trait.impl/weibo_crawler_node/service/trait.MonitorableService.js" async></script></section></div></main></body></html>