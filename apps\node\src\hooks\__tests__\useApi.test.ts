import { renderHook, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { useApi, useTauriCommand, useAsyncOperation } from '../useApi'
import { setupTauriMocks } from '../../test/utils'

// Mock fetch
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('useApi', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should fetch data successfully', async () => {
    const mockData = { id: 1, name: 'Test' }
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockData),
    })

    const { result } = renderHook(() => useApi('/api/test'))

    expect(result.current.loading).toBe(true)
    expect(result.current.data).toBe(null)
    expect(result.current.error).toBe(null)

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toEqual(mockData)
    expect(result.current.error).toBe(null)
    expect(mockFetch).toHaveBeenCalledWith('/api/test')
  })

  it('should handle fetch error', async () => {
    mockFetch.mockRejectedValueOnce(new Error('Network error'))

    const { result } = renderHook(() => useApi('/api/test'))

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toBe(null)
    expect(result.current.error).toBeInstanceOf(Error)
    expect(result.current.error?.message).toBe('Network error')
  })

  it('should handle HTTP error status', async () => {
    mockFetch.mockResolvedValueOnce({
      ok: false,
      status: 404,
    })

    const { result } = renderHook(() => useApi('/api/test'))

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toBe(null)
    expect(result.current.error).toBeInstanceOf(Error)
    expect(result.current.error?.message).toBe('HTTP error! status: 404')
  })

  it('should not fetch when disabled', async () => {
    const { result } = renderHook(() => useApi('/api/test', { enabled: false }))

    expect(result.current.loading).toBe(false)
    expect(result.current.data).toBe(null)
    expect(result.current.error).toBe(null)
    expect(mockFetch).not.toHaveBeenCalled()
  })

  it('should call onSuccess callback', async () => {
    const mockData = { id: 1, name: 'Test' }
    const onSuccess = vi.fn()
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockData),
    })

    renderHook(() => useApi('/api/test', { onSuccess }))

    await waitFor(() => {
      expect(onSuccess).toHaveBeenCalledWith(mockData)
    })
  })

  it('should call onError callback', async () => {
    const error = new Error('Network error')
    const onError = vi.fn()
    
    mockFetch.mockRejectedValueOnce(error)

    renderHook(() => useApi('/api/test', { onError }))

    await waitFor(() => {
      expect(onError).toHaveBeenCalledWith(error)
    })
  })

  it('should refetch data', async () => {
    const mockData = { id: 1, name: 'Test' }
    mockFetch.mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockData),
    })

    const { result } = renderHook(() => useApi('/api/test'))

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(mockFetch).toHaveBeenCalledTimes(1)

    await result.current.refetch()

    expect(mockFetch).toHaveBeenCalledTimes(2)
  })

  it('should mutate data', async () => {
    const initialData = { id: 1, name: 'Test' }
    const newData = { id: 1, name: 'Updated' }
    
    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(initialData),
    })

    const { result } = renderHook(() => useApi('/api/test'))

    await waitFor(() => {
      expect(result.current.data).toEqual(initialData)
    })

    result.current.mutate(newData)

    expect(result.current.data).toEqual(newData)
  })
})

describe('useTauriCommand', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    setupTauriMocks()
  })

  it('should execute Tauri command successfully', async () => {
    const mockData = { id: 1, name: 'Test' }
    vi.mocked(window.__TAURI__.invoke).mockResolvedValueOnce(mockData)

    const { result } = renderHook(() => useTauriCommand('test_command', { param: 'value' }))

    expect(result.current.loading).toBe(true)

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toEqual(mockData)
    expect(result.current.error).toBe(null)
    expect(window.__TAURI__.invoke).toHaveBeenCalledWith('test_command', { param: 'value' })
  })

  it('should handle Tauri command error', async () => {
    const error = new Error('Command failed')
    vi.mocked(window.__TAURI__.invoke).mockRejectedValueOnce(error)

    const { result } = renderHook(() => useTauriCommand('test_command'))

    await waitFor(() => {
      expect(result.current.loading).toBe(false)
    })

    expect(result.current.data).toBe(null)
    expect(result.current.error).toBeInstanceOf(Error)
    expect(result.current.error?.message).toBe('Command failed')
  })

  it('should not execute when disabled', async () => {
    const { result } = renderHook(() => useTauriCommand('test_command', {}, { enabled: false }))

    expect(result.current.loading).toBe(false)
    expect(result.current.data).toBe(null)
    expect(result.current.error).toBe(null)
    expect(window.__TAURI__.invoke).not.toHaveBeenCalled()
  })
})

describe('useAsyncOperation', () => {
  it('should execute async operation successfully', async () => {
    const mockOperation = vi.fn().mockResolvedValue('success')
    const { result } = renderHook(() => useAsyncOperation(mockOperation))

    expect(result.current.loading).toBe(false)
    expect(result.current.data).toBe(null)
    expect(result.current.error).toBe(null)

    const promise = result.current.execute('param1', 'param2')

    expect(result.current.loading).toBe(true)

    const resultData = await promise

    expect(resultData).toBe('success')
    expect(result.current.loading).toBe(false)
    expect(result.current.data).toBe('success')
    expect(result.current.error).toBe(null)
    expect(mockOperation).toHaveBeenCalledWith('param1', 'param2')
  })

  it('should handle async operation error', async () => {
    const error = new Error('Operation failed')
    const mockOperation = vi.fn().mockRejectedValue(error)
    const { result } = renderHook(() => useAsyncOperation(mockOperation))

    try {
      await result.current.execute()
    } catch (e) {
      expect(e).toBe(error)
    }

    expect(result.current.loading).toBe(false)
    expect(result.current.data).toBe(null)
    expect(result.current.error).toBe(error)
  })

  it('should reset state', async () => {
    const mockOperation = vi.fn().mockResolvedValue('success')
    const { result } = renderHook(() => useAsyncOperation(mockOperation))

    await result.current.execute()

    expect(result.current.data).toBe('success')

    result.current.reset()

    expect(result.current.data).toBe(null)
    expect(result.current.error).toBe(null)
    expect(result.current.loading).toBe(false)
  })
})
