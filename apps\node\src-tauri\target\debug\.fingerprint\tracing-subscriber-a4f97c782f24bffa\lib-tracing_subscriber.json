{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 10184879753413202140, "deps": [[1009387600818341822, "matchers", false, 17256701865294278241], [1017461770342116999, "sharded_slab", false, 6179106902627810432], [1359731229228270592, "thread_local", false, 1093819822260383629], [3424551429995674438, "tracing_core", false, 6586885485974029526], [3666196340704888985, "smallvec", false, 7465509138763068331], [3722963349756955755, "once_cell", false, 6428016074931338643], [8606274917505247608, "tracing", false, 5492527985859474487], [8614575489689151157, "nu_ansi_term", false, 8308532313803386577], [9451456094439810778, "regex", false, 1597721282727493944], [10806489435541507125, "tracing_log", false, 3779664131442794048]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-a4f97c782f24bffa\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}