import React from 'react';
import { cn } from '../../lib/utils';

// Card变体类型定义
export type CardVariant = 'default' | 'elevated' | 'interactive' | 'glass' | 'gradient';
export type CardSize = 'default' | 'compact' | 'spacious';

// Card变体样式函数
const getCardVariantStyles = (variant: CardVariant = 'default') => {
  const variants = {
    default: "shadow-sm hover:shadow-md",
    elevated: "shadow-lg hover:shadow-xl",
    interactive: "cursor-pointer hover:shadow-lg hover:-translate-y-1",
    glass: "bg-card/80 backdrop-blur-md border-border/50",
    gradient: "bg-gradient-to-br from-card to-card/80",
  };
  return variants[variant];
};

const getCardSizeStyles = (size: CardSize = 'default') => {
  const sizes = {
    default: "",
    compact: "p-4",
    spacious: "p-8",
  };
  return sizes[size];
};

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: CardVariant;
  size?: CardSize;
  asChild?: boolean;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant = 'default', size = 'default', ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "rounded-xl border bg-card text-card-foreground transition-all duration-200",
        getCardVariantStyles(variant),
        getCardSizeStyles(size),
        className
      )}
      {...props}
    />
  )
);
Card.displayName = "Card";

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-2 p-4 sm:p-6", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-lg sm:text-xl font-semibold leading-tight tracking-tight",
      className
    )}
    {...props}
  />
));
CardTitle.displayName = "CardTitle";

const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground leading-relaxed", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-4 sm:p-6 pt-0", className)} {...props} />
));
CardContent.displayName = "CardContent";

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-4 sm:p-6 pt-0", className)}
    {...props}
  />
));
CardFooter.displayName = "CardFooter";

// 特殊的统计卡片组件
export interface StatCardProps extends Omit<CardProps, 'variant'> {
  title: string;
  value: string | number;
  change?: string;
  icon?: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
}

const StatCard = React.forwardRef<HTMLDivElement, StatCardProps>(
  ({ title, value, change, icon, trend, className, ...props }, ref) => (
    <Card
      ref={ref}
      variant="interactive"
      className={cn("animate-scale-in", className)}
      {...props}
    >
      <CardContent className="p-4 sm:p-6">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 min-w-0">
            <p className="text-sm sm:text-base font-medium text-muted-foreground truncate mb-2">
              {title}
            </p>
            <p className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground leading-tight">
              {value}
            </p>
            {change && (
              <p className={cn(
                "text-xs sm:text-sm mt-2 truncate leading-relaxed",
                trend === 'up' && "text-success",
                trend === 'down' && "text-destructive",
                trend === 'neutral' && "text-muted-foreground"
              )}>
                {change}
              </p>
            )}
          </div>
          {icon && (
            <div className="flex-shrink-0">
              <div className="w-10 h-10 sm:w-12 sm:h-12 lg:w-16 lg:h-16 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl flex items-center justify-center transition-all duration-200 hover:scale-105">
                <div className="text-primary">
                  {icon}
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
);
StatCard.displayName = "StatCard";

// 指标卡片组件 - 用于显示系统指标
export interface MetricCardProps extends Omit<CardProps, 'variant'> {
  title: string;
  value: string | number;
  unit?: string;
  description?: string;
  color?: string;
  progress?: number; // 0-100
}

const MetricCard = React.forwardRef<HTMLDivElement, MetricCardProps>(
  ({ title, value, unit, description, color = 'hsl(var(--primary))', progress, className, ...props }, ref) => (
    <Card
      ref={ref}
      variant="default"
      className={cn("animate-fade-in", className)}
      {...props}
    >
      <CardContent className="p-4 sm:p-6">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-sm sm:text-base font-medium text-muted-foreground">
              {title}
            </h3>
            {unit && (
              <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                {unit}
              </span>
            )}
          </div>

          <div className="flex items-baseline space-x-2">
            <span
              className="text-2xl sm:text-3xl lg:text-4xl font-bold"
              style={{ color }}
            >
              {value}
            </span>
          </div>

          {progress !== undefined && (
            <div className="space-y-2">
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className="h-2 rounded-full transition-all duration-500 ease-out"
                  style={{
                    width: `${Math.min(100, Math.max(0, progress))}%`,
                    backgroundColor: color
                  }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>0</span>
                <span>{progress?.toFixed(1)}%</span>
                <span>100</span>
              </div>
            </div>
          )}

          {description && (
            <p className="text-xs sm:text-sm text-muted-foreground leading-relaxed">
              {description}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  )
);
MetricCard.displayName = "MetricCard";

// 信息卡片组件 - 用于显示详细信息
export interface InfoCardProps extends Omit<CardProps, 'variant'> {
  title: string;
  subtitle?: string;
  content: React.ReactNode;
  actions?: React.ReactNode;
  status?: 'default' | 'success' | 'warning' | 'error';
}

const InfoCard = React.forwardRef<HTMLDivElement, InfoCardProps>(
  ({ title, subtitle, content, actions, status = 'default', className, ...props }, ref) => {
    const statusColors = {
      default: 'border-border',
      success: 'border-success/20 bg-success/5',
      warning: 'border-warning/20 bg-warning/5',
      error: 'border-destructive/20 bg-destructive/5',
    };

    return (
      <Card
        ref={ref}
        className={cn("animate-fade-in", statusColors[status], className)}
        {...props}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <CardTitle className="text-base sm:text-lg">{title}</CardTitle>
              {subtitle && (
                <CardDescription>{subtitle}</CardDescription>
              )}
            </div>
            {actions && (
              <div className="flex-shrink-0 ml-4">
                {actions}
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {content}
        </CardContent>
      </Card>
    );
  }
);
InfoCard.displayName = "InfoCard";

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
  StatCard,
  MetricCard,
  InfoCard,
  getCardVariantStyles,
  getCardSizeStyles,
};
