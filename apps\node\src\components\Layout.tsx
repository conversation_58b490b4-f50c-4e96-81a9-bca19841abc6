import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import {
  Activity,
  Settings,
  Users,
  Globe,
  ListTodo,
  BarChart3,
  Menu,
  X,
  Monitor
} from "lucide-react";
import { cn } from "../lib/utils";

interface LayoutProps {
  children: React.ReactNode;
}

const navigation = [
  { name: "仪表板", href: "/", icon: BarChart3 },
  { name: "任务管理", href: "/tasks", icon: ListTodo },
  { name: "代理池", href: "/proxies", icon: Globe },
  { name: "账号池", href: "/accounts", icon: Users },
  { name: "系统监控", href: "/monitor", icon: Activity },
  { name: "浏览器测试", href: "/browser-test", icon: Monitor },
  { name: "设置", href: "/settings", icon: Settings },
];

export function Layout({ children }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      {/* 移动端侧边栏遮罩 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 backdrop-blur-sm lg:hidden animate-fade-in"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 xl:w-72 bg-card/95 backdrop-blur-md border-r border-border/50 transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
        sidebarOpen ? "translate-x-0 shadow-xl" : "-translate-x-full"
      )}>
        {/* 侧边栏头部 */}
        <div className="flex items-center justify-between h-16 xl:h-20 px-6 border-b border-border/50">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 xl:w-10 xl:h-10 bg-gradient-to-br from-primary to-primary-hover rounded-lg flex items-center justify-center">
              <Activity className="h-4 w-4 xl:h-5 xl:w-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-lg xl:text-xl font-bold text-gradient">微博爬虫节点</h1>
              <p className="text-xs text-muted-foreground hidden xl:block">分布式数据采集系统</p>
            </div>
          </div>
          <button
            className="lg:hidden p-2 hover:bg-accent rounded-lg transition-colors"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        {/* 导航菜单 */}
        <nav className="flex-1 px-4 xl:px-6 py-6 space-y-1 scrollbar-thin overflow-y-auto">
          {navigation.map((item, index) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.href;

            return (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  "group flex items-center px-3 xl:px-4 py-3 text-sm xl:text-base font-medium rounded-xl transition-all duration-200 animate-slide-in-left",
                  isActive
                    ? "bg-primary text-primary-foreground shadow-md transform scale-[1.02]"
                    : "text-muted-foreground hover:text-foreground hover:bg-accent/50 hover:transform hover:scale-[1.01]"
                )}
                style={{ animationDelay: `${index * 50}ms` }}
                onClick={() => setSidebarOpen(false)}
              >
                <Icon className={cn(
                  "mr-3 xl:mr-4 h-5 w-5 xl:h-6 xl:w-6 transition-transform duration-200",
                  isActive ? "scale-110" : "group-hover:scale-105"
                )} />
                <span className="font-medium">{item.name}</span>
                {isActive && (
                  <div className="ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse" />
                )}
              </Link>
            );
          })}
        </nav>

        {/* 侧边栏底部信息 */}
        <div className="p-4 xl:p-6 border-t border-border/50">
          <div className="flex items-center space-x-3 p-3 bg-muted/50 rounded-xl">
            <div className="w-2 h-2 bg-success rounded-full animate-pulse" />
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-foreground truncate">系统运行中</p>
              <p className="text-xs text-muted-foreground">节点状态正常</p>
            </div>
          </div>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部导航栏 */}
        <header className="bg-card/95 backdrop-blur-md border-b border-border/50 px-4 sm:px-6 lg:px-8 py-4 xl:py-6">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center space-x-4">
              <button
                className="lg:hidden p-2 hover:bg-accent rounded-lg transition-colors"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-5 w-5" />
              </button>

              {/* 面包屑导航 */}
              <div className="hidden sm:flex items-center space-x-2 text-sm text-muted-foreground">
                <span>微博爬虫节点</span>
                <span>/</span>
                <span className="text-foreground font-medium">
                  {navigation.find(item => item.href === location.pathname)?.name || '仪表板'}
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* 实时状态指示器 */}
              <div className="hidden md:flex items-center space-x-2 px-3 py-1.5 bg-success/10 text-success rounded-full">
                <div className="w-2 h-2 bg-success rounded-full animate-pulse" />
                <span className="text-sm font-medium">在线</span>
              </div>

              {/* 最后更新时间 */}
              <div className="text-sm text-muted-foreground">
                <span className="hidden sm:inline">最后更新: </span>
                {new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <main className="flex-1 overflow-auto">
          <div className="container-responsive py-6 xl:py-8 animate-fade-in">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}

// 基础UI组件库
import React from 'react';

// Button组件变体类型
type ButtonVariant = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
type ButtonSize = 'default' | 'sm' | 'lg' | 'icon';

// Button组件变体样式
const getButtonVariantStyles = (variant: ButtonVariant = 'default') => {
  const variants = {
    default: "bg-blue-600 text-white hover:bg-blue-700",
    destructive: "bg-red-600 text-white hover:bg-red-700",
    outline: "border border-gray-300 hover:bg-gray-50",
    secondary: "bg-gray-100 text-gray-900 hover:bg-gray-200",
    ghost: "hover:bg-gray-100",
    link: "underline-offset-4 hover:underline text-blue-600",
  };
  return variants[variant];
};

const getButtonSizeStyles = (size: ButtonSize = 'default') => {
  const sizes = {
    default: "h-10 py-2 px-4",
    sm: "h-9 px-3 text-sm",
    lg: "h-11 px-8",
    icon: "h-10 w-10",
  };
  return sizes[size];
};

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'default', size = 'default', ...props }, ref) => {
    return (
      <button
        className={cn(
          "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",
          getButtonVariantStyles(variant),
          getButtonSizeStyles(size),
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

// Input组件
export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className
        )}
        ref={ref}
        {...props}
      />
    );
  }
);
Input.displayName = "Input";

// Card组件
export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {}

export const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "rounded-lg border bg-card text-card-foreground shadow-sm",
        className
      )}
      {...props}
    />
  )
);
Card.displayName = "Card";

export const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));
CardHeader.displayName = "CardHeader";

export const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
));
CardTitle.displayName = "CardTitle";

export const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
CardDescription.displayName = "CardDescription";

export const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
));
CardContent.displayName = "CardContent";

// Badge组件
type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'outline';

const getBadgeVariantStyles = (variant: BadgeVariant = 'default') => {
  const variants = {
    default: "border-transparent bg-blue-600 text-white hover:bg-blue-700",
    secondary: "border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",
    destructive: "border-transparent bg-red-600 text-white hover:bg-red-700",
    outline: "border-gray-300 text-gray-900",
  };
  return variants[variant];
};

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: BadgeVariant;
}

export function Badge({ className, variant = 'default', ...props }: BadgeProps) {
  return (
    <div
      className={cn(
        "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
        getBadgeVariantStyles(variant),
        className
      )}
      {...props}
    />
  );
}

// 错误边界组件
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-red-600">出现错误</CardTitle>
              <CardDescription>
                应用程序遇到了一个错误，请刷新页面重试。
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {this.state.error && (
                  <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <p className="text-sm text-red-800 font-mono">
                      {this.state.error.message}
                    </p>
                  </div>
                )}
                <div className="flex space-x-2">
                  <Button
                    onClick={() => window.location.reload()}
                    variant="default"
                  >
                    刷新页面
                  </Button>
                  <Button
                    onClick={() => this.setState({ hasError: false })}
                    variant="outline"
                  >
                    重试
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// 加载骨架屏组件
export function LoadingSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                  <div className="h-3 bg-gray-200 rounded w-24"></div>
                </div>
                <div className="h-8 w-8 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(2)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-32"></div>
              <div className="h-4 bg-gray-200 rounded w-48"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(4)].map((_, j) => (
                  <div key={j} className="flex justify-between items-center">
                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                    <div className="h-4 bg-gray-200 rounded w-12"></div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

// 全局加载指示器
export function GlobalLoading() {
  return (
    <div className="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        <p className="text-gray-600">加载中...</p>
      </div>
    </div>
  );
}
