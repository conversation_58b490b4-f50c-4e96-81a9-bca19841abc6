<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="API documentation for the Rust `commands` mod in crate `weibo_crawler_node`."><title>weibo_crawler_node::commands - Rust</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../" data-static-root-path="../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../static.files/storage-4e99c027.js"></script><script defer src="../sidebar-items.js"></script><script defer src="../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../static.files/favicon-044be391.svg"></head><body class="rustdoc mod"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="mobile-topbar"><button class="sidebar-menu-toggle" title="show sidebar"></button></nav><nav class="sidebar"><div class="sidebar-crate"><h2><a href="../../weibo_crawler_node/index.html">weibo_<wbr>crawler_<wbr>node</a><span class="version">0.1.0</span></h2></div><div class="sidebar-elems"><section id="rustdoc-toc"><h2 class="location"><a href="#">Module commands</a></h2><h3><a href="#structs">Module Items</a></h3><ul class="block"><li><a href="#structs" title="Structs">Structs</a></li><li><a href="#functions" title="Functions">Functions</a></li></ul></section><div id="rustdoc-modnav"><h2 class="in-crate"><a href="../index.html">In crate weibo_<wbr>crawler_<wbr>node</a></h2></div></div></nav><div class="sidebar-resizer"></div><main><div class="width-limiter"><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><div class="rustdoc-breadcrumbs"><a href="../index.html">weibo_crawler_node</a></div><h1>Module <span>commands</span><button id="copy-path" title="Copy item path to clipboard">Copy item path</button></h1><rustdoc-toolbar></rustdoc-toolbar><span class="sub-heading"><a class="src" href="../../src/weibo_crawler_node/commands.rs.html#1-410">Source</a> </span></div><h2 id="structs" class="section-header">Structs<a href="#structs" class="anchor">§</a></h2><dl class="item-table"><dt><a class="struct" href="struct.AccountConfig.html" title="struct weibo_crawler_node::commands::AccountConfig">Account<wbr>Config</a></dt><dt><a class="struct" href="struct.AccountPoolStatus.html" title="struct weibo_crawler_node::commands::AccountPoolStatus">Account<wbr>Pool<wbr>Status</a></dt><dt><a class="struct" href="struct.LoginResult.html" title="struct weibo_crawler_node::commands::LoginResult">Login<wbr>Result</a></dt><dt><a class="struct" href="struct.NodeStatus.html" title="struct weibo_crawler_node::commands::NodeStatus">Node<wbr>Status</a></dt><dt><a class="struct" href="struct.PerformanceStats.html" title="struct weibo_crawler_node::commands::PerformanceStats">Performance<wbr>Stats</a></dt><dt><a class="struct" href="struct.ProxyConfig.html" title="struct weibo_crawler_node::commands::ProxyConfig">Proxy<wbr>Config</a></dt><dt><a class="struct" href="struct.ProxyPoolStatus.html" title="struct weibo_crawler_node::commands::ProxyPoolStatus">Proxy<wbr>Pool<wbr>Status</a></dt><dt><a class="struct" href="struct.ProxyTestResult.html" title="struct weibo_crawler_node::commands::ProxyTestResult">Proxy<wbr>Test<wbr>Result</a></dt><dt><a class="struct" href="struct.SystemMetrics.html" title="struct weibo_crawler_node::commands::SystemMetrics">System<wbr>Metrics</a></dt><dt><a class="struct" href="struct.TaskQueueStatus.html" title="struct weibo_crawler_node::commands::TaskQueueStatus">Task<wbr>Queue<wbr>Status</a></dt><dt><a class="struct" href="struct.TaskStatistics.html" title="struct weibo_crawler_node::commands::TaskStatistics">Task<wbr>Statistics</a></dt></dl><h2 id="functions" class="section-header">Functions<a href="#functions" class="anchor">§</a></h2><dl class="item-table"><dt><a class="fn" href="fn.add_account.html" title="fn weibo_crawler_node::commands::add_account">add_<wbr>account</a></dt><dt><a class="fn" href="fn.add_proxy.html" title="fn weibo_crawler_node::commands::add_proxy">add_<wbr>proxy</a></dt><dt><a class="fn" href="fn.get_account_pool_status.html" title="fn weibo_crawler_node::commands::get_account_pool_status">get_<wbr>account_<wbr>pool_<wbr>status</a></dt><dt><a class="fn" href="fn.get_browser_config.html" title="fn weibo_crawler_node::commands::get_browser_config">get_<wbr>browser_<wbr>config</a></dt><dt><a class="fn" href="fn.get_config.html" title="fn weibo_crawler_node::commands::get_config">get_<wbr>config</a></dt><dt><a class="fn" href="fn.get_node_status.html" title="fn weibo_crawler_node::commands::get_node_status">get_<wbr>node_<wbr>status</a></dt><dt><a class="fn" href="fn.get_performance_stats.html" title="fn weibo_crawler_node::commands::get_performance_stats">get_<wbr>performance_<wbr>stats</a></dt><dt><a class="fn" href="fn.get_proxy_pool_status.html" title="fn weibo_crawler_node::commands::get_proxy_pool_status">get_<wbr>proxy_<wbr>pool_<wbr>status</a></dt><dt><a class="fn" href="fn.get_system_metrics.html" title="fn weibo_crawler_node::commands::get_system_metrics">get_<wbr>system_<wbr>metrics</a></dt><dt><a class="fn" href="fn.get_task_queue_status.html" title="fn weibo_crawler_node::commands::get_task_queue_status">get_<wbr>task_<wbr>queue_<wbr>status</a></dt><dt><a class="fn" href="fn.get_task_statistics.html" title="fn weibo_crawler_node::commands::get_task_statistics">get_<wbr>task_<wbr>statistics</a></dt><dt><a class="fn" href="fn.login_account.html" title="fn weibo_crawler_node::commands::login_account">login_<wbr>account</a></dt><dt><a class="fn" href="fn.login_weibo.html" title="fn weibo_crawler_node::commands::login_weibo">login_<wbr>weibo</a></dt><dt><a class="fn" href="fn.refresh_account_cookies.html" title="fn weibo_crawler_node::commands::refresh_account_cookies">refresh_<wbr>account_<wbr>cookies</a></dt><dt><a class="fn" href="fn.remove_proxy.html" title="fn weibo_crawler_node::commands::remove_proxy">remove_<wbr>proxy</a></dt><dt><a class="fn" href="fn.restart_crawler.html" title="fn weibo_crawler_node::commands::restart_crawler">restart_<wbr>crawler</a></dt><dt><a class="fn" href="fn.start_crawler.html" title="fn weibo_crawler_node::commands::start_crawler">start_<wbr>crawler</a></dt><dt><a class="fn" href="fn.start_task_processing.html" title="fn weibo_crawler_node::commands::start_task_processing">start_<wbr>task_<wbr>processing</a></dt><dt><a class="fn" href="fn.stop_crawler.html" title="fn weibo_crawler_node::commands::stop_crawler">stop_<wbr>crawler</a></dt><dt><a class="fn" href="fn.stop_task_processing.html" title="fn weibo_crawler_node::commands::stop_task_processing">stop_<wbr>task_<wbr>processing</a></dt><dt><a class="fn" href="fn.test_browser.html" title="fn weibo_crawler_node::commands::test_browser">test_<wbr>browser</a></dt><dt><a class="fn" href="fn.test_proxy.html" title="fn weibo_crawler_node::commands::test_proxy">test_<wbr>proxy</a></dt><dt><a class="fn" href="fn.update_browser_config.html" title="fn weibo_crawler_node::commands::update_browser_config">update_<wbr>browser_<wbr>config</a></dt><dt><a class="fn" href="fn.update_config.html" title="fn weibo_crawler_node::commands::update_config">update_<wbr>config</a></dt></dl></section></div></main></body></html>