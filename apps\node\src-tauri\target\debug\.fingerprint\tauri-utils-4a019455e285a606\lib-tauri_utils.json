{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2241668132362809309, "path": 16510283438653641893, "deps": [[561782849581144631, "html5ever", false, 14639028180087473753], [3150220818285335163, "url", false, 8909501939063229777], [3334271191048661305, "windows_version", false, 1969817703771997694], [4071963112282141418, "serde_with", false, 17047765076280946177], [4899080583175475170, "semver", false, 2766431719234362145], [5986029879202738730, "log", false, 3004741351825838512], [6262254372177975231, "kuchiki", false, 4824423883880766140], [6606131838865521726, "ctor", false, 10070633853003180732], [6997837210367702832, "infer", false, 15329160264258388340], [8008191657135824715, "thiserror", false, 4083214613751541842], [8569119365930580996, "serde_json", false, 2740896963817221287], [9689903380558560274, "serde", false, 4202820352788480372], [10301936376833819828, "json_patch", false, 15539730164648236439], [11989259058781683633, "dunce", false, 16187145314844288619], [14132538657330703225, "brotli", false, 7292260503248787949], [15622660310229662834, "walkdir", false, 6313107436405264959], [15932120279885307830, "memchr", false, 12618675062812920877], [17155886227862585100, "glob", false, 1058744767332970092], [17186037756130803222, "phf", false, 14458966127025066762]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-4a019455e285a606\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}