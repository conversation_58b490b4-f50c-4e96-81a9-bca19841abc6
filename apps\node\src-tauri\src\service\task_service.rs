use super::{Service, StartableService, MonitorableService, HealthStatus};
use crate::error::{AppError, Result};
use crate::repository::{RepositoryManager, PaginatedResult, Repository, PaginatedRepository};
use crate::storage::TaskRecord;
use async_trait::async_trait;
use std::sync::{Arc, atomic::{AtomicBool, AtomicU64, Ordering}};
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTaskRequest {
    pub task_type: i32,
    pub target_url: String,
    pub priority: i32,
    pub max_retries: i32,
    pub metadata: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateTaskRequest {
    pub task_type: Option<i32>,
    pub target_url: Option<String>,
    pub priority: Option<i32>,
    pub max_retries: Option<i32>,
    pub metadata: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TaskQuery {
    pub status: Option<i32>,
    pub task_type: Option<i32>,
    pub priority_min: Option<i32>,
    pub priority_max: Option<i32>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskMetrics {
    pub total_tasks: u64,
    pub pending_tasks: u32,
    pub running_tasks: u32,
    pub completed_tasks: u64,
    pub failed_tasks: u64,
    pub success_rate: f64,
    pub average_processing_time: f64,
    pub tasks_per_hour: f64,
    pub last_updated: DateTime<Utc>,
}

pub struct TaskService {
    repository_manager: Arc<RepositoryManager>,
    is_running: AtomicBool,
    processed_count: AtomicU64,
    failed_count: AtomicU64,
    start_time: RwLock<Option<DateTime<Utc>>>,
}

impl TaskService {
    pub fn new(repository_manager: Arc<RepositoryManager>) -> Self {
        Self {
            repository_manager,
            is_running: AtomicBool::new(false),
            processed_count: AtomicU64::new(0),
            failed_count: AtomicU64::new(0),
            start_time: RwLock::new(None),
        }
    }

    /// 创建新任务
    pub async fn create_task(&self, request: CreateTaskRequest) -> Result<TaskRecord> {
        let task_id = uuid::Uuid::new_v4().to_string();
        let now = Utc::now();

        let task = TaskRecord {
            id: 0, // 将由数据库自动生成
            task_id,
            task_type: request.task_type,
            target_url: request.target_url,
            priority: request.priority,
            status: 0, // 待处理
            retry_count: 0,
            max_retries: request.max_retries,
            metadata: request.metadata,
            assigned_at: None,
            started_at: None,
            completed_at: None,
            created_at: now,
            updated_at: now,
        };

        let created_task = self.repository_manager.task.create(task).await?;
        tracing::info!("Created task: {}", created_task.task_id);
        
        Ok(created_task)
    }

    /// 批量创建任务
    pub async fn create_tasks_batch(&self, requests: Vec<CreateTaskRequest>) -> Result<Vec<TaskRecord>> {
        let mut created_tasks = Vec::new();
        
        for request in requests {
            match self.create_task(request).await {
                Ok(task) => created_tasks.push(task),
                Err(e) => {
                    tracing::error!("Failed to create task: {}", e);
                    // 继续处理其他任务，不中断整个批次
                }
            }
        }
        
        Ok(created_tasks)
    }

    /// 获取任务详情
    pub async fn get_task(&self, task_id: &str) -> Result<Option<TaskRecord>> {
        // 这里需要通过 task_id 查找，但当前仓库只支持通过 id 查找
        // 需要扩展仓库方法或在这里实现查找逻辑
        let tasks = self.repository_manager.task.find_all().await?;
        Ok(tasks.into_iter().find(|t| t.task_id == task_id))
    }

    /// 更新任务
    pub async fn update_task(&self, task_id: &str, request: UpdateTaskRequest) -> Result<TaskRecord> {
        let task = self.get_task(task_id).await?
            .ok_or_else(|| AppError::Other(format!("Task not found: {}", task_id)))?;

        let updated_task = TaskRecord {
            task_type: request.task_type.unwrap_or(task.task_type),
            target_url: request.target_url.unwrap_or(task.target_url),
            priority: request.priority.unwrap_or(task.priority),
            max_retries: request.max_retries.unwrap_or(task.max_retries),
            metadata: request.metadata.or(task.metadata),
            updated_at: Utc::now(),
            ..task
        };

        self.repository_manager.task.update(updated_task.id, updated_task).await
    }

    /// 删除任务
    pub async fn delete_task(&self, task_id: &str) -> Result<()> {
        let task = self.get_task(task_id).await?
            .ok_or_else(|| AppError::Other(format!("Task not found: {}", task_id)))?;

        self.repository_manager.task.delete(task.id).await?;
        tracing::info!("Deleted task: {}", task_id);
        
        Ok(())
    }

    /// 获取任务列表（分页）
    pub async fn list_tasks(&self, page: u32, limit: u32) -> Result<PaginatedResult<TaskRecord>> {
        self.repository_manager.task.find_paginated(page, limit).await
    }

    /// 根据条件查询任务
    pub async fn query_tasks(&self, query: TaskQuery) -> Result<Vec<TaskRecord>> {
        // 这里需要实现复杂查询逻辑
        // 当前简化实现，只支持按状态查询
        if let Some(status) = query.status {
            self.repository_manager.task.find_by_status(status).await
        } else if let Some(task_type) = query.task_type {
            self.repository_manager.task.find_by_type(task_type).await
        } else {
            self.repository_manager.task.find_all().await
        }
    }

    /// 获取待处理任务
    pub async fn get_pending_tasks(&self, limit: u32) -> Result<Vec<TaskRecord>> {
        self.repository_manager.task.get_pending_tasks(limit).await
    }

    /// 开始处理任务
    pub async fn start_task(&self, task_id: &str) -> Result<()> {
        self.repository_manager.task.start_task(task_id).await?;
        tracing::info!("Started task: {}", task_id);
        Ok(())
    }

    /// 完成任务
    pub async fn complete_task(&self, task_id: &str) -> Result<()> {
        self.repository_manager.task.complete_task(task_id).await?;
        self.processed_count.fetch_add(1, Ordering::Relaxed);
        tracing::info!("Completed task: {}", task_id);
        Ok(())
    }

    /// 任务失败
    pub async fn fail_task(&self, task_id: &str) -> Result<()> {
        self.repository_manager.task.fail_task(task_id).await?;
        self.failed_count.fetch_add(1, Ordering::Relaxed);
        tracing::warn!("Failed task: {}", task_id);
        Ok(())
    }

    /// 重置任务状态
    pub async fn reset_task(&self, task_id: &str) -> Result<()> {
        self.repository_manager.task.update_status(task_id, 0).await?;
        tracing::info!("Reset task: {}", task_id);
        Ok(())
    }

    /// 获取任务统计信息
    pub async fn get_task_statistics(&self) -> Result<TaskMetrics> {
        let stats = self.repository_manager.task.get_statistics().await?;
        let processed = self.processed_count.load(Ordering::Relaxed);
        let failed = self.failed_count.load(Ordering::Relaxed);
        
        let success_rate = if processed + failed > 0 {
            (processed as f64) / ((processed + failed) as f64) * 100.0
        } else {
            0.0
        };

        let start_time = self.start_time.read().await;
        let tasks_per_hour = if let Some(start) = *start_time {
            let duration = Utc::now().signed_duration_since(start);
            let hours = duration.num_seconds() as f64 / 3600.0;
            if hours > 0.0 {
                processed as f64 / hours
            } else {
                0.0
            }
        } else {
            0.0
        };

        Ok(TaskMetrics {
            total_tasks: stats.total,
            pending_tasks: stats.pending,
            running_tasks: stats.running,
            completed_tasks: stats.completed,
            failed_tasks: stats.failed,
            success_rate,
            average_processing_time: 0.0, // 需要额外计算
            tasks_per_hour,
            last_updated: Utc::now(),
        })
    }

    /// 清理旧任务
    pub async fn cleanup_old_tasks(&self, days: i32) -> Result<u64> {
        let deleted_count = self.repository_manager.task.cleanup_old_tasks(days).await?;
        tracing::info!("Cleaned up {} old tasks", deleted_count);
        Ok(deleted_count)
    }
}

impl Service for TaskService {
    fn name(&self) -> &'static str {
        "TaskService"
    }
}

#[async_trait]
impl StartableService for TaskService {
    async fn start(&self) -> Result<()> {
        if self.is_running.load(Ordering::Relaxed) {
            return Ok(());
        }

        self.is_running.store(true, Ordering::Relaxed);
        *self.start_time.write().await = Some(Utc::now());
        
        tracing::info!("Task service started");
        Ok(())
    }

    async fn stop(&self) -> Result<()> {
        if !self.is_running.load(Ordering::Relaxed) {
            return Ok(());
        }

        self.is_running.store(false, Ordering::Relaxed);
        *self.start_time.write().await = None;
        
        tracing::info!("Task service stopped");
        Ok(())
    }

    fn is_running(&self) -> bool {
        self.is_running.load(Ordering::Relaxed)
    }
}

#[async_trait]
impl MonitorableService for TaskService {
    type Metrics = TaskMetrics;

    async fn get_metrics(&self) -> Result<Self::Metrics> {
        self.get_task_statistics().await
    }

    async fn get_health_status(&self) -> Result<HealthStatus> {
        if !self.is_running() {
            return Ok(HealthStatus::Unhealthy("Service is not running".to_string()));
        }

        let metrics = self.get_metrics().await?;
        
        // 检查失败率
        if metrics.success_rate < 50.0 && metrics.total_tasks > 10 {
            return Ok(HealthStatus::Degraded(
                format!("Low success rate: {:.1}%", metrics.success_rate)
            ));
        }

        // 检查是否有太多待处理任务
        if metrics.pending_tasks > 1000 {
            return Ok(HealthStatus::Degraded(
                format!("High pending task count: {}", metrics.pending_tasks)
            ));
        }

        Ok(HealthStatus::Healthy)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::repository::{RepositoryManager, TaskRepository};
    use sqlx::SqlitePool;
    use std::sync::Arc;

    async fn create_test_service() -> TaskService {
        // 创建内存数据库用于测试
        let pool = SqlitePool::connect(":memory:").await.unwrap();
        let pool = Arc::new(pool);
        let repo_manager = Arc::new(RepositoryManager::new(pool));
        TaskService::new(repo_manager)
    }

    #[tokio::test]
    async fn test_service_lifecycle() {
        let service = create_test_service().await;

        assert!(!service.is_running());

        service.start().await.unwrap();
        assert!(service.is_running());

        service.stop().await.unwrap();
        assert!(!service.is_running());
    }

    #[tokio::test]
    async fn test_create_task() {
        let service = create_test_service().await;

        let request = CreateTaskRequest {
            task_type: 1,
            target_url: "https://example.com".to_string(),
            priority: 1,
            max_retries: 3,
            metadata: None,
        };

        let result = service.create_task(request).await;
        assert!(result.is_ok());

        let task = result.unwrap();
        assert_eq!(task.task_type, 1);
        assert_eq!(task.target_url, "https://example.com");
        assert_eq!(task.status, 0); // 待处理
    }

    #[tokio::test]
    async fn test_batch_create_tasks() {
        let service = create_test_service().await;

        let requests = vec![
            CreateTaskRequest {
                task_type: 1,
                target_url: "https://example1.com".to_string(),
                priority: 1,
                max_retries: 3,
                metadata: None,
            },
            CreateTaskRequest {
                task_type: 2,
                target_url: "https://example2.com".to_string(),
                priority: 2,
                max_retries: 3,
                metadata: None,
            },
        ];

        let result = service.create_tasks_batch(requests).await;
        assert!(result.is_ok());

        let tasks = result.unwrap();
        assert_eq!(tasks.len(), 2);
    }

    #[tokio::test]
    async fn test_health_status() {
        let service = create_test_service().await;

        // 服务未启动时应该是不健康的
        let health = service.get_health_status().await.unwrap();
        assert!(health.is_unhealthy());

        // 启动服务后应该是健康的
        service.start().await.unwrap();
        let health = service.get_health_status().await.unwrap();
        assert!(health.is_healthy());
    }

    #[tokio::test]
    async fn test_task_statistics() {
        let service = create_test_service().await;
        service.start().await.unwrap();

        let stats = service.get_task_statistics().await.unwrap();
        assert_eq!(stats.total_tasks, 0);
        assert_eq!(stats.pending_tasks, 0);
        assert_eq!(stats.running_tasks, 0);
        assert_eq!(stats.completed_tasks, 0);
        assert_eq!(stats.failed_tasks, 0);
    }
}
