use sqlx::SqlitePool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 从环境变量或默认值获取数据库URL
    let database_url = env::var("SQLITE_DATABASE_URL")
        .unwrap_or_else(|_| "sqlite:./data/crawler_node.db".to_string());

    println!("连接数据库: {}", database_url);

    // 连接数据库
    let pool = SqlitePool::connect(&database_url).await?;

    println!("插入种子数据...");

    // 插入示例代理数据
    sqlx::query!(
        r#"
        INSERT OR IGNORE INTO proxies (proxy_id, host, port, protocol, status, success_count, failure_count)
        VALUES 
            ('proxy_001', '*************', 8080, 'http', 1, 100, 5),
            ('proxy_002', '*************', 8080, 'http', 1, 95, 8),
            ('proxy_003', '*************', 3128, 'http', 2, 50, 20),
            ('proxy_004', '*************', 1080, 'socks5', 1, 120, 3),
            ('proxy_005', '*************', 8080, 'https', 1, 80, 10)
        "#
    )
    .execute(&pool)
    .await?;

    // 插入示例账号数据
    sqlx::query!(
        r#"
        INSERT OR IGNORE INTO accounts (account_id, username, password, status, login_count, risk_score)
        VALUES 
            ('account_001', 'test_user_001', 'password123', 1, 10, 0.1),
            ('account_002', 'test_user_002', 'password456', 1, 15, 0.05),
            ('account_003', 'test_user_003', 'password789', 3, 5, 0.8),
            ('account_004', 'test_user_004', 'passwordabc', 1, 20, 0.2),
            ('account_005', 'test_user_005', 'passworddef', 2, 0, 1.0)
        "#
    )
    .execute(&pool)
    .await?;

    // 插入示例任务数据
    sqlx::query!(
        r#"
        INSERT OR IGNORE INTO crawl_tasks (task_id, task_type, target_url, priority, status, retry_count, max_retries)
        VALUES 
            ('task_001', 1, 'https://weibo.com/u/**********', 5, 2, 0, 3),
            ('task_002', 2, 'https://weibo.com/**********/ABCDEFG', 3, 2, 0, 3),
            ('task_003', 3, 'https://weibo.com/ajax/statuses/buildComments', 4, 1, 1, 3),
            ('task_004', 4, 'https://weibo.com/p/100808topic', 2, 0, 0, 3),
            ('task_005', 1, 'https://weibo.com/u/**********', 5, 3, 3, 3)
        "#
    )
    .execute(&pool)
    .await?;

    println!("种子数据插入完成！");

    pool.close().await;
    Ok(())
}
