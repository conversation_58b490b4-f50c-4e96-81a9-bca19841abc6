#[cfg(test)]
mod tests {
    use weibo_crawler_node::browser::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BrowserConfig, LoginCredentials};
    use tokio;

    #[test]
    fn test_browser_config_creation() {
        let config = BrowserConfig::default();
        assert_eq!(config.headless, true);
        assert_eq!(config.viewport_width, 1920);
        assert_eq!(config.viewport_height, 1080);
        assert_eq!(config.timeout, 30000);
    }

    #[test]
    fn test_browser_manager_creation() {
        let config = BrowserConfig::default();
        let _manager = BrowserManager::new(config);
        // 如果能创建成功就说明结构体定义正确
    }

    #[tokio::test]
    async fn test_browser_test_function() {
        let config = BrowserConfig::default();
        let manager = BrowserManager::new(config);
        
        // 初始化
        manager.initialize().await.expect("初始化失败");
        
        // 测试浏览器功能
        let result = manager.test_browser().await;
        assert!(result.is_ok(), "浏览器测试应该成功");
        
        let title = result.unwrap();
        assert!(!title.is_empty(), "页面标题不应该为空");
        println!("页面标题: {}", title);
    }

    #[tokio::test]
    #[ignore] // 需要真实的登录凭据，所以默认忽略
    async fn test_weibo_login() {
        let config = BrowserConfig::default();
        let manager = BrowserManager::new(config);
        
        // 初始化
        manager.initialize().await.expect("初始化失败");
        
        // 测试登录（需要真实的用户名和密码）
        let credentials = LoginCredentials {
            username: "test_user".to_string(),
            password: "test_password".to_string(),
        };
        
        let result = manager.login_weibo(credentials).await;
        // 注意：这个测试可能会失败，因为我们使用的是测试凭据
        println!("登录结果: {:?}", result);
    }
}
