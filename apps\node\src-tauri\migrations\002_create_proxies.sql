-- 代理管理表
CREATE TABLE proxies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proxy_id TEXT UNIQUE NOT NULL,
    host TEXT NOT NULL,
    port INTEGER NOT NULL,
    protocol TEXT NOT NULL, -- http, https, socks5
    username TEXT,
    password TEXT,
    country TEXT,
    region TEXT,
    provider TEXT,
    status INTEGER NOT NULL DEFAULT 1, -- 1: Available, 2: Unavailable, 3: Maintenance, 4: Testing
    success_count INTEGER NOT NULL DEFAULT 0,
    failure_count INTEGER NOT NULL DEFAULT 0,
    last_used DATETIME,
    last_checked DATETIME,
    response_time INTEGER, -- 响应时间(毫秒)
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 代理健康检查记录表
CREATE TABLE proxy_health_checks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    proxy_id TEXT NOT NULL,
    is_healthy BOOLEAN NOT NULL,
    response_time INTEGER,
    error_message TEXT,
    checked_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (proxy_id) REFERENCES proxies(proxy_id)
);

-- 创建索引
CREATE INDEX idx_proxies_proxy_id ON proxies(proxy_id);
CREATE INDEX idx_proxies_status ON proxies(status);
CREATE INDEX idx_proxies_host_port ON proxies(host, port);
CREATE INDEX idx_proxy_health_checks_proxy_id ON proxy_health_checks(proxy_id);
CREATE INDEX idx_proxy_health_checks_checked_at ON proxy_health_checks(checked_at);

-- 创建更新时间触发器
CREATE TRIGGER update_proxies_updated_at
    AFTER UPDATE ON proxies
    FOR EACH ROW
BEGIN
    UPDATE proxies SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
