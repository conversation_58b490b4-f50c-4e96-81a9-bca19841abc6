import React from 'react'
import {
  ResponsiveContainer,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  Tooltip,
  Legend,
  TooltipProps
} from 'recharts'
import { cn } from '../../lib/utils'

// 基础图表配置
export interface BaseChartProps {
  data: any[]
  width?: number | string
  height?: number
  className?: string
  showGrid?: boolean
  showTooltip?: boolean
  showLegend?: boolean
  showXAxis?: boolean
  showYAxis?: boolean
  xAxisKey?: string
  yAxisKey?: string
  color?: string
  colors?: string[]
  loading?: boolean
  error?: string | null
  title?: string
  subtitle?: string
}

// 图表颜色主题
export const chartColors = {
  primary: '#3b82f6',
  secondary: '#8b5cf6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#06b6d4',
  light: '#6b7280',
  dark: '#374151'
}

export const chartColorPalette = [
  chartColors.primary,
  chartColors.secondary,
  chartColors.success,
  chartColors.warning,
  chartColors.danger,
  chartColors.info,
  chartColors.light,
  chartColors.dark
]

// 自定义 Tooltip 组件
export const CustomTooltip: React.FC<TooltipProps<any, any> & {
  formatter?: (value: any, name: string, props: any) => [React.ReactNode, string]
  labelFormatter?: (label: any) => React.ReactNode
}> = ({ 
  active, 
  payload, 
  label, 
  formatter,
  labelFormatter 
}) => {
  if (!active || !payload || !payload.length) {
    return null
  }

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 min-w-[120px]">
      {label && (
        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
          {labelFormatter ? labelFormatter(label) : label}
        </p>
      )}
      <div className="space-y-1">
        {payload.map((entry, index) => (
          <div key={index} className="flex items-center justify-between gap-2">
            <div className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {entry.name}
              </span>
            </div>
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
              {formatter ? formatter(entry.value, entry.name || '', entry)[0] : entry.value}
            </span>
          </div>
        ))}
      </div>
    </div>
  )
}

// 图表容器组件
export const ChartContainer: React.FC<{
  children: React.ReactNode
  title?: string
  subtitle?: string
  loading?: boolean
  error?: string | null
  className?: string
  height?: number
}> = ({ 
  children, 
  title, 
  subtitle, 
  loading, 
  error, 
  className,
  height = 300 
}) => {
  return (
    <div className={cn("bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700", className)}>
      {(title || subtitle) && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          {title && (
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {subtitle}
            </p>
          )}
        </div>
      )}
      
      <div className="p-4">
        {loading ? (
          <div className="flex items-center justify-center" style={{ height }}>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center" style={{ height }}>
            <div className="text-center">
              <div className="text-red-500 mb-2">
                <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">{error}</p>
            </div>
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={height}>
            {children}
          </ResponsiveContainer>
        )}
      </div>
    </div>
  )
}

// 图表网格配置
export const defaultGridConfig = {
  strokeDasharray: "3 3",
  stroke: "#e5e7eb",
  className: "opacity-30"
}

// 坐标轴配置
export const defaultXAxisConfig = {
  axisLine: false,
  tickLine: false,
  tick: { fontSize: 12, fill: "#6b7280" },
  className: "text-gray-500"
}

export const defaultYAxisConfig = {
  axisLine: false,
  tickLine: false,
  tick: { fontSize: 12, fill: "#6b7280" },
  className: "text-gray-500"
}

// 图例配置
export const defaultLegendConfig = {
  iconType: "circle" as const,
  wrapperStyle: {
    paddingTop: "20px",
    fontSize: "12px"
  }
}

// 数据格式化工具
export const formatters = {
  number: (value: number, decimals: number = 0) => {
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value)
  },
  
  percentage: (value: number, decimals: number = 1) => {
    return `${formatters.number(value, decimals)}%`
  },
  
  currency: (value: number, currency: string = 'CNY') => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency
    }).format(value)
  },
  
  bytes: (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  },
  
  duration: (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }
}

// 图表主题
export const chartTheme = {
  light: {
    background: '#ffffff',
    text: '#374151',
    grid: '#e5e7eb',
    tooltip: {
      background: '#ffffff',
      border: '#e5e7eb',
      text: '#374151'
    }
  },
  dark: {
    background: '#1f2937',
    text: '#f3f4f6',
    grid: '#374151',
    tooltip: {
      background: '#1f2937',
      border: '#374151',
      text: '#f3f4f6'
    }
  }
}
