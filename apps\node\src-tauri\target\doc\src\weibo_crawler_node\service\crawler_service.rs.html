<!DOCTYPE html><html lang="en"><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="generator" content="rustdoc"><meta name="description" content="Source of the Rust file `src\service\crawler_service.rs`."><title>crawler_service.rs - source</title><script>if(window.location.protocol!=="file:")document.head.insertAdjacentHTML("beforeend","SourceSerif4-Regular-6b053e98.ttf.woff2,FiraSans-Italic-81dc35de.woff2,FiraSans-Regular-0fe48ade.woff2,FiraSans-MediumItalic-ccf7e434.woff2,FiraSans-Medium-e1aa3f0a.woff2,SourceCodePro-Regular-8badfe75.ttf.woff2,SourceCodePro-Semibold-aa29a496.ttf.woff2".split(",").map(f=>`<link rel="preload" as="font" type="font/woff2" crossorigin href="../../../static.files/${f}">`).join(""))</script><link rel="stylesheet" href="../../../static.files/normalize-9960930a.css"><link rel="stylesheet" href="../../../static.files/rustdoc-1a91846b.css"><meta name="rustdoc-vars" data-root-path="../../../" data-static-root-path="../../../static.files/" data-current-crate="weibo_crawler_node" data-themes="" data-resource-suffix="" data-rustdoc-version="1.88.0 (6b00bc388 2025-06-23)" data-channel="1.88.0" data-search-js="search-f7877310.js" data-settings-js="settings-5514c975.js" ><script src="../../../static.files/storage-4e99c027.js"></script><script defer src="../../../static.files/src-script-63605ae7.js"></script><script defer src="../../../src-files.js"></script><script defer src="../../../static.files/main-7ef8a74a.js"></script><noscript><link rel="stylesheet" href="../../../static.files/noscript-893ab5e7.css"></noscript><link rel="alternate icon" type="image/png" href="../../../static.files/favicon-32x32-6580c154.png"><link rel="icon" type="image/svg+xml" href="../../../static.files/favicon-044be391.svg"></head><body class="rustdoc src"><!--[if lte IE 11]><div class="warning">This old browser is unsupported and will most likely display funky things.</div><![endif]--><nav class="sidebar"><div class="src-sidebar-title"><h2>Files</h2></div></nav><div class="sidebar-resizer"></div><main><rustdoc-search></rustdoc-search><section id="main-content" class="content"><div class="main-heading"><h1><div class="sub-heading">weibo_crawler_node\service/</div>crawler_service.rs</h1><rustdoc-toolbar></rustdoc-toolbar></div><div class="example-wrap digits-2"><pre class="rust"><code><a href=#1 id=1 data-nosnippet>1</a><span class="kw">use super</span>::{Service, StartableService, MonitorableService, HealthStatus};
<a href=#2 id=2 data-nosnippet>2</a><span class="kw">use </span><span class="kw">crate</span>::error::Result;
<a href=#3 id=3 data-nosnippet>3</a><span class="kw">use </span><span class="kw">crate</span>::repository::RepositoryManager;
<a href=#4 id=4 data-nosnippet>4</a><span class="kw">use </span>async_trait::async_trait;
<a href=#5 id=5 data-nosnippet>5</a><span class="kw">use </span>std::sync::{Arc, atomic::{AtomicBool, Ordering}};
<a href=#6 id=6 data-nosnippet>6</a><span class="kw">use </span>serde::{Serialize, Deserialize};
<a href=#7 id=7 data-nosnippet>7</a>
<a href=#8 id=8 data-nosnippet>8</a><span class="attr">#[derive(Debug, Clone, Serialize, Deserialize)]
<a href=#9 id=9 data-nosnippet>9</a></span><span class="kw">pub struct </span>CrawlerMetrics {
<a href=#10 id=10 data-nosnippet>10</a>    <span class="kw">pub </span>active_crawlers: u32,
<a href=#11 id=11 data-nosnippet>11</a>    <span class="kw">pub </span>total_requests: u64,
<a href=#12 id=12 data-nosnippet>12</a>    <span class="kw">pub </span>successful_requests: u64,
<a href=#13 id=13 data-nosnippet>13</a>    <span class="kw">pub </span>failed_requests: u64,
<a href=#14 id=14 data-nosnippet>14</a>    <span class="kw">pub </span>average_response_time: f64,
<a href=#15 id=15 data-nosnippet>15</a>    <span class="kw">pub </span>requests_per_minute: f64,
<a href=#16 id=16 data-nosnippet>16</a>}
<a href=#17 id=17 data-nosnippet>17</a>
<a href=#18 id=18 data-nosnippet>18</a><span class="kw">pub struct </span>CrawlerService {
<a href=#19 id=19 data-nosnippet>19</a>    repository_manager: Arc&lt;RepositoryManager&gt;,
<a href=#20 id=20 data-nosnippet>20</a>    is_running: AtomicBool,
<a href=#21 id=21 data-nosnippet>21</a>}
<a href=#22 id=22 data-nosnippet>22</a>
<a href=#23 id=23 data-nosnippet>23</a><span class="kw">impl </span>CrawlerService {
<a href=#24 id=24 data-nosnippet>24</a>    <span class="kw">pub fn </span>new(repository_manager: Arc&lt;RepositoryManager&gt;) -&gt; <span class="self">Self </span>{
<a href=#25 id=25 data-nosnippet>25</a>        <span class="self">Self </span>{
<a href=#26 id=26 data-nosnippet>26</a>            repository_manager,
<a href=#27 id=27 data-nosnippet>27</a>            is_running: AtomicBool::new(<span class="bool-val">false</span>),
<a href=#28 id=28 data-nosnippet>28</a>        }
<a href=#29 id=29 data-nosnippet>29</a>    }
<a href=#30 id=30 data-nosnippet>30</a>
<a href=#31 id=31 data-nosnippet>31</a>    <span class="kw">pub async fn </span>get_crawler_statistics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;CrawlerMetrics&gt; {
<a href=#32 id=32 data-nosnippet>32</a>        <span class="comment">// 暂时返回模拟数据
<a href=#33 id=33 data-nosnippet>33</a>        </span><span class="prelude-val">Ok</span>(CrawlerMetrics {
<a href=#34 id=34 data-nosnippet>34</a>            active_crawlers: <span class="number">0</span>,
<a href=#35 id=35 data-nosnippet>35</a>            total_requests: <span class="number">0</span>,
<a href=#36 id=36 data-nosnippet>36</a>            successful_requests: <span class="number">0</span>,
<a href=#37 id=37 data-nosnippet>37</a>            failed_requests: <span class="number">0</span>,
<a href=#38 id=38 data-nosnippet>38</a>            average_response_time: <span class="number">0.0</span>,
<a href=#39 id=39 data-nosnippet>39</a>            requests_per_minute: <span class="number">0.0</span>,
<a href=#40 id=40 data-nosnippet>40</a>        })
<a href=#41 id=41 data-nosnippet>41</a>    }
<a href=#42 id=42 data-nosnippet>42</a>}
<a href=#43 id=43 data-nosnippet>43</a>
<a href=#44 id=44 data-nosnippet>44</a><span class="kw">impl </span>Service <span class="kw">for </span>CrawlerService {
<a href=#45 id=45 data-nosnippet>45</a>    <span class="kw">fn </span>name(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="kw-2">&amp;</span><span class="lifetime">'static </span>str {
<a href=#46 id=46 data-nosnippet>46</a>        <span class="string">"CrawlerService"
<a href=#47 id=47 data-nosnippet>47</a>    </span>}
<a href=#48 id=48 data-nosnippet>48</a>}
<a href=#49 id=49 data-nosnippet>49</a>
<a href=#50 id=50 data-nosnippet>50</a><span class="attr">#[async_trait]
<a href=#51 id=51 data-nosnippet>51</a></span><span class="kw">impl </span>StartableService <span class="kw">for </span>CrawlerService {
<a href=#52 id=52 data-nosnippet>52</a>    <span class="kw">async fn </span>start(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#53 id=53 data-nosnippet>53</a>        <span class="self">self</span>.is_running.store(<span class="bool-val">true</span>, Ordering::Relaxed);
<a href=#54 id=54 data-nosnippet>54</a>        <span class="macro">tracing::info!</span>(<span class="string">"Crawler service started"</span>);
<a href=#55 id=55 data-nosnippet>55</a>        <span class="prelude-val">Ok</span>(())
<a href=#56 id=56 data-nosnippet>56</a>    }
<a href=#57 id=57 data-nosnippet>57</a>
<a href=#58 id=58 data-nosnippet>58</a>    <span class="kw">async fn </span>stop(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;()&gt; {
<a href=#59 id=59 data-nosnippet>59</a>        <span class="self">self</span>.is_running.store(<span class="bool-val">false</span>, Ordering::Relaxed);
<a href=#60 id=60 data-nosnippet>60</a>        <span class="macro">tracing::info!</span>(<span class="string">"Crawler service stopped"</span>);
<a href=#61 id=61 data-nosnippet>61</a>        <span class="prelude-val">Ok</span>(())
<a href=#62 id=62 data-nosnippet>62</a>    }
<a href=#63 id=63 data-nosnippet>63</a>
<a href=#64 id=64 data-nosnippet>64</a>    <span class="kw">fn </span>is_running(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; bool {
<a href=#65 id=65 data-nosnippet>65</a>        <span class="self">self</span>.is_running.load(Ordering::Relaxed)
<a href=#66 id=66 data-nosnippet>66</a>    }
<a href=#67 id=67 data-nosnippet>67</a>}
<a href=#68 id=68 data-nosnippet>68</a>
<a href=#69 id=69 data-nosnippet>69</a><span class="attr">#[async_trait]
<a href=#70 id=70 data-nosnippet>70</a></span><span class="kw">impl </span>MonitorableService <span class="kw">for </span>CrawlerService {
<a href=#71 id=71 data-nosnippet>71</a>    <span class="kw">type </span>Metrics = CrawlerMetrics;
<a href=#72 id=72 data-nosnippet>72</a>
<a href=#73 id=73 data-nosnippet>73</a>    <span class="kw">async fn </span>get_metrics(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;<span class="self">Self</span>::Metrics&gt; {
<a href=#74 id=74 data-nosnippet>74</a>        <span class="self">self</span>.get_crawler_statistics().<span class="kw">await
<a href=#75 id=75 data-nosnippet>75</a>    </span>}
<a href=#76 id=76 data-nosnippet>76</a>
<a href=#77 id=77 data-nosnippet>77</a>    <span class="kw">async fn </span>get_health_status(<span class="kw-2">&amp;</span><span class="self">self</span>) -&gt; <span class="prelude-ty">Result</span>&lt;HealthStatus&gt; {
<a href=#78 id=78 data-nosnippet>78</a>        <span class="kw">if </span>!<span class="self">self</span>.is_running() {
<a href=#79 id=79 data-nosnippet>79</a>            <span class="kw">return </span><span class="prelude-val">Ok</span>(HealthStatus::Unhealthy(<span class="string">"Service is not running"</span>.to_string()));
<a href=#80 id=80 data-nosnippet>80</a>        }
<a href=#81 id=81 data-nosnippet>81</a>        <span class="prelude-val">Ok</span>(HealthStatus::Healthy)
<a href=#82 id=82 data-nosnippet>82</a>    }
<a href=#83 id=83 data-nosnippet>83</a>}</code></pre></div></section></main></body></html>