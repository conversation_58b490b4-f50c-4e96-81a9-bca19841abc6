import React from 'react';
import { cn } from '../../lib/utils';

// Badge变体类型定义
export type BadgeVariant = 'default' | 'secondary' | 'destructive' | 'success' | 'warning' | 'info' | 'outline' | 'ghost';
export type BadgeSize = 'default' | 'sm' | 'lg';

// Badge变体样式函数
const getBadgeVariantStyles = (variant: BadgeVariant = 'default') => {
  const variants = {
    default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
    secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
    destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
    success: "border-transparent bg-success text-success-foreground hover:bg-success/80",
    warning: "border-transparent bg-warning text-warning-foreground hover:bg-warning/80",
    info: "border-transparent bg-info text-info-foreground hover:bg-info/80",
    outline: "text-foreground border-border",
    ghost: "hover:bg-accent hover:text-accent-foreground",
  };
  return variants[variant];
};

const getBadgeSizeStyles = (size: BadgeSize = 'default') => {
  const sizes = {
    default: "px-2.5 py-0.5 text-xs",
    sm: "px-2 py-0.5 text-xs",
    lg: "px-3 py-1 text-sm",
  };
  return sizes[size];
};

export interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: BadgeVariant;
  size?: BadgeSize;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  pulse?: boolean;
}

function Badge({
  className,
  variant = 'default',
  size = 'default',
  leftIcon,
  rightIcon,
  pulse = false,
  children,
  ...props
}: BadgeProps) {
  return (
    <div
      className={cn(
        "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        getBadgeVariantStyles(variant),
        getBadgeSizeStyles(size),
        pulse && "animate-pulse",
        className
      )}
      {...props}
    >
      {leftIcon && <span className="mr-1">{leftIcon}</span>}
      {children}
      {rightIcon && <span className="ml-1">{rightIcon}</span>}
    </div>
  );
}

// 状态Badge组件
export interface StatusBadgeProps extends Omit<BadgeProps, 'variant' | 'leftIcon'> {
  status: 'online' | 'offline' | 'warning' | 'maintenance' | 'error';
  showDot?: boolean;
}

function StatusBadge({ 
  status, 
  showDot = true, 
  children, 
  className,
  ...props 
}: StatusBadgeProps) {
  const statusConfig = {
    online: {
      variant: 'success' as BadgeVariant,
      label: '在线',
      dotColor: 'bg-success',
    },
    offline: {
      variant: 'destructive' as BadgeVariant,
      label: '离线',
      dotColor: 'bg-destructive',
    },
    warning: {
      variant: 'warning' as BadgeVariant,
      label: '警告',
      dotColor: 'bg-warning',
    },
    maintenance: {
      variant: 'info' as BadgeVariant,
      label: '维护中',
      dotColor: 'bg-info',
    },
    error: {
      variant: 'destructive' as BadgeVariant,
      label: '错误',
      dotColor: 'bg-destructive',
    },
  };

  const config = statusConfig[status];

  return (
    <Badge
      variant={config.variant}
      leftIcon={
        showDot ? (
          <div className={cn("w-2 h-2 rounded-full animate-pulse", config.dotColor)} />
        ) : undefined
      }
      className={className}
      {...props}
    >
      {children || config.label}
    </Badge>
  );
}

// 数字Badge组件
export interface CountBadgeProps extends Omit<BadgeProps, 'children'> {
  count: number;
  max?: number;
  showZero?: boolean;
}

function CountBadge({ 
  count, 
  max = 99, 
  showZero = false, 
  className,
  ...props 
}: CountBadgeProps) {
  if (count === 0 && !showZero) {
    return null;
  }

  const displayCount = count > max ? `${max}+` : count.toString();

  return (
    <Badge
      variant="destructive"
      size="sm"
      className={cn("min-w-[1.25rem] h-5 px-1 justify-center", className)}
      {...props}
    >
      {displayCount}
    </Badge>
  );
}

// 趋势Badge组件
export interface TrendBadgeProps extends Omit<BadgeProps, 'variant' | 'leftIcon'> {
  trend: 'up' | 'down' | 'neutral';
  value?: string | number;
}

function TrendBadge({ 
  trend, 
  value, 
  children, 
  className,
  ...props 
}: TrendBadgeProps) {
  const trendConfig = {
    up: {
      variant: 'success' as BadgeVariant,
      icon: (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3.293 9.707a1 1 0 010-1.414l6-6a1 1 0 011.414 0l6 6a1 1 0 01-1.414 1.414L10 4.414 4.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
        </svg>
      ),
    },
    down: {
      variant: 'destructive' as BadgeVariant,
      icon: (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M16.707 10.293a1 1 0 010 1.414l-6 6a1 1 0 01-1.414 0l-6-6a1 1 0 111.414-1.414L10 15.586l5.293-5.293a1 1 0 011.414 0z" clipRule="evenodd" />
        </svg>
      ),
    },
    neutral: {
      variant: 'secondary' as BadgeVariant,
      icon: (
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
        </svg>
      ),
    },
  };

  const config = trendConfig[trend];

  return (
    <Badge
      variant={config.variant}
      leftIcon={config.icon}
      className={className}
      {...props}
    >
      {children || value}
    </Badge>
  );
}

export { Badge, StatusBadge, CountBadge, TrendBadge, getBadgeVariantStyles, getBadgeSizeStyles };
