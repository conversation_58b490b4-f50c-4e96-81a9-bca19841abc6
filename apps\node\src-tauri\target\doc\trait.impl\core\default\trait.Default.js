(function() {
    var implementors = Object.fromEntries([["weibo_crawler_node",[["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"weibo_crawler_node/browser/struct.BrowserConfig.html\" title=\"struct weibo_crawler_node::browser::BrowserConfig\">BrowserConfig</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"weibo_crawler_node/config/struct.AppConfig.html\" title=\"struct weibo_crawler_node::config::AppConfig\">AppConfig</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"weibo_crawler_node/repository/struct.QueryParams.html\" title=\"struct weibo_crawler_node::repository::QueryParams\">QueryParams</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/struct.ServiceConfig.html\" title=\"struct weibo_crawler_node::service::ServiceConfig\">ServiceConfig</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/struct.ServiceLifecycleManager.html\" title=\"struct weibo_crawler_node::service::ServiceLifecycleManager\">ServiceLifecycleManager</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"weibo_crawler_node/service/struct.ServiceRegistry.html\" title=\"struct weibo_crawler_node::service::ServiceRegistry\">ServiceRegistry</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"weibo_crawler_node/struct.AppState.html\" title=\"struct weibo_crawler_node::AppState\">AppState</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"weibo_crawler_node/updater/struct.UpdateStatus.html\" title=\"struct weibo_crawler_node::updater::UpdateStatus\">UpdateStatus</a>"],["impl <a class=\"trait\" href=\"https://doc.rust-lang.org/1.88.0/core/default/trait.Default.html\" title=\"trait core::default::Default\">Default</a> for <a class=\"struct\" href=\"weibo_crawler_node/window_manager/struct.WindowConfig.html\" title=\"struct weibo_crawler_node::window_manager::WindowConfig\">WindowConfig</a>"]]]]);
    if (window.register_implementors) {
        window.register_implementors(implementors);
    } else {
        window.pending_implementors = implementors;
    }
})()
//{"start":57,"fragment_lengths":[2875]}